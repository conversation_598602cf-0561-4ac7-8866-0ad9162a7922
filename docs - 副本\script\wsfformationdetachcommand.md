orphan

:   

# WsfFormationDetachCommand

::: {.WsfFormationDetachCommand .inherits .WsfFormationCommand}
`WsfFormationDetachCommand`{.interpreted-text role="class"} is a command that will detach the formation to which it is assigned from its parent. Unlike `WsfFormationAttachCommand`{.interpreted-text role="class"}, this will not modify the attachment status of sub-formations of the formation to which the command is assigned. In the case that the leader of the formation was keeping station, the leader will instead change to flying level in a constant direction.
:::

## Methods

::: method
WsfFormationDetachCommand Construct()

Create a command that will detach the formation to which it is assigned from its parent.
:::
