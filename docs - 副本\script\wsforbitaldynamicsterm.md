orphan

:   

# WsfOrbitalDynamicsTerm

## Overview {#overview .WsfOrbitalDynamicsTerm}

The `WsfOrbitalDynamicsTerm`{.interpreted-text role="class"} represents one source of acceleration for platforms having a `WsfIntegratingSpaceMover`{.interpreted-text role="class"}. Orbital dynamics terms are member of a series for a `WsfOrbitalDynamics`{.interpreted-text role="class"} object which supplies the full acceleration for a platform. See also `Dynamical Term <orbital_dynamics_terms>`{.interpreted-text role="ref"}.

## Methods

::: method
string TermType()

Return a string identifying the type of term.
:::

::: method
Vec3 ComputeAcceleration(double aMass, Calendar aTime, Vec3 aPosition, Vec3 aVelocity)

Compute the acceleration for this term on an object with the given mass, position and velocity at the given time. Some dynamics terms will be unable to provide a non-zero result for the acceleration because they rely on runtime information available only when attached to an orbital dynamics, Please see the individual term types below.
:::

## Available Orbital Dynamics Terms

The following orbital dynamics terms are available for use in AFSIM:

-   `WsfEarthMonopoleTerm`{.interpreted-text role="class"}
-   `WsfEarthJ2Term`{.interpreted-text role="class"}
-   `WsfMoonMonopoleTerm`{.interpreted-text role="class"}
-   `WsfSunMonopoleTerm`{.interpreted-text role="class"}
-   `WsfJupiterMonopoleTerm`{.interpreted-text role="class"}
-   `WsfAtmosphericDragTerm`{.interpreted-text role="class"}
-   `WsfScriptedDynamicsTerm`{.interpreted-text role="class"}
