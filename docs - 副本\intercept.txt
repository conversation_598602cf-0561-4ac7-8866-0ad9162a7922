# intercept.rst.txt.md
orphan

:   



Script Type: `WsfInterceptManeuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} intercept

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [delta_time]() \... [maximum_delta_time]() \... [maximum_delta_v]() \... [optimize_time]() \... [optimize_delta_v]() \... [optimize_cost]() \... [tolerance]() \... `target_specification`{.interpreted-text role="doc"} \...

end_maneuver
:::

::: block
intercept_maneuver
:::

Perform a maneuver to intercept with the given platform. This maneuver begins with an initial `target`{.interpreted-text role="doc"} maneuver but does not complete until the time that the intercept is actually achieved. Options are provided to optimize when the maneuver occurs, either at the earliest possible time, or with minimum delta-V expended during that time.

> ::: note
> ::: title
> Note
> :::
>
> The script version of this maneuver, `WsfInterceptManeuver`{.interpreted-text role="class"}, is used dynamically to intercept track locations.
> :::
>
> ::: note
> ::: title
> Note
> :::
>
> Several conditions must be met before the maneuver can succeed. These conditions include the following:
> :::
>
> -   The platform must be valid at the start of the simulation.
> -   The transfer orbit can only be hyperbolic if the mover executing the maneuver supports hyperbolic propagation.
> -   The transfer orbit must not intersect earth.
> -   When optimizing, a valid solution must exist for the provided optimization option ([optimize_time]() or [optimize_delta_v]()).
> -   The expended energy for the transfer must be less than the available delta-v.