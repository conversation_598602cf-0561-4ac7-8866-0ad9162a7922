orphan

:   

# UCI_ControlRequestStatusMessage

## Overview {#overview .UCI_ControlRequestStatusMessage .inherits .UCI_Message}

For every `UCI_ControlRequestMessage`{.interpreted-text role="class"}, a corresponding [UCI_ControlRequestStatusMessage](#uci_controlrequeststatusmessage) is sent detailing the status of the message.

## Methods

::: method
string GetRemarks()

Returns the remarks of the message. The remarks specify the reasons for the response to the control request.
:::

::: method
bool IsApproved()

Returns true if the control request has been approved, false otherwise.
:::

::: method
bool IsDeleted()

Returns true if the control request has been deleted, false otherwise.
:::

::: method
bool IsPending()

Returns true if the control request is pending, false otherwise.
:::

::: method
bool IsRejected()

Returns true if the control request has been rejected, false otherwise.
:::

::: method
string UUID()

Returns the string value of the ControlRequestID UUID.
:::
