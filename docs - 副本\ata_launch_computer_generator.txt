# ata_launch_computer_generator.rst.txt.md
orphan

:   



::: {.command block=""}
ATA_LAUNCH_COMPUTER_GENERATOR
:::



[ATA_LAUNCH_COMPUTER_GENERATOR](#ata_launch_computer_generator) is a specialization of `weapon_tools`{.interpreted-text role="command"} to produce an air-to-air missile launch computer. The generator contains a `WSF_ATA_LAUNCH_COMPUTER`{.interpreted-text role="model"} instance internally, which is modified into a final output product so many of the commands are common between the two.



::: parsed-literal

tool ATA_LAUNCH_COMPUTER_GENERATOR

:   \... `weapon_tools`{.interpreted-text role="command"} Commands \... [altitude_and_mach]() \<length-value\> \<float-value\> [speed]() \<speed-value\> \... `WSF_ATA_LAUNCH_COMPUTER`{.interpreted-text role="model"} Commands \...

end_tool
:::



::: command
altitude_and_mach \<length-value\> \<float-value\>

Provide the altitude and mach number of the air-to-air missile firing. This setting will override any Speed value provided in [speed](), below.

Default: 10000 ft altitude, 0.6 Mach (Overrides the 0.0 altitude default of the weapon_tools base class.)
:::

::: command
speed \<speed-value\>

Provide the launch platform speed for the air-to-air missile firing. This setting will override any Mach value provided in [altitude_and_mach]().

Default: 383 knots = 0.6 Mach at 10000 ft altitude
:::