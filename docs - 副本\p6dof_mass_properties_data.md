orphan

:   

# P6DOF Mass Properties Data {#P6DOF_Mass_Properties_Data_Label}

Mass properties include the mass and moments of inertia for the object when empty (no fuel or payload). Additional mass properties contributions by fuel and payload are considered separately.

Each of these command blocks are detailed below.

::: command
mass_properties \... end_mass_properties

::: parsed-literal
mass_properties

> [mass]() \... [moment_of_inertia_ixx]() \... [moment_of_inertia_iyy]() \... [moment_of_inertia_izz]() \... [center_of_mass_x]() \... [center_of_mass_y]() \... [center_of_mass_z]() \...

end_mass_properties
:::
:::

::: command
mass \<mass-value\>

The (empty) mass of the object, not including fuel.
:::

::: command
moment_of_inertia_ixx \<angular-inertia-value\>

The (empty) moment of inertia about the x-axis the object, not including fuel.
:::

::: command
moment_of_inertia_iyy \<angular-inertia-value\>

The (empty) moment of inertia about the y-axis the object, not including fuel.
:::

::: command
moment_of_inertia_izz \<angular-inertia-value\>

The (empty) moment of inertia about the z-axis the object, not including fuel.
:::

::: command
center_of_mass_x \<length-value\>

The empty center of mass (in the object\'s x-direction) relative to the reference point.
:::

::: command
center_of_mass_y \<length-value\>

The empty center of mass (in the object\'s y-direction) relative to the reference point.
:::

::: command
center_of_mass_z \<length-value\>

The empty center of mass (in the object\'s z-direction) relative to the reference point.
:::

------------------------------------------------------------------------

Return to `p6dof_object_types`{.interpreted-text role="doc"} or `p6dof_object_type`{.interpreted-text role="doc"}
