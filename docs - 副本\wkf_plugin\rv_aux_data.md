orphan

:   

# Aux Data - Mystic

The Aux Data plugin adds data added to the simulation via the `aux_data <../aux_data>`{.interpreted-text role="doc"} command or aux data script interfaces to the `Platform Details <../wkf_plugin/rv_platform_data>`{.interpreted-text role="doc"} window.

![image](../images/rv_aux_data.png)

See `Platform Details <../wkf_plugin/rv_platform_data>`{.interpreted-text role="doc"} for more information.
