# compound.rst.txt.md
orphan

:   



Script Type: `WsfCompoundManeuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} compound

:   \... `(first maneuver)<orbital_event.maneuver>`{.interpreted-text role="ref"} \... `(second maneuver)<orbital_event.maneuver>`{.interpreted-text role="ref"}

end_maneuver
:::

The compound maneuver consists of two maneuvers nested and executed in time order as specified by their individual `constraints<orbital_event.constraints>`{.interpreted-text role="ref"}. It is useful in situations where two individual maneuvers execute closely in time so that is not obvious which should execute first.

::: parsed-literal
// Example: \"Apogee Kick\" Maneuver // (early simultaneous inclination and // eccentricity changes) maneuver compound maneuver change_eccentricity debug enabled eccentricity 0.0 execute_at apoapsis end_maneuver maneuver change_inclination debug enabled execute_at descending_node inclination 0 deg end_maneuver end_maneuver
:::