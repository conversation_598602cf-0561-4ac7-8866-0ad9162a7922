orphan

:   

# Overview

Define weapon effectiveness by creating a new or editing an existing weapon effects type.

## Creating a weapon_effects type

A weapon_effects command that occurs outside of a **platform(\_type)** definition will create a new *weapon_effects type*.

    weapon_effects <new-type-name> <base-type-name>
       weapon_effects subcommands
    end_weapon_effects

-   \<new-type-name\> : Name of the new weapon_effects type to be created. This must be unique among all the weapon_effects types.
-   \<base-type-name\> : Name of an existing or predefined weapon_effects type whose definition will be used as the initial definition of the new type.

### Subcommands

:Any of the Subcommands valid for *\<base-type-name\>*.

## Using a weapon_effects type

A weapon_effects command that occurs inside a weapon(\_type) definition indicates that engagement results for the weapon should be computed using the indicated weapon_effects type

    weapon
      weapon_effects <type-name>
    end_weapon

\<type-name\>

:   Name of an existing or predefined weapon_effects type whose definition will be used to create the new instance.
