# wiz_route.rst.txt.md
orphan

:   







Routes and orbits may be displayed on platforms via the `platform options<../wizard_platform_options>`{.interpreted-text role="doc"}.



Routes can be modified in the `text editor<../wizard_text_editor>`{.interpreted-text role="doc"} , on the `map display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"}, or the `route browser<../wkf_plugin/wiz_route_browser>`{.interpreted-text role="doc"}.



Right-clicking on a waypoint from the `map display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"} will give you the options to:

-   Delete the waypoint
-   Add a waypoint after the selected waypoint
-   Edit the waypoint



Selecting a waypoint and right clicking on the map will give you an option to add a waypoint at the cursor location after the selected waypoint.





The orbit preferences control how orbits will be displayed.



Sets the line width of the orbit in pixels.



When viewing an orbit on a flat-map or without the ECI camera (see `map display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"} preferences) the periods value will determine how many periods of the orbit will be displayed.