# distransmitter.rst.txt.md
orphan

:   



::: {.DisTransmitter .inherits .DisPdu}
::: parsed-literal
`DisTransmitter.EntityId`{.interpreted-text role="method"} `DisTransmitter.RadioId`{.interpreted-text role="method"} `DisTransmitter.RadioEntityType`{.interpreted-text role="method"} `DisTransmitter.TransmitState`{.interpreted-text role="method"} `DisTransmitter.InputSource`{.interpreted-text role="method"} `DisTransmitter.Location`{.interpreted-text role="method"} `DisTransmitter.RelativeLocation`{.interpreted-text role="method"} `DisTransmitter.AntennaPatternType`{.interpreted-text role="method"} `DisTransmitter.Frequency`{.interpreted-text role="method"} `DisTransmitter.FrequencyBandwidth`{.interpreted-text role="method"} `DisTransmitter.Power`{.interpreted-text role="method"} `DisTransmitter.ModulationType`{.interpreted-text role="method"} `DisTransmitter.CryptoSystem`{.interpreted-text role="method"} `DisTransmitter.CryptoKeyId`{.interpreted-text role="method"}
:::
:::



[DisTransmitter](#distransmitter) is an implementation of the DIS transmitter PDU. Transmitter PDUs are used to communicate information comm transmissions.



::: method
DisEntityId EntityId()

Returns the ID of the subject platform.
:::

::: method
DisEventId EventId()

Returns the ID associated with the transmitter event.
:::

::: method
int RadioId()

Returns an identifier for the radio on the emitting entity.
:::

::: method
DisRadioEntityType RadioEntityType()

Returns the radio entity type structure.
:::

::: method
int TransmitState()

Returns the transmit state of the PDU.
:::

::: method
int InputSource()

Returns the input source of the transmit event.
:::

::: method
Array\<double\> Location()

Returns the location of the transmit event. The location is in meters in a geocentric coordinate system.
:::

::: method
Array\<double\> RelativeLocation()

Returns the location of the transmit event in parent body coordinates in meters.
:::

::: method
int AntennaPatternType()

Returns an index associated with the antenna pattern type.
:::

::: method
int Frequency()

Returns the frequency of the transmit.
:::

::: method
double FrequencyBandwidth()

Return the frequency bandwidth of the transmit.
:::

::: method
double Power()

Returns the power of the transmit.
:::

::: method
DisModulationType ModulationType()

Returns the modulation type of the transmit.
:::

::: method
int CryptoSystem()

Return an index associated with the crypto system used for the transmit.
:::

::: method
int CryptoKeyId()

Returns an ID for the crypto system used for the transmit.
:::