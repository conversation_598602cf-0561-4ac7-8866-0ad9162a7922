orphan

:   

# sensor_plot_spherical_map

**Navigation:** `sensor_plot`{.interpreted-text role="command"} **spherical_map**

::: {.command block=""}
spherical_map \... end_spherical_map

::: parsed-literal
[spherical_map]()

> `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"}
>
> \# [Sensor Control](#sensor-control)
>
> [mode_name]() \... [automatic_target_cueing]() \... [fixed_sensor_position]() \| [fixed_target_position]() [sensor_platform_altitude]() \... [sensor_platform_yaw]() \... [sensor_platform_pitch]() \... [sensor_platform_roll]() \...
>
> \# [Jammer Control](#jammer-control)
>
> [jammer_to_signal_reference]() \... [jamming_platform_name]() \...
>
> [target_platform_type]() [target_speed]() [target_mach]() [target_heading]() [target_yaw]() [target_pitch]() [target_roll]()
>
> \# [Target Region](#target-region)
>
> [azimuth_limits]() \... [azimuth_step]() \... [elevation_limits]() \... [elevation_step]() \... [range]() \... [altitude]() \...
>
> \# [Output selection](#output-selection)
>
> [variable]() \... [script_variable]() \... [detection_reference]() \...
>
> [pd_map_file]() \... [header_line_1]() \... [header_line_2]() \... [header_line_3]() \... [output_column_limit]() \...
>
> [gnuplot_file]() \...

end_spherical_map
:::
:::

## Overview

The **spherical_map** command is used to generate a plot file of target detectability at a specified range or altitude over a range of azimuth and elevation viewing angles. Two types of plot files can be produced:

-   A \'pd map\' file for the program \'sigview\' to show a typical spherical plot.
-   A file suitable for plotting with the program \'gnuplot\'.

To create a file, the following process should be followed:

-   Define a platform type of the type specified by the command [target_platform_type]() (Default: **TARGET_PLATFORM_TYPE**) with the desired `radar <radar_signature>`{.interpreted-text role="command"}, `infrared <infrared_signature>`{.interpreted-text role="command"}, `optical <optical_signature>`{.interpreted-text role="command"} or `acoustic <acoustic_signature>`{.interpreted-text role="command"} signature depending on the type(s) of sensors being tested.
-   Define a platform type of **SENSOR_PLATFORM_TYPE** which contains the sensor to be used to detect the target.
-   Define the **spherical_map** input block with:
    -   [range](), [altitude](), [azimuth_limits]() and [elevation_limits]() (and optionally [azimuth_step]() and [elevation_step]() commands to define the spherical mesh of sample points.
    -   An optional [target_speed]() command to specify the target speed.
    -   Output selection commands.

## Sensor Control

::: command
mode_name \<mode_name\>

Specifies the name of the mode to be used if the sensor is a multi-mode sensor.

**Default** The default mode of the sensor. This will be the value of the `sensor.initial_mode`{.interpreted-text role="command"} command of the sensor (if defined) or the first mode (if `sensor.initial_mode`{.interpreted-text role="command"} was not defined).
:::

::: command
automatic_target_cueing \<boolean-value\>

If \'true\', the sensor will be cued to point at the target when performing a detection chance. If \'false\', the sensor will be remain in its initial condition.

**Default** true (The sensor will always be cued to point at the target)
:::

::: command
fixed_sensor_position
:::

::: command
fixed_target_position

Defines which object is held at a constant location at the center of the sample sphere, while the other object is moved over the sample sphere.

-   fixed_sensor_position means the sensor is fixed at the center of the sample sphere while the target is moved over the sample sphere.
-   fixed_target_position means the target is fixed at the center of the sample sphere while the sensor is moved over the sample sphere.

**Default** fixed_sensor_position
:::

::: command
sensor_platform_altitude \<length-value\>

Specify the altitude of the sensing platform.

**Default** 0 meters
:::

::: command
sensor_platform_yaw \<angle-value\>
:::

::: command
sensor_platform_pitch \<angle-value\>
:::

::: command
sensor_platform_roll \<angle-value\>

Specify the orientation of the sensing platform with respect to the direction of flight. The yaw angle is added to the heading and the pitch and roll angles are used directly as specified. This is useful for examining sensor coverage when the platform is flying in some other condition other than straight-and-level.

**Default** 0 degrees for all angles
:::

## Jammer Control

::: command
jammer_to_signal_reference \<db-ratio-value\>

Specifies the jammer-to-signal (J/S) reference to be used when plotting the \'**required_jamming_power**\' variable.

**Default** 0.0 db
:::

::: command
jamming_platform_name \<platform-name\>

Specifies the platform that will be used to calculate the required jamming power. This input is to be used when plotting the \'**required_jamming_power**\' variable for the location of the jammer system.

**Default** TARGET_PLATFORM_TYPE
:::

## Target Region

::: command
azimuth_limits \<min-az-value angle-value\> \<max-az-value angle-value\>

Specifies the azimuth extent of the viewing angle samples.

**Default** -180 deg 180 deg
:::

::: command
azimuth_step \<angle-value\>

Specifies the angle increment between azimuth samples.

**Default** 1 deg
:::

::: command
elevation_limits \<min-el-value angle-value\> \<max-el-value angle-value\>

Specifies the elevation extent of the viewing angle samples.

**Default** -90 deg 90 deg
:::

::: command
elevation_step \<angle-value\>

Specifies the angle increment between elevation samples.

**Default** 1 deg
:::

::: command
range \<length-value\>

Specifies the range to the sample point.

::: note
::: title
Note
:::

If specified the range will be fixed to this value and the [altitude]() input will not be used.
:::
:::

::: command
altitude \<length-value\>

Specifies the altitude to the sample point.

::: note
::: title
Note
:::

If specified the target altitude will be fixed to this value and the [range]() input will not be used.
:::
:::

## Output Selection

::: command
detection_reference \<db-ratio-value\>

The reference signal-to-noise ratio used to determine the **required_rcs** or **rcs_required** [variable]().

**Default** 12.8 dB
:::

::: command
pd_map_file \<file-name\>

Specifies the name of the file to which \'pd map\' output will be written. The name does not imply that only **pd** can be written, but rather denotes a common file format.

**Default** \'pd map\' output will not be produced.
:::

::: command
header_line_1 \<text\>
:::

::: command
header_line_2 \<text\>
:::

::: command
header_line_3 \<text\>

Specifies the text to be contained in the first three lines of the output file when [pd_map_file]() is specified.

**Default** all header lines are blank.
:::

::: command
output_column_limit \<integer\>

Specifies the maximum number of columns per physical line in the output file when [pd_map_file]() is specified.

**Default** 100

::: note
::: title
Note
:::

If the file is to be imported into a spreadsheet such as Microsoft Excel, this value should be set so that the rows do not have to be split into multiple physical lines.
:::
:::

::: command
gnuplot_file \<file-name\>

Specifies the name of the file to which \'gnuplot\' output will be written.

**Default** \'gnuplot\' output will not be produced.
:::
