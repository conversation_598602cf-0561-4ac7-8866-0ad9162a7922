orphan

:   

# event_pipe

::: {.contents local="" depth="1"}
:::

::: {.command block=""}
event_pipe \... end_event_pipe

::: parsed-literal

[event_pipe](#event_pipe)

:   [file]() \<file-name\> [use_preset]() \[ default \| low \| high \| full \] [disable]() \<group-name\> [enable]() \<group-name\> [maximum_mover_update_interval]() \<time-value\> [entity_state_angle_threshold]() \<angle-value\> [entity_state_position_threshold]() \<length-value\> [disable_entity_state_thresholds]() [entity_state_maximum_interval]() \<time-value\> [visual_part_update_interval]() \<time-value\>

end_event_pipe
:::
:::

## Overview

## Commands

::: command
file \<file-name\>

Specifies the name of the file to which the event recording is written. If the file already exists then it will be overwritten.

::: note
::: title
Note
:::

To insert the run number in the file name use \"*%d*\".
:::

For example:

    file events_%d.aer
:::

::: command
use_preset \[ default \| low \| high \| full \]

Configures the messages that will be output. By default, the default preset is used. The messages output presets are as follows:

default & low:

-   BASE_DATA
-   ENTITY_STATE
-   DRAW
-   DETECTION_CHANGE
-   COMMENT
-   TRACK
-   XIO
-   AUX_DATA
-   ROUTE_CHANGED
-   ZONES

high:

-   BASE_DATA
-   ENTITY_STATE
-   DRAW
-   DETECTION_CHANGE
-   COMMENT
-   TRACK
-   XIO
-   AUX_DATA
-   ROUTE_CHANGED
-   TRACK_UPDATE
-   COMM_INFO
-   MESSAGE_RECEIVED
-   MESSAGE_TRANSMITTED
-   MESSAGE_HOP
-   BEHAVIOR_TREE

full:

-   BASE_DATA
-   ENTITY_STATE
-   DRAW
-   DETECTION_CHANGE
-   COMMENT
-   TRACK
-   XIO
-   AUX_DATA
-   ROUTE_CHANGED
-   TRACK_UPDATE
-   COMM_INFO
-   MESSAGE_RECEIVED
-   MESSAGE_TRANSMITTED
-   MESSAGE_HOP
-   BEHAVIOR_TREE
-   DETECTION_ATTEMPT
:::

::: command
disable \<group-name\>
:::

::: command
enable \<group-name\>

Specifies the names of the event groups to be included or excluded in the file. These commands are typically specified multiple times to select the events of interest. The commands are processed in order of appearance with each successive command selecting or deselecting events as appropriate.

The groups are listed below with the events associated with them.

BASE_DATA

-   [MsgPlatformInfo](#msgplatforminfo)
-   [MsgPlatformStatus](#msgplatformstatus)
-   [MsgPartStatus](#msgpartstatus)
-   [MsgSensorModeChange](#msgsensormodechange)
-   [MsgSensorModeDefinition](#msgsensormodedefinition)
-   [MsgSetDate](#msgsetdate)
-   [MsgVisualPartDefinition](#msgvisualpartdefinition)
-   [MsgExecData](#msgexecdata)
-   [MsgScriptData](#msgscriptdata)
-   [MsgResource](#msgresource)

ENTITY_STATE

-   [MsgEntityState](#msgentitystate)
-   [MsgPartArticulationStatus](#msgpartarticulationstatus)

DETECTION_CHANGE

-   [MsgSensorDetectionChange](#msgsensordetectionchange)
-   [MsgPlatformAppearanceChanged](#msgplatformappearancechanged)

TRACK

-   [MsgLocalTrackCreated](#msglocaltrackcreated)
-   [MsgLocalTrackDrop](#msglocaltrackdrop)
-   [MsgLocalTrackCorrelation](#msglocaltrackcorrelation)
-   [MsgLocalTrackDecorrelation](#msglocaltrackdecorrelation)
-   [MsgSensorTrackCreated](#msgsensortrackcreated)
-   [MsgSensorTrackDrop](#msgsensortrackdrop)

TRACK_UPDATE

-   [MsgLocalTrackUpdate](#msglocaltrackupdate)
-   [MsgSensorTrackUpdate](#msgsensortrackupdate)

DETECTION_ATTEMPT

-   [MsgDetectAttempt](#msgdetectattempt)

DRAW

-   [MsgDrawCommand](#msgdrawcommand)

MESSAGE_RECEIVED

-   [MsgMessageReceived](#msgmessagereceived)

MESSAGE_TRANSMITTED

-   [MsgMessageTransmitted](#msgmessagetransmitted)

MESSAGE_HOP

-   [MsgMessageHop](#msgmessagehop)

COMM_INFO

-   [MsgCommInfo](#msgcomminfo)
-   [MsgRouterInfo](#msgrouterinfo)
-   [MsgNetworkInfo](#msgnetworkinfo)

COMMENT

-   [MsgComment](#msgcomment)

XIO

-   [MsgXioHeartbeat](#msgxioheartbeat)

AUX_DATA

-   [MsgAuxData](#msgauxdata)

ROUTE_CHANGED

-   [MsgRouteChanged](#msgroutechanged)

ZONES

-   [MsgCircularZone](#msgcircularzone)
-   [MsgEllipticalZone](#msgellipticalzone)
-   [MsgPolygonalZone](#msgpolygonalzone)
-   [MsgZoneSet](#msgzoneset)

BEHAVIOR_TREE

-   [MsgBehaviorTree](#msgbehaviortree)
-   [MsgBehaviorTreeState](#msgbehaviortreestate)
:::

::: command
maximum_mover_update_interval \<time-value\>

If this value is less than a mover\'s `mover.update_interval`{.interpreted-text role="command"}, the mover will use this value instead. If this value is set to 0, the event_pipe will take no active role in mover update timing, which may cause odd behavior in movers with no update-interval.

**Default:** 5 seconds
:::

::: command
entity_state_angle_threshold \<angle-value\>

Specifies an angle threshold to control entity-state publish rates. Every time a mover is updated, the previous orientation will be compared to the current orientation, and if the difference does not exceed the threshold, a new entity state will be ignored.

**Default:** 3 degrees
:::

::: command
entity_state_position_threshold \<length-value\>

Specifies a length threshold to control entity-state publish rates. Every time a mover is updated, the extrapolated previous location will be compared to the current location, and if the length between them does not exceed the threshold, a new entity state will be ignored.

**Default:** 1 meter
:::

::: command
disable_entity_state_thresholds

Disables the entity-state thresholds, causing entity-state to be published at the movers\' update intervals. This may be overwritten by further calls to [entity_state_position_threshold]() or [entity_state_angle_threshold]().
:::

::: command
entity_state_maximum_interval \<time-value\>

Specifies a maximum interval of time between entity-state updates. If a platform does not receive an update to its location within this interval, a new state, extrapolated from the last state, will be published.

**Default:** 10 seconds
:::

::: command
visual_part_update_interval \<time-value\>

Specifies the update period at which visual part articulations will be published. Time values should be greater than zero. If unset, visual parts\' articulations will not be published.

**Default:** 0 seconds
:::

## Example

The following would show all events except those dealing with track updates

::: parsed-literal

[event_pipe](#event_pipe)

:   file events.aer use_preset full disable TRACK_UPDATED

end_event_pipe
:::

## Messages

### MsgEntityState

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   double damageFactor
-   Vec3d locationWCS
-   bool velocityWCSvalid
-   Vec3f velocityWCS
-   bool accelerationWCSvalid
-   Vec3f accelerationWCS
-   bool orientationWCSvalid
-   Vec3f orientationWCS
-   bool fuelCurrentValid
-   double fuelCurrent
-   bool machNumberValid
-   double machNumber

### MsgPlatformInfo

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   bool nameValid
-   string name
-   bool typesValid
-   list\<string\> types
-   bool sideValid
-   string side
-   bool iconValid
-   string icon
-   bool markingValid
-   string marking
-   bool spatialDomainValid
-   [SpatialDomain](#spatialdomain) spatialDomain
-   bool commandChainsValid
-   list\<[CommandChain](#commandchain)\> commandChains
-   bool categoriesValid
-   list\<string\> categories
-   bool disIdValid
-   [DisEntityId](#disentityid) disId
-   bool fuelCapacityValid
-   double fuelCapacity

### MsgLocalTrackCreated

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex

### MsgLocalTrackUpdate

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track](#track) track

### MsgLocalTrackDrop

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex

### MsgLocalTrackCorrelation

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) localTrackId
-   [Track_Id](#track_id) addedTrackId

### MsgLocalTrackDecorrelation

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) localTrackId
-   [Track_Id](#track_id) removedTrackId

### MsgSensorModeChange

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string sensorName
-   string modeName
-   bool activate

### MsgPlatformStatus

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   bool broken
-   bool removed

### MsgPlatformAppearanceChanged

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   unsigned int appearance

### MsgPartStatus

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string partName
-   [PartType](#parttype) partType
-   bool on
-   bool broken
-   bool disabled

### MsgComment

-   double simTime
-   uint8 simIndex
-   string text
-   bool platformIndexValid
-   unsigned int platformIndex

### MsgDetectAttempt

-   double simTime
-   uint8 simIndex
-   bool sensorPlatformIndexValid
-   unsigned int sensorPlatformIndex
-   bool sensorNameValid
-   string sensorName
-   bool targetPlatformIndexValid
-   unsigned int targetPlatformIndex
-   bool beamValid
-   int beam
-   bool rangeErrorValid
-   float rangeError
-   bool azimuthErrorValid
-   float azimuthError
-   bool elevationErrorValid
-   float elevationError
-   bool rangeRateErrorValid
-   float rangeRateError
-   bool pdValid
-   float pd
-   bool requiredPdValid
-   float requiredPd
-   bool reportedSideValid
-   string reportedSide
-   bool reportedTypeValid
-   string reportedType
-   [EM_Interaction](#em_interaction) interaction

### MsgDrawCommand

-   double simTime
-   uint8 simIndex
-   list\<[MsgDrawCommand_Item](#msgdrawcommand_item)\> items
-   bool layerValid
-   string layer

### MsgMessageReceived

-   double simTime
-   uint8 simIndex
-   bool xmtrPlatformIndexValid
-   unsigned int xmtrPlatformIndex
-   bool xmtrCommNameValid
-   string xmtrCommName
-   bool rcvrPlatformIndexValid
-   unsigned int rcvrPlatformIndex
-   bool rcvrCommNameValid
-   string rcvrCommName
-   bool messageTypeValid
-   string messageType
-   bool messageSubTypeValid
-   string messageSubType
-   bool messageSizeValid
-   int messageSize
-   bool serialNumberValid
-   unsigned int serialNumber

### MsgMessageTransmitted

-   double simTime
-   uint8 simIndex
-   bool xmtrPlatformIndexValid
-   unsigned int xmtrPlatformIndex
-   bool xmtrCommNameValid
-   string xmtrCommName
-   bool rcvrPlatformIndexValid
-   unsigned int rcvrPlatformIndex
-   bool messageTypeValid
-   string messageType
-   bool messageSubTypeValid
-   string messageSubType
-   bool messageSizeValid
-   int messageSize
-   bool serialNumberValid
-   unsigned int serialNumber

### MsgSensorDetectionChange

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   string sensorName
-   unsigned int targetIndex
-   bool detected

### MsgSensorTrackCreated

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex
-   string sensorName

### MsgSensorTrackUpdate

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track](#track) track

### MsgSensorTrackDrop

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex

### MsgSensorModeDefinition

-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   string sensorName
-   string modeName
-   list\<[BeamDefinition](#beamdefinition)\> beamList

### MsgPartArticulationStatus

-   double simTime
-   uint8 simIndex
-   unsigned int ownerId
-   string partName
-   [PartType](#parttype) partType
-   Vec3f locationECS
-   Vec3f orientationECS

### MsgSetDate

-   double simTime
-   uint8 simIndex
-   int year
-   int month
-   int day
-   float time

### MsgVisualPartDefinition

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string partName
-   [PartType](#parttype) partType
-   string partIcon

### MsgExecData

-   double simTime
-   uint8 simIndex
-   string execPath
-   string cmdLine
-   string version
-   string features
-   string extensions
-   string timeStamp

### MsgScriptData

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string key
-   [MsgScriptData_Value](#msgscriptdata_value) value

### MsgResource

-   double simTime
-   uint8 simIndex
-   [ResourceType](#resourcetype) resourceType
-   string resourceId
-   list\<string\> resourceLocation

### MsgMessageHop

-   double simTime
-   uint8 simIndex
-   unsigned int xmtrPlatormIndex
-   string xmtrCommName
-   unsigned int rcvrPlatformIndex
-   string rcvrCommName
-   string messageType
-   string messageSubType
-   int messageSize
-   unsigned int serialNumber

### MsgXioHeartbeat

-   double simTime
-   uint8 simIndex
-   [ApplicationUniqueId](#applicationuniqueid) applicationId
-   float timeStamp

### MsgAuxData

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   list\<[AuxDataValue](#auxdatavalue)\> auxData

### MsgBookmark

-   double simTime
-   uint8 simIndex
-   double time
-   string msgType
-   string msgText

### MsgRouteChanged

-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   vec3 location
-   float heading
-   list\<[Waypoint](#waypoint)\> route

### MsgCircularZone

-   [CircularZoneInfo](#circularzoneinfo) zoneInfo

### MsgEllipticalZone

-   [EllipticalZoneInfo](#ellipticalzoneinfo) zoneInfo

### MsgPolygonalZone

-   [PolygonalZoneInfo](#polygonalzoneinfo) zoneInfo

### MsgZoneSet

-   string zoneSetName
-   string parentPlatform
-   bool isGlobal
-   ColorF lineColor
-   ColorF fillColor
-   list\<[CircularZoneInfo](#circularzoneinfo)\> circularZones
-   list\<[EllipticalZoneInfo](#ellipticalzoneinfo)\> ellipticalZones
-   list\<[PolygonalZoneInfo](#polygonalzoneinfo)\> polygonalZones

### MsgCommInfo

-   string commName
-   string parentPlatform
-   string commAddress
-   string commType
-   string networkName
-   string networkAddress
-   string routerName
-   string gatewayAddress
-   string gatewayCommName
-   string gatewayPlatformName
-   list\<[link](#link)\> links
-   StringList linkAddresses

### MsgRouterInfo

-   string routerName
-   string commName
-   string parentPlatform
-   string commAddress
-   string routerType
-   string gatewayAddress
-   string gatewayCommName
-   int hopLimit
-   bool useDefaultProtocol
-   bool useMulticastProtocol

### MsgNetworkInfo

-   string networkName
-   string networkType
-   string networkAddress

## Structures

### ApplicationUniqueId

-   unsigned int a
-   unsigned int b
-   unsigned int c

### CommandChain

-   string name
-   string commander

### DisEntityId

-   unsigned int site
-   unsigned int application
-   unsigned int entity

### Track_Id

-   int localTrackNumber
-   string ownerId

### Track

-   [Track_Id](#track_id) trackId
-   bool startTimeValid
-   double startTime
-   bool updateTimeValid
-   double updateTime
-   bool originatorIndexValid
-   unsigned int originatorIndex
-   bool sensorNameValid
-   string sensorName
-   bool sensorModeValid
-   string sensorMode
-   bool updateCountValid
-   int updateCount
-   bool originatorWCSValid
-   Vec3d originatorWCS
-   bool locationWCSValid
-   Vec3d locationWCS
-   bool rangeValid
-   float range
-   bool rangeErrorValid
-   float rangeError
-   bool bearingValid
-   float bearing
-   bool bearingErrorValid
-   float bearingError
-   bool elevationValid
-   float elevation
-   bool elevationErrorValid
-   float elevationError
-   bool rangeRateValid
-   float rangeRate
-   bool rangeRateErrorValid
-   float rangeRateError
-   bool trackQualityValid
-   float trackQuality
-   bool signalToNoiseValid
-   float signalToNoise
-   bool pixelCountValid
-   float pixelCount
-   bool spatialDomainValid
-   [SpatialDomain](#spatialdomain) spatialDomain
-   bool stateCovarianceValid
-   [Covariance](#covariance) stateCovariance
-   bool targetIndexValid
-   unsigned int targetIndex

### Covariance

-   unsigned char rowCount
-   unsigned char colCount
-   list\<float\> values

### EM_Interaction

-   [EM_Interaction_Status](#em_interaction_status) status
-   bool xmtrWCSValid
-   Vec3d xmtrWCS
-   bool rcvrWCSValid
-   Vec3d rcvrWCS
-   bool targetWCSValid
-   Vec3d targetWCS
-   bool rcvrToTargetValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) rcvrToTarget
-   bool targetToRcvrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) targetToRcvr
-   bool xmtrToTargetValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) xmtrToTarget
-   bool targetToXmtrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) targetToXmtr
-   bool xmtrToRcvrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) xmtrToRcvr
-   bool rcvrToXmtrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) rcvrToXmtr
-   bool rcvrBeamValid
-   [EM_Interaction_BeamData](#em_interaction_beamdata) rcvrBeam
-   bool xmtrBeamValid
-   [EM_Interaction_BeamData](#em_interaction_beamdata) xmtrBeam
-   bool maskingFactorValid
-   float maskingFactor
-   bool infraredSignatureValid
-   float infraredSignature
-   bool infraredSignatureAzimuthValid
-   float infraredSignatureAzimuth
-   bool infraredSignatureElevationValid
-   float infraredSignatureElevation
-   bool radarSignatureValid
-   float radarSignature
-   bool radarSignatureAzimuthValid
-   float radarSignatureAzimuth
-   bool radarSignatureElevationValid
-   float radarSignatureElevation
-   bool opticalSignatureValid
-   float opticalSignature
-   bool opticalSignatureAzimuthValid
-   float opticalSignatureAzimuth
-   bool opticalSignatureElevationValid
-   float opticalSignatureElevation
-   bool transmittedPowerValid
-   float transmittedPower
-   bool powerDensityAtTargetValid
-   float powerDensityAtTarget
-   bool receivedPowerValid
-   float receivedPower
-   bool rcvrNoisePowerValid
-   float rcvrNoisePower
-   bool clutterPowerValid
-   float clutterPower
-   bool interferencePowerValid
-   float interferencePower
-   bool signalToNoiseValid
-   float signalToNoise
-   bool propagationFactorValid
-   float propagationFactor
-   bool absorbtionFactorValid
-   float absorbtionFactor
-   bool detectionThresholdValid
-   float detectionThreshold
-   bool pixelCountValid
-   float pixelCount
-   bool zoomAttenuationFactorValid
-   float zoomAttenuationFactor

### EM_Interaction_Status

-   bool rcvrRangeLimitsValid
-   bool rcvrRangeLimits
-   bool rcvrAltitudeLimitsValid
-   bool rcvrAltitudeLimits
-   bool rcvrAngleLimitsValid
-   bool rcvrAngleLimits
-   bool rcvrHorizonMaskingValid
-   bool rcvrHorizonMasking
-   bool rcvrTerrainMaskingValid
-   bool rcvrTerrainMasking
-   bool xmtrRangeLimitsValid
-   bool xmtrRangeLimits
-   bool xmtrAltitudeLimitsValid
-   bool xmtrAltitudeLimits
-   bool xmtrAngleLimitsValid
-   bool xmtrAngleLimits
-   bool xmtrHorizonMaskingValid
-   bool xmtrHorizonMasking
-   bool xmtrTerrainMaskingValid
-   bool xmtrTerrainMasking
-   bool signalLevelValid
-   bool signalLevel

### EM_Interaction_RelativeData

-   float range
-   float trueAzimuth
-   float trueElevation
-   float apparentAzimuth
-   float apparentElevation

### EM_Interaction_BeamData

-   float azimuth
-   float elevation
-   float gain

### MsgDrawCommand_Item

-   [DrawType](#drawtype) drawType
-   bool idValid
-   unsigned int id
-   bool colorValid
-   unsigned int color
-   bool durationValid
-   float duration
-   bool drawSizeValid
-   unsigned char drawSize
-   bool drawStyleValid
-   unsigned char drawStyle
-   bool drawStyle2Valid
-   unsigned char drawStyle2
-   bool orientationValid
-   Vec3f orientation
-   bool axisValid
-   Vec3f axis
-   bool textValid
-   string text
-   bool vertex1Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex1
-   bool vertex2Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex2
-   bool vertex3Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex3
-   bool vertex4Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex4

### MsgDrawCommand_Vertex

-   [MsgDrawCommand_VertexType](#msgdrawcommand_vertextype) type
-   bool relativePlatformIndexValid
-   unsigned int relativePlatformIndex
-   bool xyzValid
-   Vec3f xyz

### BeamDefinition

-   int beamId
-   float r0_range
-   int r0_rangeType
-   float minRange
-   float maxRange
-   int stabilized
-   float tilt
-   [FieldOfView](#fieldofview) fov

### FieldOfView

-   int shape
-   list\<Vec2f\> points

### DrawPair

-   float threshold
-   float draw

### AuxDataValue

-   string name
-   [AuxDataType](#auxdatatype) type
-   bool boolean
-   int integer
-   double real
-   string text

### Waypoint

-   [WaypointLocationType](#waypointlocationtype) locationType
-   string label
-   double locationX
-   double locationY
-   bool altitudeValid
-   double altitude
-   bool headingValid
-   double heading
-   bool gotoIdValid
-   string gotoId

## Unions

### MsgScriptData_Value

-   double floating
-   int integer
-   bool boolean
-   string text

## Enumerations

### SpatialDomain

-   unknown
-   land
-   air
-   surface
-   subsurface
-   space

### PartType

-   sensor
-   mover
-   comm
-   processor
-   visual

### DrawType

-   line
-   point
-   icon
-   ellipse
-   erase
-   ellipsoid
-   quadrilateral
-   text
-   timer

### MsgDrawCommand_VertexType

-   unset_vertex
-   absolute_wcs
-   relative_zero
-   relative_ecs
-   relative_ned
-   relative_ss

### GeometryResult

-   in_progress
-   dud
-   target_impact
-   far_away_in_air
-   far_away_above_ground
-   far_away_ground_impact
-   target_proximity_air_burst
-   target_proximity_above_ground
-   target_proximity_ground_impact
-   part_disabled_or_destroyed

### ResourceType

-   other
-   dted
-   raw_dted
-   geotiff_dem

### AuxDataType

-   Unknown
-   Boolean
-   Integer
-   Real
-   Text

### WaypointLocationType

-   LatitudeAndLongitude
-   RelativeOffset

### CommonZoneData

-   string zoneName
-   string parentPlatform
-   string referencePlatform
-   bool isGlobal
-   ColorF lineColor
-   ColorF fillColor
-   double maxAltitude
-   double minAltitude
-   double referenceLat
-   double referenceLon
-   double heading
-   bool locationDefined

### CircularZoneInfo

-   [CommonZoneData]{.title-ref} commonInfo
-   double radius
-   double minRadius
-   double startAngle
-   double stopAngle

### EllipticalZoneInfo

-   [CommonZoneData]{.title-ref} commonInfo
-   double latAxis
-   double lonAxis
-   double minRadius
-   double startAngle
-   double stopAngle

### PolygonalZoneInfo

-   [CommonZoneData]{.title-ref} commonInfo
-   list\<Vec2d\> points
-   bool useLatLon

### link

-   string platformName
-   string commName

## Advanced Behavior Tree Structures

### BehaviorTreeNode

-   string filePath
-   int64 fileModTime
-   uint nodeId
-   uint treeId
-   string nodeType
-   string nodeName
-   uint numChildren
-   list\<int\> childrenIds
-   bool isRootNode

### BehaviorTreeNodeExec

-   uint nodeId
-   string nodeName
-   [NodeExecState](#nodeexecstate) execState
-   string tooltipText

### BoolDictEntry

-   string key
-   bool value

### IntDictEntry

-   string key
-   int value

### DoubleDictEntry

-   string key
-   double value

### StringDictEntry

-   string key
-   string value

### TrackDictEntry

-   string key
-   [Track](#track) value

### BehaviorTreePlatformInfo

-   string name
-   StringList types
-   string side

### PlatformDictEntry

-   string key
-   [BehaviorTreePlatformInfo](#behaviortreeplatforminfo) value

### BehaviorTreeBlackboardData

-   uint nodeId
-   string sharedBlackboard
-   list\<[BoolDictEntry](#booldictentry)\> boolDict
-   list\<[IntDictEntry](#intdictentry)\> intDict
-   list\<[DoubleDictEntry](#doubledictentry)\> doubleDict
-   list\<[StringDictEntry](#stringdictentry)\> stringDict
-   list\<[TrackDictEntry](#trackdictentry)\> trackDict
-   list\<[PlatformDictEntry](#platformdictentry)\> platformDict

## Advanced Behavior Tree Messages

### MsgBehaviorTree

-   index platformIndex
-   uint id
-   string treeName
-   string treeDesc
-   list\<[BehaviorTreeNode](#behaviortreenode)\> nodes

### MsgBehaviorTreeState

-   index platformIndex
-   uint treeId
-   list\<[BehaviorTreeNodeExec](#behaviortreenodeexec)\> execList
-   list\<[BehaviorTreeBlackboardData](#behaviortreeblackboarddata)\> blackboardList

## Advanced Behavior Tree Enumerations

### NodeExecState

-   running
-   success
-   failure
-   idle
-   halted
-   disabled
