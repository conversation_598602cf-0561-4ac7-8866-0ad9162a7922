# uci_strike_command_status_message.rst.txt.md
orphan

:   





This message holds the status for strike commands.



::: method
void CommandUUID()

Returns the UUID of the command.
:::

::: method
bool IsAccepted()

Returns true if the command state equals UCI_ACCEPTED, returns false otherwise.
:::

::: method
bool IsReceived()

Returns true if the command state equals UCI_RECEIVED, returns false otherwise.
:::

::: method
bool IsRejected()

Returns true if the command state equals UCI_REJECTED, returns false otherwise.
:::

::: method
bool IsRemoved()

Returns true if the command state equals UCI_REMOVED, returns false otherwise.
:::