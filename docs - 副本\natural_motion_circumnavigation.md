orphan

:   

# Natural Motion Circumnavigation

**Script Type:** `WsfNaturalMotionCircumnavigation`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} natural_motion_circumnavigation

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [target_platform]() \... [orbit_size]() \... [orbit_phase]() \... [out_of_plane_amplitude]() \... [out_of_plane_phase]() \... [delta_time]() \... [maximum_delta_time]() \... [maximum_delta_v]() \... [optimize_time]() \... [optimize_delta_v]() \... [optimize_cost]() \... [tolerance]() \...

end_maneuver
:::

::: block
natural_motion_circumnavigation
:::

Perform a maneuver that brings the executing platform into a natural motion circumnavigation (NMC) of another platform. The resulting motion of the executing platform is the superposition of an elliptical relative orbit in the orbital plane of the target platform, and a harmonic oscillation in the out-of-plane direction. For details on the relative orbit, please see `Natural Motion Circumnavigation Details <nmc_details>`{.interpreted-text role="doc"}.

In addition to the final relative motion, the executing platform will perform maneuvers similar to a `rendezvous`{.interpreted-text role="doc"} to transfer from its initial orbit to the final NMC. Because of this transfer, this maneuver also has many options that overlap with `rendezvous`{.interpreted-text role="doc"}.

::: command
target_platform \<string-value\>

The target platform around which the executing platform will perform a NMC.
:::

::: command
orbit_size \<length-value\>

The semi-major-axis of the relative orbit.
:::

::: command
orbit_phase \<angle-value\>

The phase along the relative orbit at which the executing platform will be inserted into the NMC.
:::

::: command
out_of_plane_amplitude \<length-value\>

The amplitude of the out-of-plane oscillation.
:::

::: command
out_of_plane_phase \<angle-value\>

The phase in the harmonic oscillation at which the executing platform will be inserted into the NMC.
:::
