# change_eccentricity.rst.txt.md
orphan

:   



Script Type: `WsfChangeEccentricity`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} change_eccentricity

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [eccentricity]() \...

end_maneuver
:::

A maneuver representing a change in the eccentricity of the orbit, making the orbit more or less elongated in shape. Changing the eccentricity will also necessarily change the semi-major axis. This maneuver is often executed to circularize an orbit (eccentricity = 0).

::: command
eccentricity \<real-value\>

The desired eccentricity of the final orbit.

::: note
::: title
Note
:::

The final orbit must be elliptical (or circular), so the allowed values are 0.0 \<= [eccentricity]() \< 1.0.
:::

::: note
::: title
Note
:::

If the initial orbit is not circular (eccentricity == 0), a `constraint<orbital_event.constraints>`{.interpreted-text role="ref"} of `periapsis`{.interpreted-text role="command"} or `apoapsis`{.interpreted-text role="command"} must be specified.
:::
:::