# AFSIM Platform Agent

一个基于向量数据库的AFSIM Platform脚本生成和参数查询智能助手。

## 功能特性

- 📚 **多文档索引**: 支持批量处理和索引MD格式的AFSIM文档
- 🔍 **智能查询**: 基于向量相似度的Platform参数查询
- 🤖 **中文总结**: 自动将英文技术文档转换为中文说明
- 🚀 **脚本生成**: 根据配置自动生成Platform脚本
- 💾 **向量存储**: 基于Chroma的高效向量数据库
- 🌐 **多语言支持**: 支持中英文混合处理

## 技术栈

- **向量数据库**: ChromaDB
- **嵌入模型**: sentence-transformers (多语言支持)
- **LLM**: DeepSeek API (支持OpenAI兼容接口)
- **文档处理**: Markdown + BeautifulSoup
- **配置管理**: YAML

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制并编辑配置文件：

```bash
cp config/config.yaml config/config.yaml.local
```

设置环境变量：

```bash
export DEEPSEEK_API_KEY="your_deepseek_api_key"
```

### 3. 准备文档

将AFSIM的MD文档放入 `docs/` 目录：

```
docs/
├── platform.md
├── sensors.md
└── weapons.md
```

### 4. 索引文档

```bash
python main.py index docs/
```

### 5. 查询参数

```bash
python main.py query "platform position parameters"
```

### 6. 生成脚本

```bash
python main.py generate --platform-name "my_fighter" --side "BLUE" --position "39.9,116.4"
```

## 使用方法

### 命令行模式

```bash
# 索引文档
python main.py index <文档目录> [--force]

# 查询参数
python main.py query "<查询文本>" [--top-k 5]

# 生成脚本
python main.py generate --platform-name <名称> [其他参数...]

# 列出所有参数
python main.py list

# 查看状态
python main.py status

# 交互模式
python main.py interactive
```

### 交互模式

```bash
python main.py interactive
```

进入交互模式后可以使用以下命令：

- `query <查询文本>` - 查询Platform参数
- `generate <平台名称>` - 生成Platform脚本
- `list` - 列出所有参数
- `status` - 显示状态
- `help` - 显示帮助
- `quit` - 退出

### Python API

```python
from core.platform_agent import AFSIMPlatformAgent

# 初始化Agent
agent = AFSIMPlatformAgent()
agent.initialize()

# 索引文档
result = agent.index_documents("docs/")

# 查询参数
result = agent.query_platform_parameters("position altitude")

# 生成脚本
config = {
    "platform_name": "fighter_01",
    "platform_type": "WSF_PLATFORM",
    "side": "BLUE",
    "position": {"lat": 39.9, "lon": 116.4},
    "altitude": 10000
}
result = agent.generate_platform_script(config)
```

## 配置说明

### 向量数据库配置

```yaml
vector_db:
  provider: "chroma"
  path: "data/chroma_db"
  collection_name: "afsim_platform_docs"
  embedding_model_path: "models/paraphrase-multilingual-mpnet-base-v2"
  chunk_size: 500
  similarity_threshold: 0.7
```

### LLM配置

```yaml
llm:
  provider: "deepseek"
  deepseek:
    api_key: ""  # 或设置环境变量 DEEPSEEK_API_KEY
    base_url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    temperature: 0.1
    max_tokens: 4000
```

### 文档处理配置

```yaml
document_processing:
  docs_path: "docs"
  chunk_size: 500
  chunk_overlap: 50
  min_content_length: 50
  encoding: "utf-8"
```

## 目录结构

```
afsim_platform_agent/
├── config/
│   └── config.yaml          # 配置文件
├── core/
│   ├── platform_agent.py    # 主Agent类
│   ├── vector_database.py   # 向量数据库管理
│   ├── document_processor.py # 文档处理
│   └── chinese_summarizer.py # 中文总结
├── data/                    # 数据目录
├── docs/                    # 文档目录
├── logs/                    # 日志目录
├── main.py                  # 主启动脚本
├── requirements.txt         # 依赖包
└── README.md               # 说明文档
```

## 示例

### 查询示例

```bash
$ python main.py query "platform altitude position"

🔍 查询Platform参数: platform altitude position
✅ 查询结果:
   - 找到结果数: 5

📋 中文总结:
这些参数主要用于设置平台的空间位置信息：

1. **altitude** - 设置平台的高度，单位通常为米或英尺
2. **position** - 设置平台的地理位置，包括经纬度坐标
3. **geo_point** - 地理点位置设置的另一种方式

这些参数在创建飞行器、舰船等平台时必须设置，用于确定平台在仿真环境中的初始位置。
```

### 脚本生成示例

```bash
$ python main.py generate --platform-name "fighter_01" --side "BLUE" --position "39.9,116.4" --altitude 10000

🚀 生成Platform脚本
✅ 脚本生成完成:

📜 生成的脚本:
==================================================
platform fighter_01 WSF_PLATFORM

    side BLUE
    position 39.9 116.4
    altitude 10000
    spatial_domain LAND

end_platform
==================================================

📝 中文说明:
这个配置创建了一个名为"fighter_01"的蓝方平台，位于北京附近的坐标位置，高度为10000米。
该平台使用标准的WSF_PLATFORM类型，适用于一般的仿真场景。
```

## 注意事项

1. **模型下载**: 首次使用需要下载嵌入模型，请确保网络连接正常
2. **API配置**: 使用前请正确配置DeepSeek API Key
3. **文档格式**: 支持标准Markdown格式的AFSIM文档
4. **内存使用**: 大量文档索引时可能占用较多内存

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型路径配置
   - 确保有足够的磁盘空间
   - 检查网络连接

2. **API调用失败**
   - 验证API Key是否正确
   - 检查网络连接
   - 确认API配额

3. **文档索引失败**
   - 检查文档格式是否正确
   - 确认文档路径存在
   - 查看日志文件获取详细错误信息

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
