# heatmap.rst.txt.md
orphan

:   

::: demo
heatmap
:::



| The heatmap_demo.txt main run file demonstrates a single aircraft with a heat map driving it behavior



| The heatmap_commander_demo.txt is the main run file demonstrating a commander with a heat map that is fed by subordinates and used to issue search jobs.
|  - The default setup for the scenario has two incoming read aircraft and one blue search aircraft.
|  - The platform availability block can be used to add an escort or additional red aircraft.
|  - fl_quantum_tasker_simple.txt was modified to issue search jobs
|  - The behavior_search.txt is also available under site_types/processors/quantum_agents