orphan

:   

# Print EM Interaction {#Print_EM_Interaction}

**Print EM Interaction** prints receiver, target, and transmitter interaction information common to a number of event records.

## Format

### One Way Interaction between Transmitter and Receiver

::: parsed-literal

PrintEM_AbsXmtrRcvrData

:   

    Xmtr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<heading\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

    Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<heading\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelXmtrRcvrData

:   

    Xmtr-\>Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

    Rcvr-\>Xmtr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_BeamData

:   XmtrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"} RcvrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"}
:::

### One Way Interaction between Receiver and Target

::: parsed-literal

PrintEM_AbsXmtrRcvrData

:   

    Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_AbsTargetData

:   

    Tgt: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<heading\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelXmtrRcvrData

:   Rcvr-\>Tgt Brg: \<bearing\> deg El: \<elevation\> deg Apparent: Brg: \<bearing\> deg El: \<elevation\> deg

PrintEM_RelTargetData

:   Tgt-\>Rcvr Brg: \<bearing\> deg El: \<elevation\> deg Apparent: Brg: \<bearing\> deg El: \<elevation\> deg

PrintEM_BeamData

:   RcvrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"}
:::

### Two Way Interaction between Transmitter, Target, and Receiver - Bistatic

    PrintEM_AbsXmtrRcvrData
     Xmtr: Type: <type> LLA: <lat> <lon> <alt> m Heading: <hdg> deg Pitch: <pitch> deg Roll: <roll> deg
      Speed: <speed> m/s
     Rcvr: Type: <type> LLA: <lat> <lon> <alt> m Heading: <hdg> deg Pitch: <pitch> deg Roll: <roll> deg
      Speed: <speed> m/s

    PrintEM_AbsTargetData
     Tgt: Type: <type> LLA: <lat> <lon> <alt> m Heading: <hdg> deg Pitch: <pitch> deg Roll: <roll> deg
      Speed: <speed> m/s

    PrintEM_RelXmtrRcvrData
     Xmtr->Tgt: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg

    PrintEM_RelTargetData
     Tgt->Xmtr: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg

    PrintEM_RelXmtrRcvrData
     Rcvr->Tgt: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg

    PrintEM_RelTargetData
     Tgt->Rcvr: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg

### Two Way Interaction between Transmitter, Target, and Receiver - Not Bistatic

::: parsed-literal

PrintEM_AbsXmtrRcvrData

:   

    Xmtr/Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_AbsTargetData

:   

    Tgt: LLA: Type: \<type\> \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelXmtrRcvrData

:   

    Xmtr/Rcvr-\>Tgt: LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelTargetData

:   Tgt-\>Xmtr/Rcvr: Brg: \<bearing\> deg El: \<elev\> deg Apparent: Brg: \<bearing\> deg El: \<elev\> deg

PrintEM_BeamData

:   XmtrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"} RcvrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"}
:::

### If Radar Signal Greater than 0.0 Add:

    Radar_Sig: <RadarSignal> dBsm (<RadarSignal (linear)> m^2) Az: <azimuth> deg El: <elev> deg

### If Optical Signal Greater than 0.0 Add:

    Optical_Sig: <RadarSignal> dBsm (<RadarSignal (linear)> m^2) Az: <azimuth> deg El: <elev> deg

### If Infrared Signal Greater than 0.0 Add:

    Infrared_Sig: <RadarSignal> dBsm (<RadarSignal (linear)> m^2) Az: <azimuth> deg El: <elev> deg

    If Signal to Noise > 0.0
     Background_radiant_intensity: <BackgroundRadiantIntensity> w/sr
     Contrast_radiant_intensity: <ContrastRadiantIntensity> w/sr
     Transmittance: <AbsorptionFactor>

### If AbsorptionFactor \> 0.0

    Absorption_factor: <AbsorptionFactor> dB (<AbsorptionFactor (linear)>)

### If PropagationFactor \> 0.0

    Propagation_factor_F^4: <PropagationFactor> dB (<PropagationFactor (linear)>) F: <PropagationFactor ^0.25>

### If Transmitted Power \> 0.0

    Xmtd_Power: <TransmittedPower (linear)> dBw

### If Received Power \> 0.0

    Rcvd_Power: <ReceivedPower (linear)> dBw

### If Receiver Noise Power \> 0.0

    Rcvr_Noise: <ReceiverNoisePower (linear)> dBw

### If Clutter Power \> 0.0

    Clutter_Power: <ClutterPower (linear)> dBw

### If Jammer Noise Power \> 0.0

    Noise_Jammer_Power: <NoiseJammerPower (linear)> dBw

### If Jammer Coherent Power \> 0.0

    Coherent_Jammer_Power: <CoherentJammerPower (linear)> dBw

### If Pixel Count \> 0.0

    Pixel_Count: <PixelCount>

### If Signal To Noise \> 0.0

    S/I: <SignalToNoise> dB
    If Detection Threshold > 0.0
     Threshold: <DetectionThreshold> dB
    If Received Power > 0.0
     S/N: <SignalPower/noisePower> dB S/(N+C): <SignalPower/(noisePower+clutterPower)> dB
      S/(N+C+J): <SignalPower/(noisePower+clutterPower+jammerPower)> dB

### If EW Effects Mask \> 0

    EW_Effects:
    If Dropped Track
     Drop_Track
    If Maintain Track
     Maintain_Track
    If Track Error
     Track_Error
    If Drop Message
     Drop_Message
    If Maintain Message
     Maintain_Message
    If Message_Error
     Message_Error
    If Jammer Power Effect
     Jammer_Power_Effect
    If False Target Effect
     False_Target

### If Various Limits have been Exceeded ONE or MORE of Following:

    Xmtr_Range_Limits_Exceeded
    Rcvr_Range_Limits_Exceeded
    Xmtr_Altitude_Limits_Exceeded
    Rcvr_Altitude_Limits_Exceeded
    Xmtr_Angle_Limits_Exceeded
    Rcvr_Angle_Limits_Exceeded
    Xmtr_Masked_By_Horizon
    Rcvr_Masked_By_Horizon
    Xmtr_Masked_By_Terrain
    Rcvr_Masked_By_Terrain
    Insufficient_Signal

## Beam Gain Data {#Beam_Gain_Data}

If beam gain is greater than 0.0 and Antenna EBS Mode is available:

    EBS Az: <angle> deg El: <angle> deg Omega: <angle> deg
    Gain: <gain> dB
