orphan

:   

# WsfCyberConstraint

## Methods {#methods .WsfCyberConstraint .inherits .WsfObject}

::: method
double CurrentResources()

Returns the current resources available for use by the owning platform.
:::

::: method
double TotalResources()

Returns the total resources available for use by the owning platform.
:::

::: method
int ConcurrentAttacks(WsfPlatform aPlatform, string aAttackType)

Returns the number of attacks of type \'aAttackType\' active against the platform \'aPlatform\'.
:::

::: method
int AttackCountAfterTime(string aAttackType, double aSimTime)

Returns the total number of attacks of type \'aAttackType\' that have occurred up to and including the time \'aSimTime\'.
:::
