# perform_staging.rst.txt.md
orphan

:   



Script Type: `WsfPerformStaging`{.interpreted-text role="class"}

::: parsed-literal

`event<orbital_event.event>`{.interpreted-text role="ref"} perform_staging

:   `<Common Mission Event Commands><orbital_event.common_commands>`{.interpreted-text role="ref"}

end_event
:::

Perform a staging operation for a multi-stage rocket, defined using `rocket orbital maneuvering<orbital_maneuvering_models.rocket>`{.interpreted-text role="ref"}. When this event is executed, the current stage is \"jettisoned,\" giving the rocket the mass properties of the later stages and the thrust and burn rate characteristics of the next stage defined.