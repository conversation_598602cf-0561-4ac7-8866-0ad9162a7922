orphan

:   

# uci_interface

::: {.contents local=""}
:::

::: {.command block=""}
uci_interface \... end_uci_interface

::: parsed-literal

uci_interface

:   [message_topic]() \... [service_name]() \... [service_descriptor]() \...

end_uci_interface
:::
:::

## Overview

The [uci_interface](#uci_interface) block provides configuration data for use when connecting and communicating `Universal Command and Control Interface (UCI)<universal_command_and_control>`{.interpreted-text role="doc"} messages over the UCI Abstract Service Bus (ASB). Options are provided to subscribe to supported UCI messages using various topics, specify the UCI service name used by the ASB, and the service descriptor associated with the UCI service name.

::: warning
::: title
Warning
:::

A uci_interface block must be provided in order to utilize UCI messaging; otherwise the simulation will execute without it.
:::

## Commands {#uci_interface.Commands}

::: command
message_topic \<Message-Type\> \<string\>

Publish / subscribe to a given UCI message with the supplied topic name.

The message types currently supported are the following:

-   `UCI_AMTI_ActivityMessage`{.interpreted-text role="class"}
-   `UCI_AMTI_CapabilityMessage`{.interpreted-text role="class"}
-   `UCI_AMTI_CapabilityStatusMessage`{.interpreted-text role="class"}
-   `UCI_AMTI_CommandMessage`{.interpreted-text role="class"}
-   `UCI_AMTI_CommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_AMTI_SettingsCommandMessage`{.interpreted-text role="class"}
-   `UCI_AMTI_SettingsCommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_ControlRequestMessage`{.interpreted-text role="class"}
-   `UCI_ControlRequestStatusMessage`{.interpreted-text role="class"}
-   `UCI_ControlStatusMessage`{.interpreted-text role="class"}
-   `UCI_EntityMessage`{.interpreted-text role="class"}
-   `UCI_ESM_ActivityMessage`{.interpreted-text role="class"}
-   `UCI_ESM_CapabilityMessage`{.interpreted-text role="class"}
-   `UCI_ESM_CapabilityStatusMessage`{.interpreted-text role="class"}
-   `UCI_ESM_CommandMessage`{.interpreted-text role="class"}
-   `UCI_ESM_CommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_ESM_SettingsCommandMessage`{.interpreted-text role="class"}
-   `UCI_ESM_SettingsCommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_POST_ActivityMessage`{.interpreted-text role="class"}
-   `UCI_POST_CapabilityMessage`{.interpreted-text role="class"}
-   `UCI_POST_CapabilityStatusMessage`{.interpreted-text role="class"}
-   `UCI_POST_CommandMessage`{.interpreted-text role="class"}
-   `UCI_POST_CommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_POST_SettingsCommandMessage`{.interpreted-text role="class"}
-   `UCI_POST_SettingsCommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_StrikeActivityMessage`{.interpreted-text role="class"}
-   `UCI_StrikeCapabilityMessage`{.interpreted-text role="class"}
-   `UCI_StrikeCapabilityStatusMessage`{.interpreted-text role="class"}
-   `UCI_StrikeCommandMessage`{.interpreted-text role="class"}
-   `UCI_StrikeCommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_StrikeSettingsCommandMessage`{.interpreted-text role="class"}
-   `UCI_StrikeSettingsCommandStatusMessage`{.interpreted-text role="class"}
-   `UCI_SubsystemStatusMessage`{.interpreted-text role="class"}
-   `UCI_SystemStatusMessage`{.interpreted-text role="class"}

::: note
::: title
Note
:::

The topic names of message publishers and subscribers must be identical.
:::

::: note
::: title
Note
:::

By default, these inputs do not need to be provided; connections are automatically made with the message name as the topic name. For example, the UCI \"Entity\" message is associated with the \"Entity\" topic name.
:::

Example:

    message_topic Entity tracks
:::

::: command
service_name \<string\>

Specifies the name of the UCI service identifier that is associated with the UCI interface\'s Abstract Bus. This service name is embedded into the xml as part of the service identifier for all transmitted messages over the Abstract Bus.

::: warning
::: title
Warning
:::

The supplied service name must match a corresponding service name as specified in the `activemq`{.interpreted-text role="doc"} configuration file.
:::

**Default:** None (Must be specified)

Example:

    service_name sense
:::

::: command
service_descriptor \<string\>

Specifies an arbitrary descriptive label to that is associated with the UCI service identifier. This label is embedded into the xml as part of the service identifier for all transmitted messages over the Abstract Bus.

**Default:** uci
:::
