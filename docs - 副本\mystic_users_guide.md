orphan

:   

# Mystic User\'s Guide

The Mystic User\'s Guide provides step-by-step instructions on how to perform various tasks. The content in this guide answers the \'How do I\...\' questions.

::: {.contents local=""}
:::

## Setup the simulation

Mystic uses the recorded results of an AFSIM simulation `event_pipe`{.interpreted-text role="command"}, known as AFSIM event recordings (AER) files generated by the simulation. AER files are binary files that similar in nature to event logs.

To generate an AER file from AFSIM input, add a block like the following:

::: parsed-literal

event_pipe

:   file myAERfile.aer

end_event_pipe
:::

There are additional options documented on the `event_pipe`{.interpreted-text role="command"} page.

## Launch Mystic

![image](./images/mystic_project_browser.png)

Double-clicking on the AER file in the Wizard project browser will launch Mystic.

Mystic may also be launched from its executable either with or without a AER file as an parameter.
