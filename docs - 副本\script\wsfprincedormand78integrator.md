orphan

:   

# WsfPrinceDormand78Integrator

## Overview {#overview .WsfPrinceDormand78Integrator .inherits .WsfOrbitalIntegrator}

The `WsfPrinceDormand78Integrator`{.interpreted-text role="class"} is a `WsfOrbitalIntegrator`{.interpreted-text role="class"} that implements an embedded Runge-Kutta scheme with an 8th order solution and an embedded 7th order estimate used to control the error in the integration (see also, `Prince-Do<PERSON>d 78 <orbital_integrator_models.dormand_prince_78>`{.interpreted-text role="ref"}).

## Methods
