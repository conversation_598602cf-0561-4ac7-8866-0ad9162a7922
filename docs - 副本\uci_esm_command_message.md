orphan

:   

# UCI_ESM_CommandMessage

## Overview {#overview .UCI_ESM_CommandMessage .inherits .UCI_Message}

This message allows the user to command an ESM sensor.

## Static Methods

::: method
static UCI_ESM_CommandMessage Construct(UCI_ESM_Command esmCommand)

This method constructs an instance of an UCI_ESM_CommandMessage.
:::

## Methods

::: method
string CommandUUID(int commandIndex)

This method returns the UUID of the command at the given index.
:::

::: method
void PushBack(UCI_ESM_Command esmCommand)

This method adds the given `UCI_ESM_Command`{.interpreted-text role="class"} to the list of commands in the message.
:::
