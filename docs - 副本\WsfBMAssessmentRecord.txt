# WsfBMAssessmentRecord.rst.txt.md
orphan

:   



::: WsfBMAssessmentRecord
WsfBMAssessmentRecord is the script interface to the record of an engagement assessment performed by an IADS C2 Battle Manager.
:::



::: method
void SetWeaponTypePriority(int priority)

Set priority of weapon type.
:::

::: method
int GetWeaponTypePriority()

Get priority of weapon type.
:::

::: method
void SetWeaponSubtypePriority(int priority)

Set priority of weapon subtype.
:::

::: method
int GetWeaponSubtypePriority()

Get priority of weapon subtype.
:::

::: method
void SetWeaponExcluded(bool excluded)

Set whether weapon is excluded in this assessment.
:::

::: method
bool IsWeaponExcluded()

Get whether weapon is excluded in this assessment.
:::

::: method
void SetPriority(double priority)

Set priority.
:::

::: method
double GetPriority()

Get priority.
:::

::: method
int GetDirectC2ID()

Get direct C2 ID.
:::

::: method
int GetDirectC2SubID()

Get direct C2 SubID.
:::

::: method
void SetDirectC2ID(int ID, int subID)

Set direct C2 idRecord ID and SubID.
:::

::: method
int GetTrackID()

Get track record ID of assessed threat.
:::

::: method
int GetTrackSubID()

Get track record SubID of assessed threat.
:::

::: method
WsfBMAssignmentMessage GetExistingAssignment()

Get assignment if one already exists. Empty assignment otherwise.
:::

::: method
int GetAssetID()

Get id record ID of assessed asset.
:::

::: method
int GetAssetSubID()

Get id record SubID of assessed asset.
:::

::: method
int GetWeaponID()

Get weapon record ID of assessed weapon.
:::

::: method
int GetWeaponSubID()

Get weapon record SubID of assessed weapon.
:::

::: method
int GetAssessedZoneTypes()

Get integer mask of assessed zone type enum values (OTHER = 1, AOR = 2, SUR = 4, MEZ = 8, FEZ = 16, JEZ = 32, GEZ = 64, DA = 128, COR = 256).
:::

::: method
WsfGeoPoint GetPredictedInterceptPt()

Get the assessed engagement\'s predicted intercept point.
:::

::: method
bool CanInterceptTrack()

Return if a successful intercept has been predicted.
:::

::: method
bool IsInterceptInsideC2Zone()

Return if a successful intercept has been predicted in a C2 Zone.
:::

::: method
bool IsInterceptInsideMaxRange()

Return if a successful intercept has been predicted inside the weapon\'s maximum range.
:::

::: method
bool IsInterceptInsideZone()

Return if a successful intercept has been predicted inside a zone attached to the weapon.
:::

::: method
bool IsInterceptOutsideMinRange()

Return if a successful intercept has been predicted outside the weapon\'s minimum range.
:::

::: method
bool IsLOSMasked()

Return if the Line Of Sight to the predicted intercept point is masked.
:::

::: method
int GetPotentialShots()

Get number of predicted potential shots in assessment.
:::

::: method
double GetAssignmentTime()

Get predicted assignment time including anticipated delays.
:::

::: method
double GetDeflectionAngleRads()

Get angle offset from track\'s current direction of travel to asset.
:::

::: method
double GetDistanceToPCA()

Get distance from weapon to predicted threat Point of Closest Approach.
:::

::: method
double GetInterceptDistance()

Get distance from weapon to predicted intercept point.
:::

::: method
double GetInterceptPK()

Get Probability of Kill of predicted intercept.
:::

::: method
double GetInterceptTime()

Get time of predicted intercept.
:::

::: method
double GetMaxRange()

Get assessed weapon max range.
:::

::: method
double GetProjectedTime()

Get time threat was projected prior to calculating predicted intercept.
:::