"""
AFSIM Platform Agent - 文档处理和解析模块
专门用于处理AFSIM Platform相关的MD文档，提取参数和结构化信息
"""

import re
import json
import logging
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import markdown
from bs4 import BeautifulSoup
import yaml
from openai import OpenAI

logger = logging.getLogger(__name__)


class PlatformDocumentProcessor:
    """Platform文档处理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化文档处理器

        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.platform_params = {}
        self.processed_docs = {}
        self.llm_client = None
        self._initialize_llm()
        
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = Path(__file__).parent.parent / "config" / "config.yaml"
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'document_processing': {
                'docs_path': 'docs',
                'chunk_size': 500,
                'chunk_overlap': 50,
                'min_content_length': 50,
                'encoding': 'utf-8'
            },
            'llm': {
                'provider': 'deepseek',
                'deepseek': {
                    'api_key': '',
                    'base_url': 'https://api.deepseek.com/v1',
                    'model': 'deepseek-chat',
                    'temperature': 0.1,
                    'max_tokens': 4000
                }
            }
        }

    def _initialize_llm(self):
        """初始化LLM客户端"""
        try:
            llm_config = self.config.get('llm', {})
            provider = llm_config.get('provider', 'deepseek')

            if provider == 'deepseek':
                config = llm_config.get('deepseek', {})
                api_key = config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')

                if not api_key:
                    logger.warning("未设置DeepSeek API Key，将使用传统正则表达式方法提取参数")
                    return

                self.llm_client = OpenAI(
                    api_key=api_key,
                    base_url=config.get('base_url', 'https://api.deepseek.com/v1')
                )
                self.model = config.get('model', 'deepseek-chat')
                self.temperature = config.get('temperature', 0.1)
                self.max_tokens = config.get('max_tokens', 4000)

                logger.info("LLM客户端初始化成功，将使用AI方法提取参数")

            else:
                logger.warning(f"不支持的LLM提供商: {provider}，将使用传统正则表达式方法")

        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}，将使用传统正则表达式方法")
            self.llm_client = None
    
    def process_md_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理单个MD文件
        
        Args:
            file_path: MD文件路径
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理文件: {file_path}")
            
            # 读取文件内容
            with open(file_path, 'r', encoding=self.config['document_processing']['encoding']) as f:
                content = f.read()
            
            # 解析文档结构
            doc_info = self._parse_document_structure(content, file_path)

            # 创建文档块
            chunks = self._create_document_chunks(content, doc_info)

            result = {
                'file_path': file_path,
                'doc_info': doc_info,
                'chunks': chunks,
                'status': 'success'
            }

            logger.info(f"文件处理完成: {file_path}, 创建了 {len(chunks)} 个文档块")
            return result
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 失败: {e}")
            return {
                'file_path': file_path,
                'error': str(e),
                'status': 'error'
            }
    
    def _parse_document_structure(self, content: str, file_path: str) -> Dict[str, Any]:
        """
        解析文档结构
        
        Args:
            content: 文档内容
            file_path: 文件路径
            
        Returns:
            Dict: 文档结构信息
        """
        # 转换为HTML
        html_content = markdown.markdown(content)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取标题
        title_tag = soup.find(['h1', 'h2'])
        title = title_tag.get_text().strip() if title_tag else Path(file_path).stem
        
        # 提取章节
        sections = []
        current_section = None
        
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'p', 'ul', 'ol', 'pre', 'code']):
            if element.name.startswith('h'):
                # 保存上一个章节
                if current_section:
                    sections.append(current_section)
                
                # 创建新章节
                level = int(element.name[1])
                current_section = {
                    'title': element.get_text().strip(),
                    'level': level,
                    'content': '',
                    'type': 'section'
                }
            elif current_section:
                # 添加内容到当前章节
                text = element.get_text().strip()
                if text:
                    current_section['content'] += f" {text}"
        
        # 添加最后一个章节
        if current_section:
            sections.append(current_section)
        
        return {
            'title': title,
            'file_name': Path(file_path).name,
            'sections': sections,
            'total_sections': len(sections)
        }

    def _create_document_chunks(self, content: str, doc_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        创建文档块
        
        Args:
            content: 文档内容
            doc_info: 文档信息
            
        Returns:
            List[Dict]: 文档块列表
        """
        chunks = []
        chunk_size = self.config.get('document_processing', {}).get('chunk_size', 500)
        chunk_overlap = self.config.get('document_processing', {}).get('chunk_overlap', 50)
        min_length = self.config.get('document_processing', {}).get('min_content_length', 50)
        
        # 按章节分块
        for section in doc_info['sections']:
            section_content = section['content'].strip()
            
            if len(section_content) < min_length:
                continue
            
            # 如果章节内容较短，直接作为一个块
            if len(section_content) <= chunk_size:
                chunks.append({
                    'content': section_content,
                    'metadata': {
                        'section_title': section['title'],
                        'section_level': section['level'],
                        'doc_title': doc_info['title'],
                        'chunk_type': 'section',
                        'chunk_index': len(chunks)
                    }
                })
            else:
                # 分割长章节
                for i in range(0, len(section_content), chunk_size - chunk_overlap):
                    chunk_content = section_content[i:i + chunk_size]
                    
                    if len(chunk_content.strip()) >= min_length:
                        chunks.append({
                            'content': chunk_content,
                            'metadata': {
                                'section_title': section['title'],
                                'section_level': section['level'],
                                'doc_title': doc_info['title'],
                                'chunk_type': 'section_part',
                                'chunk_index': len(chunks),
                                'part_index': i // (chunk_size - chunk_overlap)
                            }
                        })
        
        return chunks

    def process_directory(self, docs_path: str = None) -> Dict[str, Any]:
        """
        处理整个文档目录
        
        Args:
            docs_path: 文档目录路径
            
        Returns:
            Dict: 处理结果统计
        """
        if docs_path is None:
            docs_path = self.config['document_processing']['docs_path']
        
        docs_path = Path(docs_path)
        if not docs_path.exists():
            logger.error(f"文档目录不存在: {docs_path}")
            return {'error': f'文档目录不存在: {docs_path}'}
        
        # 查找所有MD文件
        md_files = list(docs_path.rglob('*.md')) + list(docs_path.rglob('*.markdown'))
        
        logger.info(f"找到 {len(md_files)} 个Markdown文件")
        
        results = {
            'total_files': len(md_files),
            'processed_files': 0,
            'failed_files': 0,
            'total_chunks': 0,
            'files': []
        }
        
        for md_file in md_files:
            try:
                result = self.process_md_file(str(md_file))
                results['files'].append(result)
                
                if result['status'] == 'success':
                    results['processed_files'] += 1
                    results['total_chunks'] += len(result['chunks'])
                else:
                    results['failed_files'] += 1
                    
            except Exception as e:
                logger.error(f"处理文件 {md_file} 时发生错误: {e}")
                results['failed_files'] += 1
        
        logger.info(f"目录处理完成: 成功 {results['processed_files']}, 失败 {results['failed_files']}")
        return results
