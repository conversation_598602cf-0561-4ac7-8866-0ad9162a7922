# default_fusion_method.rst.txt.md
orphan

:   





There are a standard set of rules and algorithms that WSF uses in most situations to fuse track and measurement information. Informally these constitute the \"default\" WSF fusion method. Here we describe these rules and algorithms in more detail.



::: centered
Fusion Products (F), based on Local track data (L) and Non-Local track data (NL) ! Range
:::

  Range   Bearing   Elevation   Range Rate   Position   Velocity   Comments
  ------- --------- ----------- ------------ ---------- ---------- ----------
  L       L         NL                       F                     
  NL      NL        L                        F                     
                                                                   
                                                                   

