orphan

:   

# WsfLocalTrackList

::: {.WsfLocalTrackList container=""}
`WsfLocalTrackList`{.interpreted-text role="class"} is a container of references to `WsfLocalTrack`{.interpreted-text role="class"} objects, and is typically returned by:
:::

::: parsed-literal
`WsfLocalTrackList`{.interpreted-text role="class"} tl = `PLATFORM.MasterTrackList() <WsfPlatform.MasterTrackList>`{.interpreted-text role="method"};
:::

In addition to the methods described below, the container may be processed using the **foreach** script language statement as follows:

::: parsed-literal
`WsfLocalTrackList`{.interpreted-text role="class"} localTrackList = \<method that returns a `WsfLocalTrackList`{.interpreted-text role="class"}\>; foreach (`WsfLocalTrack`{.interpreted-text role="class"} t in localTrackList) { \# \...Code to process the `WsfLocalTrack`{.interpreted-text role="class"} object referenced through the variable \'t\'\... }
:::

## Methods

::: method
int Count() int Size()

Return the number of entries in the list.
:::

::: method
bool Empty()

Returns true if the list is empty.

::: note
::: title
Note
:::

This is faster than checking for Count() != 0.
:::
:::

::: method
WsfLocalTrack Entry(int aIndex) WsfLocalTrack Get(int aIndex)

Return the entry at the given index. The value must be in the range \[ 0 .. Count()-1 \].

::: note
::: title
Note
:::

The Get method allows the entries to be accessed via array indexing: e.g:

    WsfLocalTrack t = trackList[i];
:::
:::

::: method
WsfLocalTrack Find(WsfTrackId aTrackId)

Find the entry with specified track ID. Using the `IsValid() <WsfObject.IsValid>`{.interpreted-text role="method"} method on the return value will return false if the requested entry could not be found.
:::

::: method
Iterator WsfLocalTrackListIterator GetIterator()

Return an iterator that points to the beginning of the list. This is used by the script language to support the **foreach** command but may also be used directly.
:::
