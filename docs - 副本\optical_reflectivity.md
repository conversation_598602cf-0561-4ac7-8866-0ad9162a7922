orphan

:   

# optical_reflectivity

::: {.command block=""}
optical_reflectivity \... end_optical_reflectivity
:::

::: parsed-literal

optical_reflectivity *\<signature-name\_\>* (*model-name\_*)

:   \... *model specific commands* \...

end_optical_reflectivity
:::

## Overview

optical_reflectivity defines for a given platform type how much of an optical signal is reflected. The reflectivity is used when an optical sensor attempts to detect a platform.

**\<signature-name\>** is the name to be given to the signature. If the name specifies the name of an existing definition then the new definition will replace the current one (i.e. the last occurrence will be used for the simulation.)

**(model-name)** is an optional parameter that specifies the implementation model to be used for defining the signature. Available models are:
