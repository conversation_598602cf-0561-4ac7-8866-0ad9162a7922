orphan

:   

# WsfSixDOF_PitchDeltaManeuver

## Overview {#overview .WsfSixDOF_PitchDeltaManeuver .inherits .WsfSixDOF_Maneuver}

This maneuver sets a target change in pitch angle for the platform to which it is assigned. This maneuver is done executing as soon as the target is set, so if there is a need to wait for the achieved change in pitch angle, a `WsfSixDOF_ManeuverConstraint`{.interpreted-text role="class"} must be used.

## Methods

::: method
static WsfSixDOF_PitchDeltaManeuver Construct(double aPitchDeltaDeg)

Construct a maneuver that will set a target change in pitch angle on the platform to which it assigned.
:::

::: method
double GetPitchDelta()

Return this maneuver\'s target change in pitch angle in degrees.
:::
