# six_dof_platform_mappings.rst.txt.md
orphan

:   



SixDOF objects may contain SixDOF subobjects that do not have a `platform`{.interpreted-text role="command"} until they are jettison/released from their parent SixDOF object. For example, a fighter aircraft may contain several missile SixDOF subobjects. Such subobjects will lack a platform until they are jettisoned, even though they may contribute mass property and aerodynamic effects to the parent while captive. When such subobjects are jettisoned, the SixDOF/Platform Mapping is consulted to determine which type of AFSIM `platform`{.interpreted-text role="command"} should be created for the SixDOF subobject. The SixDOF object for the subobject will be \'inserted\' into the `WSF_SIX_DOF_MOVER`{.interpreted-text role="model"} of the newly created `platform`{.interpreted-text role="command"}, automatically conveying the kinematics of the subobject to the `platform`{.interpreted-text role="command"}.

::: command
map_vehicle_to_platform \<six_dof_vehicle_type_name\> \<platform_name\> \... end_map_vehicle_to_platform

Define a mapping between the specified six_dof_vehicle_type and the specified platform type.
:::

Example [map_vehicle_to_platform]():

> ::: parsed-literal
> map_vehicle_to_platform
>
> > FA-LGT FA-LGT-Platform SAM-MR TestSAM_Platform F-16C F-16C
>
> end_map_vehicle_to_platform
> :::

Note that the same string name may be used for both the `WSF_SIX_DOF_MOVER.vehicle_type`{.interpreted-text role="command"} and the `platform`{.interpreted-text role="command"}, if appropriate (if there is a SixDOF object type and platform with the same name).

Return to `six_dof_object_types`{.interpreted-text role="doc"}