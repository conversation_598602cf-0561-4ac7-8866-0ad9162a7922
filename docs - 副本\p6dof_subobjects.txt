# p6dof_subobjects.rst.txt.md
orphan

:   



A `p6dof_object_type`{.interpreted-text role="command"} may include one or more subobjects that may represent bombs, missiles, fuel tanks, etc. Subobjects do not have an AFSIM platform until they are jettisoned. At that time, a platform will be created containing a `WSF_P6DOF_MOVER`{.interpreted-text role="model"} and the jettisoned P6DofObject will be \"injected\" into the mover, automatically transferring to the platform its kinematic state. See `map_p6dof_object_to_platform`{.interpreted-text role="command"} for more information.

::: command
subobject \<name\> \<derived_from_type_name\> \... end_subobject

Depending on the defining data, subobjects may effect the performance of their parent platform. For example, a bomb carried on an external pylon may produce additional drag on the parent. It is also possible for a subobject to contribute thrust forces to the parent (such as running Hound Dog missiles on a B-52 during takeoff, or having SRB thrust contribute to the total thrust of a Space Shuttle orbiter). In addition, subobjects will contribute mass properties and inertia to their parent.

A subobject is always derived from a previously defined `p6dof_object_type`{.interpreted-text role="command"}. Although any parameter can be overridden, typical use involves setting relative positioning and separation effects for a subobject while using all other characteristics from the object type from which it was derived.

::: parsed-literal
subobject missile1 AIM9L

> // [Relative Positioning with Parent](#relative-positioning-with-parent) [parent_rel_x]() \... [parent_rel_y]() \... [parent_rel_z]() \... [parent_rel_yaw]() \... [parent_rel_pitch]() \... [parent_rel_roll]() \...
>
> // [Separation Effects](#separation-effects) [separation_vx]() \... [separation_vy]() \... [separation_vz]() \... [separation_omega_x]() \... [separation_omega_y]() \... [separation_omega_z]() \...

end_subobject
:::
:::



> The following commands specify how a subobject should be positioned relative to its parent while captive.
>
> ::: command
> parent_rel_x \<length-value\>
>
> This specifies the x-location of the object\'s reference point relative to the parent\'s reference point using the parent\'s body coordinate system.
>
> Default: 0.0
> :::
>
> ::: command
> parent_rel_y \<length-value\>
>
> This specifies the y-location of the object\'s reference point relative to the parent\'s reference point using the parent\'s body coordinate system.
>
> Default: 0.0
> :::
>
> ::: command
> parent_rel_z \<length-value\>
>
> This specifies the z-location of the object\'s reference point relative to the parent\'s reference point using the parent\'s body coordinate system.
>
> Default: 0.0
> :::
>
> ::: command
> parent_rel_yaw \<angle-value\>
>
> This specifies the yaw of the object about its reference point relative to the parent\'s body coordinate system.
>
> Default: 0.0
> :::
>
> ::: command
> parent_rel_pitch \<angle-value\>
>
> This specifies the pitch of the object about its reference point relative to the parent\'s body coordinate system.
> :::
>
> ::: command
> parent_rel_roll \<angle-value\>
>
> This specifies the roll of the object about its reference point relative to the parent\'s body coordinate system.
>
> Default: 0.0
> :::



> The following commands specify the perturbation that will be applied to a subobject when it is jettisoned from its parent.
>
> ::: command
> separation_vx \<length-value\>
>
> This specifies the x-component of velocity (in parent body coordinates) that is imparted to the subobject when it separates from the parent.
>
> Default: 0.0
> :::
>
> ::: command
> separation_vy \<length-value\>
>
> This specifies the y-component of velocity (in parent body coordinates) that is imparted to the subobject when it separates from the parent.
>
> Default: 0.0
> :::
>
> ::: command
> separation_vz \<length-value\>
>
> This specifies the z-component of velocity (in parent body coordinates) that is imparted to the subobject when it separates from the parent.
>
> Default: 0.0
> :::
>
> ::: command
> separation_omega_x \<length-value\>
>
> This specifies the x-component of angular velocity (in subobject body coordinates) that is imparted to the subobject when it separates from the parent.
>
> Default: 0.0
> :::
>
> ::: command
> separation_omega_y \<length-value\>
>
> This specifies the y-component of angular velocity (in subobject body coordinates) that is imparted to the subobject when it separates from the parent.
>
> Default: 0.0
> :::
>
> ::: command
> separation_omega_z \<length-value\>
>
> This specifies the z-component of angular velocity (in subobject body coordinates) that is imparted to the subobject when it separates from the parent.
>
> Default: 0.0
> :::

Return to `p6dof_object_types`{.interpreted-text role="doc"} or `p6dof_object_type`{.interpreted-text role="doc"}