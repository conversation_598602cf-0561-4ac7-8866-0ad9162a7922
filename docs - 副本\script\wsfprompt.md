orphan

:   

# WsfPrompt

## Overview {#overview .WsfPrompt}

`WsfPrompt`{.interpreted-text role="class"} provides the ability to create customizable message prompts during a simulation in script. AFSIM applications that connect to the appropriate callbacks (e.g. <PERSON>lock) can display these messages in dialogs.

See the `Warlock WsfPrompt<../wkf_plugin/wk_wsfprompt>`{.interpreted-text role="doc"} plugin for more information.

## Static Methods

These methods may be invoked as follows:

    WsfPrompt.Display("title", "message");

::: method
static void Display(string aTitle, string aMessage, Array\<string\> aScripts, Array\<string\> aButtons)

Displays a prompt with a title and message given by aTitle and aMessage. The dialog will contain buttons with text given by aButtons. These buttons will execute the scripts with names given by a<PERSON>s. This function will not pause the simulation. The input arrays aScripts and aButtons must be of equal size, otherwise a script error will occur.
:::

::: method
static void Display(string aTitle, string aMessage)

Displays a prompt with a title and message given by aTit<PERSON> and a<PERSON><PERSON><PERSON>. The dialog will contain a single \"OK\" button for user acknowledgment of the message. This function will not pause the simulation.
:::

::: method
static void DisplayAndPause(string aTitle, string aMessage, Array\<string\> aScripts, Array\<string\> aButtons)

Displays a prompt with a title and message given by aTitle and aMessage. The dialog will contain buttons with text given by aButtons. These buttons will execute the scripts with names given by aScripts. This function will pause the simulation and resume when the dialog closes. The input arrays aScripts and aButtons must be of equal size, otherwise a script error will occur.
:::

::: method
static void DisplayAndPause(string aTitle, string aMessage)

Displays a prompt with a title and message given by aTitle and aMessage. The dialog will contain a single \"OK\" button for user acknowledgment of the message. This function will pause the simulation and resume when the dialog closes.
:::

::: note
::: title
Note
:::

This class and its methods are only applicable if the simulation is being executed by Warlock. If the simulation is being run by another application (e.g. Mission), the method calls are still valid syntax and won\'t produce any errors, but nothing will be displayed and the simulation will not be paused.
:::
