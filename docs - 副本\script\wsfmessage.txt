# wsfmessage.rst.txt.md
orphan

:   





::: method
string Originator()

Returns the originating platform name.
:::

::: method
int SizeInBits()

Returns the message size in bits.
:::

::: method
void SetSizeInBits(int aSizeInBits)

Sets the message size in bits.
:::

::: method
int SizeInBytes()

Returns the message size in bytes.
:::

::: method
void SetSizeInBytes(int aSizeInBytes)

Sets the message size in bytes.
:::

::: method
int Priority()

Returns the priority of the message. Higher values indicate higher priority.
:::

::: method
void SetPriority(int aPriority)

Sets the priority of the message. Higher values indicate higher priority.
:::

::: method
int SerialNumber()

Returns the message\'s serial number.
:::

::: method
double DataTag()

Returns the data tag associated with the data object(s) in the message. Data tags are normally assigned internally by the software and represent a \'tag\' or \'stamp\' that uniquely identifies an object at a particular time. For instance, the producer of tracks and images will assign a data tag when (s)he updates the track or the image.
:::

::: method
void CreateDataTag()

Creates a data tag for this message.
:::

::: method
void SetDataTag(double aDataTag)

Sets the data tag for this message.
:::

::: method
string Type()

Returns the message\'s type.
:::

::: method
string SubType()

Returns the message\'s subtype.
:::

::: method
void SetType(string aType)

Sets the message\'s type.
:::

::: method
void SetSubType(string aSubType)

Sets the message\'s subtype.
:::

::: method
WsfAddress Destination()

Returns the address indicating this message\'s destination.
:::

::: method
WsfAddress NextHop()

Returns the address indicating this message\'s next hop, if available.
:::

