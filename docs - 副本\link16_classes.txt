# link16_classes.rst.txt.md
orphan

:   





::: WsfTadilJ2_0I
::: method
WsfTadilJ2_0E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ2_0E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ2_0C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_0C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsBailOut() void IsBailOut(bool b)

Get or set the value of the IsBailOut field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsC2Unit() void IsC2Unit(bool b)

Get or set the value of the IsC2Unit field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
int SourceTrackNumber() void SourceTrackNumber(int i)

Get or set the value of the SourceTrackNumber field.
:::

::: method
bool IsFlightLeader() void IsFlightLeader(bool b)

Get or set the value of the IsFlightLeader field.
:::

::: method
bool IsMissionCommander() void IsMissionCommander(bool b)

Get or set the value of the IsMissionCommander field.
:::

::: method
int GenericUnitType() void GenericUnitType(int i)

Get or set the value of the GenericUnitType field.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int AltitudeQuality() void AltitudeQuality(int i)

Get or set the value of the AltitudeQuality field.
:::

::: method
int PositionQuality() void PositionQuality(int i)

Get or set the value of the PositionQuality field.
:::

::: method
int Site() void Site(int i)

Get or set the value of the Site field.
:::

::: method
int UnitType() void UnitType(int i)

Get or set the value of the UnitType field.
:::

::: method
int OriginatorEnvironment() void OriginatorEnvironment(int i)

Get or set the value of the OriginatorEnvironment field.
:::
:::



::: WsfTadilJ2_0E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ2_0C1
::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
double Elevation() void Elevation(double d)

Get or set the value of the Elevation field. Units are Meters.
:::

::: method
int AirPlatform() void AirPlatform(int i)

Get or set the value of the AirPlatform field.
:::

::: method
int AirActivity() void AirActivity(int i)

Get or set the value of the AirActivity field.
:::

::: method
int MissionCorrelator() void MissionCorrelator(int i)

Get or set the value of the MissionCorrelator field.
:::
:::



::: WsfTadilJ2_2I
::: method
WsfTadilJ2_2E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ2_2E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ2_2C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_2C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ2_2C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_2C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ2_2C5 AddContinuation5()

Adds continuation word 5 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation5() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_2C5 FindContinuation5()

Returns extension word 5 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsMissionCommander() void IsMissionCommander(bool b)

Get or set the value of the IsMissionCommander field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsC2Unit() void IsC2Unit(bool b)

Get or set the value of the IsC2Unit field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
bool IsAirborne() void IsAirborne(bool b)

Get or set the value of the IsAirborne field.
:::

::: method
bool IsFlightLeader() void IsFlightLeader(bool b)

Get or set the value of the IsFlightLeader field.
:::

::: method
bool IsActiveRelay() void IsActiveRelay(bool b)

Get or set the value of the IsActiveRelay field.
:::

::: method
int IsRTTRelayOperational() void IsRTTRelayOperational(int i)

Get or set the value of the IsRTTRelayOperational field.
:::

::: method
int NetParticipation() void NetParticipation(int i)

Get or set the value of the NetParticipation field.
:::

::: method
int TimeQuality() void TimeQuality(int i)

Get or set the value of the TimeQuality field.
:::

::: method
int PositionQualityFt() void PositionQualityFt(int i)

Get or set the value of the PositionQualityFt field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
bool IsBailOut() void IsBailOut(bool b)

Get or set the value of the IsBailOut field.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int NetNumber() void NetNumber(int i)

Get or set the value of the NetNumber field.
:::

::: method
int IsNPGActive() void IsNPGActive(int i)

Get or set the value of the IsNPGActive field.
:::

::: method
int AltitudeQualityFt() void AltitudeQualityFt(int i)

Get or set the value of the AltitudeQualityFt field.
:::
:::



::: WsfTadilJ2_2E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ2_2C1
::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int AirPlatform() void AirPlatform(int i)

Get or set the value of the AirPlatform field.
:::

::: method
int Activity() void Activity(int i)

Get or set the value of the Activity field.
:::
:::



::: WsfTadilJ2_2C2
::: method
bool InterpretCallsign() void InterpretCallsign(bool b)

Get or set the value of the InterpretCallsign field.
:::

::: method
bool InterpretLink4Addr() void InterpretLink4Addr(bool b)

Get or set the value of the InterpretLink4Addr field.
:::

::: method
string Callsign() void Callsign(string s)

Get or set the value of the Callsign field.
:::

::: method
int Link4Addr() void Link4Addr(int i)

Get or set the value of the Link4Addr field.
:::

::: method
int VoiceFreqChannel() void VoiceFreqChannel(int i)

Get or set the value of the VoiceFreqChannel field.
:::

::: method
int ControlChannel() void ControlChannel(int i)

Get or set the value of the ControlChannel field.
:::

::: method
bool IsRelayActiveVoice() void IsRelayActiveVoice(bool b)

Get or set the value of the IsRelayActiveVoice field.
:::

::: method
bool IsRelayActiveControl() void IsRelayActiveControl(bool b)

Get or set the value of the IsRelayActiveControl field.
:::

::: method
bool IsAlternateVoiceChannel() void IsAlternateVoiceChannel(bool b)

Get or set the value of the IsAlternateVoiceChannel field.
:::
:::



::: WsfTadilJ2_2C5
::: method
int NetNumber_NonC2_to_NonC2() void NetNumber_NonC2_to_NonC2(int i)

Get or set the value of the NetNumber_NonC2_to_NonC2 field.
:::

::: method
bool NonC2_to_NonC2_NPG_status() void NonC2_to_NonC2_NPG_status(bool b)

Get or set the value of the NonC2_to_NonC2_NPG_status field.
:::

::: method
int MissionCorrelator() void MissionCorrelator(int i)

Get or set the value of the MissionCorrelator field.
:::

::: method
int WingmanIDLetter() void WingmanIDLetter(int i)

Get or set the value of the WingmanIDLetter field.
:::

::: method
int TrackNumberCommander() void TrackNumberCommander(int i)

Get or set the value of the TrackNumberCommander field.
:::

::: method
int TrackNumberLeader() void TrackNumberLeader(int i)

Get or set the value of the TrackNumberLeader field.
:::
:::



::: WsfTadilJ2_3I
::: method
WsfTadilJ2_3E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ2_3E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ2_3C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_3C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ2_3C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_3C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ2_3C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_3C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsC2Unit() void IsC2Unit(bool b)

Get or set the value of the IsC2Unit field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
bool IsActiveRelay() void IsActiveRelay(bool b)

Get or set the value of the IsActiveRelay field.
:::

::: method
int IsReplyStatusNetwork() void IsReplyStatusNetwork(int i)

Get or set the value of the IsReplyStatusNetwork field.
:::

::: method
int NetParticipation() void NetParticipation(int i)

Get or set the value of the NetParticipation field.
:::

::: method
int TimeQuality() void TimeQuality(int i)

Get or set the value of the TimeQuality field.
:::

::: method
int PositionQualityFt() void PositionQualityFt(int i)

Get or set the value of the PositionQualityFt field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
bool Spare3() void Spare3(bool b)

Get or set the value of the Spare3 field.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int MissionCorrelator() void MissionCorrelator(int i)

Get or set the value of the MissionCorrelator field.
:::

::: method
int AltitudeQualityFt() void AltitudeQualityFt(int i)

Get or set the value of the AltitudeQualityFt field.
:::
:::



::: WsfTadilJ2_3E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ2_3C1
::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int SurfacePlatform() void SurfacePlatform(int i)

Get or set the value of the SurfacePlatform field.
:::

::: method
int Activity() void Activity(int i)

Get or set the value of the Activity field.
:::
:::



::: WsfTadilJ2_3C2
::: method
bool InterpretCallsign() void InterpretCallsign(bool b)

Get or set the value of the InterpretCallsign field.
:::

::: method
bool InterpretLink4Addr() void InterpretLink4Addr(bool b)

Get or set the value of the InterpretLink4Addr field.
:::

::: method
string Callsign() void Callsign(string s)

Get or set the value of the Callsign field.
:::

::: method
int Link4Addr() void Link4Addr(int i)

Get or set the value of the Link4Addr field.
:::

::: method
int VoiceFreqChannel() void VoiceFreqChannel(int i)

Get or set the value of the VoiceFreqChannel field.
:::

::: method
int ControlChannel() void ControlChannel(int i)

Get or set the value of the ControlChannel field.
:::

::: method
bool IsRelayActiveVoice() void IsRelayActiveVoice(bool b)

Get or set the value of the IsRelayActiveVoice field.
:::

::: method
bool IsRelayActiveControl() void IsRelayActiveControl(bool b)

Get or set the value of the IsRelayActiveControl field.
:::

::: method
bool IsAlternateVoiceChannel() void IsAlternateVoiceChannel(bool b)

Get or set the value of the IsAlternateVoiceChannel field.
:::
:::



::: WsfTadilJ2_3C3
::: method
int UCoordinate() void UCoordinate(int i)

Get or set the value of the UCoordinate field.
:::

::: method
int VCoordinate() void VCoordinate(int i)

Get or set the value of the VCoordinate field.
:::

::: method
int BetaAngle() void BetaAngle(int i)

Get or set the value of the BetaAngle field.
:::

::: method
int PositionQuality() void PositionQuality(int i)

Get or set the value of the PositionQuality field.
:::

::: method
int AzimuthQuality() void AzimuthQuality(int i)

Get or set the value of the AzimuthQuality field.
:::
:::



::: WsfTadilJ2_4I
::: method
WsfTadilJ2_4E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ2_4E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ2_4C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_4C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ2_4C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_4C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ2_4C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_4C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
WsfTadilJ2_4C4 AddContinuation4()

Adds continuation word 4 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation4() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_4C4 FindContinuation4()

Returns extension word 4 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsPositionInContinuation() void IsPositionInContinuation(bool b)

Get or set the value of the IsPositionInContinuation field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsC2Unit() void IsC2Unit(bool b)

Get or set the value of the IsC2Unit field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
bool IsActiveRelay() void IsActiveRelay(bool b)

Get or set the value of the IsActiveRelay field.
:::

::: method
int IsReplyStatusNetwork() void IsReplyStatusNetwork(int i)

Get or set the value of the IsReplyStatusNetwork field.
:::

::: method
int NetParticipation() void NetParticipation(int i)

Get or set the value of the NetParticipation field.
:::

::: method
int TimeQuality() void TimeQuality(int i)

Get or set the value of the TimeQuality field.
:::

::: method
int PositionQualityFt() void PositionQualityFt(int i)

Get or set the value of the PositionQualityFt field.
:::

::: method
double Depth() void Depth(double d)

Get or set the value of the Depth field. Units are Meters.
:::

::: method
int DepthCategory() void DepthCategory(int i)

Get or set the value of the DepthCategory field.
:::

::: method
int MissionCorrelator() void MissionCorrelator(int i)

Get or set the value of the MissionCorrelator field.
:::

::: method
int DepthQuality() void DepthQuality(int i)

Get or set the value of the DepthQuality field.
:::
:::



::: WsfTadilJ2_4E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ2_4C1
::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int SubSurfacePlatform() void SubSurfacePlatform(int i)

Get or set the value of the SubSurfacePlatform field.
:::

::: method
int Activity() void Activity(int i)

Get or set the value of the Activity field.
:::
:::



::: WsfTadilJ2_4C2
::: method
bool InterpretCallsign() void InterpretCallsign(bool b)

Get or set the value of the InterpretCallsign field.
:::

::: method
bool InterpretLink4Addr() void InterpretLink4Addr(bool b)

Get or set the value of the InterpretLink4Addr field.
:::

::: method
string Callsign() void Callsign(string s)

Get or set the value of the Callsign field.
:::

::: method
int Link4Addr() void Link4Addr(int i)

Get or set the value of the Link4Addr field.
:::

::: method
int VoiceFreqChannel() void VoiceFreqChannel(int i)

Get or set the value of the VoiceFreqChannel field.
:::

::: method
int ControlChannel() void ControlChannel(int i)

Get or set the value of the ControlChannel field.
:::

::: method
bool IsRelayActiveVoice() void IsRelayActiveVoice(bool b)

Get or set the value of the IsRelayActiveVoice field.
:::

::: method
bool IsRelayActiveControl() void IsRelayActiveControl(bool b)

Get or set the value of the IsRelayActiveControl field.
:::

::: method
bool IsAlternateVoiceChannel() void IsAlternateVoiceChannel(bool b)

Get or set the value of the IsAlternateVoiceChannel field.
:::
:::



::: WsfTadilJ2_4C3
::: method
int UCoordinate() void UCoordinate(int i)

Get or set the value of the UCoordinate field.
:::

::: method
int VCoordinate() void VCoordinate(int i)

Get or set the value of the VCoordinate field.
:::

::: method
int BetaAngle() void BetaAngle(int i)

Get or set the value of the BetaAngle field.
:::

::: method
int PositionQuality() void PositionQuality(int i)

Get or set the value of the PositionQuality field.
:::

::: method
int AzimuthQuality() void AzimuthQuality(int i)

Get or set the value of the AzimuthQuality field.
:::
:::



::: WsfTadilJ2_4C4
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::
:::



::: WsfTadilJ2_5I
::: method
WsfTadilJ2_5E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ2_5E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ2_5C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_5C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ2_5C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_5C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
WsfTadilJ2_5C4 AddContinuation4()

Adds continuation word 4 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation4() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_5C4 FindContinuation4()

Returns extension word 4 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsDisplacedPosition() void IsDisplacedPosition(bool b)

Get or set the value of the IsDisplacedPosition field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsC2Unit() void IsC2Unit(bool b)

Get or set the value of the IsC2Unit field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
bool IsActiveRelay() void IsActiveRelay(bool b)

Get or set the value of the IsActiveRelay field.
:::

::: method
int IsReplyStatusNetwork() void IsReplyStatusNetwork(int i)

Get or set the value of the IsReplyStatusNetwork field.
:::

::: method
int NetParticipation() void NetParticipation(int i)

Get or set the value of the NetParticipation field.
:::

::: method
int TimeQuality() void TimeQuality(int i)

Get or set the value of the TimeQuality field.
:::

::: method
int PositionQualityFt() void PositionQualityFt(int i)

Get or set the value of the PositionQualityFt field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
bool Spare3() void Spare3(bool b)

Get or set the value of the Spare3 field.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int MissionCorrelator() void MissionCorrelator(int i)

Get or set the value of the MissionCorrelator field.
:::

::: method
int AltitudeQualityFt() void AltitudeQualityFt(int i)

Get or set the value of the AltitudeQualityFt field.
:::
:::



::: WsfTadilJ2_5E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::
:::



::: WsfTadilJ2_5C1
::: method
bool InterpretVoiceCallsign() void InterpretVoiceCallsign(bool b)

Get or set the value of the InterpretVoiceCallsign field.
:::

::: method
string Callsign() void Callsign(string s)

Get or set the value of the Callsign field.
:::

::: method
int LandPlatform() void LandPlatform(int i)

Get or set the value of the LandPlatform field.
:::

::: method
int LandPlatformActivity() void LandPlatformActivity(int i)

Get or set the value of the LandPlatformActivity field.
:::

::: method
int VoiceFrequencyChannel() void VoiceFrequencyChannel(int i)

Get or set the value of the VoiceFrequencyChannel field.
:::

::: method
int ControlChannel() void ControlChannel(int i)

Get or set the value of the ControlChannel field.
:::

::: method
bool VoiceActiveRelayIndicator() void VoiceActiveRelayIndicator(bool b)

Get or set the value of the VoiceActiveRelayIndicator field.
:::

::: method
bool ControlActiveRelayIndicator() void ControlActiveRelayIndicator(bool b)

Get or set the value of the ControlActiveRelayIndicator field.
:::

::: method
bool IsAlternateVoiceFrequen() void IsAlternateVoiceFrequen(bool b)

Get or set the value of the IsAlternateVoiceFrequen field.
:::
:::



::: WsfTadilJ2_5C3
::: method
int UCoordinate() void UCoordinate(int i)

Get or set the value of the UCoordinate field.
:::

::: method
int VCoordinate() void VCoordinate(int i)

Get or set the value of the VCoordinate field.
:::

::: method
int BetaAngle() void BetaAngle(int i)

Get or set the value of the BetaAngle field.
:::

::: method
int PositionQuality() void PositionQuality(int i)

Get or set the value of the PositionQuality field.
:::

::: method
int AzimuthQuality() void AzimuthQuality(int i)

Get or set the value of the AzimuthQuality field.
:::
:::



::: WsfTadilJ2_5C4
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::
:::



::: WsfTadilJ2_6I
::: method
WsfTadilJ2_6E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ2_6E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ2_6C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_6C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ2_6C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_6C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
WsfTadilJ2_6C6 AddContinuation6()

Adds continuation word 6 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation6() may be used to retrieve the word.
:::

::: method
WsfTadilJ2_6C6 FindContinuation6()

Returns extension word 6 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsC2Unit() void IsC2Unit(bool b)

Get or set the value of the IsC2Unit field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
bool IsActiveRelay() void IsActiveRelay(bool b)

Get or set the value of the IsActiveRelay field.
:::

::: method
int IsReplyStatusNetwork() void IsReplyStatusNetwork(int i)

Get or set the value of the IsReplyStatusNetwork field.
:::

::: method
int NetParticipation() void NetParticipation(int i)

Get or set the value of the NetParticipation field.
:::

::: method
int TimeQuality() void TimeQuality(int i)

Get or set the value of the TimeQuality field.
:::

::: method
int PositionQualityFt() void PositionQualityFt(int i)

Get or set the value of the PositionQualityFt field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int MissionCorrelator() void MissionCorrelator(int i)

Get or set the value of the MissionCorrelator field.
:::

::: method
int AltitudeQualityFt() void AltitudeQualityFt(int i)

Get or set the value of the AltitudeQualityFt field.
:::
:::



::: WsfTadilJ2_6E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ2_6C1
::: method
bool InterpretVoiceCallsign() void InterpretVoiceCallsign(bool b)

Get or set the value of the InterpretVoiceCallsign field.
:::

::: method
string Callsign() void Callsign(string s)

Get or set the value of the Callsign field.
:::

::: method
int LandPlatform() void LandPlatform(int i)

Get or set the value of the LandPlatform field.
:::

::: method
int LandPlatformActivity() void LandPlatformActivity(int i)

Get or set the value of the LandPlatformActivity field.
:::

::: method
int VoiceFrequencyChannel() void VoiceFrequencyChannel(int i)

Get or set the value of the VoiceFrequencyChannel field.
:::

::: method
int ControlChannel() void ControlChannel(int i)

Get or set the value of the ControlChannel field.
:::

::: method
bool VoiceActiveRelayIndicator() void VoiceActiveRelayIndicator(bool b)

Get or set the value of the VoiceActiveRelayIndicator field.
:::

::: method
bool ControlActiveRelayIndicator() void ControlActiveRelayIndicator(bool b)

Get or set the value of the ControlActiveRelayIndicator field.
:::

::: method
bool IsAlternateVoiceFrequen() void IsAlternateVoiceFrequen(bool b)

Get or set the value of the IsAlternateVoiceFrequen field.
:::
:::



::: WsfTadilJ2_6C3
::: method
int UCoordinate() void UCoordinate(int i)

Get or set the value of the UCoordinate field.
:::

::: method
int VCoordinate() void VCoordinate(int i)

Get or set the value of the VCoordinate field.
:::

::: method
int BetaAngle() void BetaAngle(int i)

Get or set the value of the BetaAngle field.
:::

::: method
int PositionQuality() void PositionQuality(int i)

Get or set the value of the PositionQuality field.
:::

::: method
int AzimuthQuality() void AzimuthQuality(int i)

Get or set the value of the AzimuthQuality field.
:::
:::



::: WsfTadilJ2_6C6
::: method
int TimeTag() void TimeTag(int i)

Get or set the value of the TimeTag field.
:::

::: method
int SquareRow() void SquareRow(int i)

Get or set the value of the SquareRow field.
:::

::: method
int SquareColumn() void SquareColumn(int i)

Get or set the value of the SquareColumn field.
:::

::: method
int GridZoneRow() void GridZoneRow(int i)

Get or set the value of the GridZoneRow field.
:::

::: method
double Northing() void Northing(double d)

Get or set the value of the Northing field.
:::

::: method
double Easting() void Easting(double d)

Get or set the value of the Easting field.
:::

::: method
int GridZoneColumn() void GridZoneColumn(int i)

Get or set the value of the GridZoneColumn field.
:::
:::



::: WsfTadilJ3_0I
::: method
WsfTadilJ3_0E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_0E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ3_0C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_0C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_0C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_0C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ3_0C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_0C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
WsfTadilJ3_0C4 AddContinuation4()

Adds continuation word 4 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation4() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_0C4 FindContinuation4()

Returns extension word 4 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsResponse() void IsResponse(bool b)

Get or set the value of the IsResponse field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
bool IsSlavedToUnit() void IsSlavedToUnit(bool b)

Get or set the value of the IsSlavedToUnit field.
:::

::: method
bool IsLineAreaContinuation() void IsLineAreaContinuation(bool b)

Get or set the value of the IsLineAreaContinuation field.
:::

::: method
int ReportIndicator() void ReportIndicator(int i)

Get or set the value of the ReportIndicator field.
:::

::: method
int TimeReportFunction() void TimeReportFunction(int i)

Get or set the value of the TimeReportFunction field.
:::

::: method
int PointLineDescriptor() void PointLineDescriptor(int i)

Get or set the value of the PointLineDescriptor field.
:::

::: method
int Priority() void Priority(int i)

Get or set the value of the Priority field.
:::

::: method
int PointType() void PointType(int i)

Get or set the value of the PointType field.
:::

::: method
int PointTypeAmplification() void PointTypeAmplification(int i)

Get or set the value of the PointTypeAmplification field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::
:::



::: WsfTadilJ3_0E0
::: method
int BurstType() void BurstType(int i)

Get or set the value of the BurstType field.
:::

::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
int EstimatedYield() void EstimatedYield(int i)

Get or set the value of the EstimatedYield field.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
int Altitude1() void Altitude1(int i)

Get or set the value of the Altitude1 field.
:::

::: method
int Altitude2() void Altitude2(int i)

Get or set the value of the Altitude2 field.
:::
:::



::: WsfTadilJ3_0C1
::: method
double DeltaLat1() void DeltaLat1(double d)

Get or set the value of the DeltaLat1 field.
:::

::: method
double DeltaLon1() void DeltaLon1(double d)

Get or set the value of the DeltaLon1 field.
:::

::: method
bool IsEndPoint1() void IsEndPoint1(bool b)

Get or set the value of the IsEndPoint1 field.
:::

::: method
double DeltaLat2() void DeltaLat2(double d)

Get or set the value of the DeltaLat2 field.
:::

::: method
double DeltaLon2() void DeltaLon2(double d)

Get or set the value of the DeltaLon2 field.
:::

::: method
bool IsEndPoint2() void IsEndPoint2(bool b)

Get or set the value of the IsEndPoint2 field.
:::
:::



::: WsfTadilJ3_0C2
::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
int AreaType() void AreaType(int i)

Get or set the value of the AreaType field.
:::

::: method
double AxisOrientation() void AxisOrientation(double d)

Get or set the value of the AxisOrientation field. Units are Radians.
:::

::: method
double AreaMajorAxis() void AreaMajorAxis(double d)

Get or set the value of the AreaMajorAxis field. Units are Meters.
:::

::: method
double AreaMinorAxis() void AreaMinorAxis(double d)

Get or set the value of the AreaMinorAxis field. Units are Meters.
:::
:::



::: WsfTadilJ3_0C3
::: method
int ChannelNumber() void ChannelNumber(int i)

Get or set the value of the ChannelNumber field.
:::

::: method
int DepthIndicator() void DepthIndicator(int i)

Get or set the value of the DepthIndicator field.
:::

::: method
int DepthTransducer() void DepthTransducer(int i)

Get or set the value of the DepthTransducer field.
:::

::: method
int SonobuoyType() void SonobuoyType(int i)

Get or set the value of the SonobuoyType field.
:::

::: method
int SonobuoyPattern() void SonobuoyPattern(int i)

Get or set the value of the SonobuoyPattern field.
:::

::: method
int SonobuoyTypeNumber() void SonobuoyTypeNumber(int i)

Get or set the value of the SonobuoyTypeNumber field.
:::

::: method
double SonobuoySpacing() void SonobuoySpacing(double d)

Get or set the value of the SonobuoySpacing field.
:::

::: method
double SonobuoyRowSpacing() void SonobuoyRowSpacing(double d)

Get or set the value of the SonobuoyRowSpacing field.
:::

::: method
double SonobuoyPatternRadius() void SonobuoyPatternRadius(double d)

Get or set the value of the SonobuoyPatternRadius field.
:::

::: method
double Bearing() void Bearing(double d)

Get or set the value of the Bearing field.
:::

::: method
bool HasContact() void HasContact(bool b)

Get or set the value of the HasContact field.
:::
:::



::: WsfTadilJ3_0C4
::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int SonobuoyType() void SonobuoyType(int i)

Get or set the value of the SonobuoyType field.
:::
:::



::: WsfTadilJ3_1I
::: method
WsfTadilJ3_1E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_1E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ3_1C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_1C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int PersonnelCount() void PersonnelCount(int i)

Get or set the value of the PersonnelCount field.
:::

::: method
int EmergencyType() void EmergencyType(int i)

Get or set the value of the EmergencyType field.
:::

::: method
string TrackNumberPrevious() void TrackNumberPrevious(string s)

Get or set the value of the TrackNumberPrevious field.
:::
:::



::: WsfTadilJ3_1E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
int TimeFunction() void TimeFunction(int i)

Get or set the value of the TimeFunction field.
:::

::: method
int PositionAccuracy() void PositionAccuracy(int i)

Get or set the value of the PositionAccuracy field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::
:::



::: WsfTadilJ3_1C1
::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int Mode4Code() void Mode4Code(int i)

Get or set the value of the Mode4Code field.
:::
:::



::: WsfTadilJ3_2I
::: method
WsfTadilJ3_2E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_2E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ3_2C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_2C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_2E1 AddExtension1()

Adds extension word 1 to the message. This can only be called once for each extension word, and only after extension word 0 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension1 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_2E1 FindExtension1()

Returns extension word 1 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsSourcePPLI() void IsSourcePPLI(bool b)

Get or set the value of the IsSourcePPLI field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
int AltitudeSource() void AltitudeSource(int i)

Get or set the value of the AltitudeSource field.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
bool IsIdentityDifference() void IsIdentityDifference(bool b)

Get or set the value of the IsIdentityDifference field.
:::

::: method
int TrackQuality() void TrackQuality(int i)

Get or set the value of the TrackQuality field.
:::

::: method
int IdentityConfidence() void IdentityConfidence(int i)

Get or set the value of the IdentityConfidence field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
bool IsSpecialIntrest() void IsSpecialIntrest(bool b)

Get or set the value of the IsSpecialIntrest field.
:::
:::



::: WsfTadilJ3_2E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
bool IsSensorActive() void IsSensorActive(bool b)

Get or set the value of the IsSensorActive field.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ3_2E1
::: method
int DisSite() void DisSite(int i)

Get or set the value of the DisSite field.
:::

::: method
int DisApplication() void DisApplication(int i)

Get or set the value of the DisApplication field.
:::

::: method
int DisEntityId() void DisEntityId(int i)

Get or set the value of the DisEntityId field.
:::
:::



::: WsfTadilJ3_2C1
::: method
bool IsAirSpecificType() void IsAirSpecificType(bool b)

Get or set the value of the IsAirSpecificType field.
:::

::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int Mode4Code() void Mode4Code(int i)

Get or set the value of the Mode4Code field.
:::

::: method
int PPLI_IFF_Indicator() void PPLI_IFF_Indicator(int i)

Get or set the value of the PPLI_IFF_Indicator field.
:::

::: method
int AirSpecificType() void AirSpecificType(int i)

Get or set the value of the AirSpecificType field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::
:::



::: WsfTadilJ3_3I
::: method
WsfTadilJ3_3E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_3E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ3_3C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_3C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_3E1 AddExtension1()

Adds extension word 1 to the message. This can only be called once for each extension word, and only after extension word 0 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension1 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_3E1 FindExtension1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_3C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_3C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsSourcePPLI() void IsSourcePPLI(bool b)

Get or set the value of the IsSourcePPLI field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
int SurfaceSpecificType() void SurfaceSpecificType(int i)

Get or set the value of the SurfaceSpecificType field.
:::

::: method
bool IsIdentityDifference() void IsIdentityDifference(bool b)

Get or set the value of the IsIdentityDifference field.
:::

::: method
int TrackQuality() void TrackQuality(int i)

Get or set the value of the TrackQuality field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
bool Dummy() void Dummy(bool b)

Get or set the value of the Dummy field.
:::

::: method
int IdentityAmplify() void IdentityAmplify(int i)

Get or set the value of the IdentityAmplify field.
:::

::: method
bool IsSpecialIntrest() void IsSpecialIntrest(bool b)

Get or set the value of the IsSpecialIntrest field.
:::
:::



::: WsfTadilJ3_3E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
bool ActiveSensor() void ActiveSensor(bool b)

Get or set the value of the ActiveSensor field.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ3_3E1
::: method
int DisSite() void DisSite(int i)

Get or set the value of the DisSite field.
:::

::: method
int DisApplication() void DisApplication(int i)

Get or set the value of the DisApplication field.
:::

::: method
int DisEntityId() void DisEntityId(int i)

Get or set the value of the DisEntityId field.
:::
:::



::: WsfTadilJ3_3C1
::: method
bool IsAirSpecificType() void IsAirSpecificType(bool b)

Get or set the value of the IsAirSpecificType field.
:::

::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int Mode4Code() void Mode4Code(int i)

Get or set the value of the Mode4Code field.
:::

::: method
int PPLI_IFF_Indicator() void PPLI_IFF_Indicator(int i)

Get or set the value of the PPLI_IFF_Indicator field.
:::

::: method
int SurfacePlatform() void SurfacePlatform(int i)

Get or set the value of the SurfacePlatform field.
:::

::: method
int SurfacePlatformActivity() void SurfacePlatformActivity(int i)

Get or set the value of the SurfacePlatformActivity field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::
:::



::: WsfTadilJ3_3C2
::: method
int SAM_Type1() void SAM_Type1(int i)

Get or set the value of the SAM_Type1 field.
:::

::: method
int SAM_Type2() void SAM_Type2(int i)

Get or set the value of the SAM_Type2 field.
:::

::: method
int SAM_Type3() void SAM_Type3(int i)

Get or set the value of the SAM_Type3 field.
:::
:::



::: WsfTadilJ3_4I
::: method
WsfTadilJ3_4E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_4E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ3_4C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_4C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_4E1 AddExtension1()

Adds extension word 1 to the message. This can only be called once for each extension word, and only after extension word 0 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension1 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_4E1 FindExtension1()

Returns extension word 1 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsSourcePPLI() void IsSourcePPLI(bool b)

Get or set the value of the IsSourcePPLI field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int DataReportType() void DataReportType(int i)

Get or set the value of the DataReportType field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
int ConfidenceLevel() void ConfidenceLevel(int i)

Get or set the value of the ConfidenceLevel field.
:::

::: method
int SubsurfacePlatform() void SubsurfacePlatform(int i)

Get or set the value of the SubsurfacePlatform field.
:::

::: method
int SubsurfacePlatformActivity() void SubsurfacePlatformActivity(int i)

Get or set the value of the SubsurfacePlatformActivity field.
:::

::: method
bool IsIdentityDifference() void IsIdentityDifference(bool b)

Get or set the value of the IsIdentityDifference field.
:::

::: method
int LaunchCapability() void LaunchCapability(int i)

Get or set the value of the LaunchCapability field.
:::
:::



::: WsfTadilJ3_4E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ3_4E1
::: method
int DisSite() void DisSite(int i)

Get or set the value of the DisSite field.
:::

::: method
int DisApplication() void DisApplication(int i)

Get or set the value of the DisApplication field.
:::

::: method
int DisEntityId() void DisEntityId(int i)

Get or set the value of the DisEntityId field.
:::
:::



::: WsfTadilJ3_4C1
::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
double Depth() void Depth(double d)

Get or set the value of the Depth field. Units are Meters.
:::

::: method
int DepthContact() void DepthContact(int i)

Get or set the value of the DepthContact field.
:::

::: method
int Sensor() void Sensor(int i)

Get or set the value of the Sensor field.
:::

::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int Mode4Code() void Mode4Code(int i)

Get or set the value of the Mode4Code field.
:::

::: method
int TimeFunction() void TimeFunction(int i)

Get or set the value of the TimeFunction field.
:::
:::



::: WsfTadilJ3_5I
::: method
WsfTadilJ3_5E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_5E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ3_5C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_5C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_5E1 AddExtension1()

Adds extension word 1 to the message. This can only be called once for each extension word, and only after extension word 0 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension1 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_5E1 FindExtension1()

Returns extension word 1 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergencyStatus() void IsEmergencyStatus(bool b)

Get or set the value of the IsEmergencyStatus field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int PointOrTrack() void PointOrTrack(int i)

Get or set the value of the PointOrTrack field.
:::

::: method
int ReportType() void ReportType(int i)

Get or set the value of the ReportType field.
:::

::: method
bool IsSourcePPLI() void IsSourcePPLI(bool b)

Get or set the value of the IsSourcePPLI field.
:::

::: method
bool IsIdentityDifference() void IsIdentityDifference(bool b)

Get or set the value of the IsIdentityDifference field.
:::

::: method
int TrackQuality() void TrackQuality(int i)

Get or set the value of the TrackQuality field.
:::

::: method
int IdentityConfidence() void IdentityConfidence(int i)

Get or set the value of the IdentityConfidence field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
bool IsSpecialIntrest() void IsSpecialIntrest(bool b)

Get or set the value of the IsSpecialIntrest field.
:::
:::



::: WsfTadilJ3_5E0
::: method
int TrackIdentifier() void TrackIdentifier(int i)

Get or set the value of the TrackIdentifier field.
:::

::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
bool ActiveSensor() void ActiveSensor(bool b)

Get or set the value of the ActiveSensor field.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ3_5E1
::: method
int DisSite() void DisSite(int i)

Get or set the value of the DisSite field.
:::

::: method
int DisApplication() void DisApplication(int i)

Get or set the value of the DisApplication field.
:::

::: method
int DisEntityId() void DisEntityId(int i)

Get or set the value of the DisEntityId field.
:::
:::



::: WsfTadilJ3_5C1
::: method
bool IsLandSpecificType() void IsLandSpecificType(bool b)

Get or set the value of the IsLandSpecificType field.
:::

::: method
int Mode1Code() void Mode1Code(int i)

Get or set the value of the Mode1Code field.
:::

::: method
int Mode2Code() void Mode2Code(int i)

Get or set the value of the Mode2Code field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
int Mode4Code() void Mode4Code(int i)

Get or set the value of the Mode4Code field.
:::

::: method
int PPLI_IFF_Indicator() void PPLI_IFF_Indicator(int i)

Get or set the value of the PPLI_IFF_Indicator field.
:::

::: method
int LandSpecificType() void LandSpecificType(int i)

Get or set the value of the LandSpecificType field.
:::

::: method
int TimeFunction() void TimeFunction(int i)

Get or set the value of the TimeFunction field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::
:::



::: WsfTadilJ3_6I
::: method
WsfTadilJ3_6E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_6E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ3_6C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ3_6C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_6E1 AddExtension1()

Adds extension word 1 to the message. This can only be called once for each extension word, and only after extension word 0 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension1 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_6E1 FindExtension1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ3_6E2 AddExtension2()

Adds extension word 2 to the message. This can only be called once for each extension word, and only after extension word 1 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension2 may be used to retrieve the word.
:::

::: method
WsfTadilJ3_6E2 FindExtension2()

Returns extension word 2 if it has been added to the message.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
bool IsSpecificType() void IsSpecificType(bool b)

Get or set the value of the IsSpecificType field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Second() void Second(int i)

Get or set the value of the Second field.
:::

::: method
int TrackQuality() void TrackQuality(int i)

Get or set the value of the TrackQuality field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
bool IsIdentityDifferent() void IsIdentityDifferent(bool b)

Get or set the value of the IsIdentityDifferent field.
:::

::: method
int SpacePlatform() void SpacePlatform(int i)

Get or set the value of the SpacePlatform field.
:::

::: method
int SpaceActivity() void SpaceActivity(int i)

Get or set the value of the SpaceActivity field.
:::
:::



::: WsfTadilJ3_6E0
::: method
double WcsX() void WcsX(double d)

Get or set the value of the WcsX field. Units are Meters.
:::

::: method
double WcsVelX() void WcsVelX(double d)

Get or set the value of the WcsVelX field. Units are Meters Per Second.
:::

::: method
double WcsY() void WcsY(double d)

Get or set the value of the WcsY field. Units are Meters.
:::

::: method
int SpaceAmplify() void SpaceAmplify(int i)

Get or set the value of the SpaceAmplify field.
:::

::: method
int AmplifyConfidence() void AmplifyConfidence(int i)

Get or set the value of the AmplifyConfidence field.
:::
:::



::: WsfTadilJ3_6E1
::: method
double WcsVelY() void WcsVelY(double d)

Get or set the value of the WcsVelY field. Units are Meters Per Second.
:::

::: method
double WcsZ() void WcsZ(double d)

Get or set the value of the WcsZ field. Units are Meters.
:::

::: method
double WcsVelZ() void WcsVelZ(double d)

Get or set the value of the WcsVelZ field. Units are Meters Per Second.
:::

::: method
bool IsTrackLost() void IsTrackLost(bool b)

Get or set the value of the IsTrackLost field.
:::

::: method
bool IsBoosting() void IsBoosting(bool b)

Get or set the value of the IsBoosting field.
:::

::: method
int DataIndicator() void DataIndicator(int i)

Get or set the value of the DataIndicator field.
:::
:::



::: WsfTadilJ3_6E2
::: method
int DisSite() void DisSite(int i)

Get or set the value of the DisSite field.
:::

::: method
int DisApplication() void DisApplication(int i)

Get or set the value of the DisApplication field.
:::

::: method
int DisEntityId() void DisEntityId(int i)

Get or set the value of the DisEntityId field.
:::
:::



::: WsfTadilJ3_6C1
::: method
int SigmaX_Position() void SigmaX_Position(int i)

Get or set the value of the SigmaX_Position field.
:::

::: method
int SigmaY_Position() void SigmaY_Position(int i)

Get or set the value of the SigmaY_Position field.
:::

::: method
int SigmaZ_Position() void SigmaZ_Position(int i)

Get or set the value of the SigmaZ_Position field.
:::

::: method
int CovarianceElement22() void CovarianceElement22(int i)

Get or set the value of the CovarianceElement22 field.
:::

::: method
int CovarianceElement33() void CovarianceElement33(int i)

Get or set the value of the CovarianceElement33 field.
:::

::: method
int Element33Sign() void Element33Sign(int i)

Get or set the value of the Element33Sign field.
:::

::: method
int AbsoluteValueOfCovariance() void AbsoluteValueOfCovariance(int i)

Get or set the value of the AbsoluteValueOfCovariance field.
:::

::: method
int SignOfCovariance12() void SignOfCovariance12(int i)

Get or set the value of the SignOfCovariance12 field.
:::

::: method
int SignOfCovariance13() void SignOfCovariance13(int i)

Get or set the value of the SignOfCovariance13 field.
:::
:::



::: WsfTadilJ3_7I
::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
bool IsForceTell() void IsForceTell(bool b)

Get or set the value of the IsForceTell field.
:::

::: method
bool IsEmergency() void IsEmergency(bool b)

Get or set the value of the IsEmergency field.
:::

::: method
bool IsSpecialProcessingRequired() void IsSpecialProcessingRequired(bool b)

Get or set the value of the IsSpecialProcessingRequired field.
:::

::: method
bool IsSimulated() void IsSimulated(bool b)

Get or set the value of the IsSimulated field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
bool IsResponse() void IsResponse(bool b)

Get or set the value of the IsResponse field.
:::

::: method
int FixOrBearingDescriptor() void FixOrBearingDescriptor(int i)

Get or set the value of the FixOrBearingDescriptor field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Second() void Second(int i)

Get or set the value of the Second field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
bool IsIdentityDifferent() void IsIdentityDifferent(bool b)

Get or set the value of the IsIdentityDifferent field.
:::

::: method
int CircleSquareSwitch() void CircleSquareSwitch(int i)

Get or set the value of the CircleSquareSwitch field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::
:::



::: WsfTadilJ7_0I
::: method
WsfTadilJ7_0E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ7_0E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ7_0C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ7_0C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
bool IsExerciseIndicator() void IsExerciseIndicator(bool b)

Get or set the value of the IsExerciseIndicator field.
:::

::: method
int ActionTrackMgmt() void ActionTrackMgmt(int i)

Get or set the value of the ActionTrackMgmt field.
:::

::: method
bool IsControllingUnitIndicator() void IsControllingUnitIndicator(bool b)

Get or set the value of the IsControllingUnitIndicator field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int Strength() void Strength(int i)

Get or set the value of the Strength field.
:::

::: method
bool IsAlertStatusChange() void IsAlertStatusChange(bool b)

Get or set the value of the IsAlertStatusChange field.
:::

::: method
int PlatformType() void PlatformType(int i)

Get or set the value of the PlatformType field.
:::

::: method
int ActivityType() void ActivityType(int i)

Get or set the value of the ActivityType field.
:::

::: method
int Environment() void Environment(int i)

Get or set the value of the Environment field.
:::

::: method
int IdentityConfidence() void IdentityConfidence(int i)

Get or set the value of the IdentityConfidence field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
bool IsSpecialInterestIndicator() void IsSpecialInterestIndicator(bool b)

Get or set the value of the IsSpecialInterestIndicator field.
:::
:::



::: WsfTadilJ7_0E0
::: method
int Strength() void Strength(int i)

Get or set the value of the Strength field.
:::

::: method
int StrenghtPercentOfTrackedVehicles() void StrenghtPercentOfTrackedVehicles(int i)

Get or set the value of the StrenghtPercentOfTrackedVehicles field.
:::
:::



::: WsfTadilJ7_0C1
::: method
int PlatformSpecificType() void PlatformSpecificType(int i)

Get or set the value of the PlatformSpecificType field.
:::
:::



::: WsfTadilJ7_1I
::: method
WsfTadilJ7_1C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ7_1C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
int Action() void Action(int i)

Get or set the value of the Action field.
:::

::: method
string Spare3() void Spare3(string s)

Get or set the value of the Spare3 field.
:::

::: method
bool TrackNumber1() void TrackNumber1(bool b)

Get or set the value of the TrackNumber1 field.
:::

::: method
bool DisUsed() void DisUsed(bool b)

Get or set the value of the DisUsed field.
:::

::: method
bool IsElectronicAttack() void IsElectronicAttack(bool b)

Get or set the value of the IsElectronicAttack field.
:::

::: method
bool IsESData() void IsESData(bool b)

Get or set the value of the IsESData field.
:::

::: method
bool IsElectronicWarfareFixes() void IsElectronicWarfareFixes(bool b)

Get or set the value of the IsElectronicWarfareFixes field.
:::

::: method
bool IsWpnStatusData() void IsWpnStatusData(bool b)

Get or set the value of the IsWpnStatusData field.
:::

::: method
bool IsWeatherStatusData() void IsWeatherStatusData(bool b)

Get or set the value of the IsWeatherStatusData field.
:::

::: method
bool IsAmpData() void IsAmpData(bool b)

Get or set the value of the IsAmpData field.
:::

::: method
int FilterNumber() void FilterNumber(int i)

Get or set the value of the FilterNumber field.
:::

::: method
int FreqOfUpdate() void FreqOfUpdate(int i)

Get or set the value of the FreqOfUpdate field.
:::

::: method
bool IsCovarianceIndicator() void IsCovarianceIndicator(bool b)

Get or set the value of the IsCovarianceIndicator field.
:::

::: method
int Addressee() void Addressee(int i)

Get or set the value of the Addressee field.
:::
:::



::: WsfTadilJ7_1C1
::: method
int Addressee1() void Addressee1(int i)

Get or set the value of the Addressee1 field.
:::

::: method
int Addressee2() void Addressee2(int i)

Get or set the value of the Addressee2 field.
:::

::: method
int Addressee3() void Addressee3(int i)

Get or set the value of the Addressee3 field.
:::

::: method
int Addressee4() void Addressee4(int i)

Get or set the value of the Addressee4 field.
:::
:::



::: WsfTadilJ7_3I
::: method
WsfTadilJ7_3C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ7_3C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ7_3C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ7_3C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ7_3C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ7_3C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
int Action() void Action(int i)

Get or set the value of the Action field.
:::

::: method
int TextIndicator() void TextIndicator(int i)

Get or set the value of the TextIndicator field.
:::

::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::
:::



::: WsfTadilJ7_3C1
::: method
int Addressee1() void Addressee1(int i)

Get or set the value of the Addressee1 field.
:::

::: method
int Addressee2() void Addressee2(int i)

Get or set the value of the Addressee2 field.
:::

::: method
int Addressee3() void Addressee3(int i)

Get or set the value of the Addressee3 field.
:::

::: method
int Addressee4() void Addressee4(int i)

Get or set the value of the Addressee4 field.
:::
:::



::: WsfTadilJ7_3C2
::: method
int Char1() void Char1(int i)

Get or set the value of the Char1 field.
:::

::: method
int Char2() void Char2(int i)

Get or set the value of the Char2 field.
:::

::: method
int Char3() void Char3(int i)

Get or set the value of the Char3 field.
:::

::: method
int Char4() void Char4(int i)

Get or set the value of the Char4 field.
:::

::: method
int Char5() void Char5(int i)

Get or set the value of the Char5 field.
:::

::: method
int Char6() void Char6(int i)

Get or set the value of the Char6 field.
:::

::: method
int Char7() void Char7(int i)

Get or set the value of the Char7 field.
:::

::: method
int Char8() void Char8(int i)

Get or set the value of the Char8 field.
:::

::: method
int Char9() void Char9(int i)

Get or set the value of the Char9 field.
:::

::: method
int Char10() void Char10(int i)

Get or set the value of the Char10 field.
:::
:::



::: WsfTadilJ7_3C3
::: method
int Char11() void Char11(int i)

Get or set the value of the Char11 field.
:::

::: method
int Char12() void Char12(int i)

Get or set the value of the Char12 field.
:::

::: method
int Char13() void Char13(int i)

Get or set the value of the Char13 field.
:::

::: method
int Char14() void Char14(int i)

Get or set the value of the Char14 field.
:::

::: method
int Char15() void Char15(int i)

Get or set the value of the Char15 field.
:::

::: method
int Char16() void Char16(int i)

Get or set the value of the Char16 field.
:::

::: method
int Char17() void Char17(int i)

Get or set the value of the Char17 field.
:::

::: method
int Char18() void Char18(int i)

Get or set the value of the Char18 field.
:::

::: method
int Char19() void Char19(int i)

Get or set the value of the Char19 field.
:::

::: method
int Char20() void Char20(int i)

Get or set the value of the Char20 field.
:::
:::



::: WsfTadilJ9_0I
::: method
WsfTadilJ9_0E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ9_0E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ9_0C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ9_0C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ9_0C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ9_0C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
int Addressee() void Addressee(int i)

Get or set the value of the Addressee field.
:::

::: method
int Command() void Command(int i)

Get or set the value of the Command field.
:::

::: method
int ThreatWarningCondition() void ThreatWarningCondition(int i)

Get or set the value of the ThreatWarningCondition field.
:::

::: method
int WeaponType() void WeaponType(int i)

Get or set the value of the WeaponType field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
bool IsExerciseIndicator() void IsExerciseIndicator(bool b)

Get or set the value of the IsExerciseIndicator field.
:::

::: method
int ReceiptCompliance() void ReceiptCompliance(int i)

Get or set the value of the ReceiptCompliance field.
:::

::: method
int RecurrenceRate() void RecurrenceRate(int i)

Get or set the value of the RecurrenceRate field.
:::
:::



::: WsfTadilJ9_0E0
::: method
int DutyAssignment() void DutyAssignment(int i)

Get or set the value of the DutyAssignment field.
:::

::: method
int Mission() void Mission(int i)

Get or set the value of the Mission field.
:::

::: method
int NumberOfAircraft() void NumberOfAircraft(int i)

Get or set the value of the NumberOfAircraft field.
:::

::: method
int ThreatEnvironment() void ThreatEnvironment(int i)

Get or set the value of the ThreatEnvironment field.
:::

::: method
int DutyAssignmentArea() void DutyAssignmentArea(int i)

Get or set the value of the DutyAssignmentArea field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::
:::



::: WsfTadilJ9_0C1
::: method
string Callsign() void Callsign(string s)

Get or set the value of the Callsign field.
:::

::: method
int Frequency() void Frequency(int i)

Get or set the value of the Frequency field.
:::

::: method
int ControlChannel() void ControlChannel(int i)

Get or set the value of the ControlChannel field.
:::

::: method
bool IsSecureRadioIndicator() void IsSecureRadioIndicator(bool b)

Get or set the value of the IsSecureRadioIndicator field.
:::

::: method
bool IsAlternateVoiceFrequencyChannel() void IsAlternateVoiceFrequencyChannel(bool b)

Get or set the value of the IsAlternateVoiceFrequencyChannel field.
:::

::: method
int MethodOfFire() void MethodOfFire(int i)

Get or set the value of the MethodOfFire field.
:::
:::



::: WsfTadilJ9_0C2
::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Second() void Second(int i)

Get or set the value of the Second field.
:::

::: method
int NumberOfMissiles() void NumberOfMissiles(int i)

Get or set the value of the NumberOfMissiles field.
:::
:::



::: WsfTadilJ9_1I
::: method
int Addressee() void Addressee(int i)

Get or set the value of the Addressee field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int FireMethod() void FireMethod(int i)

Get or set the value of the FireMethod field.
:::

::: method
int EngagementAction() void EngagementAction(int i)

Get or set the value of the EngagementAction field.
:::

::: method
int ProbabilityOfKill() void ProbabilityOfKill(int i)

Get or set the value of the ProbabilityOfKill field.
:::

::: method
int TrackingStatus() void TrackingStatus(int i)

Get or set the value of the TrackingStatus field.
:::

::: method
int ReceiptCompliance() void ReceiptCompliance(int i)

Get or set the value of the ReceiptCompliance field.
:::

::: method
int RecurrenceRate() void RecurrenceRate(int i)

Get or set the value of the RecurrenceRate field.
:::
:::



::: WsfTadilJ10_2I
::: method
WsfTadilJ10_2C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ10_2C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ10_2C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ10_2C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
string TrackNumber1() void TrackNumber1(string s)

Get or set the value of the TrackNumber1 field.
:::

::: method
string TrackNumber2() void TrackNumber2(string s)

Get or set the value of the TrackNumber2 field.
:::

::: method
int WeaponSystem() void WeaponSystem(int i)

Get or set the value of the WeaponSystem field.
:::

::: method
int WeaponEngagementStatus() void WeaponEngagementStatus(int i)

Get or set the value of the WeaponEngagementStatus field.
:::
:::



::: WsfTadilJ10_2C1
::: method
string TrackNumber1() void TrackNumber1(string s)

Get or set the value of the TrackNumber1 field.
:::

::: method
int AirWeaponEngagement1() void AirWeaponEngagement1(int i)

Get or set the value of the AirWeaponEngagement1 field.
:::

::: method
string TrackNumber2() void TrackNumber2(string s)

Get or set the value of the TrackNumber2 field.
:::

::: method
int AirWeaponEngagement2() void AirWeaponEngagement2(int i)

Get or set the value of the AirWeaponEngagement2 field.
:::
:::



::: WsfTadilJ10_2C2
::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int TimeFunctionIntercept() void TimeFunctionIntercept(int i)

Get or set the value of the TimeFunctionIntercept field.
:::

::: method
double Second() void Second(double d)

Get or set the value of the Second field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::
:::



::: WsfTadilJ11_0I
::: method
WsfTadilJ11_0E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ11_0E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ11_0C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_0C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ11_0C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_0C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ11_0C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_0C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
WsfTadilJ11_0C4 AddContinuation4()

Adds continuation word 4 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation4() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_0C4 FindContinuation4()

Returns extension word 4 if it has been added to the message.
:::

::: method
WsfTadilJ11_0C5 AddContinuation5()

Adds continuation word 5 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation5() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_0C5 FindContinuation5()

Returns extension word 5 if it has been added to the message.
:::

::: method
WsfTadilJ11_0C9 AddContinuation9()

Adds continuation word 9 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation9() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_0C9 FindContinuation9()

Returns extension word 9 if it has been added to the message.
:::

::: method
int WeaponStatus() void WeaponStatus(int i)

Get or set the value of the WeaponStatus field.
:::

::: method
int WeaponType() void WeaponType(int i)

Get or set the value of the WeaponType field.
:::

::: method
int WeaponProfile() void WeaponProfile(int i)

Get or set the value of the WeaponProfile field.
:::

::: method
int SeekerAquisitionConfidence() void SeekerAquisitionConfidence(int i)

Get or set the value of the SeekerAquisitionConfidence field.
:::
:::



::: WsfTadilJ11_0E0
::: method
int SeekerAcquisitionStatus() void SeekerAcquisitionStatus(int i)

Get or set the value of the SeekerAcquisitionStatus field.
:::

::: method
int TrackNumberIndexOrig2() void TrackNumberIndexOrig2(int i)

Get or set the value of the TrackNumberIndexOrig2 field.
:::
:::



::: WsfTadilJ11_0C1
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double HeightAboveEllipsoid() void HeightAboveEllipsoid(double d)

Get or set the value of the HeightAboveEllipsoid field. Units are Meters.
:::
:::



::: WsfTadilJ11_0C2
::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
int SelfAbortReason() void SelfAbortReason(int i)

Get or set the value of the SelfAbortReason field.
:::

::: method
double TimeToGoSeconds() void TimeToGoSeconds(double d)

Get or set the value of the TimeToGoSeconds field.
:::
:::





::: WsfTadilJ11_0C4
::: method
int TrackNumberController() void TrackNumberController(int i)

Get or set the value of the TrackNumberController field.
:::
:::



::: WsfTadilJ11_0C5
::: method
int TimeToLockout() void TimeToLockout(int i)

Get or set the value of the TimeToLockout field.
:::
:::



::: WsfTadilJ11_0C9
::: method
int DetailedStatusInformation() void DetailedStatusInformation(int i)

Get or set the value of the DetailedStatusInformation field.
:::
:::



::: WsfTadilJ11_0C13
::: method
double WeaponLatitude() void WeaponLatitude(double d)

Get or set the value of the WeaponLatitude field. Units are Deg.
:::

::: method
double WeaponLongitude() void WeaponLongitude(double d)

Get or set the value of the WeaponLongitude field. Units are Deg.
:::
:::



::: WsfTadilJ11_0C14
::: method
double WeaponHeightAboveEllipsoid() void WeaponHeightAboveEllipsoid(double d)

Get or set the value of the WeaponHeightAboveEllipsoid field. Units are Meters.
:::

::: method
double WeaponVelocityNorth() void WeaponVelocityNorth(double d)

Get or set the value of the WeaponVelocityNorth field. Units are Meters Per Second.
:::

::: method
double WeaponVelocityEast() void WeaponVelocityEast(double d)

Get or set the value of the WeaponVelocityEast field. Units are Meters Per Second.
:::

::: method
double WeaponVelocityDown() void WeaponVelocityDown(double d)

Get or set the value of the WeaponVelocityDown field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ11_0C15
::: method
double TargetLatitude() void TargetLatitude(double d)

Get or set the value of the TargetLatitude field. Units are Deg.
:::

::: method
double TargetLongitude() void TargetLongitude(double d)

Get or set the value of the TargetLongitude field. Units are Deg.
:::
:::



::: WsfTadilJ11_0C16
::: method
double TargetHeightAboveEllipsoid() void TargetHeightAboveEllipsoid(double d)

Get or set the value of the TargetHeightAboveEllipsoid field. Units are Meters.
:::

::: method
double TargetVelocityNorth() void TargetVelocityNorth(double d)

Get or set the value of the TargetVelocityNorth field. Units are Meters Per Second.
:::

::: method
double TargetVelocityEast() void TargetVelocityEast(double d)

Get or set the value of the TargetVelocityEast field. Units are Meters Per Second.
:::

::: method
double TargetVelocityDown() void TargetVelocityDown(double d)

Get or set the value of the TargetVelocityDown field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ11_0C17
::: method
double AimpointLatitude() void AimpointLatitude(double d)

Get or set the value of the AimpointLatitude field. Units are Deg.
:::
:::



::: WsfTadilJ11_0C18
::: method
double AimpointLongitude() void AimpointLongitude(double d)

Get or set the value of the AimpointLongitude field. Units are Deg.
:::

::: method
double AimpointHeightAboveEllipsoid() void AimpointHeightAboveEllipsoid(double d)

Get or set the value of the AimpointHeightAboveEllipsoid field. Units are Meters.
:::
:::



::: WsfTadilJ11_1I
::: method
WsfTadilJ11_1E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ11_1E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ11_1C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_1C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ11_1C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_1C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ11_1C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_1C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
WsfTadilJ11_1C5 AddContinuation5()

Adds continuation word 5 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation5() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_1C5 FindContinuation5()

Returns extension word 5 if it has been added to the message.
:::

::: method
WsfTadilJ11_1C9 AddContinuation9()

Adds continuation word 9 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation9() may be used to retrieve the word.
:::

::: method
WsfTadilJ11_1C9 FindContinuation9()

Returns extension word 9 if it has been added to the message.
:::

::: method
int TrackNumberAddressee() void TrackNumberAddressee(int i)

Get or set the value of the TrackNumberAddressee field.
:::

::: method
int WeaponDirective() void WeaponDirective(int i)

Get or set the value of the WeaponDirective field.
:::

::: method
bool ResponseRequest() void ResponseRequest(bool b)

Get or set the value of the ResponseRequest field.
:::

::: method
int IndexNumber() void IndexNumber(int i)

Get or set the value of the IndexNumber field.
:::

::: method
bool FuseModeCommand() void FuseModeCommand(bool b)

Get or set the value of the FuseModeCommand field.
:::

::: method
int Environment() void Environment(int i)

Get or set the value of the Environment field.
:::
:::





::: WsfTadilJ11_1C1
::: method
int ThirdPartySource() void ThirdPartySource(int i)

Get or set the value of the ThirdPartySource field.
:::

::: method
int TrackNumberHandOff() void TrackNumberHandOff(int i)

Get or set the value of the TrackNumberHandOff field.
:::
:::



::: WsfTadilJ11_1C2
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double TargetVelocityDown() void TargetVelocityDown(double d)

Get or set the value of the TargetVelocityDown field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ11_1C3
::: method
double HeightAboveEllipsoid() void HeightAboveEllipsoid(double d)

Get or set the value of the HeightAboveEllipsoid field. Units are Meters.
:::

::: method
double TargetVelocityNorth() void TargetVelocityNorth(double d)

Get or set the value of the TargetVelocityNorth field. Units are Meters Per Second.
:::

::: method
double TargetVelocityEast() void TargetVelocityEast(double d)

Get or set the value of the TargetVelocityEast field. Units are Meters Per Second.
:::

::: method
int TimeOfTgtMeasurement() void TimeOfTgtMeasurement(int i)

Get or set the value of the TimeOfTgtMeasurement field.
:::

::: method
bool EstimatedMeasureIndicator() void EstimatedMeasureIndicator(bool b)

Get or set the value of the EstimatedMeasureIndicator field.
:::

::: method
bool IFTUSuppIndicator() void IFTUSuppIndicator(bool b)

Get or set the value of the IFTUSuppIndicator field.
:::
:::



::: WsfTadilJ11_1C5
::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field. MUST be set for a J11.1 to be sent out over the network. Internally checking for target index, though other non-zero integer values should work.
:::

::: method
int SpecificType() void SpecificType(int i)

Get or set the value of the SpecificType field.
:::

::: method
double ImpactCourse() void ImpactCourse(double d)

Get or set the value of the ImpactCourse field. Units are Radians.
:::

::: method
int ImpactElevationAngle() void ImpactElevationAngle(int i)

Get or set the value of the ImpactElevationAngle field.
:::

::: method
int TrackNumberOrig2() void TrackNumberOrig2(int i)

Get or set the value of the TrackNumberOrig2 field.
:::
:::



::: WsfTadilJ11_1C9
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double TargetVelocityDown() void TargetVelocityDown(double d)

Get or set the value of the TargetVelocityDown field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ11_1C10
::: method
double TargetVelocityNorth() void TargetVelocityNorth(double d)

Get or set the value of the TargetVelocityNorth field. Units are Meters Per Second.
:::

::: method
double TargetVelocityEast() void TargetVelocityEast(double d)

Get or set the value of the TargetVelocityEast field. Units are Meters Per Second.
:::

::: method
double TargetVelocityDown() void TargetVelocityDown(double d)

Get or set the value of the TargetVelocityDown field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ11_2I
::: method
int WeaponCoordination() void WeaponCoordination(int i)

Get or set the value of the WeaponCoordination field.
:::
:::



::: WsfTadilJ12_0I
::: method
WsfTadilJ12_0C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_0C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ12_0C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_0C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ12_0C3 AddContinuation3()

Adds continuation word 3 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation3() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_0C3 FindContinuation3()

Returns extension word 3 if it has been added to the message.
:::

::: method
WsfTadilJ12_0C4 AddContinuation4()

Adds continuation word 4 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation4() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_0C4 FindContinuation4()

Returns extension word 4 if it has been added to the message.
:::

::: method
WsfTadilJ12_0C5 AddContinuation5()

Adds continuation word 5 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation5() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_0C5 FindContinuation5()

Returns extension word 5 if it has been added to the message.
:::

::: method
WsfTadilJ12_0C6 AddContinuation6()

Adds continuation word 6 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation6() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_0C6 FindContinuation6()

Returns extension word 6 if it has been added to the message.
:::

::: method
WsfTadilJ12_0C7 AddContinuation7()

Adds continuation word 7 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation7() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_0C7 FindContinuation7()

Returns extension word 7 if it has been added to the message.
:::

::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
int MissionAssignment() void MissionAssignment(int i)

Get or set the value of the MissionAssignment field.
:::

::: method
string TrackNumberObjective() void TrackNumberObjective(string s)

Get or set the value of the TrackNumberObjective field.
:::

::: method
int ThreatWarningEnvironment() void ThreatWarningEnvironment(int i)

Get or set the value of the ThreatWarningEnvironment field.
:::

::: method
int ReceiptCompliance() void ReceiptCompliance(int i)

Get or set the value of the ReceiptCompliance field.
:::

::: method
int RecurrenceRate() void RecurrenceRate(int i)

Get or set the value of the RecurrenceRate field.
:::
:::



::: WsfTadilJ12_0C1
::: method
int Strength() void Strength(int i)

Get or set the value of the Strength field.
:::

::: method
int Environment() void Environment(int i)

Get or set the value of the Environment field.
:::

::: method
bool IsReceiversIndexNumber() void IsReceiversIndexNumber(bool b)

Get or set the value of the IsReceiversIndexNumber field.
:::

::: method
int IndexNumber() void IndexNumber(int i)

Get or set the value of the IndexNumber field.
:::

::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::
:::



::: WsfTadilJ12_0C2
::: method
int MethodOfFire() void MethodOfFire(int i)

Get or set the value of the MethodOfFire field.
:::

::: method
double Heading() void Heading(double d)

Get or set the value of the Heading field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
bool IsExerciseTrackUnit() void IsExerciseTrackUnit(bool b)

Get or set the value of the IsExerciseTrackUnit field.
:::

::: method
int SpecificType() void SpecificType(int i)

Get or set the value of the SpecificType field.
:::

::: method
bool IsSpecialIntrest() void IsSpecialIntrest(bool b)

Get or set the value of the IsSpecialIntrest field.
:::
:::



::: WsfTadilJ12_0C3
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Elevation() void Elevation(double d)

Get or set the value of the Elevation field. Units are Meters.
:::

::: method
int PointType() void PointType(int i)

Get or set the value of the PointType field.
:::

::: method
int PointNumber() void PointNumber(int i)

Get or set the value of the PointNumber field.
:::
:::



::: WsfTadilJ12_0C4
::: method
int TargetType() void TargetType(int i)

Get or set the value of the TargetType field.
:::

::: method
int TargetDefenses() void TargetDefenses(int i)

Get or set the value of the TargetDefenses field.
:::

::: method
double RunInHeading() void RunInHeading(double d)

Get or set the value of the RunInHeading field. Units are Radians.
:::

::: method
double EngressHeading() void EngressHeading(double d)

Get or set the value of the EngressHeading field. Units are Radians.
:::

::: method
int NumberOfStores() void NumberOfStores(int i)

Get or set the value of the NumberOfStores field.
:::

::: method
int TypeOfStores() void TypeOfStores(int i)

Get or set the value of the TypeOfStores field.
:::

::: method
int ModeOfDelivery() void ModeOfDelivery(int i)

Get or set the value of the ModeOfDelivery field.
:::

::: method
int TimeDiscrete() void TimeDiscrete(int i)

Get or set the value of the TimeDiscrete field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::
:::



::: WsfTadilJ12_0C5
::: method
double TargetRange() void TargetRange(double d)

Get or set the value of the TargetRange field. Units are Meters.
:::

::: method
double TargetBearing() void TargetBearing(double d)

Get or set the value of the TargetBearing field. Units are Radians.
:::

::: method
double DeltaElevation() void DeltaElevation(double d)

Get or set the value of the DeltaElevation field. Units are Meters.
:::

::: method
int BeaconCode() void BeaconCode(int i)

Get or set the value of the BeaconCode field.
:::
:::



::: WsfTadilJ12_0C6
::: method
int LaserIlluminatorCode() void LaserIlluminatorCode(int i)

Get or set the value of the LaserIlluminatorCode field.
:::

::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
int IndexNumber() void IndexNumber(int i)

Get or set the value of the IndexNumber field.
:::
:::



::: WsfTadilJ12_0C7
::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
int IndexNumber() void IndexNumber(int i)

Get or set the value of the IndexNumber field.
:::

::: method
double ElevationAngle() void ElevationAngle(double d)

Get or set the value of the ElevationAngle field. Units are Radians.
:::
:::



::: WsfTadilJ12_1I
::: method
WsfTadilJ12_1E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_1E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int VectorType() void VectorType(int i)

Get or set the value of the VectorType field.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::
:::



::: WsfTadilJ12_1E0
::: method
int SecondsToIntercept() void SecondsToIntercept(int i)

Get or set the value of the SecondsToIntercept field.
:::

::: method
int ReceiptCompliance() void ReceiptCompliance(int i)

Get or set the value of the ReceiptCompliance field.
:::

::: method
int RecurrenceRate() void RecurrenceRate(int i)

Get or set the value of the RecurrenceRate field.
:::
:::



::: WsfTadilJ12_2I
::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
int VerticalControl() void VerticalControl(int i)

Get or set the value of the VerticalControl field.
:::

::: method
int VerticalError() void VerticalError(int i)

Get or set the value of the VerticalError field.
:::

::: method
int LateralError() void LateralError(int i)

Get or set the value of the LateralError field.
:::

::: method
int LateralControl() void LateralControl(int i)

Get or set the value of the LateralControl field.
:::

::: method
int PrecisionDirection() void PrecisionDirection(int i)

Get or set the value of the PrecisionDirection field.
:::

::: method
int TimeToGoSeconds() void TimeToGoSeconds(int i)

Get or set the value of the TimeToGoSeconds field.
:::

::: method
bool IsAutopilotControlled() void IsAutopilotControlled(bool b)

Get or set the value of the IsAutopilotControlled field.
:::

::: method
bool DoDropWeapon() void DoDropWeapon(bool b)

Get or set the value of the DoDropWeapon field.
:::
:::



::: WsfTadilJ12_3I
::: method
WsfTadilJ12_3E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_3E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
int SequenceNumber() void SequenceNumber(int i)

Get or set the value of the SequenceNumber field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
int TimeFunction() void TimeFunction(int i)

Get or set the value of the TimeFunction field.
:::

::: method
int WaypointAction() void WaypointAction(int i)

Get or set the value of the WaypointAction field.
:::

::: method
int TotalPoints() void TotalPoints(int i)

Get or set the value of the TotalPoints field.
:::
:::



::: WsfTadilJ12_3E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int AltitudeFunction() void AltitudeFunction(int i)

Get or set the value of the AltitudeFunction field.
:::
:::



::: WsfTadilJ12_4I
::: method
WsfTadilJ12_4E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_4E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
int TrackNumberAddressee() void TrackNumberAddressee(int i)

Get or set the value of the TrackNumberAddressee field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int ControlChange() void ControlChange(int i)

Get or set the value of the ControlChange field.
:::

::: method
int ControlChannel() void ControlChannel(int i)

Get or set the value of the ControlChannel field.
:::

::: method
int RadioType() void RadioType(int i)

Get or set the value of the RadioType field.
:::

::: method
bool IsSecureRadio() void IsSecureRadio(bool b)

Get or set the value of the IsSecureRadio field.
:::

::: method
int ReceiptCompliance() void ReceiptCompliance(int i)

Get or set the value of the ReceiptCompliance field.
:::

::: method
int RecurrenceRate() void RecurrenceRate(int i)

Get or set the value of the RecurrenceRate field.
:::
:::



::: WsfTadilJ12_4E0
::: method
bool IsAlternateVoiceChannel() void IsAlternateVoiceChannel(bool b)

Get or set the value of the IsAlternateVoiceChannel field.
:::

::: method
string VoiceCallsign() void VoiceCallsign(string s)

Get or set the value of the VoiceCallsign field.
:::

::: method
int Link4aFrequency() void Link4aFrequency(int i)

Get or set the value of the Link4aFrequency field.
:::

::: method
int Mode3Code() void Mode3Code(int i)

Get or set the value of the Mode3Code field.
:::

::: method
bool SquawkIndicator() void SquawkIndicator(bool b)

Get or set the value of the SquawkIndicator field.
:::

::: method
int VoiceFrequency() void VoiceFrequency(int i)

Get or set the value of the VoiceFrequency field.
:::
:::



::: WsfTadilJ12_5I
::: method
WsfTadilJ12_5E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_5E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ12_5E1 AddExtension1()

Adds extension word 1 to the message. This can only be called once for each extension word, and only after extension word 0 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension1 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_5E1 FindExtension1()

Returns extension word 1 if it has been added to the message.
:::

::: method
string ObjectiveTrackNumber() void ObjectiveTrackNumber(string s)

Get or set the value of the ObjectiveTrackNumber field.
:::

::: method
int OriginatorTrackNumber() void OriginatorTrackNumber(int i)

Get or set the value of the OriginatorTrackNumber field.
:::

::: method
int IndexNumber() void IndexNumber(int i)

Get or set the value of the IndexNumber field.
:::

::: method
bool IsCorrelated() void IsCorrelated(bool b)

Get or set the value of the IsCorrelated field.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
int IdIndicator() void IdIndicator(int i)

Get or set the value of the IdIndicator field.
:::
:::



::: WsfTadilJ12_5E0
::: method
int OriginatorTrackNumberB() void OriginatorTrackNumberB(int i)

Get or set the value of the OriginatorTrackNumberB field.
:::

::: method
int OriginatorIndexB() void OriginatorIndexB(int i)

Get or set the value of the OriginatorIndexB field.
:::

::: method
bool IsCorrelatedB() void IsCorrelatedB(bool b)

Get or set the value of the IsCorrelatedB field.
:::

::: method
int OriginatorTrackNumberC() void OriginatorTrackNumberC(int i)

Get or set the value of the OriginatorTrackNumberC field.
:::

::: method
int OriginatorIndexC() void OriginatorIndexC(int i)

Get or set the value of the OriginatorIndexC field.
:::

::: method
bool IsCorrelatedC() void IsCorrelatedC(bool b)

Get or set the value of the IsCorrelatedC field.
:::

::: method
int OriginatorTrackNumberD() void OriginatorTrackNumberD(int i)

Get or set the value of the OriginatorTrackNumberD field.
:::

::: method
int OriginatorIndexD() void OriginatorIndexD(int i)

Get or set the value of the OriginatorIndexD field.
:::

::: method
bool IsCorrelatedD() void IsCorrelatedD(bool b)

Get or set the value of the IsCorrelatedD field.
:::
:::



::: WsfTadilJ12_5E1
::: method
int OriginatorTrackNumberE() void OriginatorTrackNumberE(int i)

Get or set the value of the OriginatorTrackNumberE field.
:::

::: method
int OriginatorIndexE() void OriginatorIndexE(int i)

Get or set the value of the OriginatorIndexE field.
:::

::: method
bool IsCorrelatedE() void IsCorrelatedE(bool b)

Get or set the value of the IsCorrelatedE field.
:::

::: method
int OriginatorTrackNumberF() void OriginatorTrackNumberF(int i)

Get or set the value of the OriginatorTrackNumberF field.
:::

::: method
int OriginatorIndexF() void OriginatorIndexF(int i)

Get or set the value of the OriginatorIndexF field.
:::

::: method
bool IsCorrelatedF() void IsCorrelatedF(bool b)

Get or set the value of the IsCorrelatedF field.
:::

::: method
int OriginatorTrackNumberG() void OriginatorTrackNumberG(int i)

Get or set the value of the OriginatorTrackNumberG field.
:::

::: method
int OriginatorIndexG() void OriginatorIndexG(int i)

Get or set the value of the OriginatorIndexG field.
:::

::: method
bool IsCorrelatedG() void IsCorrelatedG(bool b)

Get or set the value of the IsCorrelatedG field.
:::
:::



::: WsfTadilJ12_6I
::: method
WsfTadilJ12_6E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_6E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ12_6C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_6C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ12_6C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_6C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
int StatusInformation() void StatusInformation(int i)

Get or set the value of the StatusInformation field.
:::

::: method
int TargetPositionQuality() void TargetPositionQuality(int i)

Get or set the value of the TargetPositionQuality field.
:::

::: method
int Environment() void Environment(int i)

Get or set the value of the Environment field.
:::

::: method
int MsgOriginator() void MsgOriginator(int i)

Get or set the value of the MsgOriginator field.
:::

::: method
int IndexNumber() void IndexNumber(int i)

Get or set the value of the IndexNumber field.
:::

::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::
:::



::: WsfTadilJ12_6E0
::: method
string TrackNumberObjective() void TrackNumberObjective(string s)

Get or set the value of the TrackNumberObjective field.
:::

::: method
bool CorrelationIndicator() void CorrelationIndicator(bool b)

Get or set the value of the CorrelationIndicator field.
:::

::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int Identity() void Identity(int i)

Get or set the value of the Identity field.
:::

::: method
bool ExerciseIndicator() void ExerciseIndicator(bool b)

Get or set the value of the ExerciseIndicator field.
:::

::: method
int SpecificType() void SpecificType(int i)

Get or set the value of the SpecificType field.
:::

::: method
bool SpecialInterest() void SpecialInterest(bool b)

Get or set the value of the SpecialInterest field.
:::
:::



::: WsfTadilJ12_6C1
::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
bool Pointer() void Pointer(bool b)

Get or set the value of the Pointer field.
:::

::: method
int WeaponSystem() void WeaponSystem(int i)

Get or set the value of the WeaponSystem field.
:::

::: method
int WeaponEngagementStatus() void WeaponEngagementStatus(int i)

Get or set the value of the WeaponEngagementStatus field.
:::

::: method
string TrackNumberReference() void TrackNumberReference(string s)

Get or set the value of the TrackNumberReference field.
:::

::: method
int Mode3_IFF() void Mode3_IFF(int i)

Get or set the value of the Mode3_IFF field.
:::

::: method
int Mode4_Indicator() void Mode4_Indicator(int i)

Get or set the value of the Mode4_Indicator field.
:::

::: method
int Strength() void Strength(int i)

Get or set the value of the Strength field.
:::
:::



::: WsfTadilJ12_6C2
::: method
bool IsEndOfTargetReporting() void IsEndOfTargetReporting(bool b)

Get or set the value of the IsEndOfTargetReporting field.
:::

::: method
int TimeIndicator() void TimeIndicator(int i)

Get or set the value of the TimeIndicator field.
:::

::: method
int Minutes() void Minutes(int i)

Get or set the value of the Minutes field.
:::

::: method
int Seconds() void Seconds(int i)

Get or set the value of the Seconds field.
:::

::: method
int Hundredths() void Hundredths(int i)

Get or set the value of the Hundredths field.
:::

::: method
int PrfType() void PrfType(int i)

Get or set the value of the PrfType field.
:::

::: method
int EmitterStatus() void EmitterStatus(int i)

Get or set the value of the EmitterStatus field.
:::

::: method
int JammerType() void JammerType(int i)

Get or set the value of the JammerType field.
:::

::: method
int TrackNumber() void TrackNumber(int i)

Get or set the value of the TrackNumber field.
:::

::: method
int TrackIndex() void TrackIndex(int i)

Get or set the value of the TrackIndex field.
:::

::: method
int SensorType() void SensorType(int i)

Get or set the value of the SensorType field.
:::

::: method
bool IsDataFusion() void IsDataFusion(bool b)

Get or set the value of the IsDataFusion field.
:::
:::



::: WsfTadilJ12_7I
::: method
WsfTadilJ12_7E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_7E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ12_7C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ12_7C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ12_7E1 AddExtension1()

Adds extension word 1 to the message. This can only be called once for each extension word, and only after extension word 0 has been added. Returns the extension word. Once the extension word belongs to the message, FindExtension1 may be used to retrieve the word.
:::

::: method
WsfTadilJ12_7E1 FindExtension1()

Returns extension word 1 if it has been added to the message.
:::

::: method
int IndexNumber() void IndexNumber(int i)

Get or set the value of the IndexNumber field.
:::

::: method
bool IsOffboard() void IsOffboard(bool b)

Get or set the value of the IsOffboard field.
:::

::: method
bool IsMultipleTargets() void IsMultipleTargets(bool b)

Get or set the value of the IsMultipleTargets field.
:::

::: method
bool IsLastInSequence() void IsLastInSequence(bool b)

Get or set the value of the IsLastInSequence field.
:::

::: method
int SensorType() void SensorType(int i)

Get or set the value of the SensorType field.
:::

::: method
int JammerType() void JammerType(int i)

Get or set the value of the JammerType field.
:::

::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::
:::



::: WsfTadilJ12_7E0
::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
double ElevationAngle() void ElevationAngle(double d)

Get or set the value of the ElevationAngle field. Units are Radians.
:::

::: method
double BearingIntercept() void BearingIntercept(double d)

Get or set the value of the BearingIntercept field. Units are Radians.
:::

::: method
int ElevationUncertainty() void ElevationUncertainty(int i)

Get or set the value of the ElevationUncertainty field.
:::

::: method
int BearingUncertainty() void BearingUncertainty(int i)

Get or set the value of the BearingUncertainty field.
:::

::: method
int FrequencyIntercept() void FrequencyIntercept(int i)

Get or set the value of the FrequencyIntercept field.
:::

::: method
int FrequencyExponent() void FrequencyExponent(int i)

Get or set the value of the FrequencyExponent field.
:::
:::



::: WsfTadilJ12_7E1
::: method
int TimeIndicator() void TimeIndicator(int i)

Get or set the value of the TimeIndicator field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Second() void Second(int i)

Get or set the value of the Second field.
:::

::: method
int Hundredths() void Hundredths(int i)

Get or set the value of the Hundredths field.
:::

::: method
int BearingAngle() void BearingAngle(int i)

Get or set the value of the BearingAngle field.
:::

::: method
int BearingAngleRate() void BearingAngleRate(int i)

Get or set the value of the BearingAngleRate field.
:::

::: method
int ElevationAngle() void ElevationAngle(int i)

Get or set the value of the ElevationAngle field.
:::

::: method
int ElevationAngleRate() void ElevationAngleRate(int i)

Get or set the value of the ElevationAngleRate field.
:::

::: method
int EmitterPRF() void EmitterPRF(int i)

Get or set the value of the EmitterPRF field.
:::

::: method
int EmitterStatus() void EmitterStatus(int i)

Get or set the value of the EmitterStatus field.
:::
:::



::: WsfTadilJ12_7C1
::: method
double Range() void Range(double d)

Get or set the value of the Range field. Units are Meters.
:::

::: method
int RangeUncertainty() void RangeUncertainty(int i)

Get or set the value of the RangeUncertainty field.
:::

::: method
int RangeRate() void RangeRate(int i)

Get or set the value of the RangeRate field.
:::

::: method
int RangeRateUncertainty() void RangeRateUncertainty(int i)

Get or set the value of the RangeRateUncertainty field.
:::

::: method
int ObserverVelocityNorth() void ObserverVelocityNorth(int i)

Get or set the value of the ObserverVelocityNorth field.
:::

::: method
int ObserverVelocityEast() void ObserverVelocityEast(int i)

Get or set the value of the ObserverVelocityEast field.
:::
:::



::: WsfTadilJ13_2I
::: method
WsfTadilJ13_2C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ13_2C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
WsfTadilJ13_2C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ13_2C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
int OperationalCapability() void OperationalCapability(int i)

Get or set the value of the OperationalCapability field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
bool IsOffLoadableFuel() void IsOffLoadableFuel(bool b)

Get or set the value of the IsOffLoadableFuel field.
:::

::: method
double Fuel() void Fuel(double d)

Get or set the value of the Fuel field. Units are Kg.
:::

::: method
int RadarChannelType() void RadarChannelType(int i)

Get or set the value of the RadarChannelType field.
:::

::: method
int RadarChannel() void RadarChannel(int i)

Get or set the value of the RadarChannel field.
:::

::: method
int TimeReportFunction() void TimeReportFunction(int i)

Get or set the value of the TimeReportFunction field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::
:::



::: WsfTadilJ13_2C1
::: method
int RadarStatus() void RadarStatus(int i)

Get or set the value of the RadarStatus field.
:::

::: method
int InfaredStatus() void InfaredStatus(int i)

Get or set the value of the InfaredStatus field.
:::

::: method
int MissileChannel() void MissileChannel(int i)

Get or set the value of the MissileChannel field.
:::

::: method
int LaserStatus() void LaserStatus(int i)

Get or set the value of the LaserStatus field.
:::

::: method
int TelevisionStatus() void TelevisionStatus(int i)

Get or set the value of the TelevisionStatus field.
:::

::: method
int RadarWarningAndReceivingStatus() void RadarWarningAndReceivingStatus(int i)

Get or set the value of the RadarWarningAndReceivingStatus field.
:::

::: method
int AirToAirWeaponsStatus() void AirToAirWeaponsStatus(int i)

Get or set the value of the AirToAirWeaponsStatus field.
:::

::: method
int AirToGroundWeaponsStatus() void AirToGroundWeaponsStatus(int i)

Get or set the value of the AirToGroundWeaponsStatus field.
:::

::: method
int RadioType1() void RadioType1(int i)

Get or set the value of the RadioType1 field.
:::

::: method
int RadioStatus1() void RadioStatus1(int i)

Get or set the value of the RadioStatus1 field.
:::

::: method
int RadioSecurity1() void RadioSecurity1(int i)

Get or set the value of the RadioSecurity1 field.
:::

::: method
int RadioType2() void RadioType2(int i)

Get or set the value of the RadioType2 field.
:::

::: method
int RadioStatus2() void RadioStatus2(int i)

Get or set the value of the RadioStatus2 field.
:::

::: method
int RadioSecurity2() void RadioSecurity2(int i)

Get or set the value of the RadioSecurity2 field.
:::

::: method
int RadioType3() void RadioType3(int i)

Get or set the value of the RadioType3 field.
:::

::: method
int RadioStatus3() void RadioStatus3(int i)

Get or set the value of the RadioStatus3 field.
:::

::: method
int RadioSecurity3() void RadioSecurity3(int i)

Get or set the value of the RadioSecurity3 field.
:::

::: method
bool IsAutopilot() void IsAutopilot(bool b)

Get or set the value of the IsAutopilot field.
:::

::: method
int IsSensorReportingAll() void IsSensorReportingAll(int i)

Get or set the value of the IsSensorReportingAll field.
:::

::: method
int AircraftModel() void AircraftModel(int i)

Get or set the value of the AircraftModel field.
:::

::: method
int AirSpecificType() void AirSpecificType(int i)

Get or set the value of the AirSpecificType field.
:::
:::



::: WsfTadilJ13_2C2
::: method
bool IsSummation() void IsSummation(bool b)

Get or set the value of the IsSummation field.
:::

::: method
int NumberOfStores1() void NumberOfStores1(int i)

Get or set the value of the NumberOfStores1 field.
:::

::: method
int StoreType1() void StoreType1(int i)

Get or set the value of the StoreType1 field.
:::

::: method
int NumberOfStores2() void NumberOfStores2(int i)

Get or set the value of the NumberOfStores2 field.
:::

::: method
int StoreType2() void StoreType2(int i)

Get or set the value of the StoreType2 field.
:::

::: method
int NumberOfStores3() void NumberOfStores3(int i)

Get or set the value of the NumberOfStores3 field.
:::

::: method
int StoreType3() void StoreType3(int i)

Get or set the value of the StoreType3 field.
:::

::: method
int NumberOfStores4() void NumberOfStores4(int i)

Get or set the value of the NumberOfStores4 field.
:::

::: method
int StoreType4() void StoreType4(int i)

Get or set the value of the StoreType4 field.
:::

::: method
bool IsGunCapable() void IsGunCapable(bool b)

Get or set the value of the IsGunCapable field.
:::

::: method
int TransponderStatus() void TransponderStatus(int i)

Get or set the value of the TransponderStatus field.
:::

::: method
int InterrogatorStatus() void InterrogatorStatus(int i)

Get or set the value of the InterrogatorStatus field.
:::
:::



::: WsfTadilJ13_3I
::: method
WsfTadilJ13_3C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ13_3C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
int OperationalCapability() void OperationalCapability(int i)

Get or set the value of the OperationalCapability field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int FlightDeckStatus() void FlightDeckStatus(int i)

Get or set the value of the FlightDeckStatus field.
:::

::: method
int LandingApproachCondition() void LandingApproachCondition(int i)

Get or set the value of the LandingApproachCondition field.
:::

::: method
int ReportTimeFunction() void ReportTimeFunction(int i)

Get or set the value of the ReportTimeFunction field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
int SurfaceSpecificType() void SurfaceSpecificType(int i)

Get or set the value of the SurfaceSpecificType field.
:::
:::



::: WsfTadilJ13_3C1
::: method
int MissileType1() void MissileType1(int i)

Get or set the value of the MissileType1 field.
:::

::: method
int MissileCount1() void MissileCount1(int i)

Get or set the value of the MissileCount1 field.
:::

::: method
int FireControlSystemCount1() void FireControlSystemCount1(int i)

Get or set the value of the FireControlSystemCount1 field.
:::

::: method
int MissileType2() void MissileType2(int i)

Get or set the value of the MissileType2 field.
:::

::: method
int MissileCount2() void MissileCount2(int i)

Get or set the value of the MissileCount2 field.
:::

::: method
int FireControlSystemCount2() void FireControlSystemCount2(int i)

Get or set the value of the FireControlSystemCount2 field.
:::

::: method
int MissileType3() void MissileType3(int i)

Get or set the value of the MissileType3 field.
:::

::: method
int MissileCount3() void MissileCount3(int i)

Get or set the value of the MissileCount3 field.
:::

::: method
int FireControlSystemCount3() void FireControlSystemCount3(int i)

Get or set the value of the FireControlSystemCount3 field.
:::

::: method
bool IsOffloadableFuel() void IsOffloadableFuel(bool b)

Get or set the value of the IsOffloadableFuel field.
:::

::: method
int FuelAmmount() void FuelAmmount(int i)

Get or set the value of the FuelAmmount field.
:::
:::



::: WsfTadilJ13_4I
::: method
WsfTadilJ13_4C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ13_4C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
string TrackNumberReference() void TrackNumberReference(string s)

Get or set the value of the TrackNumberReference field.
:::

::: method
int SubsurfaceSpecificType() void SubsurfaceSpecificType(int i)

Get or set the value of the SubsurfaceSpecificType field.
:::
:::



::: WsfTadilJ13_4C1
::: method
int MissileType1() void MissileType1(int i)

Get or set the value of the MissileType1 field.
:::

::: method
int MissileCount1() void MissileCount1(int i)

Get or set the value of the MissileCount1 field.
:::

::: method
int MissileType2() void MissileType2(int i)

Get or set the value of the MissileType2 field.
:::

::: method
int MissileCount2() void MissileCount2(int i)

Get or set the value of the MissileCount2 field.
:::

::: method
int MissileType3() void MissileType3(int i)

Get or set the value of the MissileType3 field.
:::

::: method
int MissileCount3() void MissileCount3(int i)

Get or set the value of the MissileCount3 field.
:::

::: method
int TorpedoType1() void TorpedoType1(int i)

Get or set the value of the TorpedoType1 field.
:::

::: method
int TorpedoCount1() void TorpedoCount1(int i)

Get or set the value of the TorpedoCount1 field.
:::

::: method
int TorpedoType2() void TorpedoType2(int i)

Get or set the value of the TorpedoType2 field.
:::

::: method
int TorpedoCount2() void TorpedoCount2(int i)

Get or set the value of the TorpedoCount2 field.
:::

::: method
int TorpedoTubeStatus() void TorpedoTubeStatus(int i)

Get or set the value of the TorpedoTubeStatus field.
:::

::: method
int VerticalLauncherStatus() void VerticalLauncherStatus(int i)

Get or set the value of the VerticalLauncherStatus field.
:::

::: method
int FireControlSystemStatus() void FireControlSystemStatus(int i)

Get or set the value of the FireControlSystemStatus field.
:::
:::



::: WsfTadilJ13_5I
::: method
WsfTadilJ13_5C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ13_5C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
int OperationalCapability() void OperationalCapability(int i)

Get or set the value of the OperationalCapability field.
:::

::: method
string TrackNumberReference() void TrackNumberReference(string s)

Get or set the value of the TrackNumberReference field.
:::

::: method
int SiteType() void SiteType(int i)

Get or set the value of the SiteType field.
:::

::: method
int HotMissileCount() void HotMissileCount(int i)

Get or set the value of the HotMissileCount field.
:::

::: method
int SamModeState() void SamModeState(int i)

Get or set the value of the SamModeState field.
:::

::: method
int TimeFunction() void TimeFunction(int i)

Get or set the value of the TimeFunction field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
bool PerimeterEngagementStatus() void PerimeterEngagementStatus(bool b)

Get or set the value of the PerimeterEngagementStatus field.
:::
:::



::: WsfTadilJ13_5C1
::: method
int ColdInventory() void ColdInventory(int i)

Get or set the value of the ColdInventory field.
:::

::: method
int OperationalImpairment() void OperationalImpairment(int i)

Get or set the value of the OperationalImpairment field.
:::

::: method
int NATOLink1Status() void NATOLink1Status(int i)

Get or set the value of the NATOLink1Status field.
:::

::: method
int Link14Status() void Link14Status(int i)

Get or set the value of the Link14Status field.
:::

::: method
int Link11Status() void Link11Status(int i)

Get or set the value of the Link11Status field.
:::

::: method
int Link11BStatus() void Link11BStatus(int i)

Get or set the value of the Link11BStatus field.
:::

::: method
int Link16Status() void Link16Status(int i)

Get or set the value of the Link16Status field.
:::

::: method
int ATDL_1Status() void ATDL_1Status(int i)

Get or set the value of the ATDL_1Status field.
:::

::: method
int IJMSStatus() void IJMSStatus(int i)

Get or set the value of the IJMSStatus field.
:::

::: method
int CommunicationsImpairment() void CommunicationsImpairment(int i)

Get or set the value of the CommunicationsImpairment field.
:::

::: method
int ControlPositions() void ControlPositions(int i)

Get or set the value of the ControlPositions field.
:::

::: method
int TimeFunction() void TimeFunction(int i)

Get or set the value of the TimeFunction field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
int PrimarySurvRadarStatus() void PrimarySurvRadarStatus(int i)

Get or set the value of the PrimarySurvRadarStatus field.
:::

::: method
int SecondarySurvRadarStatus() void SecondarySurvRadarStatus(int i)

Get or set the value of the SecondarySurvRadarStatus field.
:::

::: method
int TertiarySurvRadarStatus() void TertiarySurvRadarStatus(int i)

Get or set the value of the TertiarySurvRadarStatus field.
:::

::: method
int AcquistionRadarStatus() void AcquistionRadarStatus(int i)

Get or set the value of the AcquistionRadarStatus field.
:::

::: method
bool IlluminatingRadarStatus() void IlluminatingRadarStatus(bool b)

Get or set the value of the IlluminatingRadarStatus field.
:::

::: method
bool Mode4InterrogatorStatus() void Mode4InterrogatorStatus(bool b)

Get or set the value of the Mode4InterrogatorStatus field.
:::

::: method
int IFFInterrogatorStatus() void IFFInterrogatorStatus(int i)

Get or set the value of the IFFInterrogatorStatus field.
:::
:::



::: WsfTadilJ14_0I
::: method
WsfTadilJ14_0C2 AddContinuation2()

Adds continuation word 2 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation2() may be used to retrieve the word.
:::

::: method
WsfTadilJ14_0C2 FindContinuation2()

Returns extension word 2 if it has been added to the message.
:::

::: method
WsfTadilJ14_0C4 AddContinuation4()

Adds continuation word 4 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation4() may be used to retrieve the word.
:::

::: method
WsfTadilJ14_0C4 FindContinuation4()

Returns extension word 4 if it has been added to the message.
:::

::: method
int ThreatEvaluation() void ThreatEvaluation(int i)

Get or set the value of the ThreatEvaluation field.
:::

::: method
bool ResponseIndicator() void ResponseIndicator(bool b)

Get or set the value of the ResponseIndicator field.
:::

::: method
bool SpecialProcessingIndicator() void SpecialProcessingIndicator(bool b)

Get or set the value of the SpecialProcessingIndicator field.
:::

::: method
bool AgileEmitter() void AgileEmitter(bool b)

Get or set the value of the AgileEmitter field.
:::

::: method
bool TrackNumberReference() void TrackNumberReference(bool b)

Get or set the value of the TrackNumberReference field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
bool BearingOrigin() void BearingOrigin(bool b)

Get or set the value of the BearingOrigin field.
:::

::: method
int SquareCircleSwitch() void SquareCircleSwitch(int i)

Get or set the value of the SquareCircleSwitch field.
:::

::: method
int Second() void Second(int i)

Get or set the value of the Second field.
:::

::: method
int Minute() void Minute(int i)

Get or set the value of the Minute field.
:::

::: method
int Hour() void Hour(int i)

Get or set the value of the Hour field.
:::

::: method
int RequestNumber() void RequestNumber(int i)

Get or set the value of the RequestNumber field.
:::

::: method
int Environment() void Environment(int i)

Get or set the value of the Environment field.
:::

::: method
int LockOn() void LockOn(int i)

Get or set the value of the LockOn field.
:::
:::



::: WsfTadilJ14_0C2
::: method
double TargetLatitude() void TargetLatitude(double d)

Get or set the value of the TargetLatitude field. Units are Deg.
:::

::: method
double TargetLongitude() void TargetLongitude(double d)

Get or set the value of the TargetLongitude field. Units are Deg.
:::

::: method
int AreaMajorAxis() void AreaMajorAxis(int i)

Get or set the value of the AreaMajorAxis field.
:::

::: method
int AreaMinorAxis() void AreaMinorAxis(int i)

Get or set the value of the AreaMinorAxis field.
:::

::: method
int AxisOrientation() void AxisOrientation(int i)

Get or set the value of the AxisOrientation field.
:::
:::



::: WsfTadilJ14_0C4
::: method
int PRF_PRI_Indicator() void PRF_PRI_Indicator(int i)

Get or set the value of the PRF_PRI_Indicator field.
:::

::: method
double PulseRepetitionFrequency() void PulseRepetitionFrequency(double d)

Get or set the value of the PulseRepetitionFrequency field. Units are Hz.
:::

::: method
double PulseWidthMessageSizeCheck() void PulseWidthMessageSizeCheck(double d)

Get or set the value of the PulseWidthMessageSizeCheck field.
:::

::: method
int Jitter() void Jitter(int i)

Get or set the value of the Jitter field.
:::

::: method
int ScanType() void ScanType(int i)

Get or set the value of the ScanType field.
:::

::: method
int AntennaScanRatePeriod() void AntennaScanRatePeriod(int i)

Get or set the value of the AntennaScanRatePeriod field.
:::

::: method
double AntennaScanRate() void AntennaScanRate(double d)

Get or set the value of the AntennaScanRate field.
:::

::: method
int Polarization() void Polarization(int i)

Get or set the value of the Polarization field.
:::

::: method
int JammerReceivedSignalLevel() void JammerReceivedSignalLevel(int i)

Get or set the value of the JammerReceivedSignalLevel field.
:::
:::



::: WsfTadilJ15_0I
::: method
WsfTadilJ15_0E0 AddExtension0()

Adds extension word 0 to the message. This can only be called once for each extension word. Returns the extension word. Once the extension word belongs to the message, FindExtension0 may be used to retrieve the word.
:::

::: method
WsfTadilJ15_0E0 FindExtension0()

Returns extension word 0 if it has been added to the message.
:::

::: method
WsfTadilJ15_0C1 AddContinuation1()

Adds continuation word 1 to the message. Returns the continuation word. Once the continuation word belongs to the message, FindContinuation1() may be used to retrieve the word.
:::

::: method
WsfTadilJ15_0C1 FindContinuation1()

Returns extension word 1 if it has been added to the message.
:::

::: method
int Addressee() void Addressee(int i)

Get or set the value of the Addressee field.
:::

::: method
int ThreatPosture() void ThreatPosture(int i)

Get or set the value of the ThreatPosture field.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::

::: method
int TrackStrength() void TrackStrength(int i)

Get or set the value of the TrackStrength field.
:::

::: method
bool IsExerciseTrack() void IsExerciseTrack(bool b)

Get or set the value of the IsExerciseTrack field.
:::

::: method
int ThreatType() void ThreatType(int i)

Get or set the value of the ThreatType field.
:::

::: method
int WeaponType() void WeaponType(int i)

Get or set the value of the WeaponType field.
:::
:::



::: WsfTadilJ15_0E0
::: method
double Latitude() void Latitude(double d)

Get or set the value of the Latitude field. Units are Deg.
:::

::: method
double Longitude() void Longitude(double d)

Get or set the value of the Longitude field. Units are Deg.
:::

::: method
string TrackNumber() void TrackNumber(string s)

Get or set the value of the TrackNumber field.
:::
:::



::: WsfTadilJ15_0C1
::: method
double Course() void Course(double d)

Get or set the value of the Course field. Units are Radians.
:::

::: method
double Speed() void Speed(double d)

Get or set the value of the Speed field. Units are Meters Per Second.
:::

::: method
double Altitude() void Altitude(double d)

Get or set the value of the Altitude field. Units are Meters.
:::

::: method
int ThreatFuel() void ThreatFuel(int i)

Get or set the value of the ThreatFuel field.
:::

::: method
int SpecificType() void SpecificType(int i)

Get or set the value of the SpecificType field.
:::

::: method
int Environment() void Environment(int i)

Get or set the value of the Environment field.
:::
:::

