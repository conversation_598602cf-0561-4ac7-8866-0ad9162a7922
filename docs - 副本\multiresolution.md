orphan

:   

::: demo
multiresolution_demos
:::

Included are several examples demonstrating multiresolution platform components. The [fidelity]{.title-ref} parameters may be adjusted to demonstrate how the choice of model and scenario results change with fidelity. Included in most is a comparison platform to demonstrate that the behavior is the same when using the same models.

Examples demonstrating multiresolution movers, sensors, comms, processors, fuel, optical signatures, and radar signatures are included. Each scenario is fairly simple to show how a multiresolution component can be set up. The scenarios are intended to illustration multiresolution flexibility, and are not necessarily exquisite examples of how to use each of the constituent models.

# air_mover_demo.txt

This example demonstrates a simple use of `multiresolution_mover`{.interpreted-text role="command"}: Two identical mover models (`WSF_AIR_MOVER`{.interpreted-text role="model"}), but with different update intervals. For comparison, a platform that uses `WSF_AIR_MOVER`{.interpreted-text role="model"} is included. The fidelity parameter can be adjusted to choose the model that will be used on the platform. A fidelity value \[0,0.5) will choose an `WSF_AIR_MOVER`{.interpreted-text role="model"} with an update interval of 30 seconds and a value in \[0.5, 1.0\] will choose an AIR_MOVER with an update interval of 0.1 s.

# comm_demo.txt

This example demonstrates a simple use of `multiresolution_comm`{.interpreted-text role="command"}. Two different comm models can be used (`WSF_COMM_TRANSCEIVER`{.interpreted-text role="model"} or `WSF_RADIO_TRANSCEIVER`{.interpreted-text role="model"}). For comparison, platforms with standard comm models are included as well. In this example the two platforms directly send messages via scripting.

# comm_with_command_chain_demo.txt

This example demonstrates a slightly more involved use of `multiresolution_comm`{.interpreted-text role="command"}. As in comm_demo.txt, two different comm models can be used (`WSF_COMM_TRANSCEIVER`{.interpreted-text role="model"} or `WSF_RADIO_TRANSCEIVER`{.interpreted-text role="model"}). However, in this demo, a command chain with a network is also used. For comparison, platforms with standard comm models are included as well. In this example a commander platform sends messages to subordinates.

# fuel_demo.txt

This example demonstrates a simple use of `multiresolution_fuel`{.interpreted-text role="command"}: Two models are included in the multiresolution model, one being a constant rate `WSF_FUEL`{.interpreted-text role="model"} and one being a `WSF_TABULAR_RATE_FUEL`{.interpreted-text role="model"}. For comparison, a platform that uses the same `WSF_TABULAR_RATE_FUEL`{.interpreted-text role="model"} is also included. The fidelity parameter can be adjusted to choose the model that will be used on the platform. A fidelity value in \[0,0.5) will choose the constant rate `WSF_FUEL`{.interpreted-text role="model"} model and a value in \[0.5, 1.0\] will choose the `WSF_TABULAR_RATE_FUEL`{.interpreted-text role="model"} model.

# optical_signature_demo.txt

This demo adapts the demo in `signature_demos`{.interpreted-text role="demo"} to make the fighter platform\'s optical signature multiresolution by combining the three optical signatures defined in signature_demos/signatures with `multiresolution_optical_signature`{.interpreted-text role="command"}. It uses the same sensors, but alters the fighter\'s route. Varying the fidelity parameter in the platform definition will choose different signatures, with \[0-0.33) choosing the spherical signature, \[0.33-0.66) choosing the box signature, and \[0.66-1.0\] the multi-shape signature.

# processor_demo.txt

This example demonstrates a simple use of `multiresolution_processor`{.interpreted-text role="command"}. Two different implementations of a `WSF_TASK_PROCESSOR`{.interpreted-text role="model"} are included as options in a `multiresolution_processor`{.interpreted-text role="command"} model. One model simply sets the *achilles* platform to go to a given location at a given speed. The other sets the *achilles* platform to advance to the location at which it detected the tortoise platform indefinitely.

# processor_with_link_demo.txt

This example demonstrates a simple use of `multiresolution_processor`{.interpreted-text role="command"} in which internal links are used. Two `WSF_SCRIPT_PROCESSOR`{.interpreted-text role="model"} models are included which have different implementations. Two platforms send messages to each other over a comm, and messages are routed to the script processor.

# radar_signature_demo.txt

Similar to optical_signature_demo, this demonstrates the usage of `multiresolution_radar_signature`{.interpreted-text role="command"}. An air platform with a `multiresolution_radar_signature`{.interpreted-text role="command"} is used with two signature options, a constant signature and a tabular signature. Two ground-based radars track the air platform. The fidelity parameter may be adjusted to choose the signature.

# sensor_demo.txt

This example demonstrates a simple use of `multiresolution_sensor`{.interpreted-text role="command"}. Two different sensor models are used (`WSF_IRST_SENSOR`{.interpreted-text role="model"} and `WSF_GEOMETRIC_SENSOR`{.interpreted-text role="model"}). For comparison, a platform with a standard `WSF_GEOMETRIC_SENSOR`{.interpreted-text role="model"} is included as well. An air-based platform flies past the ground-based sensor sites and events are recorded for detection attempts, and tracks initiated, updated, and dropped.

# space_mover_demo.txt

This example demonstrates a simple use of `multiresolution_mover`{.interpreted-text role="command"}. Two different mover models are used (`WSF_SPACE_MOVER`{.interpreted-text role="model"} and `WSF_INTEGRATING_SPACE_MOVER`{.interpreted-text role="model"}). For comparison, a platform that uses `WSF_SPACE_MOVER`{.interpreted-text role="model"} is included. The [fidelity]{.title-ref} parameter can be adjusted to choose the model that will be used on the platform.
