# swarm.rst.txt.md
orphan

:   

::: demo
swarm
:::

| Two types of Air To Ground Missiles (AGM\'s) are launched from an aircraft to a ground target.
| The first AGM that is launched is the leader. The following 200 AGM\'s are \"clients\".
| The client AGM\'s loosely follow the leader to the target.
| 
| The two types of weapons definitions are located in the directory \"weapons/agm\".
| 
| It is recommended to view the replay with trace lines on and set the trace lines to a length
| of 10 seconds to see the affect of the \"swarm\" as they fly to the bullseye.