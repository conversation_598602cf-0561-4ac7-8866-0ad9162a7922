# wsfem_interaction.rst.txt.md
orphan

:   



::: {.WsfEM_Interaction .inherits .Object}
::: {.contents local=""}
:::
:::

All base WsfEM_Interaction methods are available on both the `WsfCommInteraction`{.interpreted-text role="class"} and the `WsfSensorInteraction`{.interpreted-text role="class"} script objects.

NOTE: A value of -999 indicates a non-valid value. Most methods return this default value if not used in the EM interaction.



::: method
bool Succeeded()

Returns \'true\' if the EM interaction \'succeeded.\' Success indicates that limits have been checked and no failures have occurred.
:::

::: method
bool Failed()

Returns \'true\' if either the limits have NOT been checked or a failure has occurred.
:::

::: method
string FailedStatus()

Returns a string containing the reason(s) for the failure. This will be an empty string if no failure occurred.
:::

::: method
WsfPlatform XmtrPlatform()

Returns the transmitter WsfPlatform object used in the EM interaction.

::: note
::: title
Note
:::

Check the returned platform for validity before using.
:::
:::

::: method
string XmtrPlatformName()

Returns the name of the transmitter platform.
:::

::: method
WsfGeoPoint XmtrLocation()

Returns the location of the transmitter if valid, otherwise the location of the platform is given.
:::

::: method
WsfEM_Xmtr Xmtr()

Returns the transmitter WsfEM_Xmtr object used in the EM interaction.

::: note
::: title
Note
:::

Check the returned transmitter for validity before using.
:::
:::

::: method
WsfEM_Rcvr Rcvr()

Returns the receiver WsfEM_Rcvr object used in the EM interaction.

::: note
::: title
Note
:::

Check the returned receiver for validity before using.
:::
:::

::: method
WsfPlatform RcvrPlatform()

Returns the receiver WsfPlatform object used in the EM interaction.

::: note
::: title
Note
:::

Check the returned platform for validity before using.
:::
:::

::: method
string RcvrPlatformName()

Returns the name of the receiver platform.
:::

::: method
WsfGeoPoint RcvrLocation()

Returns the location of the receiver if valid, otherwise the location of the platform is given.
:::

::: method
WsfPlatform TargetPlatform()

Returns the target WsfPlatform object used in the EM interaction.

::: note
::: title
Note
:::

Check the returned platform for validity before using.
:::
:::

::: method
string TargetPlatformName()

Returns the name of the target platform, if applicable.
:::

::: method
WsfGeoPoint TargetLocation()

Returns the location of the target if valid.
:::



::: method
double RcvrToTargetRange()

Returns the range from the receiver to the target. (meters)
:::

::: method
double RcvrToTargetTrueAzimuth()

Returns the true azimuth from the receiver to the target. (degrees)
:::

::: method
double RcvrToTargetTrueElevation()

Returns the true elevation from the receiver to the target. (degrees)
:::

::: method
double RcvrToTargetApparentAzimuth()

Returns the apparent azimuth from the receiver to the target. (degrees)
:::

::: method
double RcvrToTargetApparentElevation()

Returns the apparent elevation from the receiver to the target. (degrees)
:::

::: method
double TargetToRcvrRange()

Returns the range from the target to the receiver. (meters)
:::

::: method
double TargetToRcvrTrueAzimuth()

Returns the true azimuth from the target to the receiver. (degrees)
:::

::: method
double TargetToRcvrTrueElevation()

Returns the true elevation from the target to the receiver. (degrees)
:::

::: method
double TargetToRcvrApparentAzimuth()

Returns the apparent azimuth from the target to the receiver. (degrees)
:::

::: method
double TargetToRcvrApparentElevation()

Returns the apparent elevation from the target to the receiver. (degrees)
:::

::: method
double XmtrToTargetRange()

Returns the range from the transmitter to the target. (meters)
:::

::: method
double XmtrToTargetTrueAzimuth()

Returns the true azimuth from the transmitter to the target. (degrees)
:::

::: method
double XmtrToTargetTrueElevation()

Returns the true elevation from the transmitter to the target. (degrees)
:::

::: method
double XmtrToTargetApparentAzimuth()

Returns the apparent azimuth from the transmitter to the target. (degrees)
:::

::: method
double XmtrToTargetApparentElevation()

Returns the apparent elevation from the transmitter to the target. (degrees)
:::

::: method
double TargetToXmtrRange()

Returns the range from the target to the transmitter. (meters)
:::

::: method
double TargetToXmtrTrueAzimuth()

Returns the true azimuth from the target to the transmitter. (degrees)
:::

::: method
double TargetToXmtrTrueElevation()

Returns the true elevation from the target to the transmitter. (degrees)
:::

::: method
double TargetToXmtrApparentAzimuth()

Returns the apparent azimuth from the target to the transmitter. (degrees)
:::

::: method
double TargetToXmtrApparentElevation()

Returns the apparent elevation from the target to the transmitter. (degrees)
:::

::: method
double XmtrToRcvrRange()

Returns the range from the transmitter to the receiver. (meters)
:::

::: method
double XmtrToRcvrTrueAzimuth()

Returns the true azimuth from the transmitter to the receiver. (degrees)
:::

::: method
double XmtrToRcvrTrueElevation()

Returns the true elevation from the transmitter to the receiver. (degrees)
:::

::: method
double XmtrToRcvrApparentAzimuth()

Returns the apparent azimuth from the transmitter to the receiver. (degrees)
:::

::: method
double XmtrToRcvrApparentElevation()

Returns the apparent elevation from the transmitter to the receiver. (degrees)
:::

::: method
double RcvrToXmtrRange()

Returns the range from the receiver to the transmitter. (meters)
:::

::: method
double RcvrToXmtrTrueAzimuth()

Returns the true azimuth from the receiver to the transmitter. (degrees)
:::

::: method
double RcvrToXmtrTrueElevation()

Returns the true elevation from the receiver to the transmitter. (degrees)
:::

::: method
double RcvrToXmtrApparentAzimuth()

Returns the apparent azimuth from the receiver to the transmitter. (degrees)
:::

::: method
double RcvrToXmtrApparentElevation()

Returns the apparent elevation from the receiver to the transmitter. (degrees)
:::



::: method
double XmtrBeamBearing()

Returns the bearing of the transmitter beam. (degrees)
:::

::: method
double XmtrBeamElevation()

Returns the elevation of the transmitter beam. (degrees)
:::

::: method
double XmtrBeamTargetRelativeAzimuth()

Returns of the azimuth of the target with respect to the transmitter beam. (degrees)
:::

::: method
double XmtrBeamTargetRelativeElevation()

Returns of the elevation of the target with respect to the transmitter beam. (degrees)
:::

::: method
double XmtrBeamGain()

Returns the gain at the specified az/el. (dB)
:::

::: method
double XmtrBeamEBS_Azimuth()

Returns the azimuth of the beam with respect to the antenna face. (degrees)
:::

::: method
double XmtrBeamEBS_Elevation()

Returns the elevation of the beam with respect to the antenna face. (degrees)
:::

::: method
double XmtrBeamEBS_Omega()
:::

::: method
double RcvrBeamBearing()

Returns the bearing of the receiver beam. (degrees)
:::

::: method
double RcvrBeamElevation()

Returns the elevation of the receiver beam. (degrees)
:::

::: method
double RcvrBeamTargetRelativeAzimuth()

Returns of the azimuth of the target with respect to the receiver beam. (degrees)
:::

::: method
double RcvrBeamTargetRelativeElevation()

Returns of the elevation of the target with respect to the receiver beam. (degrees)
:::

::: method
double RcvrBeamGain()

Returns the gain at the specified az/el. (dB)
:::

::: method
double RcvrBeamEBS_Azimuth()

Returns the azimuth of the beam with respect to the antenna face. (degrees)
:::

::: method
double RcvrBeamEBS_Elevation()

Returns the elevation of the beam with respect to the antenna face. (degrees)
:::

::: method
double RcvrBeamEBS_Omega()
:::



::: method
double InfraredSignature()

Returns the infrared signature. (m\^2)
:::

::: method
double InfraredSignatureAzimuth()

Returns the azimuth used for infrared signature determination. (degrees)
:::

::: method
double InfraredSignatureElevation()

Returns the elevation used for infrared signature determination. (degrees)
:::

::: method
double RadarSignature()

Returns the radar signature. (m\^2)
:::

::: method
double RadarSignatureAzimuth()

Returns the azimuth used for radar signature determination. (degrees)
:::

::: method
double RadarSignatureElevation()

Returns the elevation used for radar signature determination. (degrees)
:::

::: method
double OpticalSignature()

Returns the optical signature. (m\^2)
:::

::: method
double OpticalSignatureAzimuth()

Returns the azimuth used for optical signature determination. (degrees)
:::

::: method
double OpticalSignatureElevation()

Returns the elevation used for optical signature determination. (degrees)
:::



::: method
double XmtdPower()

Returns the transmitted power at the antenna face. (dBw)
:::

::: method
double RcvdPower()

Returns the received power at the antenna face. (dBw)
:::

::: method
double RcvrNoise()

Returns the receiver noise power. (dBw)
:::

::: method
double ClutterPower()

Returns the clutter power. (dBw)
:::



::: method
double SignalToInterference()

Returns the signal-to-interference value. (dB)
:::

::: method
double SignalToNoise()

Returns the signal-to-noise value. (dB)
:::

::: method
double SignalToNoiseClutter()

Returns the signal-to-noise + clutter value. (dB)
:::



::: method
double AbsorptionFactor()

Returns the atmospheric absorption factor (typically the atmospheric attenuation or transmittance). (dB)
:::

::: method
double MaskingFactor()

Returns the masking factor (in the range \[0..1\] that accounts for blockage or attenuation by structure. A value of 1 indicates there is no blockage or attenuation by structure while a value of zero indicates blockage or attenuation.

::: note
::: title
Note
:::

This includes the effects of both the transmitter and receiver sides.
:::
:::

::: method
double PropagationFactor()

Returns the pattern propagation factor (typically called \'F40\') (dB)
:::

::: method
double BackgroundRadiantIntensity()

Returns the infrared background radiant intensity. (w/sr)
:::

::: method
double ContrastRadiantIntensity()

Returns the contrast radiant intensity. (w/sr)
:::

::: method
double DetectionThreshold()

Returns the nominal detection threshold.
:::

::: method
double PixelCount()

Returns the approximate number of pixels the target occupies in the image. (For imaging sensors only)
:::