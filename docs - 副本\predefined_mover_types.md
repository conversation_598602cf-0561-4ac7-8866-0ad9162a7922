orphan

:   

# Predefined Mover Types {#Predefined_Mover_Types}

::: {.contents local=""}
:::

## Route Types

These movers are simplified movers in that routes with waypoints can be defined in order for them to move from position to position during the simulation. Limits defined on a platform restrict their movement. Platform movement is based on mathematics and not necessarily by aero or mass properties of the platform.

-   `WSF_AIR_MOVER`{.interpreted-text role="model"}
-   `WSF_GROUND_MOVER`{.interpreted-text role="model"}
-   `WSF_KINEMATIC_MOVER`{.interpreted-text role="model"}
-   `WSF_ROAD_MOVER`{.interpreted-text role="model"}
-   `WSF_ROTORCRAFT_MOVER`{.interpreted-text role="model"}
-   `WSF_SURFACE_MOVER`{.interpreted-text role="model"}
-   `WSF_TSPI_MOVER`{.interpreted-text role="model"}

## Follower Types

These movers become \'attached\' to the Route Type Movers and are used to have a platform instance \'follow\' other platforms.

-   `WSF_HYBRID_MOVER`{.interpreted-text role="model"}
-   `WSF_OFFSET_MOVER`{.interpreted-text role="model"}
