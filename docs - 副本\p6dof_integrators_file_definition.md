orphan

:   

# P6DOF Integrators File Definition {#P6DOF_Integrators_File_Definition}

The P6DOF integrators file is very simple. A single input line is used for each desired integrator.

The file format is as follows:

    p6dof_integrators

      create_integrator  STANDARD_P6DOF_INTEGRATOR

    end_p6dof_integrators

This should typically include the default integrator (STANDARD_P6DOF_INTEGRATOR \-- must be all caps) unless all objects in the scenario use other integrators.

Return to `p6dof_object_types`{.interpreted-text role="doc"} or `P6DOF_Integrators_Support`{.interpreted-text role="ref"}
