# protocol.rst.txt.md
orphan

:   



See `Predefined_Protocol_Types`{.interpreted-text role="ref"}

Global Context Commands

> ::: {.command block=""}
> protocol \<name-or-type\> \<base-type-name\> \... end_protocol
>
> ::: parsed-literal
> protocol \<name-or-type\> \<base-type-name\> \... end_protocol
> :::
> :::

<PERSON><PERSON> and <PERSON>mm Type Scope Commands

:   ::: command
    add protocol \<name-or-type\> \<base-type-name\> \... end_protocol
    :::

    ::: command
    edit protocol \<name\> \... end_protocol
    :::

    ::: {.command block=""}
    delete protocol \<name\> end_protocol

    ::: parsed-literal
    add protocol \<name-or-type\> \<base-type-name\> \... end_protocol

    edit protocol \<name\> \... end_protocol

    delete protocol \<name\> end_protocol
    :::
    :::



A (comm) protocol is an abstract representation of a particular comm capability. It can provide or restrict functionality from the norm, and may alter many of the layer processes on the comm with which it is associated. Any activity that is defined within the logic of the comm model OSI 7-layer implementations may be modified to the extent of the protocol API. As such, every layer in all existing comm models in AFSIM query all attached protocols in the receive/send protocol stack during normal comm operation, giving each protocol an opportunity to modify the execution of message processing.

At a more general level, this allows capabilities to be defined external to the comm object, and added or removed as seen fit by the user. Any number of protocol types may be defined, but only one of each type can be used. Assuming that each protocol is compatible (dependent on the implementation), they can all function to the degree that they wish to cooperate (i.e. multiple transport layer protocols can\'t all control how a message is split into packets; one of them has to be primary).

Again, note that each comm should only have one of each type. In the current version of AFSIM, no protocols are used by default.