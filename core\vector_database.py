"""
AFSIM Platform Agent - 向量数据库管理模块
基于Chroma实现的向量数据库，支持多MD文件的索引和检索功能
"""

import os
import logging
import warnings
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import chromadb
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
from sentence_transformers import SentenceTransformer
import yaml

# 忽略警告
warnings.filterwarnings("ignore", message=".*does not have a valid file.*")
warnings.filterwarnings("ignore", message=".*was never saved.*")

logger = logging.getLogger(__name__)


class VectorDatabase:
    """向量数据库管理类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化向量数据库
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.client = None
        self.collection = None
        self.embedding_function = None
        
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = Path(__file__).parent.parent / "config" / "config.yaml"
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'vector_db': {
                'path': 'data/chroma_db',
                'collection_name': 'afsim_platform_docs',
                'embedding_model_path': 'models/paraphrase-multilingual-mpnet-base-v2',
                'chunk_size': 500,
                'similarity_threshold': 0.7
            }
        }
    
    def initialize(self) -> bool:
        """
        初始化向量数据库
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化向量数据库...")
            
            # 创建数据库目录
            db_path = self.config['vector_db']['path']
            os.makedirs(db_path, exist_ok=True)
            logger.info(f"数据库路径: {db_path}")
            
            # 创建持久化客户端
            self.client = chromadb.PersistentClient(path=db_path)
            logger.info("Chroma客户端创建成功")
            
            # 初始化嵌入模型
            self._initialize_embedding_model()
            
            # 创建或获取集合
            collection_name = self.config['vector_db']['collection_name']
            self.collection = self.client.get_or_create_collection(
                name=collection_name,
                embedding_function=self.embedding_function,
                metadata={"hnsw:space": "cosine"}
            )
            logger.info(f"集合 '{collection_name}' 初始化完成")
            
            return True
            
        except Exception as e:
            logger.error(f"向量数据库初始化失败: {e}")
            return False
    
    def _initialize_embedding_model(self):
        """初始化嵌入模型"""
        model_path = self.config['vector_db']['embedding_model_path']
        
        # 检查模型是否存在
        if not os.path.exists(model_path):
            logger.error(f"嵌入模型路径不存在: {model_path}")
            logger.info("请先下载模型或检查路径配置")
            raise FileNotFoundError(f"模型路径不存在: {model_path}")
        
        # 设置离线模式
        os.environ["TRANSFORMERS_OFFLINE"] = "1"
        
        # 验证模型完整性
        try:
            test_model = SentenceTransformer(model_path, device="cpu")
            logger.info("嵌入模型验证成功")
            del test_model
        except Exception as e:
            logger.error(f"嵌入模型验证失败: {e}")
            raise
        
        # 创建嵌入函数
        self.embedding_function = SentenceTransformerEmbeddingFunction(
            model_name=model_path,
            device="cpu"
        )
        logger.info("嵌入函数创建成功")
    
    def add_documents(self, documents: List[str], metadatas: List[Dict[str, Any]],
                     ids: List[str] = None) -> bool:
        """
        添加文档到向量数据库

        Args:
            documents: 文档内容列表
            metadatas: 文档元数据列表
            ids: 文档ID列表，如果为None则自动生成

        Returns:
            bool: 添加是否成功
        """
        try:
            if not self.collection:
                logger.error("向量数据库未初始化")
                return False

            if ids is None:
                ids = self._generate_unique_ids(documents, metadatas)

            # 检查并处理重复ID
            ids = self._ensure_unique_ids(ids)

            # 分批添加文档以避免超过最大批量大小限制
            return self._add_documents_in_batches(documents, metadatas, ids)

        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return False

    def _generate_unique_ids(self, documents: List[str], metadatas: List[Dict[str, Any]]) -> List[str]:
        """
        生成唯一的文档ID

        Args:
            documents: 文档内容列表
            metadatas: 文档元数据列表

        Returns:
            List[str]: 唯一ID列表
        """
        import hashlib
        import time

        ids = []
        timestamp = int(time.time() * 1000)  # 毫秒级时间戳

        for i, (doc, metadata) in enumerate(zip(documents, metadatas)):
            # 使用文档内容、元数据和时间戳生成唯一ID
            content_hash = hashlib.md5(doc.encode('utf-8')).hexdigest()[:8]

            # 从元数据中获取文件信息
            file_path = metadata.get('file_path', 'unknown')
            chunk_index = metadata.get('chunk_index', i)

            # 生成唯一ID
            unique_id = f"{Path(file_path).stem}_{chunk_index}_{content_hash}_{timestamp}_{i}"
            ids.append(unique_id)

        return ids

    def _ensure_unique_ids(self, ids: List[str]) -> List[str]:
        """
        确保ID列表中没有重复

        Args:
            ids: 原始ID列表

        Returns:
            List[str]: 去重后的ID列表
        """
        unique_ids = []
        seen_ids = set()

        for id_str in ids:
            if id_str in seen_ids:
                # 如果ID重复，添加后缀
                counter = 1
                new_id = f"{id_str}_dup_{counter}"
                while new_id in seen_ids:
                    counter += 1
                    new_id = f"{id_str}_dup_{counter}"
                id_str = new_id

            seen_ids.add(id_str)
            unique_ids.append(id_str)

        return unique_ids

    def _add_documents_in_batches(self, documents: List[str], metadatas: List[Dict[str, Any]],
                                 ids: List[str]) -> bool:
        """
        分批添加文档到向量数据库

        Args:
            documents: 文档内容列表
            metadatas: 文档元数据列表
            ids: 文档ID列表

        Returns:
            bool: 添加是否成功
        """
        try:
            # ChromaDB的最大批量大小限制，设置为安全值
            max_batch_size = 5000
            total_docs = len(documents)

            if total_docs <= max_batch_size:
                # 如果文档数量不超过限制，直接添加
                self.collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids
                )
                logger.info(f"成功添加 {total_docs} 个文档到向量数据库")
                return True

            # 分批处理
            logger.info(f"文档数量 {total_docs} 超过批量限制 {max_batch_size}，开始分批处理...")

            successful_batches = 0
            total_batches = (total_docs + max_batch_size - 1) // max_batch_size

            for i in range(0, total_docs, max_batch_size):
                end_idx = min(i + max_batch_size, total_docs)
                batch_docs = documents[i:end_idx]
                batch_metas = metadatas[i:end_idx]
                batch_ids = ids[i:end_idx]

                batch_num = (i // max_batch_size) + 1
                logger.info(f"处理批次 {batch_num}/{total_batches}: {len(batch_docs)} 个文档")

                try:
                    self.collection.add(
                        documents=batch_docs,
                        metadatas=batch_metas,
                        ids=batch_ids
                    )
                    successful_batches += 1
                    logger.info(f"批次 {batch_num} 添加成功")

                except Exception as batch_error:
                    logger.error(f"批次 {batch_num} 添加失败: {batch_error}")
                    # 继续处理下一批次，而不是完全失败
                    continue

            if successful_batches == total_batches:
                logger.info(f"所有 {total_batches} 个批次添加成功，总计 {total_docs} 个文档")
                return True
            elif successful_batches > 0:
                logger.warning(f"部分批次添加成功: {successful_batches}/{total_batches}")
                return True  # 部分成功也认为是成功
            else:
                logger.error("所有批次添加失败")
                return False

        except Exception as e:
            logger.error(f"分批添加文档失败: {e}")
            return False

    def clear_collection(self) -> bool:
        """
        清空当前集合中的所有文档

        Returns:
            bool: 清空是否成功
        """
        try:
            if not self.collection:
                logger.error("向量数据库未初始化")
                return False

            # 获取所有文档ID
            result = self.collection.get()
            if result['ids']:
                # 删除所有文档
                self.collection.delete(ids=result['ids'])
                logger.info(f"成功清空集合，删除了 {len(result['ids'])} 个文档")
            else:
                logger.info("集合已经为空")

            return True

        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False


    
    def search(self, query: str, top_k: int = 5, 
               filter_metadata: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        搜索相关文档
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            filter_metadata: 元数据过滤条件
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        try:
            if not self.collection:
                logger.error("向量数据库未初始化")
                return []
            
            # 执行搜索
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k,
                where=filter_metadata
            )
            
            # 格式化结果
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    result = {
                        'content': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i] if results['metadatas'][0] else {},
                        'distance': results['distances'][0][i] if results['distances'][0] else 0,
                        'id': results['ids'][0][i] if results['ids'][0] else f"result_{i}"
                    }
                    formatted_results.append(result)
            
            logger.info(f"搜索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息

        Returns:
            Dict: 集合信息
        """
        try:
            if not self.collection:
                return {"error": "向量数据库未初始化"}

            count = self.collection.count()

            # 获取一些示例ID
            result = self.collection.get(limit=5)
            sample_ids = result['ids'] if result['ids'] else []

            return {
                "total_documents": count,
                "collection_name": self.collection.name,
                "sample_ids": sample_ids,
                "metadata": self.collection.metadata
            }

        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {"error": str(e)}
    
    def delete_documents(self, ids: List[str]) -> bool:
        """
        删除文档
        
        Args:
            ids: 要删除的文档ID列表
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if not self.collection:
                logger.error("向量数据库未初始化")
                return False
            
            self.collection.delete(ids=ids)
            logger.info(f"成功删除 {len(ids)} 个文档")
            return True
            
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    def clear_collection(self) -> bool:
        """
        清空集合
        
        Returns:
            bool: 清空是否成功
        """
        try:
            if not self.collection:
                logger.error("向量数据库未初始化")
                return False
            
            # 获取所有文档ID并删除
            all_docs = self.collection.get()
            if all_docs['ids']:
                self.collection.delete(ids=all_docs['ids'])
            
            logger.info("集合已清空")
            return True
            
        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False
