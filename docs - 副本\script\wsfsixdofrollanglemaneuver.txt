# wsfsixdofrollanglemaneuver.rst.txt.md
orphan

:   





This maneuver sets the target roll angle of the platform to which it is assigned. This maneuver is done executing as soon as the target is set, so if there is a need to wait for the achieved roll angle to reach the target value, a `WsfSixDOF_ManeuverConstraint`{.interpreted-text role="class"} must be used.



::: method
static WsfSixDOF_RollAngleManeuver Construct(double aRollAngleDeg)

Construct a maneuver that will set a target roll angle on the platform to which the maneuver is assigned.
:::

::: method
double GetRollAngle()

Return this maneuver\'s target roll angle in degrees.
:::