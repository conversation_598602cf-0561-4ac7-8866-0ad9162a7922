# intersect_mesh.rst.txt.md
orphan

:   



::: {.command block=""}
intersect_mesh \... end_intersect_mesh
:::

::: parsed-literal

intersect_mesh \<name\>

:   [define_offset]() \... [file]() \...

end_intersect_mesh
:::



The [intersect_mesh](#intersect_mesh) allows one to query a three-dimensional representation, or intersection mesh, to determine geometric information during a simulation. These queries occur completely within the simulation so there is no need to use a separate visual system.

Currently, the intersect mesh is used to determine intersection geometry, occlusion, and material code for high-energy-laser interactions with targets (see `WSF_LASER_WEAPON`{.interpreted-text role="model"} ). This capability could be expanded in the future, however, to incorporate other features such as LADAR image formation. In order to enable these calculations, the processor must be placed within the target\'s platform_type (or platform instance) definition; e.g.:

    // Define the intersection mesh outside the platform type definition.
    intersect_mesh su37
      define_offset CANOPY
          3.461642 m
          0.000184 m
         -0.943480 m
      define_offset IRST
          5.138014 m
          0.000005 m
         -0.629733 m        
      file ./mesh/su37.imesh
    end_intersect_mesh

    // Then referenced the above mesh definition inside it.
    platform_type su-37 WSF_PLATFORM
       ...
       intersect_mesh su37
       ...
    end_platform_type



::: command
define_offset \<string-value\> \<length-value\> \<length-value\> \<length-value\>

Define the offset name and location on the intersection mesh associated with the target area of interest (see example above).
:::

::: command
file \<string-value\>

Define the file for the intersection mesh associated with this definition.
:::