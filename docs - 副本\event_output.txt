# event_output.rst.txt.md
orphan

:   



::: {.contents local=""}
:::

::: {.command block=""}
event_output \... end_event_output
:::

::: parsed-literal

event_output

:   [file]() \[ \| STDOUT \| NULL\] [lat_lon_format]() d\[:m\[:s\]\]\[.#\] [time_format]() \[\[h:\]m:\]s\[.#\] [print_eci_locations]() [print_failed_message_delivery_attempts]() [print_failed_sensor_detection_attempts]() [print_single_line_per_event]() [print_track_in_message]() [print_track_covariance]() [print_track_residual_covariance]() [flush_output]() [disable]() \[ \<event\> \| all \] [enable]() \[ \<event\> \| all \]

end_event_output
:::



+--------------------+-----------------------------------------------------------------------+
| One-party event:   | ::: parsed-literal                                                    |
|                    | \<time\> \<event\> \<party-1\> \<extra-data\>                         |
|                    | :::                                                                   |
+--------------------+-----------------------------------------------------------------------+
| Two-party event:   | ::: parsed-literal                                                    |
|                    | \<time\> \<event\> \<party-1\> \<party-2\> \<extra-data\>             |
|                    | :::                                                                   |
+--------------------+-----------------------------------------------------------------------+
| Three-party event: | ::: parsed-literal                                                    |
|                    | \<time\> \<event\> \<party-1\> \<party-2\> \<party-3\> \<extra-data\> |
|                    | :::                                                                   |
+--------------------+-----------------------------------------------------------------------+



::: {.toctree maxdepth="1" glob=""}
event/\*
:::

::: command
lat_lon_format d\[:m\[:s\]\]\[.#\]

Specifies the format and number of decimal places for displaying and .

Default d:m:s.2
:::

::: command
time_format \[\[h:\]m:\]s\[.#\]

Specifies the format and number of decimal places for displaying \<time\>.

Default s.5
:::

::: command
print_eci_locations \<boolean\>

Specifies whether position, velocity, and acceleration data are output referenced to the ECI coordinate frame. This will override the standard output of positions in LLA, and velocities and accelerations in NED coordinates.

::: note
::: title
Note
:::

This option is primarily used for space-based simulation output analysis.
:::

Default false
:::

::: command
print_failed_message_delivery_attempts \<boolean\>

Specifies if `docs/event/message_events:MESSAGE_DELIVERY_ATTEMPT`{.interpreted-text role="ref"} events should be printed if the attempt was unsuccessful.

Default true
:::

::: command
print_failed_sensor_detection_attempts \<boolean\>

Specifies if `docs/event/sensor_events:SENSOR_DETECTION_ATTEMPT`{.interpreted-text role="ref"} events should be printed if the attempt was unsuccessful. Setting this to false will dramatically reduce the event file size because only successful detection events will be printed.

Default true
:::

::: command
print_single_line_per_event \<boolean\>

Specifies if each event should be printed on a single line.

Default false
:::

::: command
print_track_in_message \<boolean\>

If a track is part of a message (such as in a `WsfTrackMessage`{.interpreted-text role="class"}) then also print out the contents of the track itself.

Default false
:::

::: command
print_track_covariance \<boolean\>

If covariance data is contained in a track that is being logged, the contents of the track\'s covariance matrix are also logged.

Default false
:::

::: command
print_track_residual_covariance \<boolean\>

If residual covariance data is contained in a track that is being logged, the contents of the track\'s residual covariance matrix are also logged.

Default false
:::

::: note
::: title
Note
:::

The track covariance matrix is converted from WCS to originator-referenced NED, and the principal axes are computed in this coordinate system.
:::

    00:00:00.0 PLATFORM_ADDED SimpleStriker Type: BLUE_STRIKER Side: blue \
     LLA: 00:00:00.00n 00:00:00.00e 0 m Heading: 90.000 deg Pitch: 0.000 deg Roll: 0.000 deg \
     Speed: 0.000 m/s * [ 0.000 0.000 0.000 ] Acceleration: 0.000 m/s2 * [ 0.000 0.000 0.000 ]
    00:00:00.0 SIMULATION_STARTING Year: 2003 Month: 6 Day: 1 Hour: 12 Minute: 0 Second: 0
    00:10:01.0 SIMULATION_COMPLETE Year: 2003 Month: 6 Day: 1 Hour: 12 Minute: 10 Second: 1
    00:10:01.0 PLATFORM_DELETED SimpleStriker Type: BLUE_STRIKER Side: blue Ps: 1 \
     LLA: 01:03:02.17n 02:39:42.30e 9144 m Heading: 89.993 deg Pitch: 0.000 deg Roll: 0.000 deg \
     Speed: 298.378 m/s * [ 0.000 1.000 -0.000 ] Acceleration: 0.014 m/s2 * [ 0.000 0.000 1.000 ]