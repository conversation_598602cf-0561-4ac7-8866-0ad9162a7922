orphan

:   

# WsfFieldOfView

::: WsfFieldOfView
`WsfFieldOfView`{.interpreted-text role="class"} provides an interface to dynamically set the `field of view<../field_of_view_commands>`{.interpreted-text role="doc"} on `sensors<WsfSensor>`{.interpreted-text role="class"}, using one of the following types:
:::

-   `WsfCircularFieldOfView`{.interpreted-text role="class"}
-   `WsfRectangularFieldOfView`{.interpreted-text role="class"}
-   `WsfPolygonalFieldOfView`{.interpreted-text role="class"}
-   `WsfEquatorialFieldOfView`{.interpreted-text role="class"}

## Methods

::: method
string Type()

Returns the field of view type based on the corresponding derived field of view type as follows:
:::

+-------------------------------------------------------------+-----------------+
| Field of View Type                                          | Type() Result   |
+=============================================================+=================+
| `WsfCircularFieldOfView`{.interpreted-text role="class"}    | > *circular*    |
+-------------------------------------------------------------+-----------------+
| `WsfRectangularFieldOfView`{.interpreted-text role="class"} | > *rectangular* |
+-------------------------------------------------------------+-----------------+
| `WsfPolygonalFieldOfView`{.interpreted-text role="class"}   | > *polygonal*   |
+-------------------------------------------------------------+-----------------+
| `WsfEquatorialFieldOfView`{.interpreted-text role="class"}  | > *equatorial*  |
+-------------------------------------------------------------+-----------------+
