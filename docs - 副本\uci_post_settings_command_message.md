orphan

:   

# UCI_POST_SettingsCommandMessage

## Overview {#overview .UCI_POST_SettingsCommandMessage .inherits .UCI_Message}

This message allows the user to enable/disable a capability (mode) on a `WSF_IRST_SENSOR`{.interpreted-text role="model"}.

## Static Methods

::: method
static UCI_POST_SettingsCommandMessage Construct(UCI_CapabilityId aCapabilityId, UCI_CapabilityState aState)

This method constructs an instance of an UCI_POST_SettingsCommandMessage that will command the capability with the UUID of aCapabilityId to the state, aState. If aState is `ENABLE<UCI_CapabilityState.ENABLE>`{.interpreted-text role="method"}, the capability state will be set to UCI_AVAILABLE. `DISABLE<UCI_CapabilityState.DISABLE>`{.interpreted-text role="method"} will set the capability state to UCI_UNAVAILABLE.
:::
