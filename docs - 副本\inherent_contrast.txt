# inherent_contrast.rst.txt.md
orphan

:   



::: {.command block=""}
inherent_contrast
:::

::: parsed-literal

inherent_contrast *\<signature-name\>*

:   [interpolate_tables]() \<boolean-value\> [interpolation_type]() \[linear \| logarithmic\] [state]() \<*state-name\>* \| default \... `Azimuth-elevation_Table_Definition`{.interpreted-text role="ref"} \... [state]() \...

end_inherent_contrast
:::



[inherent_contrast](#inherent_contrast) defines the inherent contrast for a platform type. The inherent contrast is used when an optical sensor attempts to detect a platform. The inherent contrast consists of one or more sets of tables that apply when the platform is in a particular \'state\'. A \'state\' represents a condition such as \'landing-gear-down\'.

\<signature-name\> is the name to be given to the signature. If the name specifies the name of an existing definition then the new definition will replace the current one (i.e. the last occurrence will be used for the simulation.)



::: command
interpolate_tables \<boolean-value\>

Specifies whether to apply interpolation to the defined `Azimuth-elevation_Table_Definition`{.interpreted-text role="ref"}. The type of interpolation can be either linear or logarithmic (see [interpolation_type]()).

Default true
:::

::: command
interpolation_type \<linear \| logarithmic\>

Specifies whether to use linear or logarithmic interpolation when interpolating data in the azimuth elevation tables.

Default linear
:::

::: command
state \<state-name\>

Indicates that the following table definition will be used when the platform is in the signature state *\<state-name\>*. If *\<state-name\>* is default then the subsequent table will be used if the platform is in a signature state that does not match any of the states defined in the signature.

If a state command is not specified then the contrast has one contrast table that applies to all signature states.
:::



Use a constant contrast named *cueball* for all states:

::: parsed-literal

[inherent_contrast](#inherent_contrast) cueball

:   constant 0.1

end_inherent_contrast
:::

Use one contrast by default and a different contrast if the landing gear is down:

::: parsed-literal

[inherent_contrast](#inherent_contrast) dummy

:   

    [state]() default

    :   constant 0.1

    [state]() landing_gear_down

    :   constant 0.5

end_inherent_contrast
:::