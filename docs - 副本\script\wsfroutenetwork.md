orphan

:   

# WsfRouteNetwork

## Static Methods {#static-methods .WsfRouteNetwork .inherits .WsfObject}

::: method
WsfRouteNetwork FindRouteNetwork(string aRouteNetworkName)

Returns the route network with the given name.
:::

## Methods

::: method
WsfRoute GeneratePathOffRouteToRoute(double aStartLatitude, double aStartLongitude, double aEndLatitude, double aEndLongitude) WsfRoute GeneratePathOffRouteToRoute(WsfGeoPoint aStartPoint, WsfGeoPoint aEndPoint)

Finds the shortest path on a route network given any starting and ending points. If the start or end points are off of the network, the shortest path to get onto the network is computed first.
:::

::: method
WsfRoute GeneratePathOnRoute(WsfGeoPoint aStartPoint, WsfGeoPoint aEndPoint)

Finds the shortest path on a route network given any starting and ending points. If the start or end points are off of the network, the returned route will get as close as possible to the end point without leaving the route network.
:::

::: method
WsfGeoPoint SnapToNearestSegment(WsfGeoPoint aPoint)

Returns a `WsfGeoPoint`{.interpreted-text role="class"} at the closest location to aPoint along any segment of the route network.
:::

::: method
WsfGeoPoint SnapToNearestWaypoint(WsfGeoPoint aPoint)

Returns a `WsfGeoPoint`{.interpreted-text role="class"} at the location of the route network\'s waypoint closest to aPoint.
:::

::: method
Array\<WsfRoute\> Routes()

Returns an array containing all routes in the route network. The routes returned in this way should not be modified.
:::
