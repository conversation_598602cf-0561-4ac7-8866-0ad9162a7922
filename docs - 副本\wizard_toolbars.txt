# wizard_toolbars.rst.txt.md
orphan

:   



-   Edit Toolbar

> 
>
> -   Cut - Cut the highlighted text to the OS clipboard.
> -   Copy - Copy the highlighted text to the OS clipboard.
> -   Paste - Paste from the OS clipboard to the cursor location.
> -   Find - Input and find the a text fragment.
> -   Find in Files - Find all instances of a text fragment in every project file.
> -   Back - Move the cursor back to its previous location.
> -   Forward - Move the cursor forward to a location previous moved *Back* from.

-   File Toolbar

> 
>
> -   Save - Save the file in the currently active editor window.
> -   Save All - Save all files in editor windows.