orphan

:   

# WsfTrackManager

::: {.WsfTrackManager .inherits .Object}
WsfTrackManager is a script representation of a `track_manager`{.interpreted-text role="command"}.
:::

## Methods

::: method
WsfLocalTrack AddTrackReport(WsfTrack aTrack)

Add a track report (sensor measurement or processed track update). The track manager will attempt to associate the report with an existing track in the local track list. If no association is made, a new local track is created from the report; otherwise, the data in the report is fused with data in the associated track. A copy of the report is stored in the `raw track list<WsfTrackManager.RawTrackList>`{.interpreted-text role="method"}.

Return the local track that was associated with (or, created from) the track report.

::: note
::: title
Note
:::

The track report is associated with existing local tracks according to the track manager\'s `correlation method<correlation_methods>`{.interpreted-text role="ref"}, and it is fused according to its `fusion method<fusion_methods>`{.interpreted-text role="ref"}.
:::
:::

::: method
WsfTrackList RawTrackList()

Return the raw track list (the tracks that are input to the track manager).
:::

::: method
WsfLocalTrackList LocalTrackList()

Return the local track list (the fused tracks from inputs to the track manager).
:::

::: method
void DropTrack(WsfTrackId aTrackId)

Remove a raw track from the raw track list. The local track associated with the dropped raw track may be deleted if there are no other raw tracks associated with it, and if the track manager\'s `track_manager.uncorrelated_track_drops`{.interpreted-text role="command"} feature is enabled.
:::
