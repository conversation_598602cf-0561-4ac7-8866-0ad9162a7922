# wsforbitalreferenceframe.rst.txt.md
orphan

:   



::: WsfOrbitalReferenceFrame
The `WsfOrbitalReferenceFrame`{.interpreted-text role="class"} provides a means to select among the available reference frames for some script methods.
:::



::: method
WsfOrbitalReferenceFrame INERTIAL()

Return a `WsfOrbitalReferenceFrame`{.interpreted-text role="class"} instance representing the inertial reference frame. This frame is usually the `ECI`{.interpreted-text role="ref"} frame, but may represent the equivalent frames around other central bodies when used in the context of a space domain platform that is being propagated around a central body other than the Earth.
:::

::: method
WsfOrbitalReferenceFrame RIC()

Return a `WsfOrbitalReferenceFrame`{.interpreted-text role="class"} instance representing the RIC frame.

The Radial, In-track, Cross-track (RIC) is a reference frame defined relative to a given position and velocity, which are often taken from the current state of a platform. The frame uses an x-axis (Radial) along the position of the target point, a z-direction (Cross-track) normal to the orbital plane (i.e., aligned with the orbital angular momentum of the target), and a y-direction (In-track) that completes the right-handed orthonormal frame. The In-track direction will be in the same general direction as the given velocity, though it will only be parallel to the given velocity vector in certain situations.

When used for relative positions or velocities, the origin of the RIC frame coincides with the given position or velocity.
:::

::: method
WsfOrbitalReferenceFrame NTW()

Return a `WsfOrbitalReferenceFrame`{.interpreted-text role="class"} instance representing the NTW frame.

The NTW frame uses an x-axis along the velocity vector of the target point, a z-axis perpendicular to x and in the same plane as the target\'s nadir vector, and a y-axis that completes the right-handed orthonormal frame.
:::