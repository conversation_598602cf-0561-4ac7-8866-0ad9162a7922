# wsfsaprocessor.rst.txt.md
orphan

:   



::: {.contents local=""}
:::

::: {.WsfSA_Processor .inherits .WsfProcessor}
`WsfSA_Processor`{.interpreted-text role="class"} is a script reference to a `WSF_SA_PROCESSOR`{.interpreted-text role="model"}, which provides situation awareness functions within the cognitive model.
:::

The table below defines common technical terms used throughout the documentation for air-to-air combat related concepts.

  ---------- -----------------------------------------------------------------------------------------------------------------
  bogie      A perceived entity (unknown) that has yet to be identified as a bandit, friendly, or neutral

  bandit     A perceived entity that is known to be an enemy

  friendly   A perceived entity that is known to be friendly

  neutral    A perceived entity that is known to be neutral (often a non-combatant)

  asset      Another term for \"friendly\". This term is typically used to denote friendly forces that could provide support

  threat     A bandit/bogie that has been prioritized by threat scoring and maintained in a prioritized/ordered list

  target     A bandit/bogie that has been prioritized by target scoring and maintained in a prioritized/ordered list
  ---------- -----------------------------------------------------------------------------------------------------------------



The following methods provide access to perception-related settings.

::: method
double GetDisplayPerceptionDelay() double GetVisualPerceptionDelay()

Returns the current value of `WSF_SA_PROCESSOR.display_perception_delay`{.interpreted-text role="command"} or `WSF_SA_PROCESSOR.visual_perception_delay`{.interpreted-text role="command"} respectfully. Returns true on success and false if there is an error.
:::

::: method
bool SetDisplayPerceptionDelay(double aDelayTime) bool SetVisualPerceptionDelay(double aDelayTime)

Sets the `WSF_SA_PROCESSOR.display_perception_delay`{.interpreted-text role="command"} or `WSF_SA_PROCESSOR.visual_perception_delay`{.interpreted-text role="command"} value to aDelayTime. Returns true on success and false if there is an error.
:::



The following methods provide top-level assessment data for the `WsfSA_Processor`{.interpreted-text role="class"}.

::: method
double Risk()

Returns the overall assessed risk, a normalized value between 0.0 and 1.0.
:::

::: method
double SelfRisk()

Returns the assessed self-risk, a normalized value between 0.0 and 1.0.
:::

::: method
double FlightRisk()

Returns the assessed risk to the flight of aircraft, a normalized value between 0.0 and 1.0.
:::

::: method
double PackageRisk()

Returns the assessed risk to the package of aircraft, a normalized value between 0.0 and 1.0.
:::

::: method
double MissionRisk()

Returns the assessed risk to the mission (possibly composed of multiple aircraft and groups), a normalized value between 0.0 and 1.0.
:::

::: method
double Defensiveness()

Returns the overall assessed defensiveness level presented by the situation, a normalized value between 0.0 and 1.0.
:::

::: method
double Urgency()

Returns the overall assessed sense of urgency imposed by the situation, a normalized value between 0.0 and 1.0.
:::



The following methods provide asset (friendly forces) support in the `WsfSA_Processor`{.interpreted-text role="class"}.

::: method
Array\<WsfSA_EntityPerception\> PerceivedAssets()

Returns an array of perceived assets (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PerceivedAircraftAssets()

Returns an array of perceived aircraft assets (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PerceivedMissileAssets()

Returns an array of perceived missile assets (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
void SetAssetImportant(WsfPlatform aPlatform) void SetAssetImportant(WsfSA_EntityPerception aEntity)

Sets the designated platform or entity as important.
:::

::: method
void SetAssetTypeImportant(string aType)

Sets the designated type as important.
:::

::: method
void SetAssetTypeUnimportant(string aType)

Sets the designated type as unimportant.
:::

::: method
void SetAssetUnimportant(WsfPlatform aPlatform) void SetAssetUnimportant(WsfSA_EntityPerception aEntity)

Sets the designated platform or entity as unimportant.
:::

::: method
WsfSA_EntityPerception NearestAsset()

Returns the nearest asset.
:::

::: method
WsfSA_EntityPerception NearestAircraftAsset()

Returns the nearest aircraft asset.
:::

::: method
WsfSA_EntityPerception NearestMissileAsset()

Returns the nearest missile asset.
:::



The following methods provide bogie/bandit (unknown/enemy forces) support in the `WsfSA_Processor`{.interpreted-text role="class"}.

::: method
Array\<WsfSA_EntityPerception\> PerceivedBogies()

Returns an array of perceived bogies (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PerceivedAircraftBogies()

Returns an array of perceived aircraft bogies (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PerceivedMissileBogies()

Returns an array of perceived missile bogies (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PerceivedBandits()

Returns an array of perceived bandits (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PerceivedAircraftBandits()

Returns an array of perceived aircraft bandits (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PerceivedMissileBandits()

Returns an array of perceived missile bandits (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
void SetThreatImportant(WsfLocalTrack aTrack)

Sets the designated track as important.
:::

::: method
void SetThreatTypeImportant(string aType)

Sets the designated type as important.
:::

::: method
void SetThreatTypeUnimportant(string aType)

Sets the designated type as unimportant.
:::

::: method
void SetThreatUnimportant(WsfLocalTrack aTrack)

Sets the designated track as unimportant.
:::

::: method
WsfSA_EntityPerception NearestBogie()

Returns the nearest bogie.
:::

::: method
WsfSA_EntityPerception NearestAircraftBogie()

Returns the nearest aircraft bogie.
:::

::: method
WsfSA_EntityPerception NearestMissileBogie()

Returns the nearest missile bogie.
:::

::: method
WsfSA_EntityPerception NearestBandit()

Returns the nearest bandit.
:::

::: method
WsfSA_EntityPerception NearestAircraftBandit()

Returns the nearest aircraft bandit.
:::

::: method
WsfSA_EntityPerception NearestMissileBandit()

Returns the nearest missile bandit.
:::



The following methods provide prioritized lists for threats and targets. Each of these lists may be composed of bandits and bogies. These lists are prioritized, from highest threat to lowest threat and from the best target to the least attractive target. Threats are perceived entities that present an adverse condition (a threat) to the platform, while targets are perceived entities that the platform may wish to attack.

::: method
Array\<WsfSA_PerceivedItem\> PrioritizedThreatItems()

Returns an array of prioritized threats (`WsfSA_PerceivedItem`{.interpreted-text role="class"}) which includes entities and unfocused groups.
:::

::: method
Array\<WsfSA_EntityPerception\> PrioritizedThreatEntities()

Returns an array of prioritized threats (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PrioritizedAircraftThreatEntities()

Returns an array of prioritized aircraft threats (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PrioritizedMissileThreatEntities()

Returns an array of prioritized missile threats (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_Group\> PrioritizedThreatGroups()

Returns an array of prioritized threat groups (`WsfSA_Group`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_PerceivedItem\> PrioritizedTargetItems()

Returns an array of prioritized targets (`WsfSA_PerceivedItem`{.interpreted-text role="class"}) which includes entities and unfocused groups.
:::

::: method
Array\<WsfSA_EntityPerception\> PrioritizedTargetEntities()

Returns an array of prioritized targets (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PrioritizedAircraftTargetEntities()

Returns an array of prioritized aircraft targets (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_EntityPerception\> PrioritizedMissileTargetEntities()

Returns an array of prioritized missile targets (`WsfSA_EntityPerception`{.interpreted-text role="class"}).
:::

::: method
Array\<WsfSA_Group\> PrioritizedTargetGroups()

Returns an array of prioritized target groups (`WsfSA_Group`{.interpreted-text role="class"}).
:::

::: method
WsfSA_EntityPerception HighestThreat()

Returns the highest threat entity perception.
:::

::: method
WsfSA_EntityPerception HighestThreatAircraft()

Returns the highest threat aircraft entity perception.
:::

::: method
WsfSA_EntityPerception HighestThreatMissile()

Returns the highest threat missile entity perception.
:::

::: method
WsfSA_Group HighestThreatGroup()

Returns the highest threat group.
:::

::: method
WsfSA_EntityPerception BestTarget()

Returns the best target entity perception.
:::

::: method
WsfSA_EntityPerception BestTargetAircraft()

Returns the best target aircraft entity perception.
:::

::: method
WsfSA_EntityPerception BestTargetMissile()

Returns the best target missile entity perception.
:::

::: method
int MaxAssetLoad()

Returns the max asset load current value.
:::

::: method
void SetMaxAssetLoad(int aLoad)

Sets the max asset load to the aLoad current value.
:::

::: method
void ResetMaxAssetLoad()

Sets the max asset load current value to match the user\'s initialized value (default of -1 if not provided).
:::

::: method
int MaxThreatLoad()

Returns the max threat load current value.
:::

::: method
void SetMaxThreatLoad(int aLoad)

Sets the max threat load to the aLoad current value.
:::

::: method
void ResetMaxThreatLoad()

Sets the max threat load current value to match the user\'s initialized value (default of -1 if not provided).
:::

::: method
unsigned int MaxPrioritizedThreats()

Returns the prioritized threat maximum current value.
:::

::: method
void SetMaxPrioritizedThreats(unsigned int aMax)

Sets the prioritized threat maximum current value to the aMax value.
:::

::: method
void ResetMaxPrioritizedThreats()

Sets the prioritized threat maximum current value to match the user\'s initialized value (default of 0 if not provided).
:::

::: method
unsigned int MaxPrioritizedTargets()

Returns the prioritized target maximum current value.
:::

::: method
void SetMaxPrioritizedTargets(unsigned int aMax)

Sets the prioritized target maximum current value to the aMax value.
:::

::: method
void ResetMaxPrioritizedTargets()

Sets the prioritized target maximum current value to match the user\'s initialized value (default of 0 if not provided).
:::



All methods project the location of `aEntity` at a time `aSimTime` in the future (seconds), and can be replaced by different logic by defining a custom script with the correct signature.

The purpose of providing these functions, rather than leaving implementation entirely to the user, is multi-fold:

-   Providing simple, fast building blocks which have been compiled into code
-   Facilitating future visualization of predictions in Air Combat tools
-   Providing an accessible starting point for newcomers

::: method
WsfGeoPoint ProjectPositionInTime(double aSimTime, WsfSA_EntityPerception aEntity)

Extrapolates based on the underlying track extrapolation behavior. This is intended to be a sort of sandbox function, where users can implement more complex predictions if they choose.
:::

::: method
WsfGeoPoint ProjectPositionForward(double aSimTime, WsfSA_EntityPerception aEntity)

Extrapolates based on perceived speed and bearing.
:::

::: method
WsfGeoPoint ProjectPositionLevelTurnLeft(double aSimTime, WsfSA_EntityPerception aEntity, double aGees)
:::

::: method
WsfGeoPoint ProjectPositionLevelTurnRight(double aSimTime, WsfSA_EntityPerception aEntity, double aGees)

Assumes a simple geometric prediction of an aircraft maintaining an orbit with bank angle defined by `aGees`.
:::

::: method
WsfGeoPoint ProjectPositionTurnToHeading(double aSimTime, WsfSA_EntityPerception aEntity, double aHeading_deg, double aGees)

Assumes a simple geometric prediction of an aircraft turning to `aHeading_deg` with bank angle defined by `aGees`.
:::

::: method
WsfGeoPoint ProjectPositionGoToPoint(double aSimTime, WsfSA_EntityPerception aEntity, WsfGeoPoint aPointOfInterest, double aGees)

Assumes a simple geometric prediction of an aircraft turning toward `aPointOfInterest` with bank angle defined by `aGees`. Assumes altitude hold. This will not be a perfect estimator, as it does not iterate to account for effects to the approach path imposed by the radius required to actually change heading.
:::

::: method
WsfGeoPoint ProjectPositionSlice(double aSimTime, WsfSA_EntityPerception aEntity, double aRollAngle, double aGees)

Assumes a simple geometric prediction of an aircraft rolling from level flight to `double aRollAngle` and pulling through `aGees`. Assumes a 180-degree change of heading. Assumes level flyout after change of heading is complete.
:::

::: method
WsfGeoPoint ProjectPositionSliceToHeading(double aSimTime, WsfSA_EntityPerception aEntity, double aHeading_deg, double aRollAngle, double aGees)

Assumes a simple geometric prediction of an aircraft rolling from level flight to `double aRollAngle` and pulling through `aGees` until reaching `aHeading_deg`. Assumes sharp transition to level flyout after achieving `aHeading_deg`.
:::

::: method
WsfGeoPoint ProjectPositionSplitS(double aSimTime, WsfSA_EntityPerception aEntity, double aGees)

By default, acts as a special case of `ProjectPositionSlice<WsfSA_Processor.ProjectPositionSlice>`{.interpreted-text role="method"} where the entity rolls through 180 degrees.
:::

::: method
WsfSA_EntityPerception BestTargetGroup()

Returns the best target group.
:::



The following methods provide perceived group (`WsfSA_Group`{.interpreted-text role="class"}) support in the `WsfSA_Processor`{.interpreted-text role="class"}.

::: method
Array\<WsfSA_PerceivedItem\> PerceivedThreatItems()

Returns an unsorted array of `WsfSA_PerceivedItem`{.interpreted-text role="class"} objects (perceived entities and unfocused groups).
:::

::: method
Array\<WsfSA_Group\> PerceivedGroups()

Returns an unsorted array of `WsfSA_Group`{.interpreted-text role="class"} objects (perceived groups).
:::

::: method
WsfSA_Group GetGroup(string aName)

Returns the `WsfSA_Group`{.interpreted-text role="class"} (perceived group) with the specified name. Use `IsValid() <Object>`{.interpreted-text role="class"} to verify value returned.
:::

::: method
bool RenameGroup(WsfSA_Group aGroup, string aName)

If no other group recognized by the SA Processor has the given name, then it sets the name of aGroup to aName and returns true. Otherwise, it does not set the name and returns false.
:::

::: method
WsfSA_Group MergeGroups(Array\<WsfSA_Group\> aGroups)

Merges the groups. Returns the merged group, or null if merging failed. If successful, all groups are deleted, and a new group is created, so the original groups should be considered invalid after successfully calling this function.
:::

::: method
Array\<WsfSA_Group\> SplitGroup(WsfSA_Group aGroup, int aNumber, string aSplitCriteria)

Splits a group according to the aSplitCriteria enumeration. The number of entities specified by the aNumber argument will be added to a new group which fits aSplitCriteria. The remainder will be put into the other created group. If successful, the input group is deleted, so the original group should be considered invalid after successfully calling this function. Returns an array containing the two new groups if successful, with the remainder of the original first, followed by the new group defined by the split criteria and the given number of elements. Otherwise, it returns an empty array.

aSplitCriteria can be any of the following:

-   \"maximum threat\"
-   \"minimum threat\"
-   \"maximum target\"
-   \"minimum target\"
-   \"maximum altitude\"
-   \"minimum altitude\"
-   \"maximum distance\"
-   \"minimum distance\"
-   \"rightmost from heading\"
-   \"leftmost from heading\"
:::

::: method
WsfSA_Group CreateGroup(Array\<WsfSA_EntityPerception\> aEntities)

Creates a new group and inserts all entities from the array argument into it. As an exception to the general approach, if an entity in the array is already in another group, it will be removed from that group, but that group will not be marked as protected. Returns the new group if successful, or null otherwise.
:::

::: method
bool DisbandGroup(WsfSA_Group aGroup)

Disbands the group. This does not delete the entities within that group. Returns false if disbanding an unfocused group causes the PI limit to be exceeded, true otherwise. The group is deleted, so it should be considered invalid after successfully calling the function.
:::

::: method
bool TransferEntity(WsfSA_EntityPerception aEntity, WsfSA_Group aDestination)

Transfers the entity into the group. As an exception to the general approach, if the entity is already in another group, it will be removed from that group, but that group will not be marked as protected. Returns false if transferring an entity from an unfocused group to a focused one causes the PI limit to be exceeded, true otherwise.
:::

::: method
bool RemoveEntityFromGroup(WsfSA_EntityPerception aEntity)

Removes an entity from a group, but does not delete the entity. Returns false if removing an entity from an unfocused group causes the PI limit to be exceeded, true otherwise.
:::

::: method
bool SetFocus(WsfSA_Group aGroup, bool aFocus)

Sets a group to focused or unfocused depending on whether aFocus is true or false, respectively. Returns false if setting an unfocused group to focused causes the PI limit to be exceeded.
:::

::: method
bool GetFocus(WsfSA_Group aGroup)

Gets the focus state of aGroup.
:::

::: method
Array\<WsfSA_Group\> FocusedGroups()

Gets the set of all focused groups recognized by the SAP.
:::

::: method
Array\<WsfSA_Group\> UnfocusedGroups()

Gets the set of all unfocused groups recognized by the SAP.
:::

::: method
Array\<WsfSA_Group\> SortedGroups(string aSortingOrder)

Returns a list of groups, sorted according to aSortingOrder.

aSortingOrder can be any of he following:

-   \"ascending threat\"
-   \"descending threat\"
-   \"ascending target\"
-   \"descending target\"
-   \"ascending altitude\"
-   \"descending altitude\"
-   \"ascending distance\"
-   \"descending distance\"
-   \"ascending entity count\"
-   \"descending entity count\"
-   \"ascending radius\"
-   \"descending radius\"
:::

::: method
int PerceivedItemCount()

Returns the number of perceived items recognized by the SAP.
:::

::: method
int CalculatePerceivedItemCount(Array\<WsfSA_PerceivedItem\> aItemList)

Returns the number of PIs present in the given array.
:::

::: method
int PerceivedThreatItemLimit()

Returns the maximum number of perceived threat items the SAP can recognize at once. This is the number given to `WSF_SA_PROCESSOR.max_threat_load`{.interpreted-text role="command"}.
:::

::: method
void SetGroupImportant(WsfSA_Group aGroup)

Sets the given group as important. This does not change the importance of its elements.
:::

::: method
void SetGroupUnimportant(WsfSA_Group aGroup)

Sets the given group as unimportant. This does not change the importance of its elements.
:::

::: method
bool GetGroupImportance(WsfSA_Group aGroup)

Returns true if the group is marked as important, false otherwise.
:::



::: method
void AddExtraCognitiveLoading(double aTimeDelaySeconds)

This will add a time delay to perception updating or any other cognitive task. This is provided to represent a condition where a platform or agent is busy doing some task and regular perception updating should be delayed. Be cautious when using this, since if it is called from a regularly updating script and a delay is added each time that is equal to or greater than the update interval, then no perception updating will ever occur.
:::



::: note
::: title
Note
:::

The aUpdateIntervalName string parameter in these methods matches the corresponding WSF_SA_PROCESSOR command names listed in `sa_update_interval_commands`{.interpreted-text role="ref"} and in `sa_update_interval_groups`{.interpreted-text role="ref"}. For example, GetUpdateInterval(\"report_interval\") will return the update interval setting established by the report_interval command. The update_event_base_interval and asset_purge_lifetime command strings are excluded from valid aUpdateIntervalName options since they serve a different, but related purpose; however, separate methods provide access to these settings.
:::

The following methods retrieve the individual and grouped update intervals:

::: method
double GetUpdateInterval(string aUpdateIntervalName)

Returns the active value of the update interval. If all the values in a specified group are not the same or the supplied aUpdateIntervalName is invalid, -1.0 is returned to indicate an error.
:::

::: method
double GetStartupInterval(string aUpdateIntervalName)

Returns the update interval established at startup. If all the values in a specified group are not the same or the supplied aUpdateIntervalName is invalid, -1.0 is returned to indicate an error.
:::

The following methods modify the individual and grouped update intervals:

::: method
bool SetUpdateInterval(string aUpdateIntervalName, double aUpdateIntervalSeconds)

Sets the active update interval for the specified aUpdateIntervalName. Returns true on success and false if there is an error. The specified aUpdateIntervalSeconds will be rounded to the nearest multiple of update_event_base_interval. To prevent rounding to zero, a positive aUpdateIntervalSeconds value less than update_event_base_interval/2 will be rounded to update_event_base_interval; however, a value of zero can be used to disable aUpdateIntervalName updates.
:::

::: method
void ResetUpdateInterval(string aUpdateIntervalName)

Sets the active update interval to the startup value for the specified aUpdateIntervalName.
:::

The following methods provide access to the asset_purge_lifetime setting:

::: method
double GetAssetDataPurgeLifetime()

Returns the active asset_purge_lifetime setting.
:::

::: method
bool SetAssetDataPurgeLifetime(double aAssetPurgeLifetime)

Sets the active asset_purge_lifetime. Returns true on success and false if there is an error.
:::

::: method
void ResetAssetDataPurgeLifetime()

Sets the active asset_purge_lifetime to its startup value.
:::



The following methods provide weapon support in the `WsfSA_Processor`{.interpreted-text role="class"}.

::: method
string GetSelectedWeapon()

Returns the name of the currently selected weapon. If no weapon is selected, an empty string will be returned.
:::

::: method
void SetSelectedWeapon(string aName)

Sets the name of the selected weapon to the specified string.
:::

::: method
void SetMasterArm(bool aArmed)

Sets the master arm condition to \'Armed\' if true, \'Safe\' if false.
:::

::: method
bool GetMasterArm()

Returns the master arm condition. True indicates \'Armed\' and false indicates \'Safe\'.
:::

::: method
bool WeaponBayDoorsAreOpen()

Returns true if weapon bay doors are open, false if the doors are closed.
:::

::: method
void OpenWeaponBayDoors()

Sets the weapon bay doors condition to \'Open\'.
:::

::: method
void CloseWeaponBayDoors()

Sets the weapon bay doors condition to \'Closed\'.
:::

::: method
bool SupportingWeapon()

Returns true if a weapon is being supported (i.e. is being provided track information by the parent platform).
:::

::: method
bool ShootCueActive()

Returns true if the shoot cue is active.
:::

::: method
bool ShootCueBlinking()

Returns true if the shoot cue is blinking.
:::



The following methods provide various warnings/cautions in the `WsfSA_Processor`{.interpreted-text role="class"}.

::: method
bool MasterWarningActive()

Returns true if the master warning is active, else false.
:::

::: method
bool MasterCautionActive()

Returns true if the master caution is active, else false.
:::

::: method
bool JammingDetected()

Returns true if jamming is being detected, else false.
:::



The `WsfSA_Processor`{.interpreted-text role="class"} supports the use of a simple, mission-level, expendable countermeasures (CM) approach through the use of the `WSF_SA_PROCESSOR.use_simple_countermeasures`{.interpreted-text role="command"} command.

If true, the processor will use `WSF_SA_PROCESSOR.num_chaff`{.interpreted-text role="command"}, `WSF_SA_PROCESSOR.num_flares`{.interpreted-text role="command"}, and `WSF_SA_PROCESSOR.num_decoys`{.interpreted-text role="command"} as the initial countermeasure quantities. When DispenseChaff, DispenseFlare, and DispenseDecoy are invoked, these quantities are simply decremented (nothing is physically fired).

If false, the processor will model chaff countermeasures using `WSF_CHAFF_WEAPON`{.interpreted-text role="model"} defined on the parent platform. At the present time, there are no corresponding models for flares or decoys. In this situation, when DispenseChaff is called, the `WSF_CHAFF_WEAPON`{.interpreted-text role="model"} will be fired, but if DispenseFlare and DispenseDecoy are called, nothing will happen, since no dispensers of those types are available.

This approach provides a simple means of using expendable countermeasures (CM), allowing weapon scripts to inquire how many CM are present when the weapon was fired and then checking again when the weapon detonates \-- using the difference of the two quantities provides the number of CM that were dispensed during the weapon flight, which can then be used to model an effect of the CM upon weapon effectiveness.

The methods below provide the means to determine the current CM quantities as well as the ability to dispense CM.

::: method
int QuantityOfChaff()

Returns the quantity of chaff present.
:::

::: method
int QuantityOfFlares()

Returns the quantity of flares present.
:::

::: method
int QuantityOfDecoys()

Returns the quantity of decoys present.
:::

::: method
void DispenseChaff()

Dispenses a unit of chaff, if available.
:::

::: method
void DispenseFlare()

Dispenses a unit of flares, if available.
:::

::: method
void DispenseDecoy()

Dispenses a unit of decoys, if available.
:::



The following methods provide status of various emissions from the platform.

::: method
bool RadarEmitting()

Returns true if an onboard radar is emitting.
:::

::: method
bool JammerEmitting()

Returns true if an onboard jammer is emitting.
:::

::: method
bool OtherSystemsEmitting()

Returns true if any non-radar/non-jammer systems are emitting (such as communications).
:::



The following methods provide fuel support in the `WsfSA_Processor`{.interpreted-text role="class"}.

::: method
bool FuelStateBingoReached()

Returns true if the bingo fuel state has been reached.
:::

::: method
bool FuelStateJokerReached()

Returns true if the joker fuel state has been reached.
:::

::: method
double FuelStateNormalized()

Returns a normalized fuel state, ranging from 1.0 (full) to 0.0 (empty).
:::

::: method
double TimeToJoker()

Returns the number of seconds until the joker fuel state is reached, assuming the current fuel consumption rate.
:::

::: method
double TimeToBingo()

Returns the number of seconds until the bingo fuel state is reached, assuming the current fuel consumption rate.
:::

::: method
double TimeToEmpty()

Returns the number of seconds until all fuel is consumed, assuming the current fuel consumption rate.
:::



The following methods provide approximate target detection estimates as well as estimates of the ability of the target to detect the platform.

::: method
double ExpectedDetectionRangeAgainstTarget(WsfPlatform aTarget)

Returns the expected detection range (meters) against the specified target platform, based upon the SA Processor estimates. See `../aircraft_signature_parameters`{.interpreted-text role="doc"}.
:::

::: method
double ExpectedDetectionRangeByTarget(WsfPlatform aTarget)

Returns the expected detection range (meters) by the specified target platform of the current platform, based upon the SA Processor estimates. See `../aircraft_signature_parameters`{.interpreted-text role="doc"}.
:::



The following methods provide approximate WEZ range estimates by the platform against a target and the target against the platform.

::: method
double ExpectedWezRangeAgainstTarget(WsfPlatform aTarget)

Returns the expected WEZ range (meters) against the specified target platform, based upon the SA Processor estimates. See `../missile_wez_parameters`{.interpreted-text role="doc"}.
:::

::: method
double ExpectedWezRangeByTarget(WsfPlatform aTarget)

Returns the expected WEZ range (meters) by the specified target platform against the current platform, based upon the SA Processor estimates. See `../missile_wez_parameters`{.interpreted-text role="doc"}.
:::



The SA Processor is capable of interacting with various optional track processors. Each of the functions below will return the name of the track processor for a specified sensor system category/type. These names can be then be used in script to locate the desired track processor on the platform, allowing the list of tracks provided by that track processor to be retrieved.

::: method
string ESM_TrackProcessor()

Returns the name of the ESM (or RWR) track processor. If no such track processor exists, an empty string will be returned.
:::

::: method
string MWS_TrackProcessor()

Returns the name of the Missile Warning System (MWS) track processor. If no such track processor exists, an empty string will be returned.
:::

::: method
string RadarTrackProcessor()

Returns the name of the radar track processor. If no such track processor exists, an empty string will be returned.
:::

::: method
string IRST_TrackProcessor()

Returns the name of the InfraRed Search and Track (IRST) track processor. If no such track processor exists, an empty string will be returned.
:::

::: method
string DAS_TrackProcessor()

Returns the name of the Distributed Aperture System (DAS) track processor. If no such track processor exists, an empty string will be returned.
:::

::: method
string FLIR_TrackProcessor()

Returns the name of the Forward-Looking InfraRed (FLIR) track processor. If no such track processor exists, an empty string will be returned.
:::

::: method
string EyeTrackProcessor()

Returns the name of the eye (visual/eyesight) track processor. If no such track processor exists, an empty string will be returned.
:::

::: method
string PerceptionMasterTrackProcessor()

Returns the name of the perception master track processor (PMTP). If no such track processor exists, an empty string will be returned. The PMTP is an optional track processor that can be used to augment the master track processor (MTP) of the platform. In many cases, the PMTP includes feeds from more sensors and/or track processors than the MTP \-- in particular, it often includes eye sensors (aircrew eyesight) that do not route their tracks to the MTP. By using the a PMTP, additional perception can be provided by visual sensors (eyesight) that are not avionics and hence do not have an \"electronic path\" to the MTP.
:::



The following methods provide geometry support, such as \"angle off\" and \"aspect angle\" calculations, including 2D and 3D variants.

::: method
double AngleOffTarget2D(WsfPlatform aPlatform, WsfPlatform aTargetPlatform)

This returns the 2D angle-off between the target and the platform (the difference in headings). This calculates angle-off as the angle between the headings of the target and the platform. A positive angle-off is when the difference in between the target heading and the platform heading is positive and is less than or equal to Pi. If the difference is negative and is greater than negative Pi, the angle-off is negative. The angle is in degrees.
:::

::: method
double AngleOffTarget2DSelf(WsfPlatform aTargetPlatform)

This returns the 2D angle-off between the target and the current platform (the difference in headings). This calculates angle-off as the angle between the headings of the target and the platform. A positive angle-off is when the difference in between the target heading and the platform heading is positive and is less than or equal to Pi. If the difference is negative and is greater than negative Pi, the angle-off is negative. The angle is in degrees.
:::

::: method
double AngleOffTarget3D(WsfPlatform aPlatform, WsfPlatform aTargetPlatform)

This returns the 3D angle-off between the target and the platform (the difference in heading/velocity). This calculates angle-off as the angle between the velocity vectors of the target and the platform. A positive angle-off is when the target is to the right of the platform. This is determined based on the y-vector of the platform. If the dot-product of the target\'s velocity vector and the platform\'s right side vector is positive, it is considered a positive angle-off. When the dot-product is negative, the angle-off is a negative value. The angle is in degrees.
:::

::: method
double AngleOffTarget3DSelf(WsfPlatform aTargetPlatform)

This returns the 3D angle-off between the target and the current platform (the difference in heading/velocity). This calculates angle-off as the angle between the velocity vectors of the target and the platform. A positive angle-off is when the target is to the right of the platform. This is determined based on the y-vector of the platform. If the dot-product of the target\'s velocity vector and the platform\'s right side vector is positive, it is considered a positive angle-off. When the dot-product is negative, the angle-off is a negative value. The angle is in degrees.
:::

::: method
double AspectAngleForTarget2D(WsfPlatform aPlatform, WsfPlatform aTargetPlatform)

This returns the aspect-angle for the target relative to the platform\'s position in 2D, meaning the aspect is based on a horizontal plane that contains the target, with the platform\'s position projected onto the plane. A positive angle is the \"right side\" of the target and a negative angle is \"left side\" of the target. The angle is in degrees.
:::

::: method
double AspectAngleForTarget2DSelf(WsfPlatform aTargetPlatform)

This returns the aspect-angle for the target relative to the current platform\'s position in 2D, meaning the aspect is based on a horizontal plane that contains the target, with the platform\'s position projected onto the plane. A positive angle is the \"right side\" of the target and a negative angle is \"left side\" of the target. The angle is in degrees.
:::

::: method
double AspectAngleForTarget3D(WsfPlatform aPlatform, WsfPlatform aTargetPlatform)

This returns the aspect-angle for the target relative to the platform\'s position in 3D, meaning the aspect is based on the angle between a 3D vector opposite of the target\'s velocity vector and the vector from the target to the platform\'s position. Right side (positive) and left side (negative) is determined based on the y-vector of the target aircraft. If the vector dot-product of the target y-vector and the vector towards the platform is positive, it is considered \"right side\". The angle is measured in degrees.
:::

::: method
double AspectAngleForTarget3DSelf(WsfPlatform aTargetPlatform)

This returns the aspect-angle for the target relative to the current platform\'s position in 3D, meaning the aspect is based on the angle between a 3D vector opposite of the target\'s velocity vector and the vector from the target to the platform\'s position. Right side (positive) and left side (negative) is determined based on the y-vector of the target aircraft. If the vector dot-product of the target y-vector and the vector towards the platform is positive, it is considered \"right side\". The angle is measured in degrees.
:::