# wsftargetoffsetframe.rst.txt.md
orphan

:   



::: WsfTargetOffsetFrame
The `WsfTargetOffsetFrame`{.interpreted-text role="class"} provides a means to select the frame in which offsets are specified in `WsfTargetPoint`{.interpreted-text role="class"} objects.
:::

::: note
::: title
Note
:::

This class is being replaced by `WsfOrbitalReferenceFrame`{.interpreted-text role="class"} and is only used in deprecated methods of `WsfTargetPoint`{.interpreted-text role="class"}. The use of this class is deprecated, and it will be removed in a future release.
:::



::: method
WsfTargetOffsetFrame RIC()

Return a `WsfTargetOffsetFrame`{.interpreted-text role="class"} instance representing the RIC frame.

The RIC frame uses an x-axis (Radial) along the position of the target point, a z-direction (Cross-track) aligned with the orbital angular momentum of the target, and a y-direction (In-track) that completes the right-handed orthonormal frame.
:::

::: method
WsfTargetOffsetFrame NTW()

Return a `WsfTargetOffsetFrame`{.interpreted-text role="class"} instance representing the NTW frame.

The NTW frame uses an x-axis along the velocity vector of the target point, a z-axis perpendicular to and in the same plane as the target\'s nadir vector, and a y-axis that completes the right-handed orthonormal frame.
:::