orphan

:   

# WsfScriptedManeuver

## Overview {#overview .WsfScriptedManeuver .inherits .WsfManeuver}

Construct a maneuver that uses a user-supplied script to perform the execution of the maneuver.

## Methods

::: method
WsfScriptedManeuver Construct(string aScriptName)

Construct a maneuver that uses the provided script at global context to perform the execution of the maneuver.

When executed, this maneuver will call into the provided script one or more times, with each call providing the time of the call, and the mover to which the maneuver was assigned. The script should return the time of the next execution. If no further calls of the script are needed, it should return a time before the current simulation time.

For example, the following script would produce a maneuver that will print out a message once before completing:

    script double SampleManeuverScript(double aSimTime, WsfP6DOF_Mover aMover)
       writeln("Hello from a scripted maneuver!");
       return -1.0;
    end_script

    ...

    WsfScriptedManeuver mvr = WsfScriptedManeuver.Construct("SampleManeuverScript");

    ...
:::
