# wsfem_rcvr.rst.txt.md
orphan

:   



::: {.WsfEM_Rcvr .inherits .WsfEM_XmtrRcvr}
WsfEM_Rcvr is the receiver of electromagnetic radiation associated with a `WsfEM_Interaction`{.interpreted-text role="class"} and defined by the `_.receiver`{.interpreted-text role="command"} block.
:::

::: note
::: title
Note
:::

Use the `WsfSensor.Rcvr`{.interpreted-text role="method"}, `WsfComm.Rcvr`{.interpreted-text role="method"}, or `WsfEM_Interaction.Rcvr`{.interpreted-text role="method"} script methods to access the `WsfEM_Rcvr`{.interpreted-text role="class"}.
:::



::: method
void Activate()

Activates, or turns on the receiver, allowing EM interactions to occur within the simulation regarding this receiver. Note that this is the default state for receivers.
:::

::: method
void Deactivate()

Deactivates, or turns off the receiver, preventing EM interactions from occurring within the simulation regarding this receiver.
:::

::: method
double BandwidthEffect(double aFrequency, double aBandwidth)

Given a frequency(Hz) and bandwidth(Hz), returns a fraction \[0..1\] of the signal that is receivable for this receiver.
:::

::: method
bool CanInteractWith(WsfEM_Xmtr aXmtr)

Given a transmitter, returns true if this receiver can interact (receive) from the transmitter, false if it cannot.
:::

::: method
double DetectionThreshold()

Returns the minimum signal-to-noise db ratio detectable by this receiver.
:::

::: method
void SetDetectionThreshold(double aRatio)

Sets the minimum signal-to-noise db ratio detectable by this receiver.
:::

::: method
string Function()

Returns the function of this receiver, describing the receiver use case. The following types are possible:

-   rf_comm
-   rf_sensor
-   rf_passive_sensor
-   rf_interferer
-   rf_undefined
:::

::: method
double InstantaneousBandwidth()

Returns the receiver instantaneous bandwidth (Hz).
:::

::: method
void SetInstantaneousBandwidth(double aBandwidth)

Sets the receiver instantaneous bandwidth (Hz).
:::

::: method
double NoisePower()

Returns the receiver noise power level (dBW).
:::

::: method
void SetNoisePower(double aPower)

Sets the receiver noise power level (dBW).
:::

::: method
double NoiseFigure()

Returns the receiver noise figure (db ratio) attributed to this receiver.
:::

::: method
void SetNoiseFigure(double aFigure)

Sets the receiver noise figure (db ratio) for this receiver.
:::

::: method
double NoiseMultiplier()

Returns the noise multiplier (db ratio) that is used in SNR calculations for this receiver.
:::

::: method
void SetNoiseMultiplier(double aRatio)

Sets the noise multiplier (db ratio) for use in SNR calculations for this receiver.
:::

::: method
double PolarizationEffect(string aPolarization)

Returns the ratio of the signal that would be received given a signal of the provided polarization type on this receiver. See `WsfEM_XmtrRcvr.PolarizationTypes`{.interpreted-text role="method"} for the available polarization types in AFSIM.
:::

::: method
void SetPolarizationEffect(string aPolarizationType, double aRatio)

For the provided polarization type, set the fraction of the signal \[0..1\] that is received. Note that this value is set in respect to the current operating polarization type of this receiver at the time of the call. See `WsfEM_XmtrRcvr.PolarizationTypes`{.interpreted-text role="method"} for the available polarization types in AFSIM.
:::

::: method
double AntennaOhmicLoss()

Returns the loss factor (dB) on this receiver attributed to the ohmic resistance of the antenna.
:::

::: method
void SetAntennaOhmicLoss(double aRatio)

Sets the loss factor (dB) on this receiver attributed to the ohmic resistance of the antenna. Since this is a loss factor (inverse of gain), it should be greater than 0 dB.
:::

::: method
double ReceiveLineLoss()

Returns the loss factor (dB) on this receiver attributed to the loss between the antenna and the receiver.
:::

::: method
void SetReceiveLineLoss(double aRatio)

Sets the loss factor (dB) on this receiver attributed to the loss between the antenna and the receiver. Since this is a loss factor (inverse of gain), it should be greater than 0 dB.
:::