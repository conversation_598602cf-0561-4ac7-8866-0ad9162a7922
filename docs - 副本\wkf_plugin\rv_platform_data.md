orphan

:   

![image](../images/rv_platform_details_updater.png)

# Plotting {#rv_platform_data_plot}

![PLATFORM_DETAILS_PLOT_IMAGE](../images/rv_platform_details_plot.png)

Right clicking on a numerical value provides an option to plot that data over time for the selected platform.

The plot has a context menu with the following options:

  --------------------------------------------------------------------------------------------------------------------------------------------------------------
  Option            Description
  ----------------- --------------------------------------------------------------------------------------------------------------------------------------------
  Plot View         This option only appears when in the Data View, and it will switch the display back to the Plot View.

  Data View         This option only appears when in the Plot View, and it will show the Data View which shows the values for all points displayed on the plot

  Export Data       Allows the user to export the data in csv format to a file

  Show Legend       Toggles the legend on the plot

  Full Screen       Displays the plot in full screen mode

  Set Sample Rate   Sets desired sample rate(in seconds) for the data. Default sample rate is zero in which case all possible data is presented.
  --------------------------------------------------------------------------------------------------------------------------------------------------------------

Additionally, from the Platform Details right-click menu, the user may plot data for all platforms in one of the selected platform\'s groups (type, side, category). This will automatically apply a filter to the plot. The user may also manually add a filter in the plot dialog, located above the list of platforms.

# Data Rings

![PLATFORM_DETAILS_DATA_RING_IMAGE](../images/rv_platform_details_data_ring.png)

Right clicking on a numerical value also provides an option to create or remove a data ring for the selected platform. When adding a ring, the user will be presented with a dialog in which they can specifiy a desired minimum and maximum value for the ring. A platform can have any number of data rings, but only one ring per data item.

# Data Displays

![image](../images/wkf_data_displays_prefs.png)

Most items in the platform details dialog may be dragged onto the map or tether views to create a data overlay. The overlays may be dragged around or closed using the close button which appears after hovering over the overlay.

::: note
::: title
Note
:::

The preferences above will populate when the user edits the font size or color of the overlay.
:::
