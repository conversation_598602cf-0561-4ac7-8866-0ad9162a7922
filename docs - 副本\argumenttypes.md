orphan

:   

# Argument Types {#argument_types}

This section describes the format of the various command arguments.

::: {.contents local=""}
:::

## Primitive Types

::: argtype
boolean-value boolean

A value with two states: true & false:

  Value   Allowable Input Values
  ------- ---------------------------------------
  true    true on yes enable enabled
  false   false off no disable disabled unknown
:::

::: argtype
string-value string file-name

A contiguous set of printable characters that are surrounded by \'white-space\' (blank tab new-line).

**Note:** In some cases (mostly where a string value is being used to provide a file name) the string may be surrounded by quote marks and may contain white space characters. Quoted string values are not allowed where they might appear in an output file and potentially cause post-processing problems.
:::

::: argtype
integer-value integer

Any numerical value that is generally accepted as an integer value by C, C++ and FORTRAN programming languages. For example:

    1
    6
    -33
:::

::: argtype
positive-integer

Any numerical value that is generally accepted as an non-negative integer value by C, C++ and FORTRAN programming languages (i.e.: not preceded by a minus sign). For example:

    1
    6
    33
:::

::: argtype
real-value real

Any numerical value that is generally accepted as double precision floating point value by the C, C++ and FORTRAN programming languages. The decimal point can be omitted if there is no fractional value. Examples:

    1.5
    9.67e10
    42
:::

## Types with Units

::: argtype
acceleration-value \<real\> \<acceleration-units\> acceleration-units

  Unit of Measure    Allowable Input Values
  ------------------ ------------------------
  Length/            /\^2 /2
  standard gravity   g

Examples:

    10 m/s^2
    10 m/s2
    1 g
:::

::: argtype
angle-value \<real\> \<angle-units\> angle-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  degrees           deg degree degrees
  radians           rad radian radians
  mils              mil mils
  arcseconds        arcsecond arcseconds
:::

::: argtype
angle-rate-value \<real\> \<angular-speed-units\> angular-speed-value \<real\> \<angular-speed-units\> angular-speed-units

  Unit of Measure      Allowable Input Values
  -------------------- ------------------------
  Angle/Time           /
  revolutions/minute   rpm
:::

::: argtype
angular-acceleration-value \<real\> \<angular-acceleration-units\> angular-acceleration-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Angle/            /\^2 /2
:::

::: argtype
angular-inertia-value \<real\> \<angular-inertia-units\> angular-inertia-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Mass\*            \* \^2
:::

::: argtype
area-value \<real\> \<area-units\> area-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
                    \^2 2

Examples:

    1 m^2
    1 m2
    1 ft^2
    1 ft2
:::

::: argtype
capacitance-value \<real\> \<capacitance-units\> capacitance-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  farads            farads farad
  millifarads       millifarads millifarad
  microfarads       microfarads microfarad
  nanofarads        nanofarads nanofarad
  picofarads        picofarads picofarad
  femtofarads       femtofarads femtofarad
:::

::: argtype
current-value \<real\> \<current-units\> current-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  amps              amps amp
  milliamps         milliamps milliamp
  microamps         microamps microamp
  nanoamps          nanoamps nanoamp
:::

::: argtype
current-density-value \<real\> \<current-density-units\> current-density-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Current/Area      /
:::

::: argtype
data-rate-value \<real\> \<data-rate-units\> data-rate-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  DataSize/Time     /
:::

::: argtype
data-size-value \<real\> \<data-size-units\> data-size-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  bits              bits bit
  kilobits          kbits kbit
  megabits          mbits mbit
  gigabits          gbits gbit
  bytes             bytes
  kilobytes         kbytes kbyte
  megabytes         mbytes mbyte
  gigabytes         gbytes gbyte
:::

::: argtype
energy-value \<real\> \<energy-units\> energy-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  joules            joules j
  kilojoules        kilojoules kj
:::

::: argtype
fluence-value \<real\> \<fluence-units\> fluence-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Energy/Area       /
:::

::: argtype
force-value \<real\> \<force-units\> force-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Newton            newtons newton nt
  Kilogram-Force    kgf
  Pound-Force       lbf lbsf
:::

::: argtype
frequency-value \<real\> \<frequency-units\> frequency-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  hertz             hz
  kilohertz         khz
  megahertz         mhz
  gigahertz         ghz
:::

::: argtype
impulse-value \<real\> \<impulse-units\> impulse-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Force\*Time       \*
:::

::: argtype
irradiance-value \<real\> \<irradiance-units\> irradiance-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Power/Area        /
:::

::: argtype
length-value \<real\> \<length-units\> length-units

  Unit of Measure     Allowable Input Values
  ------------------- ------------------------------------------
  meters              meters meter m
  kilometers          kilometers km
  megameters          megameters megameter
  feet                feet ft
  kilofeet            kfeet kft
  miles               miles mile mi
  nautical miles      nm nmi
  centimeters         centimeters centimeter cm
  millimeters         millimeters millimeter mm
  micrometers         micrometers micrometer um microns micron
  nanometers          nanometers nanometer
  angstroms           angstroms angstrom
  inches              inches inch in
  astronomical_unit   au ua

Examples:

    6 meters
    123.5 miles
    6.0e5 km
:::

::: argtype
mass-density-value \<real\> \<mass-density-units\> mass-density-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Mass/Volume       /
:::

::: argtype
mass-flow-value \<real\> \<mass-flow-units\> mass-flow-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Mass/Time         /

Examples:

    10 kg/s
    10 lbm/sec
:::

::: argtype
mass-value \<real\> \<mass-units\> mass-units

  Unit of Measure   Allowable Input Values
  ----------------- ----------------------------
  kilograms         kilograms kg kilo kilogram
  grams             grams gram g
  pounds            pounds pound lbs lbm lb
  kilopounds        klb
  tons              tons ton
  tonnes            tonnes tonne
  slugs             slugs slug
:::

::: argtype
power-value \<real\> \<power-units\> power-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  watts             watts w
  kilowatts         kilowatts kw
  megawatts         megawatts mw
  gigawatts         gigawatts gw
  milliwatts        milliwatts
  microwatts        microwatts
:::

::: argtype
power-value-db \<real\> \<db-power-units\> db-power-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  watts             watts w
  kilowatts         kilowatts kw
  megawatts         megawatts mw
  gigawatts         gigawatts gw
  milliwatts        milliwatts
  microwatts        microwatts
  dbw               dbw dBw
  dbm               dbm dBm
:::

::: argtype
pressure-value \<real\> \<pressure-units\> pressure-units

  Unit of Measure   Allowable Input Values
  ----------------- ----------------------------------
  pascal            Pa pa pascal
  kilopascal        kPa kpa kilopascal kilopascals
  micropascal       uPa upa micropascal micropascals
  nanopascal        nanopascals nanopascal
  pounds/inch\^2    psi
  pounds/foot\^2    psf
:::

::: argtype
radiance-value \<real\> \<radiance-units\> radiance-units

  Unit of Measure         Allowable Input Values
  ----------------------- ------------------------
  Irradiance/SolidAngle   /
:::

::: argtype
ratio-value \<ratio-units\> db-ratio-value \<ratio-units\> ratio-units db-ratio-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  dimensionless     absolute
  dimensionless     db
:::

::: argtype
resistance-value \<real\> \<resistance-units\> resistance-units

  Unit of Measure   Allowable Input Values
  ----------------- ---------------------------------
  ohms              ohms ohm
  microohms         microohms microohm
  milliohms         milliohms milliohm
  kiloohms          kiloohms kiloohm kilohms kilohm
  megaohms          megaohms megaohm megohms megohm
  gigaohms          gigaohms gigaohm gigohms gigohm
:::

::: argtype
responsivity-value \<real\> \<responsivity-units\> responsivity-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Current/Power     /
:::

::: argtype
solid-angle-value \<real\> \<solid-angle-units\> solid-angle-units

  Unit of Measure   Allowable Input Values
  ----------------- -------------------------
  steradians        steradians steradian sr
:::

::: argtype
specific-range-value \<real\> \<specific-range-units\> specific-range-units

  Unit of Measure            Allowable Input Values
  -------------------------- ------------------------
  Meters/Kilogram            m/kg
  Miles/Pound                mi/lb
  Miles/Kilopound            mi/klb
  Nautical-Miles/Pound       nmi/lb
  Nautical-Miles/Kilopound   nmi/klb
:::

::: argtype
spectral-irradiance-value \<real\> \<spectral-irradiance-values\> spectral-irradiance-units

  Unit of Measure     Allowable Input Values
  ------------------- ------------------------
  Irradiance/Length   /
:::

::: argtype
spectral-radiance-value \<real\> \<spectral-radiance-values\> spectral-radiance-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Radiance/Length   /
:::

::: argtype
speed-value \<speed-units\> speed-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  Length/Time       /
  meters/second     m/s
  kilometers/hour   km/h kmh
  feet/second       ft/s fps
  feet/minute       ft/m fpm
  miles/hour        mi/h mph
  knots             knots kts
:::

::: argtype
temperature-value \<real\> \<temperature-units\> temperature-units

  Unit of Measure      Allowable Input Values
  -------------------- ---------------------------
  degrees Kelvin       Kelvin kelvin K k
  degrees Celsius      Celsius celsius C c
  degrees Fahrenheit   Fahrenheit fahrenheit F f
:::

::: argtype
time-value \<real\> \<time-units\> time-units

  Unit of Measure   Allowable Input Values
  ----------------- ----------------------------------------
  seconds           seconds second secs sec s
  minutes           minutes minute mins min m
  hours             hours hour hrs hr h
  milliseconds      milliseconds millisecond msecs msec ms
  microseconds      microseconds microsecond usecs usec us
  nanoseconds       nanoseconds nanosecond nsecs nsec ns
  days              days day

Examples:

    1 hour
    1 hr
    60 minutes
    60 min
    3600 seconds
    3600 sec
:::

::: argtype
voltage-value \<real\> \<voltage-units\> voltage-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
  volts             volts volt v
  millivolts        millivolts millivolt
  microvolts        microvolts microvolt
  kilovolts         kilovolts kilovolt
  megavolts         megavolts megavolt
:::

::: argtype
volume-value \<real\> \<volume-unit\> volume-units

  Unit of Measure   Allowable Input Values
  ----------------- ------------------------
                    \^3 3

Examples:

    1 m^3
    1 m3
    1 ft^3
    1 ft2
:::

## Other Types

::: argtype
address address-value

An IPV4 based address, using CIDR notation, in the following format:

    <integer>.<integer>.<integer>.<integer>/<integer>

where the first four values (separated by periods) represent the IPV4 address, and the value following the forward slash represent the CIDR value, which indicates the number of bits used for network addressing. Note that the addressing values consist of values between 0 and 255, while the CIDR notation value should be between 1 and 31. Restriction to these appropriate values is not performed by the Wizard, but provision of incorrect values will be restricted by the simulation object during initialization.

An example of such an address is as follows:

    ***********/24

This is the AFSIM standard method for internally consistent addressing of all comm objects in the simulation, where previous versions simply used a string. The benefit for doing so is providing the knowledge of a particular address and knowledge of network membership in a single value.

IPV6 addresses are not currently supported, but are expected in future updates.
:::

::: argtype
latitude-value longitude-value latitude longitude

Latitude and longitude values must be entered in the following format:

    <angle><hemisphere>

where \<angle\> is:

    d.f      Degrees and fractions of degrees
    d:m.f    Degrees, minutes and fractions of minutes
    d:m:s.f  Degrees, minutes, seconds and fractions of seconds

The fractional component (.f) of \<angle\> is optional.

\<hemisphere\> is case-sensitive and must be \'n\' or \'s\' for a latitude value and \'e\' or \'w\' for a longitude value. There can be no space between \<angle\> and \<hemisphere\>.

For example, all of the following represent the approximately the same position:

    position 38.747775n    90.36w
    position 38:44.866n    90:21.6w
    position 38:44:51.99n  90:21:36w
:::

::: argtype
MGRS-value

The Military Grid Reference System (MGRS) is a grid-based coordinate system that is useful for determining the relative locations of entities within a theater of operations. It is the primary geo-coordinate standard of the U.S. Army.

An example of an MGRS coordinate (grid reference) for a location near St. Louis MO is:

    15SYC1111122222

This coordinate consists of the following subcomponents:

-   15: The 6 degree wide longitude band measured east from the international date line.
-   S: The 8 degree wide latitude band. Letters C-X (except I and O) are used to designate these bands measured northward from the -80 degrees. Band \'X\' is 12 degrees high providing coverage from -80 degrees to +84 degrees with 20 total bands. A useful rule is that band \'N\' (for \"North\") is the first band north of the equator.
-   Y: The 100,000 meter \"major easting\" offset east of the start of the longitude band. Letters A-Z (except I and O) are used for the major easting and the start letter of these zones alternate depending on the longitude band (UTM zone).
-   C: The 100,000 meter \"major northing\" offset north of the start of the latitude band. Letters A-V (except I and O) are used for the major northing.
-   11111: The easting within the Square Identification.
-   22222: The northing within the Square Identification.

**Notes**

-   The latitude and longitude bands together (15S) constitute a 6x8 degree **MGRS Grid Zone**.
-   The major easting and northing offsets together (YC) constitute a 100 square kilometer **MGRS Square Identification** within the Grid Zone.
-   The easting and northing offsets together constitute the **MGRS Numerical Location** within the Square Identification.

In summary:

    15S           YC         11111 22222
     |            |               |
    Grid Zone    Square Id    Numerical Location
    (6x8 degree  (within Grid  (within Square Id)
    location)    Zone)

One final point is that the Numerical Location can be specified with 0-5 decimals to specify various levels of precision. For example:

-   15SYC1111122222 (1 meter precision)
-   15SYC11112222 (10 meter precision)
-   15SYC111222 (100 meter precision)
-   15SYC1122 (1 km precision)
-   15SYC12 (10 km precision)
-   15SYC (100 km precision)
:::

::: argtype
epoch-value

Epoch values are used to specify the reference time for which a two-line-element (TLE) is valid, and it can also be used to specify a simulation start time. Epoch values are specified in either of two formats:

    <YYYYDDD.fractional-day>
    <YYDDD.fractional-day>

where:

    YYYY           is the four-digit year or
    YY             is a two-digit year.
    DDD            is a three-digit day of the year (001 - 366).
    fractional-day is an eight digit fractional day referenced to UT midnight.

For two digit years it is understood that years less than 57 occur in the 21st century and those greater than or equal to 57 occur in the 20th century. For example the default epoch for simulation start time noon UT June 1, 2003 is:

    2003152.5 (four digit format)
    03152.5 (two digit format)
:::

::: argtype
color color-value

Colors may be specified in the following formats:

  Color Specification   Format         Allowable Values
  --------------------- -------------- ------------------
  Floating point RGBA   float          \[0, 1\]
  Floating point RGB    float          \[0, 1\]
  Integral RGBA                        \[0, 255\]
  Integral RGB                         \[0, 255\]
  Hexadecimal RGBA      \<RRGGBBAA\>   \[00, FF\]
  Hexadecimal RGB       \<RRGGBB\>     \[00, FF\]
  Named color                          See table below

The following are valid named colors with their corresponding \[0, 255\] RGB values, many of which are taken from MIL-STD-2525D - TABLE XV.

  Color Name     RGB
  -------------- -----------------
  black          (0, 0, 0)
  blue           (0, 168, 220)
  brown          (61, 33, 255)
  dark_blue      (0, 107, 140)
  dark_green     (0, 160, 0)
  dark_purple    (80, 0, 80)
  dark_red       (200, 0, 0)
  dark_yellow    (225, 220, 0)
  gray           (102, 102, 102)
  grey           (102, 102, 102)
  green          (0, 226, 0)
  indigo         (74, 0, 159)
  light_blue     (128, 224, 255)
  light_green    (170, 255, 170)
  light_purple   (255, 161, 255)
  light_red      (255, 128, 128)
  light_yellow   (255, 255, 128)
  magenta        (255, 0, 255)
  orange         (255, 170, 0)
  pink           (255, 0, 192)
  purple         (128, 0, 128)
  red            (255, 48, 49)
  tan            (182, 133, 56)
  violet         (192, 128, 255)
  white          (255, 255, 255)
  yellow         (255, 255, 0)
:::

## Reference Values {#argument_types.reference_values}

::: note
::: title
Note
:::

The WSF `WSF_User_Input_Format.Pre-Processor`{.interpreted-text role="ref"} (introduced in AFSIM 1.9.0) provide a better and more general way to accomplish what is provided by this capability. Please consider using it if possible.
:::

This section describes the accepted syntax for reference values. See the article `Reference_Values`{.interpreted-text role="ref"} for a more detailed description on how they are used. As an example, prior to the implementation of reference values, the `processor.update_interval`{.interpreted-text role="command"} command contained in `processor`{.interpreted-text role="command"} was defined as follows:

::: parsed-literal
update_interval `\<time-value\> <time-value>`{.interpreted-text role="argtype"}
:::

With the implementation of reference values, the documentation now reads:

::: parsed-literal
update_interval `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"}
:::

Commands that accept reference values will use the \'\<X-reference\>\' syntax in their description. Commands that do not accept a reference value will use the \'\<X-value\>\' syntax.

For each of the items that have units, *Script Units* indicates the units that *MUST* be used when assigning a value to the referenced script variable.

::: argtype
real-reference

Explicit

:   `\<real-value\> <real-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<real-value\> <real-value>`{.interpreted-text role="argtype"} \]
:::

::: argtype
integer-reference

Explicit

:   `\<integer-value\> <integer-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<integer-value\> <integer-value>`{.interpreted-text role="argtype"} \]
:::

::: argtype
boolean-reference

Explicit

:   `\<boolean-value\> <boolean-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<boolean-value\> <boolean-value>`{.interpreted-text role="argtype"} \]
:::

::: argtype
string-reference

Explicit

:   `\<string-value\> <string-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<string-value\> <string-value>`{.interpreted-text role="argtype"} \]
:::

::: argtype
mass-reference

Explicit

:   `\<mass-value\> <mass-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<mass-value\> <mass-value>`{.interpreted-text role="argtype"} \]

Script Units

:   kilograms
:::

::: argtype
length-reference

Explicit

:   `\<length-value\> <length-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<length-value\> <length-value>`{.interpreted-text role="argtype"} \]

Script Units

:   meters
:::

::: argtype
time-reference

Explicit

:   `\<time-value\> <time-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<time-value\> <time-value>`{.interpreted-text role="argtype"} \]

Script Units

:   seconds

For example:

    update_interval 2 secs
    update_interval /variable UPDATE_INTERVAL /default 2 secs
:::

::: argtype
speed-reference

Explicit

:   `\<speed-value\> <speed-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<speed-value\> <speed-value>`{.interpreted-text role="argtype"} \]

Script Units

:   meters/second
:::

::: argtype
acceleration-reference

Explicit

:   `\<acceleration-value\> <acceleration-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<acceleration-value\> <acceleration-value>`{.interpreted-text role="argtype"} \]

Script Units

:   meters/second\^2
:::

::: argtype
force-reference

Explicit

:   `\<force-value\> <force-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<force-value\> <force-value>`{.interpreted-text role="argtype"} \]

Script Units

:   Newtons
:::

::: argtype
angle-reference

Explicit

:   `\<angle-value\> <angle-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<angle-value\> <angle-value>`{.interpreted-text role="argtype"} \]

Script Units

:   radians
:::

::: argtype
angle-rate-reference

Explicit

:   `\<angular-speed-value\> <angular-speed-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<angular-speed-value\> <angular-speed-value>`{.interpreted-text role="argtype"} \]

Script Units

:   radians/second
:::

::: argtype
angle-acceleration-reference

Explicit

:   `\<angular-acceleration-value\> <angular-acceleration-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<angular-acceleration-value\> <angular-acceleration-value>`{.interpreted-text role="argtype"} \]

Script Units

:   radians/second\^2
:::

::: argtype
data-rate-reference

Explicit

:   `\<data-rate-value\> <data-rate-value>`{.interpreted-text role="argtype"}

Reference

:   /variable \<variable-name\> \[ /default `\<data-rate-value\> <data-rate-value>`{.interpreted-text role="argtype"} \]

Script Units

:   bits/second
:::

## Random Values

::: argtype
random-time-reference

Some commands accept a special syntax that allows specification of a constant value or a statistical distribution from which a random value is drawn. The following statistical distributions are provided:

-   **uniform** with a user-specified minimum and maximum.
-   **normal** with a user-specified mean and standard deviation.
-   **log_normal**, with a user-specified mean and standard deviation.
-   **exponential**, with an optional user-specified lambda, defaulting to 1.0.

Note that the constant value and the parameters for the statistical distribution are specified in [Reference Values](#reference-values).

A time value that allows a time to be drawn from a user specified statistical distribution. \'random-time-value\' can have any of the following formats

::: parsed-literal
`\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} constant `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} uniform minimum `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} maximum `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} normal mean `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} sigma `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} log_normal mean `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} sigma `\<time-reference\> <time-reference>`{.interpreted-text role="argtype"} exponential \[lambda `\<real-value\> <real-value>`{.interpreted-text role="argtype"}\]
:::

For example valid examples for `platform.creation_time`{.interpreted-text role="command"} in `platform`{.interpreted-text role="command"} would be:

    creation_time 10 min
    creation_time constant 10 min
    creation_time uniform minimum 5 min maximum 15 min
    creation_time normal mean 10 min sigma 5 min
    creation_time log_normal mean 10 min sigma 5 min
    creation_time exponential
    creation_time exponential lambda 1.0

The values within a \<random-time-reference\> may also be reference values. So for `platform.creation_time`{.interpreted-text role="command"} one could also have:

    creation_time /variable TRUCK_CREATION_TIME /default 10.0 sec
    creation_time uniform minimum /variable TRUCK_CREATION_TIME_MIN /default 5 min
                          maximum /variable TRUCK_CREATION_TIME_MAX /default 10 min
:::

::: argtype
random-speed-reference

Identical to \<random-time-reference\>, except using \<speed-reference\> instead of \<time-reference\> for constant or statistical distributions regarding speed.
:::

::: argtype
random-data-rate-reference

Identical to \<random-time-reference\>, except using \<data-rate-reference\> instead of \<time-reference\> for constant or statistical distributions regarding data rates.
:::
