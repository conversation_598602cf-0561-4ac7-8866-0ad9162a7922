# wsforbitalmissionsequence.rst.txt.md
orphan

:   



::: WsfOrbitalMissionSequence
A [WsfOrbitalMissionSequence](#wsforbitalmissionsequence) is passed to a `WsfSpaceMover`{.interpreted-text role="class"} perform a specific set of actions, such as a sequence of `maneuvers<WsfOrbitalManeuver>`{.interpreted-text role="class"}. Each `event<WsfOrbitalEvent>`{.interpreted-text role="class"} is constructed individually then added to the sequence with the `WsfOrbitalMissionSequence.AddEvent`{.interpreted-text role="method"} or `WsfOrbitalMissionSequence.AddManeuver`{.interpreted-text role="method"} methods.
:::

::: method
static WsfOrbitalMissionSequence Construct()

Construct a new [WsfOrbitalMissionSequence](#wsforbitalmissionsequence).
:::

::: method
void AddEvent(WsfOrbitalEvent aEvent)

Add any `event<WsfOrbitalEvent>`{.interpreted-text role="class"} to the list of events to be executed.
:::

::: method
void AddManeuver(WsfOrbitalManeuver aManeuver)

Add a specific `orbital maneuver<WsfOrbitalManeuver>`{.interpreted-text role="class"} to the list of events to be executed.
:::