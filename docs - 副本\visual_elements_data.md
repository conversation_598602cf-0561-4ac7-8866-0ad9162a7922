orphan

:   

```{=html}
<!-- -->
```

orphan

:   

# Visual Elements Data {#Visual_Elements_Data_label}

::: parsed-literal

visual_elements

:   [poi]() \<poi-name\> \... [decoration](#decoration) \<entity-name\> \... [range_ring]() \<ring-name\> \...

end_visual_elements
:::

::: command
visual_elements \... end_visual_elements

Visual elements blocks contain three types of map annotations: points-of-interest, decorations and range rings. Although they have no bearing on the simulation itself, these map annotations can significantly improve the user experience in AFSIM visual applications.

::: warning
::: title
Warning
:::

Points of interest and platforms are commonly referred to as **\*entities**\*. Range rings and decorations are attached to entities, which are referenced by name. Please avoid having multiple entities with the same name because this may cause unintended behavior.
:::
:::

-   Points-of-interest are small markers used to keep track of important locations.
-   Decorations are text or image attachments that may be added to platforms or points-of-interest.
-   Range rings are a set of concentric circles labeled by distance from a central point (platform or point-of-interest) that can be useful for visualizing effects such as weapon ranges.

Each of these command blocks are detailed below.

# Point-Of-Interest

Each point-of-interest has a name, a position, an altitude and a bullseye flag.

::: command
poi \<name\> \... end_poi

::: parsed-literal

poi \<string\>

:   [position]() \... [altitude]() \... [bullseye]()

end_poi
:::
:::

::: command
position \<latitude-value\> \<longitude-value\>

Specifies the latitude and longitude of the point-of-interest.

**Default** 0n 0e
:::

::: command
altitude \<length-value\>

Specifies the altitude of the point.

**Default** 0 m
:::

::: command
bullseye

This flag determines whether the point-of-interest will be displayed as a bullseye instead.
:::

# Decoration

Each decoration has an entity name, which represents the entity that it is attached to. The inside of the block contains a description of what should be drawn on the map.

::: command
decoration \<name\> \... end_decoration

::: parsed-literal

decoration \<string\>

:   [text]() \... [preset]() \... [image]() \...

end_decoration
:::
:::

::: command
text \<string\>

Specifies the text that should be displayed on the entity.
:::

::: command
preset \<PresetType\>

Specifies the preset image that should be displayed on the entity. The possible choices are one, two, three, four, heart, diamond, spade and club.
:::

::: command
image \<string\>

Specifies the file path of the image that should be displayed on the entity.

::: note
::: title
Note
:::

The three decoration description commands are mutually exclusive. Only the most recent command will be considered when drawing the decoration.
:::
:::

# Range Ring

Every range ring block has a unique name. The inside of the block contains a description of how the ring should be drawn, as well as the name of the entity it should be attached to.

::: command
range_ring \<name\> \... end_range_ring

::: parsed-literal

range_ring \<string\>

:   [entity]() \... [show_range_labels]() [range_label_color]() \... [ring_color]() \... [ring_width]() \... [center_radius]() \... [additional_rings]() \... [additional_radii]() \... [show_angle_labels]() [angle_label_color]() \... [radial_color]() \... [radial_width]() \... [center_angle]() \... [angle_width]() \... [radial_rate]() \... [align_north]()

end_range_ring
:::
:::

::: command
entity \<string\>

Specifies the name of the entity that the range ring should be attached to.
:::

::: command
show_range_labels

This flag determines whether range labels should be visible.
:::

::: command
range_label_color \<color-value\>

Specifies the color of the range labels.

**Default** white
:::

::: command
ring_color \<color-value\>

Specifies the color of the rings.

**Default** white
:::

::: command
ring_width \<integer\>

Specifies the line width of the rings.

**Default** 1
:::

::: command
center_radius \<length-value\>

Specifies the radius of the innermost range ring.

**Default** 111120 m
:::

::: command
additional_rings \<integer\>

Specifies the number of additional rings that should be drawn.

**Default** 0
:::

::: command
additional_radii \<length-value\>

Specifies how much longer the radius is for each additional ring

**Default** 18520 m
:::

::: command
show_angle_labels

This flag determines whether angle labels should be visible.
:::

::: command
angle_label_color \<color-value\>

Specifies the color of the angle labels.

**Default** white
:::

::: command
radial_color \<color-value\>

Specifies the color of the radial lines.

**Default** white
:::

::: command
radial_width \<integer\>

Specifies the line width of the radial lines.

**Default** 1
:::

::: command
center_angle \<angle-value\>

Specifies the zero-angle offset of the ring (relative to the attached entity\'s heading).

**Default** 0.0 degrees
:::

::: command
angle_width \<angle-value\>

Specifies the span of the ring.

**Default** 360 degrees
:::

::: command
radial_rate \<angle-value\>

Specifies the rate at which radial lines will be drawn.

**Default** 0 degrees
:::

::: command
align_north

By default, the zero-angle offset of the ring is relative to the entity\'s heading. This flag aligns it with true north instead.
:::
