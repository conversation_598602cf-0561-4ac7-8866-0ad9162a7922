# 增强版AFSIM Platform Agent

一个结合LangChain、记忆功能和智能对话的AFSIM平台脚本生成助手，既能问答解释，又能生成专业的脚本代码。

## 🚀 新增功能

### 1. 智能对话系统
- **LangChain集成**: 使用LangChain框架实现智能对话
- **记忆功能**: 保持对话上下文，支持多轮对话
- **工具调用**: 自动选择合适的工具来回答问题

### 2. 专业脚本生成
- **F-22 Raptor**: 完整的F-22隐形战斗机脚本
- **F-35 Lightning II**: F-35多用途战斗机脚本
- **B-2 Spirit**: B-2隐形轰炸机脚本
- **自定义配置**: 支持用户自定义参数

### 3. 智能问答
- **参数解释**: 详细解释AFSIM平台参数含义
- **最佳实践**: 提供配置建议和优化方案
- **错误诊断**: 帮助解决配置问题

## 📦 安装依赖

```bash
# 安装增强版依赖
pip install langchain langchain-openai langchain-community langchain-core
pip install httpx pydantic python-dotenv

# 或者安装完整依赖
pip install -r requirements.txt
```

## 🔧 环境配置

```bash
# 设置DeepSeek API密钥
export DEEPSEEK_API_KEY="your_deepseek_api_key"
```

## 🎯 快速开始

### 1. 智能对话模式

```bash
python enhanced_main.py --mode interactive
```

### 2. F-22脚本生成器

```bash
python examples/f22_script_generator.py
```

### 3. Python API使用

```python
from core.enhanced_platform_agent import EnhancedPlatformAgent

# 初始化增强版Agent
agent = EnhancedPlatformAgent()
agent.initialize()

# 智能对话
response = agent.chat("F-22的platform脚本应该怎么写？")
print(response)

# 生成F-22脚本
f22_script = agent.generate_f22_script({
    "platform_name": "f22_alpha",
    "altitude": 18000,
    "position": {"lat": 35.0, "lon": -118.0}
})
print(f22_script)
```

## 💡 使用示例

### 问答示例

**用户**: "platform命令有哪些主要参数？"

**助手**: 根据AFSIM文档，platform命令的主要参数包括：

1. **基础参数**:
   - `platform_name`: 平台实例的唯一标识符
   - `platform_type`: 平台类型定义
   - `side`: 阵营设置（BLUE/RED等）

2. **位置参数**:
   - `position`: 纬度和经度坐标
   - `altitude`: 高度设置
   - `heading`: 航向角度

3. **物理参数**:
   - `empty_mass`: 空重
   - `fuel_mass`: 燃料重量
   - `length/width/height`: 尺寸参数

4. **特征参数**:
   - `radar_signature`: 雷达特征
   - `infrared_signature`: 红外特征
   - `spatial_domain`: 空间域（air/land/sea等）

### 脚本生成示例

**用户**: "生成一个F-22战斗机的脚本"

**助手**: 生成的AFSIM平台脚本:

```
platform f22_raptor WSF_PLATFORM
    side BLUE
    spatial_domain air
    position 35.0n 118.0w
    altitude 15000 ft msl
    heading 0 deg
    
    empty_mass 19700 kg
    fuel_mass 8200 kg
    
    length 18.9 m
    width 13.6 m  
    height 5.1 m
    
    radar_signature f22_rcs
    infrared_signature f22_ir
    
    icon F22
    marking "F-22 Raptor"
end_platform
```

## 🛠️ 高级功能

### 1. 对话记忆管理

```python
# 清空记忆
agent.clear_memory()

# 保存对话历史
agent.save_conversation("conversation.json")

# 加载对话历史
agent.load_conversation("conversation.json")
```

### 2. 平台模板系统

```python
# 获取可用模板
templates = agent.list_platform_templates()
print(templates)  # ['F22', 'F35', 'B2']

# 获取特定模板
f22_template = agent.get_platform_template("F22")
print(f22_template)
```

### 3. 参数解释功能

```python
# 解释特定参数
explanation = agent.explain_platform_parameters([
    "altitude", "radar_signature", "spatial_domain"
])
print(explanation)
```

## 🎮 交互命令

在交互模式下可使用以下命令：

- `help` - 显示帮助信息
- `status` - 显示Agent状态
- `clear` - 清空对话记忆
- `templates` - 显示可用平台模板
- `save <file>` - 保存对话历史
- `load <file>` - 加载对话历史
- `quit/exit/q` - 退出程序

## 📚 预定义平台模板

### F-22 Raptor
- **类型**: 隐形战斗机
- **空重**: 19,700 kg
- **燃料**: 8,200 kg
- **尺寸**: 18.9m × 13.6m × 5.1m
- **特点**: 超音速巡航、隐形设计

### F-35 Lightning II
- **类型**: 多用途战斗机
- **空重**: 13,300 kg
- **燃料**: 8,300 kg
- **尺寸**: 15.7m × 10.7m × 4.4m
- **特点**: 短距起降、多任务能力

### B-2 Spirit
- **类型**: 隐形轰炸机
- **空重**: 71,700 kg
- **燃料**: 90,700 kg
- **尺寸**: 21.0m × 52.4m × 5.2m
- **特点**: 远程打击、隐形设计

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   export DEEPSEEK_API_KEY="your_correct_api_key"
   ```

2. **依赖包缺失**
   ```bash
   pip install -r requirements.txt
   ```

3. **向量数据库未初始化**
   ```bash
   python main.py index docs/ --force
   ```

## 📈 性能优化

- 使用本地嵌入模型减少API调用
- 批量处理文档索引
- 智能缓存查询结果
- 异步处理提升响应速度

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License
