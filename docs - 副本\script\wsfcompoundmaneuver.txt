# wsfcompoundmaneuver.rst.txt.md
orphan

:   



::: {.WsfCompoundManeuver .inherits .WsfOrbitalManeuver}
Input type: `compound<../compound>`{.interpreted-text role="doc"}
:::

[WsfCompoundManeuver](#wsfcompoundmaneuver) can be used to specify two maneuvers that should be executed nearly simultaneously.

::: method
static WsfCompoundManeuver Construct()

Construct a new [WsfCompoundManeuver](#wsfcompoundmaneuver).
:::

::: method
void AddManeuver(WsfOrbitalManeuver aManeuver)

Add a maneuver to the list of maneuvers to be executed.
:::