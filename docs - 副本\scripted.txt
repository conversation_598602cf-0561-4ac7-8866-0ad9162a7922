# scripted.rst.txt.md
orphan

:   



Script Type: `WsfOrbitalScriptEvent`{.interpreted-text role="class"}

::: parsed-literal

`event<orbital_event>`{.interpreted-text role="doc"} scripted

:   `<Common Mission Event Commands><orbital_event.common_commands>`{.interpreted-text role="ref"} [on_initialize]() \... end_on_initialize [on_update]() \... end_on_update [on_complete]() \... end_on_complete [is_complete]() \... end_is_complete

end_event
:::

Perform a set of scripted operations. Scripts may be defined completely within each block, or they may reference scripts defined elsewhere (such as on the `WSF_SPACE_MOVER`{.interpreted-text role="model"}, the parent platform, or at global scope).

::: note
::: title
Note
:::

Scripts that invoke other mission events can only be called from the [on_complete]() script, as doing so would invalidate the current event from which the script is being called.
:::

::: command
on_initialize \...script_body\... end_on_initialize

Define a script that is executed when the event is initialized. This initialization occurs when the mission event is scheduled, which is either when the mission sequence is scheduled for the first event of a mission sequence, or immediately upon completion of the previous event of a mission sequence.

::: parsed-literal

on_initialize

:   `Script Body <script>`{.interpreted-text role="ref"}

end_on_initialize
:::
:::

::: command
on_update \...script_body\... end_on_update

Define a script that executes with the `update_interval<orbital_event.common_commands>`{.interpreted-text role="ref"} and `duration<orbital_event.common_commands>`{.interpreted-text role="ref"} of the event, starting from the time at which the event\'s constraint is first met. If *duration* is not defined the script is always executed once. If the [is_complete]() script is defined and the event is `finite<orbital_event.common_commands>`{.interpreted-text role="ref"}, it is also updated at subsequent constraint times, until the [is_complete]() script returns *true*. Alternately, if `duration<orbital_event.common_commands>`{.interpreted-text role="ref"} is defined, the event will execute at the specified `update_rate<orbital_event.common_commands>`{.interpreted-text role="ref"} for the given duration (see examples, below).

::: parsed-literal

on_update

:   `Script Body <script>`{.interpreted-text role="ref"}

end_on_update
:::
:::

::: command
on_complete \...script_body\... end_on_complete

Define a script that executes on completion of a mission event.

::: parsed-literal

on_complete

:   `Script Body <script>`{.interpreted-text role="ref"}

end_on_complete
:::
:::

::: command
is_complete \...script_body\... end_is_complete

Define a script that determines whether the script event is complete. If *false*, the event continues to execute; if *true*, the event is considered complete, and the [on_complete]() script is called. If defined, this script is called immediately after each event update, and it overrides any `duration<orbital_event.common_commands>`{.interpreted-text role="ref"} command.

::: note
::: title
Note
:::

This script must return a bool.
:::

::: parsed-literal

is_complete

:   `Script Body <script>`{.interpreted-text role="ref"}

end_is_complete
:::
:::



::: parsed-literal
// Example input for a scripted event within a WSF_SPACE_MOVER definition script_variables int gUpdateNum = 0; end_script_variables

script void DoSomething()

:   // Insert code here

end_script

mission_sequence

:   

    event scripted

    :   execute_at eclipse_entry update_interval 10 s finite // needed to execute multiple times on_initialize writeln(\"Initialized\"); end_on_initialize on_update gUpdateNum += 1; DoSomething(); writeln(\"Update \", gUpdateNum); end_on_update is_complete return (gUpdateNum == 100); // Execute 100 times end_is_complete on_complete writeln(\"Complete\"); end_on_complete

    end_event

end_mission_sequence
:::

::: parsed-literal
// Example input for a scripted event within a WSF_SPACE_MOVER definition // (same as above, using duration instead of is_complete script) script void DoSomething() // Insert code here end_script

mission_sequence

:   

    event scripted

    :   execute_at eclipse_entry update_interval 10 s duration 990 s // Execute 100 times on_initialize writeln(\"Initialized\"); end_on_initialize on_update static int sUpdateNum = 0; sUpdateNum += 1; DoSomething(); writeln(\"Update \", sUpdateNum); end_on_update on_complete writeln(\"Complete\"); end_on_complete

    end_event

end_mission_sequence
:::