# AFSIM Platform Agent - LLM参数提取配置示例

# 向量数据库配置
vector_db:
  provider: "chroma"
  path: "data/chroma_db"
  collection_name: "afsim_platform_docs"
  embedding_model: "sentence-transformers/paraphrase-multilingual-mpnet-base-v2"
  embedding_model_path: "models/paraphrase-multilingual-mpnet-base-v2"
  chunk_size: 500
  chunk_overlap: 50
  similarity_threshold: 0.7

# 文档处理配置
document_processing:
  docs_path: "docs"
  supported_formats: ["md", "markdown"]
  max_file_size: 10485760  # 最大文件大小 (10MB)
  encoding: "utf-8"
  chunk_size: 500
  chunk_overlap: 50
  min_content_length: 50
  
  # 内容过滤配置
  filters:
    min_content_length: 50
    remove_code_blocks: false
    remove_tables: false
    remove_links: true

# LLM配置 - 用于智能参数提取
llm:
  provider: "deepseek"  # 支持: deepseek, openai, ollama
  
  # DeepSeek配置 (推荐)
  deepseek:
    api_key: ""  # 请在环境变量DEEPSEEK_API_KEY中设置，或直接填写
    base_url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    temperature: 0.1  # 较低的温度确保输出稳定
    max_tokens: 4000
    
  # OpenAI配置 (备用)
  openai:
    api_key: ""  # 请在环境变量OPENAI_API_KEY中设置
    base_url: "https://api.openai.com/v1"
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 4000
    
  # Ollama配置 (本地部署)
  ollama:
    base_url: "http://localhost:11434"
    model: "llama3"
    temperature: 0.1

# 参数提取配置
parameter_extraction:
  # 提取方法: "auto", "llm", "regex"
  # auto: 如果LLM可用则使用LLM，否则使用regex
  # llm: 强制使用LLM方法
  # regex: 强制使用传统正则表达式方法
  method: "auto"
  
  # LLM参数提取配置
  llm_extraction:
    max_content_length: 8000  # 单次处理的最大内容长度
    retry_on_failure: true    # 失败时是否回退到regex方法
    extract_default_values: true   # 是否提取默认值
    extract_examples: true         # 是否提取示例
    extract_requirements: true     # 是否提取必需性信息
    
  # 正则表达式参数提取配置
  regex_extraction:
    patterns:
      # 基本参数模式
      - pattern: '\[([a-zA-Z_][a-zA-Z0-9_]*)\]\(\)\s*\.{3}'
        description: "基本参数模式: [parameter_name]() ..."
      # 命令模式
      - pattern: '([a-zA-Z_][a-zA-Z0-9_]*)\s+\.{3}\s+end_\1'
        description: "命令模式: parameter_name ... end_parameter"
      # 脚本类模式
      - pattern: '`(Wsf[A-Z][a-zA-Z]*)`'
        description: "脚本类模式: WsfPlatform"
      # 属性模式
      - pattern: 'platform\.([a-zA-Z_][a-zA-Z0-9_]*)'
        description: "属性模式: platform.property"
    
    context_window: 200  # 参数上下文窗口大小

# 翻译和总结配置
translation:
  enable_translation: true
  target_language: "zh"
  source_language: "en"
  
  summary:
    max_length: 500
    min_length: 100
    enable_bullet_points: true

# 平台生成配置
platform_generation:
  default_params:
    platform_type: "WSF_PLATFORM"
    side: "BLUE"
    altitude: 0.0
    heading: 0.0
    speed: 0.0
    
  validation:
    required_params: ["platform_name", "position"]
    
  templates:
    basic: "templates/basic_platform.template"
    advanced: "templates/advanced_platform.template"

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/afsim_agent.log"
  max_size: "10MB"
  backup_count: 5

# 性能配置
performance:
  max_concurrent_extractions: 3  # 最大并发提取数
  cache_extracted_parameters: true  # 是否缓存提取的参数
  cache_ttl: 3600  # 缓存过期时间（秒）
