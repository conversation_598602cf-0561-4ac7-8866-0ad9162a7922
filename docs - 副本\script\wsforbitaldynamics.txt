# wsforbitaldynamics.rst.txt.md
orphan

:   





The `WsfOrbitalDynamics`{.interpreted-text role="class"} represents the chosen dynamics for a `WsfIntegratingSpaceMover`{.interpreted-text role="class"}. A dynamics is conceptually a series of `WsfOrbitalDynamicsTerm`{.interpreted-text role="class"} objects, each of which contributes to the total acceleration.



::: method
int NumTerms()

Return the number of `WsfOrbitalDynamicsTerm`{.interpreted-text role="class"} objects that make up these dynamics.
:::

::: method
WsfOrbitalDynamicsTerm Term(int aIndex)

Return the term at the given index. The provided index should be in the range \[0, N), where N is the return value from `WsfOrbitalDynamics.NumTerms`{.interpreted-text role="method"}.
:::

::: method
void AddTerm(WsfOrbitalDynamicsTerm aTerm)

Add the given term to these dynamics.
:::

::: method
bool RemoveTerm(int aIndex)

Remove the term at the given index. If the term is successfully removed, this will return true. If there is some problem, such as the provided index being out of range, this will return false.
:::

::: method
Vec3 ComputeAcceleration(double aMass, Calendar aTime, Vec3 aPosition, Vec3 aVelocity)

Compute the acceleration for these dynamics on an object with the given mass, position and velocity at the given time.
:::