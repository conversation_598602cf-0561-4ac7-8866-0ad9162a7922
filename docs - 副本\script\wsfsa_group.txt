# wsfsa_group.rst.txt.md
orphan

:   





The `WsfSA_Group`{.interpreted-text role="class"} provides a representation of a perceived group, which may contain a list of `WsfSA_EntityPerception`{.interpreted-text role="class"} that make up the \"elements\" of the group. A list of the perceived `WsfSA_Group`{.interpreted-text role="class"} objects is provided by the `WsfSA_Processor`{.interpreted-text role="class"}, which provides the situation awareness component of the cognitive model. The `WsfSA_Processor`{.interpreted-text role="class"} also provides lists of groups prioritized by user-defined threat level or target value.

Groups may contain bogies or bandits \-- known friendly or neutral `WsfSA_EntityPerception`{.interpreted-text role="class"} objects may not be a member of a group, and if an element of the group is later found to be a friendly or neutral, it will immediately be dropped from the group.

Perceived groups include a centroid for the group, which is based on the locations of the elements of the group. If all elements of the group are removed, the group will be removed.

All groups are owned by a `WsfSA_Processor`{.interpreted-text role="class"}. Additionally, they can only be created or modified by calling group functions on the owning `WsfSA_Processor`{.interpreted-text role="class"}.



::: method
string GroupName()

Returns the name of the group.
:::

::: method
double CentroidLat()

Returns the group\'s centroid latitude (deg).
:::

::: method
double CentroidLon()

Returns the group\'s centroid longitude (deg).
:::

::: method
double CentroidAlt()

Returns the group\'s centroid altitude (meters).
:::

::: method
double CentroidHeading()

Returns the group\'s centroid heading (deg).
:::

::: method
double CentroidSpeed()

Returns the group\'s centroid speed (meters/sec).
:::

::: method
double RadiusCurrent()

Returns the current radius of the group (meters).
:::

::: method
double RadiusMinimum()

Returns the minimum radius of the group (meters).
:::

::: method
double LastUpdateTime()

Returns the last update time (sec) of the group.
:::

::: method
int NumElements()

Returns the number of elements (`WsfSA_EntityPerception`{.interpreted-text role="class"}) in the group.
:::

::: method
Array\<WsfSA_EntityPerception\> ElementList()

Returns an array of `WsfSA_EntityPerception`{.interpreted-text role="class"} elements from the group list.
:::

::: method
bool GetFocus()

Returns true if the group is focused, false otherwise.
:::

::: method
void SetElementProtection(WsfSA_Group aGroup, bool aIsProtected)

Sets the element protection status of aGroup. When a group is element protected, it will not be automatically split or merged with another group.
:::

::: method
void SetFocusProtection(WsfSA_Group aGroup, bool aIsProtected)

Sets the focus protection status of aGroup. When a group is focus protected, its focus will not be automatically changed unless there is no other option. A group being marked as important supercedes this.
:::

::: method
bool GetElementProtection(WsfSA_Group aGroup)

Gets the element protection status of aGroup.
:::

::: method
bool GetFocusProtection(WsfSA_Group aGroup)

Gets the focus protection status of aGroup.
:::