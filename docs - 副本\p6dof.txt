# p6dof.rst.txt.md
orphan

:   

::: demo
p6dof
:::

| This is a collection of demos that utilize WSF Pseudo Six-Degrees-of-Freedom (P6DOF) mover objects. The goal of these demos is to show features and capabilities of the P6DOF mover and how they can be used in other scenarios.
| 
| The collection also includes some simple support scenarios to help create P6DOF models and analyze their performance.



| This scenario demonstrates five examples of bombing using P6DOF movers, including the use of sequencers to drop bombs.
| 
| The first example uses a heavy bomber (Rolling Thunder) at very low altitude that drops a series of high-drag bombs to destroy a runway.
| 
| The second example uses a heavy bomber (Arc Light) at high altitude that drops a series of low-drag bombs to destroy another runway. This aircraft is in the contrail band and will produce contrails in the Warlock and Mystic tools.
| 
| The third example uses a flying-wing bomber (Ghost) at medium altitude that drops a series of guided-bombs (representing GPS-guided bombs) that use pre-briefed tracks to attack point targets at the air base.
| 
| The fourth example uses a tactical fighter (Viper 1) to drop low-drag bombs using a dive bombing approach. This shows the use of a single, nested sequencer to drop multiple bombs with a single call.
| 
| The fifth (and last) example also uses a tactical fighter (Viper 2) to drop low-drag bombs with a dive bombing approach. In this case, however, it uses individual sequencers to drop bombs individually.



| This scenario demonstrates full flight operations including taxi, takeoff, script-commanded maneuvers, route following, approach, landing/touchdown, roll-out, and taxi to the ramp.
| 
| Two aircraft begin the demo parked in the Quick Reaction Alert (QRA) area at Honolulu International Airport. The lead aircraft begins to taxi and pauses to test brakes. The leader then taxis to the runway, with the wingman taxiing slightly behind. The leader lines up on the left side of the runway and the wingman lines up on the right side. A formation takeoff (simulated) is performed with a steep climb-out. The aircraft then perform a route orbit.
| 
| The leader then descends and makes an approach and landing with the wingman slightly behind. Both aircraft taxi back to the ramp and park.
| 
| This scenario demonstrates the ability of P6DOF to support a realistic scramble (and recovery) by two interceptors.



| This scenario demonstrates the use of four air-launched cruise missiles from a heavy bomber.
| 
| The scenario begins with the bomber avoiding detection by flying a low-level (200 ft) profile. It then pops up to 1000 ft and launches four cruise missiles, with a five second interval between each missile.
| 
| The missiles each fly several waypoints en-route to their targets, with all impacting nearly simultaneously (all detonating within less than a 2 sec period).
| 
| Each cruise missile also performs a pitch-up terminal maneuver, which makes them a more difficult target for AAA.



| This scenario demonstrates various features of the formation capability supported by the WSF_P6DOF_MOVER. This scenario involves one tanker and a four-ship formation that takes on fuel before heading off toward their mission.
| 
| This scenario demonstrates the formation command system, the two kinds of station keeping available, and the flexibility of the defined formations.



| This scenario demonstrates several missile engagements.
| 
| The first engagement, south of Oahu, involves two short-range (SR) air-to-air missiles (AAMs). The blue fighter fires two SR-AAMs at the red target aircraft. These include a nominal SR-AAM configuration along with a variant that uses thrust-vector control (TVC). This direct comparison dramatically shows how TVC can increase the agility of a missile, and how P6DOF movers can perform high-alpha maneuvers that 3DOF guided movers cannot emulate.
| 
| The second engagement, also in the south, involves a medium-range (MR) air-to-air missile. The same blue fighter fires a MR-AAM at another red target aircraft. Note that the MR-AAM uses a loft maneuver to increase its range.
| 
| The third engagement, in the northwest, involves a surface-to-air missile (SAM). A red SAM detects an aircraft, but does not identify it. The red rules of engagement (ROE) allow engagement of any aircraft in its sector, so a SAM is launched. The missile guides to its target, which was actually \"white air\" \-- a commercial airliner.



| This scenario demonstrates the hierarchical maneuver capability available for P6DOF movers.
| 
| A maneuver can be formed of other more elementary maneuvers which, put together, can lead to complex behaviors. This demonstrates the technique of using a library of maneuvers that can then be used in many different situations.



| This scenario demonstrates the basic use of formations for platforms with P6DOF movers. This covers formation creation via both input and script, as well as the formation command system.



| This scenario presents a range of tactical maneuvers that P6DOF aircraft movers can perform. The demo includes two aircraft.
| 
| The first aircraft (red) performs a series of maneuvers. The second aircraft (blue) pursues the first aircraft.



| This scenario is an alternate take on TacticalManevuers.txt.
| 
| This version of the tactical maneuvers demo uses WsfManeuver objects to produce the same sequence of maneuvers as in the original demo.



| This scenario demonstrates the hierarchical maneuver capability now available for P6DOF movers.
| 
| A maneuver can be formed of other more elementary maneuvers which put together can lead to complex behaviors. This demonstrates the technique of using a library of maneuvers that can then be used in many different situations.



| In this scenario, an FA-LGT aircraft is launched using a Zero Length Launch (ZELL) technique. ZELL was developed during the 1950\'s to allow aircraft to takeoff without the use of a runway.
| ZELL involves attaching a large solid-propellant rocket to the bottom of a fighter. The rocket is ignited and provides thrust to accelerate the fighter to flying speed before the rocket is jettisoned.
| 
| This demo shows how a P6DOF sub-object can command itself to be launched from a parent object. The zell launch platform has a sub-object fighter of type FA-LGT-ZLL.
| 
| A sequencer on the fighter is activated 5 seconds after the FA-LGT-ZLL platform is instantiated. This sequencer ignites the attached rocket engine and separates the fighter from the launcher. Three seconds later, after the rocket burns out, the rocket is jettisoned from the fighter.



| This scenario demonstrates the use of unguided rockets using P6DOF movers.
| 
| An aircraft begins the demo in level flight and bunts over into a shallow dive, lines up on the target (tank) and fires a salvo of 16 rockets from a right and left \"weapon\", representing four quad-tubes under the wings. After firing the rockets, the aircraft pulls away and the rockets destroy the target.



| This scenario uses two aircraft flying the same route but the second aircraft has two 300 gallon external fuel tanks attached to it. Both aircraft have full internal fuel and the second aircraft is adjusted to have 250 lbs of fuel in each external tank. Each aircraft maintains the same speed.
| 
| Even though the second aircraft has more fuel, the external fuel tanks (and fuel) results in additional drag and weight, causing it to burn more fuel to produce the additional thrust required. After 5 minutes, the second aircraft uses a sequencer to drop the external tanks, reducing the drag and weight on the aircraft. The tanks then fall realistically down to the ground. After roughly 57 minutes, both aircraft are nearly out of fuel, but the first aircraft has more fuel.



| This scenario uses two aircraft flying straight and level at 10000ft. One aircraft has 5000 lbs of internal fuel while the second aircraft has 5000 lbs fuel internally plus another 4400lbs of fuel in two external fuel tanks.
| 
| The aircraft fly until they run out of fuel, then data is output including the range and endurance of each flight.



| This scenario shows how differently a p6dof mover will fly as compared to an air mover. In this demo, there is one air mover and three p6dof movers, with all aircraft flying the same route, but with different bank angle limits and with \"follow_vertical_track\" enabled/disabled.
| 
| The red aircraft (P6DOF Mover 1) has no max bank angle limits set and has follow_vertical_track set.
| The blue aircraft (P6DOF Mover 2) has max bank angle limits set for some turns and has follow_vertical_track set.
| The yellow aircraft (P6DOF Mover 3) has max bank angle limits set for some turns and does not follow_vertical_track set.
| The green aircraft is an Air Mover.



| This scenario is intended to provide a rough approximation of level flight range for P6DOF AAM models during model file creation. The is accomplished by controlling the angle of attack (alpha) of the missile to make it fly level. No autopilot/guidance object is required. When the minimum termination speed is reached, details about flight time and range will be provided.
| 
| To use this scenario, simply change the included model file path and the MOVER_TYPE to the desired model.



| This scenario shows a medium range Surface-to-Air Missile (SAM) launched at multiple launch angles. The missiles fly a ballistic trajectory (no autopilot/guidance controls). The maximum range launch angle will be computed and displayed along with other data about the max range flight.
| 
| This scenario can be modified to test other missiles. Simply change the included model file path and the MOVER_TYPE to the desired model.



| This scenario is a simple ballistic missile demo. It presents a three stage ICBM complete with interstage pieces, nosecone, simple/generic decoys, and a reentry vehicle. This demo does not include any guidance \-- it is merely launched with a fixed tilt (pitch) angle. It demonstrates the ability to create a ballistic missile with intercontinental range that performs realistic staging events and trajectory.



| This demo generates a flight envelope for a P6DOF model. (Flight envelopes are often referred to by using the slang term, \"dog house plot\".)
| 
| A flight envelope shows the minimum and maximum speed that the aircraft can fly under sustained conditions as a function of altitude. Flight envelopes may be generated under different conditions, such as maximum (military) power, augmented (afterburner) power, or other conditions such as flap settings or customized weapons/fuel load-outs (external fuel tanks and weapons will create drag and all weapons and fuel will effect mass properties, such as weight and rotational inertia as well as center-of-gravity). P6DOF models consider all of these effects in their kinematic calculations.
| 
| This test is set up to use the FA_LGT model, but can be easily changed to use another model by changing the include model type and the MOVER_TYPE variable.
| 
| This test will generate up to three different files:
| 
| MilEnvelope.csv : CSV file which represents the flight envelope for the model using MIL power.
| 
| ABEnvelope.csv : CSV file which represents the flight envelope for the model using AB power.
| 
| DotPlot.csv : CSV file that contains three columns of data. One includes points at which the aircraft can fly and be controlled, a second column for points with the aircraft can fly but can not be controlled, and a final column for points where the aircraft can not fly. When these three columns are put on a scatter plot as different series, the plot shows information similar to the flight envelope.



| This scenario determines the maximum range and maximum flight time (endurance) for a P6DOF aircraft.
| 
| This test is set up to use the FA_LGT model, but can be easily changed to use another model by changing the include model type and the MOVER_TYPE variables.
| 
| This test can execute a single run at a given altitude and speed, or execute multiple runs at a variety of altitudes and/or speeds. This is controlled by modifying the variables in the script_variables block.



| This scenario tests the system performance as P6DOF entities run in order to estimate the maximum number of P6DOF entities that the system can handle while maintaining realtime performance.



| This scenario can be used to generate an autopilot_support_tables.txt file that is used with aircraft and missiles that use the P6DOF autopilot/guidance. The tabular data defines angle of attack (alpha) and lift coefficient (CL) limits as well a control data that is used for feed-forward (control bias) in the autopilot PID-based (Proportional, Integral, Derivative) feedback control system used in the P6DOF autopilot. This is helpful for users creating their own P6DOF models.
| 
| To use this scenario, change the included model file path and the MOVER_TYPE to the desired model, and run the scenario to generate the autopilot_support_tables.txt file. Copy the autopilot_support_tables.txt to model\'s directory.