# wsfcommmediummodeunguided.rst.txt.md
orphan

:   





This script class provides access to mode objects utilized by `WSF_COMM_MEDIUM_UNGUIDED`{.interpreted-text role="model"}.



::: method
bool GetUseXferRateTable()

Returns true if this mode is indicated to use a SNR transfer rate table when determining transmission rate. Return false, otherwise.
:::

::: method
double GetBitErrorProbability()

Returns the bit error probability rate associated with this mode.
:::

::: method
double GetErrorCorrection()

Returns the error correction value associated with this mode.
:::

::: method
bool GetUseBER_EbNoTable()

Returns true if this mode is indicated to use the bit error rate table for determining transmission rate. Returns false, otherwise.
:::