# l16_j11.rst.txt.md
orphan

:   

::: demo
l16_j11
:::

| This directory contains the files necessary to run a weapon server demo with Link-16 network-enabled weapons.
| 
| The jtids.txt file contains the JTIDS terminal and Link-16 computer definitions for both the weapon fighter platform and the network-enabled weapon.
| 
| The platform.txt file contains the network-enabled weapon definition and the fighter and weapon fighter definition.
| 
| For this scenario, three platforms are created a red fighter, a bogus point, and a blue weapon fighter (see j11_out.txt). At T=15s, a network-enabled weapon is fired. The blue weapon fighter then begins sending J11.1 In-Flight Target Updates (IFTUs) to the weapon.
| 
| The weapon server is defined in the j11_in.txt file. The client is defined in the j11_out.txt file.
| 
| Steps to run:
| \* Ensure the DIS multicast address and port are acceptable (see dis_network.txt line 38).
| \* Update the IP address for the weapon server (see j11_out.txt line 103).
| \* If the port is changed, it must be modified on the server side as well (see j11_in.txt line 76).
| \* Open two command prompt windows or two instances of Wizard.
| \* If using <PERSON>, set the j11_in.txt file as the startup file in one instance and the j11_out.txt as the startup in the other instance.
| \* Start the weapon server first (j11_in.txt).
| \* Start the client (j11_out.txt).
| 
| If viewing in a DIS viewer the weapon is launched and immediately turns toward the red fighter if it is receiving IFTUs from the client-side blue fighter. If there are issues with the connection settings the weapon will fly in a straight line until detonating due to max time of flight exceeded.