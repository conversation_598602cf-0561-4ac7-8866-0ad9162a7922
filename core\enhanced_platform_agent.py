"""
简化版AFSIM Platform Agent
专注于脚本解释、参数查询和脚本生成
"""

import os
import json
import yaml
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

# 本地模块
from .platform_agent import AFSIMPlatformAgent
from .chinese_summarizer import ChineseSummarizer

logger = logging.getLogger(__name__)


class ScriptExplainer:
    """AFSIM脚本解释器"""

    def __init__(self, agent: AFSIMPlatformAgent):
        self.agent = agent
        self.summarizer = ChineseSummarizer()

    def explain_script(self, script_content: str) -> str:
        """解释AFSIM脚本内容"""
        try:
            # 解析脚本结构
            parsed_info = self._parse_script_structure(script_content)

            # 生成解释
            explanation = self._generate_explanation(parsed_info)

            return explanation

        except Exception as e:
            logger.error(f"脚本解释失败: {e}")
            return f"脚本解释失败: {str(e)}"

    def _parse_script_structure(self, script: str) -> Dict[str, Any]:
        """解析脚本结构"""
        info = {
            "platform_name": "",
            "platform_type": "",
            "parameters": {},
            "components": [],
            "structure": []
        }

        lines = script.strip().split('\n')
        current_block = None
        indent_level = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 解析platform声明
            if line.startswith('platform '):
                parts = line.split()
                if len(parts) >= 3:
                    info["platform_name"] = parts[1]
                    info["platform_type"] = parts[2]
                info["structure"].append({"type": "platform_start", "content": line})

            # 解析参数
            elif any(line.startswith(param) for param in ['side ', 'icon ', 'position ', 'altitude ', 'speed ']):
                param_parts = line.split(' ', 1)
                if len(param_parts) == 2:
                    info["parameters"][param_parts[0]] = param_parts[1]
                info["structure"].append({"type": "parameter", "content": line})

            # 解析组件
            elif line.startswith('add ') or line.startswith('mover '):
                current_block = line
                info["components"].append(line)
                info["structure"].append({"type": "component_start", "content": line})

            elif line.startswith('end_'):
                info["structure"].append({"type": "component_end", "content": line})
                current_block = None

            else:
                info["structure"].append({"type": "content", "content": line})

        return info

    def _generate_explanation(self, parsed_info: Dict[str, Any]) -> str:
        """生成脚本解释"""
        explanation = f"## AFSIM平台脚本解释\n\n"

        # 基本信息
        explanation += f"**平台名称**: {parsed_info['platform_name']}\n"
        explanation += f"**平台类型**: {parsed_info['platform_type']}\n\n"

        # 参数解释
        if parsed_info['parameters']:
            explanation += "### 主要参数:\n"
            for param, value in parsed_info['parameters'].items():
                param_explanation = self._explain_parameter(param, value)
                explanation += f"- **{param}**: {value}\n  {param_explanation}\n\n"

        # 组件解释
        if parsed_info['components']:
            explanation += "### 组件配置:\n"
            for component in parsed_info['components']:
                comp_explanation = self._explain_component(component)
                explanation += f"- {component}\n  {comp_explanation}\n\n"

        return explanation

    def _explain_parameter(self, param: str, value: str) -> str:
        """解释参数含义"""
        explanations = {
            "side": "指定平台所属阵营，通常为BLUE（蓝方）或RED（红方）",
            "icon": "指定平台在可视化界面中显示的图标",
            "position": "设置平台的地理位置坐标（纬度、经度）",
            "altitude": "设置平台的飞行高度",
            "speed": "设置平台的移动速度",
            "heading": "设置平台的航向角度"
        }
        return explanations.get(param, "参数说明暂未收录")

    def _explain_component(self, component: str) -> str:
        """解释组件含义"""
        if "WSF_FORMATION_FLYER" in component:
            return "编队飞行器组件，用于实现编队飞行功能"
        elif "mover" in component:
            return "移动器组件，控制平台的运动行为"
        elif "sensor" in component:
            return "传感器组件，用于探测和跟踪目标"
        elif "weapon" in component:
            return "武器组件，用于攻击目标"
        else:
            return "平台组件，提供特定功能"


class SimplePlatformAgent:
    """简化版AFSIM Platform Agent"""

    def __init__(self, config_path: str = None):
        """初始化简化版Agent"""
        self.config_path = config_path
        self.config = self._load_config()

        # 初始化基础Agent
        self.base_agent = AFSIMPlatformAgent(config_path)

        # 初始化脚本解释器
        self.script_explainer = None

        # 初始化中文总结器
        self.summarizer = None

        # 对话历史
        self.conversation_history = []

        self.is_initialized = False

        # 预定义的飞机数据库
        self.aircraft_database = self._load_aircraft_database()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        if self.config_path and Path(self.config_path).exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)

        # 默认配置
        return {
            "simple_agent": {
                "max_history": 20,
                "enable_explanation": True
            }
        }

    def _load_aircraft_database(self) -> Dict[str, Dict]:
        """加载飞机数据库"""
        # 从文件加载数据库，如果文件不存在则创建默认数据
        db_file = Path("data/aircraft_database.json")

        if db_file.exists():
            try:
                with open(db_file, 'r', encoding='utf-8') as f:
                    aircraft_db = json.load(f)
                logger.info(f"从文件加载飞机数据库，共 {len(aircraft_db)} 种飞机")
                return aircraft_db
            except Exception as e:
                logger.error(f"加载飞机数据库失败: {e}")

        # 默认数据库
        aircraft_db = {
            "F-18": {
                "name": "F/A-18 Hornet",
                "type": "多用途战斗机",
                "country": "美国",
                "manufacturer": "麦克唐纳·道格拉斯/波音",
                "first_flight": "1978",
                "length": 17.1,
                "wingspan": 11.4,
                "height": 4.7,
                "empty_weight": 10455,
                "max_takeoff_weight": 25401,
                "fuel_capacity": 4900,
                "max_speed": 1915,
                "service_ceiling": 15240,
                "combat_radius": 722,
                "crew": 1,
                "engines": "2 × General Electric F404-GE-402",
                "armament": [
                    "1 × 20mm M61A1 Vulcan机炮",
                    "AIM-9 Sidewinder空对空导弹",
                    "AIM-7 Sparrow空对空导弹",
                    "AGM-65 Maverick空对地导弹"
                ],
                "avionics": [
                    "AN/APG-73雷达",
                    "前视红外系统(FLIR)",
                    "电子战系统"
                ],
                "source": "database"
            },
            "F-22": {
                "name": "F-22 Raptor",
                "type": "隐形战斗机",
                "country": "美国",
                "manufacturer": "洛克希德·马丁",
                "first_flight": "1997",
                "length": 18.9,
                "wingspan": 13.6,
                "height": 5.1,
                "empty_weight": 19700,
                "max_takeoff_weight": 38000,
                "fuel_capacity": 8200,
                "max_speed": 2410,
                "service_ceiling": 19812,
                "combat_radius": 852,
                "crew": 1,
                "engines": "2 × Pratt & Whitney F119-PW-100",
                "armament": [
                    "1 × 20mm M61A2 Vulcan机炮",
                    "AIM-120 AMRAAM空对空导弹",
                    "AIM-9 Sidewinder空对空导弹"
                ],
                "avionics": [
                    "AN/APG-77 AESA雷达",
                    "AN/ALR-94电子战系统",
                    "综合航电系统"
                ],
                "source": "database"
            },
            "F-35": {
                "name": "F-35 Lightning II",
                "type": "多用途隐形战斗机",
                "country": "美国",
                "manufacturer": "洛克希德·马丁",
                "first_flight": "2006",
                "length": 15.7,
                "wingspan": 10.7,
                "height": 4.4,
                "empty_weight": 13300,
                "max_takeoff_weight": 31800,
                "fuel_capacity": 8300,
                "max_speed": 1930,
                "service_ceiling": 15240,
                "combat_radius": 1135,
                "crew": 1,
                "engines": "1 × Pratt & Whitney F135",
                "armament": [
                    "1 × 25mm GAU-22/A机炮",
                    "AIM-120 AMRAAM空对空导弹",
                    "AIM-9X Sidewinder空对空导弹"
                ],
                "avionics": [
                    "AN/APG-81 AESA雷达",
                    "分布式孔径系统(DAS)",
                    "电光瞄准系统(EOTS)"
                ],
                "source": "database"
            }
        }

        # 保存默认数据库
        self._save_aircraft_database(aircraft_db)
        return aircraft_db

    def _save_aircraft_database(self, aircraft_db: Dict[str, Dict]):
        """保存飞机数据库到文件"""
        try:
            db_file = Path("data/aircraft_database.json")
            db_file.parent.mkdir(parents=True, exist_ok=True)

            with open(db_file, 'w', encoding='utf-8') as f:
                json.dump(aircraft_db, f, ensure_ascii=False, indent=2)

            logger.info(f"飞机数据库已保存，共 {len(aircraft_db)} 种飞机")

        except Exception as e:
            logger.error(f"保存飞机数据库失败: {e}")

    def _query_aircraft_from_llm(self, aircraft_name: str) -> Optional[Dict]:
        """使用大模型查询飞机信息"""
        try:
            if not hasattr(self, 'summarizer') or not self.summarizer:
                logger.warning("中文总结器未初始化，无法使用大模型查询")
                return None

            prompt = f"""请提供 {aircraft_name} 军用飞机的详细技术参数，包括：
1. 基本信息：全名、类型、制造国、制造商、首飞时间
2. 尺寸参数：长度、翼展、高度（单位：米）
3. 重量参数：空重、最大起飞重量、燃料容量（单位：公斤）
4. 性能参数：最大速度（公里/小时）、实用升限（米）、作战半径（公里）、机组人员数
5. 动力系统：发动机型号和数量
6. 武器系统：主要武器装备（列表形式）
7. 航电系统：主要航电设备（列表形式）

请以JSON格式返回，字段名使用英文，值使用中文。如果某些参数不确定，请标注"未知"。
只返回JSON数据，不要其他说明文字。"""

            response = self.summarizer.summarize_text(prompt)

            # 尝试解析JSON
            try:
                # 提取JSON部分
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = response[json_start:json_end]
                    aircraft_data = json.loads(json_str)
                    aircraft_data['source'] = 'llm'
                    aircraft_data['query_time'] = datetime.now().isoformat()
                    return aircraft_data
                else:
                    logger.error("大模型返回的数据不包含有效JSON")
                    return None

            except json.JSONDecodeError as e:
                logger.error(f"解析大模型返回的JSON失败: {e}")
                logger.debug(f"原始响应: {response}")
                return None

        except Exception as e:
            logger.error(f"大模型查询飞机信息失败: {e}")
            return None

    def _add_aircraft_to_database(self, aircraft_name: str, aircraft_data: Dict):
        """将新飞机数据添加到数据库"""
        try:
            self.aircraft_database[aircraft_name.upper()] = aircraft_data
            self._save_aircraft_database(self.aircraft_database)
            logger.info(f"已将 {aircraft_name} 添加到飞机数据库")

        except Exception as e:
            logger.error(f"添加飞机到数据库失败: {e}")

    def get_aircraft_info_smart(self, aircraft_name: str) -> str:
        """智能获取飞机信息（优先数据库，其次大模型）"""
        aircraft_key = aircraft_name.upper()

        # 1. 首先检查数据库
        if aircraft_key in self.aircraft_database:
            aircraft_data = self.aircraft_database[aircraft_key]
            logger.info(f"从数据库获取 {aircraft_name} 信息")
            return self._format_aircraft_info(aircraft_data, f"数据来源: 本地数据库")

        # 2. 数据库中没有，使用大模型查询
        logger.info(f"数据库中未找到 {aircraft_name}，尝试使用大模型查询...")
        aircraft_data = self._query_aircraft_from_llm(aircraft_name)

        if aircraft_data:
            # 将大模型查询结果添加到数据库
            self._add_aircraft_to_database(aircraft_key, aircraft_data)
            return self._format_aircraft_info(aircraft_data, f"数据来源: 大模型查询并已入库")
        else:
            # 大模型查询也失败
            available_aircraft = ', '.join(self.aircraft_database.keys())
            return f"未找到 {aircraft_name} 的信息。\n\n可用飞机: {available_aircraft}\n\n您也可以尝试询问其他军用飞机，系统会自动查询并入库。"

    def _format_aircraft_info(self, aircraft_data: Dict, source_info: str = "") -> str:
        """格式化飞机信息显示"""
        try:
            info = f"## {aircraft_data.get('name', '未知飞机')} 技术参数\n\n"

            # 基本信息
            info += f"**基本信息**:\n"
            info += f"- 类型: {aircraft_data.get('type', '未知')}\n"
            info += f"- 制造国: {aircraft_data.get('country', '未知')}\n"
            info += f"- 制造商: {aircraft_data.get('manufacturer', '未知')}\n"
            info += f"- 首飞: {aircraft_data.get('first_flight', '未知')}\n\n"

            # 尺寸参数
            info += f"**尺寸参数**:\n"
            info += f"- 长度: {aircraft_data.get('length', '未知')} 米\n"
            info += f"- 翼展: {aircraft_data.get('wingspan', '未知')} 米\n"
            info += f"- 高度: {aircraft_data.get('height', '未知')} 米\n\n"

            # 重量参数
            info += f"**重量参数**:\n"
            info += f"- 空重: {aircraft_data.get('empty_weight', '未知')} 公斤\n"
            info += f"- 最大起飞重量: {aircraft_data.get('max_takeoff_weight', '未知')} 公斤\n"
            info += f"- 燃料容量: {aircraft_data.get('fuel_capacity', '未知')} 公斤\n\n"

            # 性能参数
            info += f"**性能参数**:\n"
            info += f"- 最大速度: {aircraft_data.get('max_speed', '未知')} 公里/小时\n"
            info += f"- 实用升限: {aircraft_data.get('service_ceiling', '未知')} 米\n"
            info += f"- 作战半径: {aircraft_data.get('combat_radius', '未知')} 公里\n"
            info += f"- 机组人员: {aircraft_data.get('crew', '未知')} 人\n\n"

            # 动力系统
            info += f"**动力系统**: {aircraft_data.get('engines', '未知')}\n\n"

            # 武器系统
            if aircraft_data.get('armament'):
                info += f"**武器系统**:\n"
                for weapon in aircraft_data['armament']:
                    info += f"- {weapon}\n"
                info += "\n"

            # 航电系统
            if aircraft_data.get('avionics'):
                info += f"**航电系统**:\n"
                for avionics in aircraft_data['avionics']:
                    info += f"- {avionics}\n"
                info += "\n"

            # 数据来源
            if source_info:
                info += f"**{source_info}**\n"

            return info

        except Exception as e:
            logger.error(f"格式化飞机信息失败: {e}")
            return f"格式化 {aircraft_data.get('name', '未知飞机')} 信息时出错"

    def initialize(self) -> bool:
        """初始化Agent"""
        try:
            logger.info("开始初始化简化版AFSIM Platform Agent...")

            # 初始化基础Agent
            if not self.base_agent.initialize():
                logger.error("基础Agent初始化失败")
                return False

            # 初始化脚本解释器
            self.script_explainer = ScriptExplainer(self.base_agent)

            # 初始化中文总结器（用于大模型查询）
            try:
                self.summarizer = ChineseSummarizer()
                logger.info("中文总结器初始化成功")
            except Exception as e:
                logger.warning(f"中文总结器初始化失败: {e}，将无法使用大模型查询新飞机")
                self.summarizer = None

            self.is_initialized = True
            logger.info("简化版AFSIM Platform Agent初始化完成")
            return True

        except Exception as e:
            logger.error(f"简化版Agent初始化失败: {e}")
            return False

    def chat(self, message: str) -> str:
        """处理用户消息"""
        try:
            if not self.is_initialized:
                return "Agent未初始化，请先调用initialize()方法"

            # 添加到对话历史
            self.conversation_history.append({"role": "user", "content": message})

            # 判断消息类型并处理
            response = self._process_message(message)

            # 添加回复到历史
            self.conversation_history.append({"role": "assistant", "content": response})

            # 保持历史长度
            max_history = self.config.get("simple_agent", {}).get("max_history", 20)
            if len(self.conversation_history) > max_history:
                self.conversation_history = self.conversation_history[-max_history:]

            return response

        except Exception as e:
            logger.error(f"对话处理失败: {e}")
            return f"处理失败: {str(e)}"

    def _process_message(self, message: str) -> str:
        """处理消息内容"""
        message_lower = message.lower()

        # 脚本解释
        if "platform " in message and ("end_platform" in message or "WSF_PLATFORM" in message):
            return self.explain_script(message)

        # 飞机参数查询
        elif any(aircraft.lower() in message_lower for aircraft in self.aircraft_database.keys()) or "参数" in message or "信息" in message:
            aircraft_name = self._extract_aircraft_name(message)
            if aircraft_name:
                return self.get_aircraft_info_smart(aircraft_name)
            # 如果没有匹配到已知飞机，尝试从消息中提取飞机名称
            else:
                potential_aircraft = self._extract_potential_aircraft_name(message)
                if potential_aircraft:
                    return self.get_aircraft_info_smart(potential_aircraft)

        # 脚本生成请求
        elif "生成" in message or "generate" in message_lower:
            aircraft_name = self._extract_aircraft_name(message)
            if aircraft_name:
                return self.generate_aircraft_script(aircraft_name)

        # 参数查询
        elif "参数" in message or "parameter" in message_lower:
            return self.query_parameters(message)

        # 默认处理
        else:
            return self.general_query(message)

    def explain_script(self, script_content: str) -> str:
        """解释AFSIM脚本"""
        if not self.script_explainer:
            return "脚本解释器未初始化"

        return self.script_explainer.explain_script(script_content)

    def get_aircraft_info(self, aircraft_name: str) -> str:
        """获取飞机信息"""
        aircraft_data = self.aircraft_database.get(aircraft_name.upper())
        if not aircraft_data:
            return f"未找到 {aircraft_name} 的数据，可用飞机: {', '.join(self.aircraft_database.keys())}"

        info = f"## {aircraft_data['name']} 技术参数\n\n"
        info += f"**基本信息**:\n"
        info += f"- 类型: {aircraft_data['type']}\n"
        info += f"- 制造国: {aircraft_data['country']}\n"
        info += f"- 制造商: {aircraft_data['manufacturer']}\n"
        info += f"- 首飞: {aircraft_data['first_flight']}\n\n"

        info += f"**尺寸参数**:\n"
        info += f"- 长度: {aircraft_data['length']} 米\n"
        info += f"- 翼展: {aircraft_data['wingspan']} 米\n"
        info += f"- 高度: {aircraft_data['height']} 米\n\n"

        info += f"**重量参数**:\n"
        info += f"- 空重: {aircraft_data['empty_weight']} 公斤\n"
        info += f"- 最大起飞重量: {aircraft_data['max_takeoff_weight']} 公斤\n"
        info += f"- 燃料容量: {aircraft_data['fuel_capacity']} 公斤\n\n"

        info += f"**性能参数**:\n"
        info += f"- 最大速度: {aircraft_data['max_speed']} 公里/小时\n"
        info += f"- 实用升限: {aircraft_data['service_ceiling']} 米\n"
        info += f"- 作战半径: {aircraft_data['combat_radius']} 公里\n"
        info += f"- 机组人员: {aircraft_data['crew']} 人\n\n"

        info += f"**动力系统**: {aircraft_data['engines']}\n\n"

        if aircraft_data.get('armament'):
            info += f"**武器系统**:\n"
            for weapon in aircraft_data['armament']:
                info += f"- {weapon}\n"
            info += "\n"

        if aircraft_data.get('avionics'):
            info += f"**航电系统**:\n"
            for avionics in aircraft_data['avionics']:
                info += f"- {avionics}\n"

        return info

    def generate_aircraft_script(self, aircraft_name: str) -> str:
        """生成飞机AFSIM脚本"""
        aircraft_data = self.aircraft_database.get(aircraft_name.upper())
        if not aircraft_data:
            return f"未找到 {aircraft_name} 的数据"

        # 构建配置
        config = {
            "platform_name": aircraft_name.lower().replace("-", "_"),
            "platform_type": "WSF_PLATFORM",
            "side": "BLUE",
            "spatial_domain": "air",
            "empty_mass": aircraft_data['empty_weight'],
            "fuel_mass": aircraft_data['fuel_capacity'],
            "length": aircraft_data['length'],
            "width": aircraft_data['wingspan'],
            "height": aircraft_data['height'],
            "description": aircraft_data['name']
        }

        # 生成脚本
        result = self.base_agent.generate_platform_script(config)
        if 'error' in result:
            return f"脚本生成失败: {result['error']}"

        script_content = result.get('script_content', '')
        explanation = result.get('explanation', '')

        response = f"## {aircraft_data['name']} AFSIM脚本\n\n"
        response += f"```\n{script_content}\n```\n\n"
        response += f"**脚本说明**: {explanation}\n\n"
        response += f"**数据来源**: 基于 {aircraft_data['name']} 公开技术参数生成"

        return response

    def query_parameters(self, query: str) -> str:
        """查询平台参数"""
        try:
            result = self.base_agent.query_platform_parameters(query, top_k=5)
            if 'error' in result:
                return f"参数查询失败: {result['error']}"

            summary = result.get('summary', '未找到相关信息')
            results_count = result.get('total_results', 0)

            response = f"## 参数查询结果\n\n找到 {results_count} 个相关结果:\n\n{summary}"

            if 'results' in result and result['results']:
                response += "\n\n**详细信息**:\n"
                for i, res in enumerate(result['results'][:3], 1):
                    response += f"{i}. 来源: {res['metadata'].get('file_name', 'Unknown')}\n"
                    response += f"   内容: {res['content'][:150]}...\n\n"

            return response

        except Exception as e:
            return f"参数查询失败: {str(e)}"

    def general_query(self, query: str) -> str:
        """通用查询"""
        try:
            result = self.base_agent.query_platform_parameters(query, top_k=3)
            if 'error' in result:
                return f"查询失败: {result['error']}"

            summary = result.get('summary', '抱歉，我无法理解您的问题。您可以尝试：\n- 输入AFSIM脚本让我解释\n- 询问飞机参数（如"F-18参数"）\n- 请求生成脚本（如"生成F-18脚本"）')

            return summary

        except Exception as e:
            return f"查询失败: {str(e)}"

    def _extract_aircraft_name(self, message: str) -> Optional[str]:
        """从消息中提取已知飞机名称"""
        message_upper = message.upper()
        for aircraft_name in self.aircraft_database.keys():
            if aircraft_name in message_upper:
                return aircraft_name
        return None

    def _extract_potential_aircraft_name(self, message: str) -> Optional[str]:
        """从消息中提取潜在的飞机名称"""
        import re

        # 常见的飞机名称模式
        patterns = [
            r'([A-Z]-?\d+[A-Z]*)',  # F-16, F22, A10A等
            r'([A-Z]{2,}-?\d+)',    # SU-27, MIG-29等
            r'(歼-?\d+)',           # 歼-20, 歼10等
            r'(苏-?\d+)',           # 苏-27等
            r'(米格-?\d+)',         # 米格-29等
        ]

        for pattern in patterns:
            matches = re.findall(pattern, message.upper())
            if matches:
                return matches[0].replace('-', '-')  # 标准化连字符

        return None

    def get_available_aircraft(self) -> List[str]:
        """获取可用飞机列表"""
        return list(self.aircraft_database.keys())

    def get_conversation_history(self) -> List[Dict]:
        """获取对话历史"""
        return self.conversation_history.copy()

    def clear_history(self):
        """清空对话历史"""
        self.conversation_history.clear()
        logger.info("对话历史已清空")

    def get_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        base_status = self.base_agent.get_status() if self.base_agent.is_initialized else {}

        return {
            "initialized": self.is_initialized,
            "base_agent_status": base_status,
            "conversation_turns": len(self.conversation_history),
            "available_aircraft": list(self.aircraft_database.keys()),
            "features": [
                "脚本解释",
                "参数查询",
                "飞机信息查询",
                "脚本生成"
            ]
        }




