"""
增强版AFSIM Platform Agent
结合LangChain、记忆功能和智能对话的平台脚本生成助手
"""

import os
import json
import yaml
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

# LangChain imports
from langchain.memory import ConversationBufferWindowMemory
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain.callbacks.base import BaseCallbackHandler
from langchain.tools import BaseTool
from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_openai import ChatOpenAI

# 本地模块
from .platform_agent import AFSIMPlatformAgent
from .vector_database import VectorDatabase
from .chinese_summarizer import ChineseSummarizer

logger = logging.getLogger(__name__)


class PlatformQueryTool(BaseTool):
    """平台参数查询工具"""
    
    name = "platform_query"
    description = "查询AFSIM平台参数和配置信息"
    
    def __init__(self, agent: AFSIMPlatformAgent):
        super().__init__()
        self.agent = agent
    
    def _run(self, query: str) -> str:
        """执行平台参数查询"""
        try:
            result = self.agent.query_platform_parameters(query, top_k=10)
            if 'error' in result:
                return f"查询失败: {result['error']}"
            
            # 格式化返回结果
            summary = result.get('summary', '未找到相关信息')
            results_count = result.get('total_results', 0)
            
            response = f"找到 {results_count} 个相关结果:\n\n{summary}"
            
            # 添加详细结果
            if 'results' in result and result['results']:
                response += "\n\n详细信息:\n"
                for i, res in enumerate(result['results'][:3], 1):
                    response += f"{i}. 来源: {res['metadata'].get('file_name', 'Unknown')}\n"
                    response += f"   内容: {res['content'][:200]}...\n\n"
            
            return response
            
        except Exception as e:
            logger.error(f"平台查询工具执行失败: {e}")
            return f"查询执行失败: {str(e)}"


class ScriptGeneratorTool(BaseTool):
    """脚本生成工具"""
    
    name = "script_generator"
    description = "生成AFSIM平台脚本代码"
    
    def __init__(self, agent: AFSIMPlatformAgent):
        super().__init__()
        self.agent = agent
    
    def _run(self, config_json: str) -> str:
        """执行脚本生成"""
        try:
            # 解析配置
            config = json.loads(config_json)
            
            result = self.agent.generate_platform_script(config)
            if 'error' in result:
                return f"脚本生成失败: {result['error']}"
            
            script_content = result.get('script_content', '')
            explanation = result.get('explanation', '')
            
            response = f"生成的AFSIM平台脚本:\n\n```\n{script_content}\n```\n\n"
            response += f"脚本说明:\n{explanation}"
            
            return response
            
        except json.JSONDecodeError:
            return "配置格式错误，请提供有效的JSON格式配置"
        except Exception as e:
            logger.error(f"脚本生成工具执行失败: {e}")
            return f"脚本生成失败: {str(e)}"


class ParameterExtractorTool(BaseTool):
    """参数提取工具"""
    
    name = "parameter_extractor"
    description = "从文档中提取特定类型的平台参数"
    
    def __init__(self, agent: AFSIMPlatformAgent):
        super().__init__()
        self.agent = agent
    
    def _run(self, parameter_type: str) -> str:
        """提取指定类型的参数"""
        try:
            result = self.agent.get_all_parameters(parameter_type)
            if 'error' in result:
                return f"参数提取失败: {result['error']}"
            
            total_params = result.get('total_parameters', 0)
            parameters = result.get('parameters', [])
            summary = result.get('summary', {}).get('summary', '')
            
            response = f"找到 {total_params} 个{parameter_type}参数:\n\n"
            response += f"总结: {summary}\n\n"
            
            if parameters:
                response += "主要参数列表:\n"
                for i, param in enumerate(parameters[:10], 1):
                    response += f"{i}. {param.get('name', 'Unknown')}: {param.get('description', 'No description')}\n"
            
            return response
            
        except Exception as e:
            logger.error(f"参数提取工具执行失败: {e}")
            return f"参数提取失败: {str(e)}"


class EnhancedPlatformAgent:
    """增强版AFSIM Platform Agent"""
    
    def __init__(self, config_path: str = None):
        """初始化增强版Agent"""
        self.config_path = config_path
        self.config = self._load_config()
        
        # 初始化基础Agent
        self.base_agent = AFSIMPlatformAgent(config_path)
        
        # 初始化LangChain组件
        self.memory = ConversationBufferWindowMemory(
            k=10,  # 保留最近10轮对话
            memory_key="chat_history",
            return_messages=True
        )
        
        # 初始化LLM
        self.llm = ChatOpenAI(
            model="deepseek-chat",
            api_key=os.getenv("DEEPSEEK_API_KEY"),
            base_url="https://api.deepseek.com/v1",
            temperature=0.1
        )
        
        # 初始化工具
        self.tools = [
            PlatformQueryTool(self.base_agent),
            ScriptGeneratorTool(self.base_agent),
            ParameterExtractorTool(self.base_agent)
        ]
        
        # 创建Agent
        self.agent_executor = None
        self.is_initialized = False
        
        # 预定义的平台模板
        self.platform_templates = self._load_platform_templates()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        if self.config_path and Path(self.config_path).exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        
        # 默认配置
        return {
            "enhanced_agent": {
                "max_memory_turns": 10,
                "temperature": 0.1,
                "enable_memory": True
            }
        }
    
    def _load_platform_templates(self) -> Dict[str, Dict]:
        """加载平台模板"""
        templates = {
            "F22": {
                "platform_name": "f22_raptor",
                "platform_type": "WSF_PLATFORM",
                "side": "BLUE",
                "spatial_domain": "air",
                "altitude": 15000,
                "heading": 0,
                "empty_mass": 19700,
                "fuel_mass": 8200,
                "length": 18.9,
                "width": 13.6,
                "height": 5.1,
                "radar_signature": "f22_rcs",
                "infrared_signature": "f22_ir",
                "description": "F-22 Raptor隐形战斗机"
            },
            "F35": {
                "platform_name": "f35_lightning",
                "platform_type": "WSF_PLATFORM", 
                "side": "BLUE",
                "spatial_domain": "air",
                "altitude": 12000,
                "heading": 0,
                "empty_mass": 13300,
                "fuel_mass": 8300,
                "length": 15.7,
                "width": 10.7,
                "height": 4.4,
                "radar_signature": "f35_rcs",
                "infrared_signature": "f35_ir",
                "description": "F-35 Lightning II多用途战斗机"
            },
            "B2": {
                "platform_name": "b2_spirit",
                "platform_type": "WSF_PLATFORM",
                "side": "BLUE", 
                "spatial_domain": "air",
                "altitude": 15000,
                "heading": 0,
                "empty_mass": 71700,
                "fuel_mass": 90700,
                "length": 21.0,
                "width": 52.4,
                "height": 5.2,
                "radar_signature": "b2_rcs",
                "infrared_signature": "b2_ir",
                "description": "B-2 Spirit隐形轰炸机"
            }
        }
        return templates

    def initialize(self) -> bool:
        """初始化增强版Agent"""
        try:
            logger.info("开始初始化增强版AFSIM Platform Agent...")

            # 初始化基础Agent
            if not self.base_agent.initialize():
                logger.error("基础Agent初始化失败")
                return False

            # 创建Agent提示模板
            prompt = ChatPromptTemplate.from_messages([
                ("system", self._get_system_prompt()),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad")
            ])

            # 创建Agent
            agent = create_openai_functions_agent(
                llm=self.llm,
                tools=self.tools,
                prompt=prompt
            )

            # 创建Agent执行器
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=self.tools,
                memory=self.memory,
                verbose=True,
                max_iterations=5,
                early_stopping_method="generate"
            )

            self.is_initialized = True
            logger.info("增强版AFSIM Platform Agent初始化完成")
            return True

        except Exception as e:
            logger.error(f"增强版Agent初始化失败: {e}")
            return False

    def _get_system_prompt(self) -> str:
        """获取系统提示"""
        return """你是一个专业的AFSIM平台脚本生成和参数查询助手。你具备以下能力：

1. **平台参数查询**: 使用platform_query工具查询AFSIM平台的各种参数和配置信息
2. **脚本生成**: 使用script_generator工具生成完整的AFSIM平台脚本代码
3. **参数提取**: 使用parameter_extractor工具从文档中提取特定类型的参数

你的专业领域包括：
- AFSIM仿真平台配置
- 军用飞机（F-22、F-35、B-2等）参数设置
- 平台脚本编写和优化
- 传感器、武器系统配置
- 仿真场景设计

回答时请：
1. 优先使用工具获取准确信息
2. 提供详细的中文解释
3. 给出具体的代码示例
4. 考虑实际应用场景
5. 保持专业和准确

可用的预定义平台模板：
- F22: F-22 Raptor隐形战斗机
- F35: F-35 Lightning II多用途战斗机
- B2: B-2 Spirit隐形轰炸机

如果用户询问特定平台的脚本，可以基于这些模板生成。"""

    def chat(self, message: str) -> str:
        """与Agent对话"""
        try:
            if not self.is_initialized:
                return "Agent未初始化，请先调用initialize()方法"

            # 执行对话
            response = self.agent_executor.invoke({
                "input": message,
                "chat_history": self.memory.chat_memory.messages
            })

            return response.get("output", "抱歉，我无法处理这个请求")

        except Exception as e:
            logger.error(f"对话执行失败: {e}")
            return f"对话执行失败: {str(e)}"

    def get_platform_template(self, platform_type: str) -> Optional[Dict]:
        """获取平台模板"""
        return self.platform_templates.get(platform_type.upper())

    def list_platform_templates(self) -> List[str]:
        """列出所有可用的平台模板"""
        return list(self.platform_templates.keys())

    def generate_f22_script(self, custom_params: Dict = None) -> str:
        """生成F-22脚本的便捷方法"""
        template = self.get_platform_template("F22").copy()
        if custom_params:
            template.update(custom_params)

        config_json = json.dumps(template)
        tool = ScriptGeneratorTool(self.base_agent)
        return tool._run(config_json)

    def explain_platform_parameters(self, parameter_names: List[str]) -> str:
        """解释平台参数的含义"""
        explanations = []
        for param in parameter_names:
            query_tool = PlatformQueryTool(self.base_agent)
            result = query_tool._run(f"{param} parameter meaning usage")
            explanations.append(f"**{param}**:\n{result}\n")

        return "\n".join(explanations)

    def get_conversation_history(self) -> List[BaseMessage]:
        """获取对话历史"""
        return self.memory.chat_memory.messages

    def clear_memory(self):
        """清空对话记忆"""
        self.memory.clear()
        logger.info("对话记忆已清空")

    def save_conversation(self, filepath: str):
        """保存对话历史"""
        try:
            messages = []
            for msg in self.memory.chat_memory.messages:
                messages.append({
                    "type": msg.__class__.__name__,
                    "content": msg.content,
                    "timestamp": datetime.now().isoformat()
                })

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(messages, f, ensure_ascii=False, indent=2)

            logger.info(f"对话历史已保存到: {filepath}")

        except Exception as e:
            logger.error(f"保存对话历史失败: {e}")

    def load_conversation(self, filepath: str):
        """加载对话历史"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                messages = json.load(f)

            self.memory.clear()
            for msg_data in messages:
                if msg_data["type"] == "HumanMessage":
                    self.memory.chat_memory.add_user_message(msg_data["content"])
                elif msg_data["type"] == "AIMessage":
                    self.memory.chat_memory.add_ai_message(msg_data["content"])

            logger.info(f"对话历史已从 {filepath} 加载")

        except Exception as e:
            logger.error(f"加载对话历史失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        base_status = self.base_agent.get_status() if self.base_agent.is_initialized else {}

        return {
            "enhanced_agent_initialized": self.is_initialized,
            "base_agent_status": base_status,
            "memory_messages": len(self.memory.chat_memory.messages),
            "available_templates": list(self.platform_templates.keys()),
            "tools_count": len(self.tools)
        }
