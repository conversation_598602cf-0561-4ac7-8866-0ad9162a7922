# wsfeventpipe.rst.txt.md
orphan

:   





::: deprecated
2.9 WsfEventPipe.Record with the sim_time argument has been deprecated and will be removed in a future release. Use WsfEventPipe.Record without the sim_time argument instead.
:::

::: method
static void Record(double aSimTime, WsfPlatform aPlatform, string aKey, double aValue)

Publish a key/value pair associated with the platform. The given aSimTime is ignored; the message will be posted at TIME_NOW.
:::

::: method
static void Record(WsfPlatform aPlatform, string aKey, double aValue)
:::

::: method
static void Record(WsfPlatform aPlatform, string aKey, int aValue)
:::

::: method
static void Record(WsfPlatform aPlatform, string aKey, bool aValue)
:::

::: method
static void Record(WsfPlatform aPlatform, string aKey, string aValue)

Publish a key/value pair associated with the platform at the current sim-time.
:::

::: note
::: title
Note
:::

Recorded values will be displayed in the Mystic Platform Details dialog. Values of double or integer type may be plotted.
:::

::: method
static void AddBookmark(double aSimTime, string aType, string aText)

Add a bookmark at the specified time with the defined type and message text. Type is a free-form text field used to classify the event type.
:::

::: method
static void AddBookmark(string aType, string aText)

Add a bookmark at the current time with the defined type and message text.
:::

::: note
::: title
Note
:::

Bookmarks are placeholders in time used in Mystic, typically used to mark important or unusual events. With bookmarks, users can conveniently jump to various points of the scenario.
:::



This example will publish the sine of the sim-time to the event-pipe.

    platform sinpub WSF_PLATFORM
      icon bullseye
      position 00.32075n 01.51704e altitude 10000 ft msl

      execute at_interval_of 0.1 s
         WsfEventPipe.Record(PLATFORM, "sine", MATH.Sin(TIME_NOW));
      end_execute
    end_platform