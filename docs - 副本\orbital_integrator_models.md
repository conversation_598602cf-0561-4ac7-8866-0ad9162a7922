orphan

:   

::: {#orbital_integrator_models}
:::

# Orbital Integrator Types

## Prince-Dormand 45 {#orbital_integrator_models.dormand_prince_45}

::: parsed-literal

[integrator]() prince_dormand_45

:   \... `Embedded Runge-Kutta commands<orbital_integrator_models.embedded_runge_kutta>`{.interpreted-text role="ref"} \...

end_integrator
:::

This integrator is the embedded Runge-Kutta scheme RK5(4)7S as described in: A family of embedded Runge-Kutta formulae, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, Journal of Computational and Applied Mathematics, Vol. 6, no 1, 1980.

This integrator gives a 5th order solution with a 4th order error estimator. This integrator uses adaptive timesteps to control the error in the computed solution.

## Prince-Dormand 78 {#orbital_integrator_models.dormand_prince_78}

::: parsed-literal

[integrator]() prince_dormand_78

:   \... `Embedded Runge-Kutta commands<orbital_integrator_models.embedded_runge_kutta>`{.interpreted-text role="ref"} \...

end_integrator
:::

This integrator is the embedded Runge-Kutta scheme RK8(7)13M as described in: High order embedded Runke-Kutta formulae, P.J. Prince and J.R. Dormand, Journal of Computational and Applied Mathematics, Vol. 7, no 1, 1981.

This integrator gives an 8th order solution with a 7th order error estimator. This integrator uses adaptive timesteps to control the error in the computed solution.

## Embedded Runge-Kutta {#orbital_integrator_models.embedded_runge_kutta}

The following commands are common to every embedded Runge-Kutta integrator available for the `WSF_INTEGRATING_SPACE_MOVER`{.interpreted-text role="model"}.

::: command
tolerance \<real-value\>

This gives the error tolerance for a single step of the integrator. The default value for the tolerance is 1.0e-10.
:::

::: command
max_adjustment_attempts \<integer-value\>

The maximum number of timestep adjustments attempts that will be made. If a suitable timestep is not found, a warning will be printed and the simulation will continue, but the satisfaction of the user requested tolerance is no longer guaranteed. The default value for this is 50.
:::

::: command
max_step_size \<real-value\>

The maximum timestep in seconds. If the integrator would adapt its step size to larger than this value, it will use this value for the step size instead. The default value for this is effectively infinite.
:::

::: command
min_step_size \<real-value\>

The minimum allowable timestep in seconds. If the integrator would adapt its step size to smaller than this value, it will use this value for the step size instead. The default value for this is 0.

::: note
::: title
Note
:::

Setting a non-zero value for this may cause the computed solution to no longer satisfy the specified tolerance.
:::
:::

::: command
initial_step_size \<real-value\>

The size of the initial step size in seconds for the first integration attempt. The default step size for this parameter is 0.1.
:::

::: command
error_criterion \<error-criterion-type\>

Set the criterion for assessing the error in a step. The possible values are L_infinity and L_2. Selecting L_infinity will require that the maximum magnitude of any component of the error in the solution be smaller than the specified tolerance. Selecting L_2 will require the square root of the sum of the squared errors be smaller than the tolerance. Further, this error is computed as relative to the difference in the state over the computed step.

The default option is L_2.
:::
