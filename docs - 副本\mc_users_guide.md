orphan

:   

# Mover Creator User\'s Guide

The Mover Creator User\'s Guide contains explanations of all Mover Creator features. The content in this guide answers the \'What is the \[blank\] feature\' types of questions.

## Core Functionality

Mover Creator walks the user through the design of their vehicle or engine in a linear, step-by-step process. This process is described in the sections below.

### *Start Dialog*

When Mover Creator is first opened, the first page that appears is the Start Dialog, shown below. From here, the user may choose to begin designing an aircraft, a weapon, or an engine. In each of these cases, the user can either create a new model or edit an existing model.

![image](images/start_dialog.png)

If the user chooses to create a new model, a dialog appears prompting the user to select a type to derive their model from and to enter a name for their new model. For example, selecting \"Create New Aircraft\" opens the dialog shown below (left side). This will create a new aircraft identical to the derived type. The user may make changes to this new aircraft model without affecting the original derived aircraft. The Mover Creator application comes with a set of templates from which users can derive their vehicle. The naming convention for these templates is a bit cryptic and is explained in the `Standard Vehicle Templates <mc_standard_vehicles>`{.interpreted-text role="doc"} page. Selecting \"Edit Existing Aircraft\" brings up a dialog requesting the aircraft type to be edited (right side of the image below). Any changes will be applied to the aircraft model that is selected. In both dialogs, the \"Aircraft Category\" drop-down menu is populated with the names of the folders located in *\\resources\\mover_creator\\data\\Vehicles\\Aircraft\\*. The \"Aircraft Type\" menu is populated with the names of the .amc files in the folder selected. Near the bottom of the dialog are checkboxes indicating which mover types are to be generated by Mover Creator.

![image](images/create_edit_convert_aircraft.png)

The dialog to convert a `WSF_BRAWLER_MOVER`{.interpreted-text role="model"} aircraft is the odd one out. Rather than constructing a full model component-by-component and sending it through the typical Mover Creator flow, Mover Creator converts the aircraft to a `WSF_POINT_MASS_SIX_DOF_MOVER`{.interpreted-text role="model"} and gives the user the option to carry out further tuning. This flow may become better integrated with Mover Creator in later versions, as support for SixDOF vehicles expands to the point-mass mover model.

On the Start Dialog, to the left of the Quit/Exit button is an Options button. This allows the user to specify directories for the purpose of outputting a 3D model to use in `AFSIM<main_page>`{.interpreted-text role="doc"} scenarios. The theme may also be specified. When the Options button is clicked, a dialog appears (see the image below). The Site Directory refers to the directory where .obj model files will be output. An AFSIM input file containing a model definition block will also be output to this directory. The OSG Convert file is used to convert the model files to the OSGB or IVE format, determined by which one is selected under \"Preferred 3D Model File Type\". This file format is generally preferred, but requires the user to download OSG and select the osgconv executable. The theme may be set to either Light or Dark. \"Reset Settings\" will restore all of the settings in this dialog to defaults. To the right of the Quit/Exit button is an About/Info button. Clicking on this will open a dialog with version information and documentation links.

![image](images/directory_theme_settings.png)

### *Application Flow*

It is recommended that the user start with his or her engine design before moving on to the vehicle design. Once an engine has been selected for creation/modification from the Start Dialog, Mover Creator will open the \"Engine\" page which contains the Engine Designer. See the `Engine Designer<mc_engine_designer>`{.interpreted-text role="doc"} page for details of the engine definition process. When the user is content with their engine design, they can begin the design of their vehicle.

Once the user has selected a vehicle to edit or create from the Start Dialog, Mover Creator opens the Geometry page for the selected vehicle. Once the desired geometry is specified, the user may move to the Aerodynamics page. This can be accomplished by clicking on the Aerodynamics tab at the very bottom of the application window (See Figure 5); or alternatively, by clicking on the Next button located in the bottom right portion of the window. The steps to designing a vehicle must be completed in a particular order; therefore, the user may only navigate to the pages immediately before or after the current pages. Each of the pages are explained in detail in the `Vehicle Designer <Vehicle_Designer>`{.interpreted-text role="ref"} and `Engine Designer <Engine_Designer>`{.interpreted-text role="ref"}

![image](images/application_flow.png)

Once all steps have been completed, Mover Creator outputs the vehicle definition in a format recognizable by the `AFSIM<main_page>`{.interpreted-text role="doc"} library. The generated AFSIM input files contain the definition blocks necessary to define the desired vehicle design. This includes but is not limited to:

  ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
                  P6DOF Mover                                                                                               RB6DOF Mover                                                                                              PM6DOF Mover                                                          Guided Mover
  --------------- --------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------- --------------------------------------------------------------------- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Platform Type   `platform.platform_type`{.interpreted-text role="command"}                                                `platform.platform_type`{.interpreted-text role="command"}                                                `platform.platform_type`{.interpreted-text role="command"}            `platform.platform_type`{.interpreted-text role="command"}

  Mover           `mover<wsf_p6dof_mover>`{.interpreted-text role="doc"}                                                    `mover<wsf_rigid_body_six_dof_mover>`{.interpreted-text role="doc"}                                       `mover<wsf_point_mass_six_dof_mover>`{.interpreted-text role="doc"}   `mover<wsf_guided_mover>`{.interpreted-text role="doc"}

  Aerodynamics    `aero_data`{.interpreted-text role="command"}                                                             `aero_data`{.interpreted-text role="command"}                                                             `aero_data`{.interpreted-text role="command"}                         `aero`{.interpreted-text role="command"} (`aero.aspect_ratio`{.interpreted-text role="command"}, `aero.cl_max`{.interpreted-text role="command"}, `aero.mach_and_cd`{.interpreted-text role="command"})

  Mass            `mass_properties`{.interpreted-text role="command"}                                                       `mass_properties`{.interpreted-text role="command"}                                                       `mass_properties`{.interpreted-text role="command"}                   `WSF_GUIDED_MOVER.total_mass`{.interpreted-text role="command"}, `WSF_GUIDED_MOVER.fuel_mass`{.interpreted-text role="command"}

  Propulsion      `propulsion_data`{.interpreted-text role="command"}                                                       `propulsion_data`{.interpreted-text role="command"}                                                       `propulsion_data`{.interpreted-text role="command"}                   `WSF_GUIDED_MOVER.sea_level_specific_impulse`{.interpreted-text role="command"}, `WSF_GUIDED_MOVER.vacuum_specific_impulse`{.interpreted-text role="command"}, `WSF_GUIDED_MOVER.sea_level_thrust_table`{.interpreted-text role="command"}, `WSF_GUIDED_MOVER.throttle`{.interpreted-text role="command"}.

  Controls        `flight_controls`{.interpreted-text role="command"}, `control_inputs`{.interpreted-text role="command"}   `flight_controls`{.interpreted-text role="command"}, `control_inputs`{.interpreted-text role="command"}   `flight_controls`{.interpreted-text role="command"}                   
  ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

These files will be located in *\\resources\\data\\mover_creator\\AFSIM_Scripts\\*.

## Vehicle Designer {#Vehicle_Designer}

Selecting any of the options related to aircraft or weapons on the Start Dialog launches the Vehicle Designer. This allows the user to define the controls, geometry, and aerodynamics for his or her vehicle. The user may also run performance tests and perform flight tests for the vehicle. Each aspect of the vehicle design has a corresponding page in the Vehicle Designer. These pages include:

-   `Geometry<mc_geometry>`{.interpreted-text role="doc"}
-   `Aerodynamics<mc_aerodynamics>`{.interpreted-text role="doc"}
-   `Performance<mc_performance>`{.interpreted-text role="doc"}
-   `Pilots/Controls<mc_pilots_controls>`{.interpreted-text role="doc"}
-   `Autopilot<mc_autopilot>`{.interpreted-text role="doc"}
-   `Flight Test<mc_flight_test>`{.interpreted-text role="doc"}

In addition to using the Mover Creator GUI to define a vehicle, a user can modify the vehicle\'s raw .amc file. The vehicle files (located in *\\resources\\data\\mover_creator\\Vehicles\\*) are in the JSON format and are what ultimately get read into the GUI.

::: note
::: title
Note
:::

The vehicle .amc file is structured in a specific format, and should only be modified by advanced users.
:::

## Engine Designer {#Engine_Designer}

The `Engine Designer <mc_engine_designer>`{.interpreted-text role="doc"} is launched by selecting \"Create New Engine\" or \"Edit Existing Engine\" on the Start Dialog. The Engine Designer looks different for each engine type. Supported types include:

-   `Jets<Jet_Engines>`{.interpreted-text role="ref"}
-   `Ramjets<Ramjet_Engines>`{.interpreted-text role="ref"}
-   `Liquid Propellant Rockets<Liquid_Propellant_Rocket_Engines>`{.interpreted-text role="ref"}
-   `Solid Propellant Rockets<Solid_Propellant_Rocket_Engines>`{.interpreted-text role="ref"}

In addition to using the Mover Creator GUI to define an engine, a user can modify the engine\'s raw .amc file. The engine files (located in *\\resources\\data\\mover_creator\\Engines\\*) are in the JSON format and are what ultimately get read into the GUI.

::: note
::: title
Note
:::

The engine .amc file is structured in a specific format, and should only be modified by advanced users.
:::
