# sensor_plot_demos.rst.txt.md
orphan

:   

::: demo
sensor_plot
:::

| This directory holds examples for use by the sensor_plot tool in WSF. These are
| examples of how to setup and use sensor_plot for the more common uses. The following
| describes how to run these examples, their output formats, and how to view the output.
| For more information on how to setup and modify the input files see the WSF documentation.
| 
| To Run:
|    At the command prompt type \"run\" and the file name you want to run.
| 
| Output Formats:
| 1. Row-Column
|       Some of the outputs have standard ASCII output of data. This is row-column format of the data.
|       MS Excel or similar spreadsheet can be used to view and plot data.
| 2. 2D format
|       Some output types are in a 2-dimensional format. This format is common for antenna patterns.
|       Plotting of these can be achieved easily in MS Excel or similar spreadsheet application
|       through the import function.
| 3. GNUPlot
|       GNU plot is an optional output format in the input files. The output is formatted to be
|       plotted with gnuplot, the \*.cmd files located in this directory provide an example setup
|       that can be used to plot the gnuplot output files. The \*.cmd files can also be used with the
|       \'load\' \"filename\" method in gnuplot.
|    circular_2d.gnu - commands to plot a 2-dimensional antenna patter plot output file.
|    circular_3d.gnu - commands to plot a 3-dimensional antenna pattern plot output file.
|    hmplot.gnu - commands to plot a horizontal map plot output file.
|    pdoverlay.gnu - commands to plot a probability-of-detection output file.
|    vmplot-cntr.gnu - commands to plot a vertical contour map plot of the output file.
|    vmplot-surface.gnu - commands to plot a vertical mesh plot o fhte output file.
|    vcdplot.gnu - commands to plot a vertical coverage diagram from the vcd output file.
| 
| Input Files:
| 1. Antenna Plot files
| 2. Horizontal Map files
|    1. hm-pass-detect.txt - sample input file for plotting a passive detection of a jammer.
|    2. hm-ucav-dry.txt - sample input file for plotting detection of a target around an acquisition radar.
|    3. hm-ucav-soj.txt - extension of hm-ucav-dry.txt with an added jammer jamming the radar.
|    4. hm-ucav-soj2.txt - a different jammer include extension of hm-ucav-soj.txt.
| 3. Vertical coverage and vertical map files
|    1. vcd-acq_radar.txt - sample vertical coverage diagram input file for an acquisition type radar.
|    2. vm-ucav-dry.txt - sample input file for plotting detection of a target vertically of an acquisition radar.
|    3. vm-ucav-soj.txt - extension of vm-ucav-dry.txt with an added jammer jamming the radar.
|    4. vm-ucav-soj2.txt - a different jammer include extension of vm-ucav-soj.txt.
| 4. SEDIT coverage contour
|    1. sedit-ucav-dry.txt - sample file that plots the detection contours to import into Vespa/SEDIT.
| 5. Clutter Map files
|    1. clutter_table_generation.txt - Sample that creates an generic clutter table.
|    2. clutter_table_regeneration_test.txt - Sample that re-creates the clutter table (1) using the table itself.