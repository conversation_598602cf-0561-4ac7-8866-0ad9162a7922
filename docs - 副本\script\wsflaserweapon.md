orphan

:   

# WsfLaserWeapon

## General Methods {#general-methods .WsfLaserWeapon .inherits .WsfDirectedEnergyWeapon}

::: method
bool CanLase()

Return whether the Laser is capable of lasing. It will return false if the thermal \"magazine\" of the the laser has been expended (in which case it must cool down for a given period of time).
:::

::: method
bool TargetOccluded(string aTargetName, string aTargetOffset)

Return whether \"aTargetOffset\" is occluded, based on a raycast from the laser weapon to the target offset location.

::: note
::: title
Note
:::

This method only produces reliable results when an intersect mesh is used, and a `WSF_INTERSECT_PROCESSOR`{.interpreted-text role="model"} is placed on the target platform.
:::

::: note
::: title
Note
:::

This method does not check for line-of-sight occlusion from such things as terrain or horizon. It only targets self-occlusion.
:::
:::

## Engagement Summary Methods

These methods can be used to query the results of an engagement.

::: method
double EdgeRadius()

Return the edge radius or characteristic size of the beam on target. It is defined to be the width between the peak and the radius at which the power is 1/e of the peak power.
:::

::: method
double EnergyDensity()

Return the energy density (in joules / m\^2) within the target area.
:::

::: method
double Power()

Return the average power within the target area.
:::

::: method
double PeakFluence()

Return the peak fluence (in watts / m\^2) on target.
:::

::: method
double IncidenceAngle()

Return the incidence angle (in degrees) of the laser on the target.
:::
