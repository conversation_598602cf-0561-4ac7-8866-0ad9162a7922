# classification_commands.rst.txt.md
orphan

:   



::: parsed-literal

[classification_levels]()

:   

    [level]() \<string\>

    :   [color]() \...

    end_level

end_classification_levels

[classification]()

:   [level]() \<string\> [caveats]() \... end_caveats [trigraphs]() \... end_trigraphs

end_classification
:::



The [classification]() command is used to specify the classification [level](), [caveats]() and [trigraphs]() associated with a particular input file or scenario. If multiple [classification]() blocks are encountered within the scenario, the trigraphs and caveats are aggregated (duplicates removed) and the level of the scenario is set to the highest level found. The highest level is determined by the [classification_levels]() block, in which levels are defined in order of increasing degree of sensitivity, and optionally with a [color]() to use when displaying the level in the GUI applications. In the example below, *HIGHEST* is more sensitive than *HIGHER*, which is more sensitive than *HIGH*. The aggregated scenario classification string, including level, caveats and trigraphs, is echoed to the console and displayed in a banner within the GUI applications. The classification string and color are also accessible via script with the `ClassificationString<WsfSimulation.ClassificationString>`{.interpreted-text role="method"} and `ClassificationColor<WsfSimulation.ClassificationColor>`{.interpreted-text role="method"} methods of `WsfSimulation`{.interpreted-text role="class"}.

::: note
::: title
Note
:::

The format of the scenario classification string is as follows: Level//Trigraph-1//Trigraph-2//\...//Trigraph-N - Caveat-1//Caveat-2//\...//Caveat-N
:::

::: note
::: title
Note
:::

A [classification]() command can be placed within any file in the scenario, but it must come after the [classification_levels]() block that defines the levels. Therefore, there is an input file order dependency.
:::

Example:

::: parsed-literal

[classification_levels]()

:   [level]() HIGH [color]() yellow end_level [level]() HIGHER [color]() red end_level [level]() HIGHEST [color]() orange end_level

end_classification_levels

[classification]()

:   [level]() HIGHER [caveats]() ABC DEF end_caveats [trigraphs]() RST XYZ end_trigraphs

end_classification



[classification]()

:   [level]() HIGHEST [caveats]() DEF GHI end_caveats [trigraphs]() RST UVW end_trigraphs

end_classification
:::

The resulting aggregated classification string would be as follows: HIGHEST//RST//UVW//XYZ - ABC//DEF//GHI



::: {.command block=""}
classification_levels \... end_classification_levels

Used to define the list of potential classification levels in a scenario, in order of increasing degree of sensitivity.

::: note
::: title
Note
:::

It is an error to define multiple [classification_levels]() blocks.
:::

::: {.command block=""}
level \<string\> \... end_level

Defines a level that can be used in [classification]() blocks.

::: command
color \<color-value\>

Optionally define the color to be used when displaying the level in the classification banner within the GUI applications.

Default: black

::: note
::: title
Note
:::

The alpha component of the color will not be used in the classification banner even if specified.
:::
:::
:::

::: end
:::
:::

::: end
:::

::: {.command block=""}
classification \... end_classification

::: command
level \<string\>

Specifies the classification level of the input file or scenario. An error will occur if the level string does not appear in the [classification_levels]() block.

Default: None
:::

::: command
caveats \<caveat-1\> \<caveat-2\> \... \<caveat-n\> end_caveats

Specifies the caveats associated with the classification. Each caveat is a string literal specified by the user. The user can provide as many caveats as needed.

Default: None
:::

::: command
trigraphs \<trigraph-1\> \<trigraph-2\> \... \<trigraph-n\> end_trigraphs

Specifies the trigraphs associated with the classification. Each trigraph is a string literal specified by the user. The user can provide as many trigraphs as needed.

Default: None
:::
:::

::: end
:::