# eclipse_report.rst.txt.md
orphan

:   



::: note
::: title
Note
:::

The feature has been re-factored to the Post Processor. See Eclipse Report in the Post Processor documentation.
:::

::: {.command block=""}
eclipse_report \... end_eclipse_report

::: parsed-literal

eclipse_report

:   [file_prefix]() \... [file_format]() \... [report]() \...

end_eclipse_report
:::
:::

[Eclipse Report](#eclipse-report)s provide start time, end time, and the duration that platforms are within the Earth\'s shadow.

::: note
::: title
Note
:::

Results do not take into account the effect of the oblate Earth or motion of the Earth during a single orbit of a platform.
:::



::: command
file_prefix

Specify the output file prefix. The file extension is specified by the [file_format]() command.
:::

::: command
file_format { csv \| tsv }

Specify the output file format, which must be either of *csv* (comma-separated variables) or *tsv* (tab-separated variables).

Default csv
:::

::: note
::: title
Note
:::

The csv [file_format]() can be viewed in Microsoft Excel, but the formats for start and end times must be changed by selecting cells in the spreadsheet and using the *Format Cells\...* option to select a compatible format (i.e., *m/d/yyyy h:mm.000* ).
:::

::: command
report { \<platform\> \| all }

Specify platform names for which eclipse events are to be reported in the output file. The keyword *all* can be inserted, meaning that eclipse data will be computed for all orbiting platforms.
:::