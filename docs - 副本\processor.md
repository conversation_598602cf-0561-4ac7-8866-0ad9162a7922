orphan

:   

# processor

see `Predefined_Processor_Types`{.interpreted-text role="ref"}

Script Class: `WsfProcessor`{.interpreted-text role="class"}

::: {.command block=""}
processor
:::

::: parsed-literal

processor \<name\> \<base-type\>

:   \... `Platform_Part_Commands`{.interpreted-text role="ref"} \...

    [update_interval]() \...

end_processor
:::

## Overview

## Commands

::: command
update_interval \<time-reference\>

If non-zero, specifies a periodic time interval at which the simulation will call the processor. If zero then the processor will only respond to explicit messages.

**Default** 0.0

::: note
::: title
Note
:::

Not all processors support periodic updates.
:::
:::
