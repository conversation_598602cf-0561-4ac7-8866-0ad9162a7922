# wsfequatorialfieldofview.rst.txt.md
orphan

:   



::: {.WsfEquatorialFieldOfView .inherits .WsfFieldOfView cloneable=""}
`WsfEquatorialFieldOfView`{.interpreted-text role="class"} defines a `equatorial field of view<field_of_view_commands.equatorial>`{.interpreted-text role="ref"} that is used to dynamically change a sensor\'s field of view (originally defined using `antenna field of view commands<field_of_view>`{.interpreted-text role="command"}) using `WsfSensor.SetFOV`{.interpreted-text role="method"}.
:::



::: method
static WsfEquatorialFieldOfView Construct(double aMinEquatorial, double aMaxEquatorial, double aMinPolar, double aMaxPolar)

Returns a new `WsfEquatorialFieldOfView`{.interpreted-text role="class"} object with the specified equatorial and polar extents (in degrees).
:::



::: method
Array\<double\> EquatorialFieldOfView()

Returns the minimum and maximum equatorial field of view extents (in degrees).
:::

::: method
Array\<double\> PolarFieldOfView()

Returns the minimum and maximum polar field of view extents (in degrees).
:::