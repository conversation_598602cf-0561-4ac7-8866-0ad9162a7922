# wsftargetmaneuver.rst.txt.md
orphan

:   



::: {.WsfTargetManeuver .inherits .WsfOrbitalManeuver}
Input type: `target<../target>`{.interpreted-text role="doc"}
:::

[WsfTargetManeuver](#wsftargetmaneuver) is used to attempt to intercept the location of a `local track<WsfLocalTrack>`{.interpreted-text role="class"}. The maneuver is complete when the computed intercept transfer orbit is achieved. Several conditions must be met before the maneuver can succeed:

::: {#conditions}
-   The track id must reference a valid `WsfLocalTrack`{.interpreted-text role="class"} with both valid location and velocity.

-   The transfer orbit can only be hyperbolic if the mover executing the maneuver supports hyperbolic propagation.

-   The transfer orbit must not intersect earth.

-   When optimizing, a valid solution must exist for the provided [optimize option](#optimize-option).

-   The expended energy for the transfer must be less than the available delta-v.

    ::: note
    ::: title
    Note
    :::

    Both `WsfInterceptManeuver`{.interpreted-text role="class"} and `WsfRendezvousManeuver`{.interpreted-text role="class"} utilize [WsfTargetManeuver](#wsftargetmaneuver).
    :::

    ::: note
    ::: title
    Note
    :::

    The targeting algorithm is direct (less than one orbit). Phasing orbits are not considered.
    :::
:::



An argument to the first `Construct<WsfTargetManeuver.Construct>`{.interpreted-text role="method"} method must contain one of the following options:

::: method
int EARLIEST_TIME()
:::

Attempt to optimize the maneuver such that it will execute at the earliest possible time, up to the maximum time specified in the `Construct<WsfTargetManeuver.Construct>`{.interpreted-text role="method"} method.

::: method
int LEAST_DELTA_V()
:::

Attempt to optimize the maneuver such that it will execute with the least possible delta-v, up to the maximum specified in the `Construct<WsfTargetManeuver.Construct>`{.interpreted-text role="method"} method, or the `current available amount<WsfSpaceMover.AvailableDeltaV>`{.interpreted-text role="method"}, whichever is less. Optimization will proceed up to the the maximum time specified in the `Construct<WsfTargetManeuver.Construct>`{.interpreted-text role="method"} method.



::: method
static WsfTargetManeuver Construct(WsfOrbitalEventCondition aCondition, WsfTargetPoint aTargetPoint, int aOptimizeOption, double aMaximumDeltaTime, double aMaximumDeltaV)

Create a [WsfTargetManeuver](#wsftargetmaneuver) with the intent of finding an optimal solution in delta-v or time, using the following:
:::

-   aCondition: A specific `condition<WsfOrbitalEventCondition>`{.interpreted-text role="class"}.
-   aTargetPoint: A `WsfTargetPoint`{.interpreted-text role="class"} specifying the target of this maneuver.
-   aOptimizeOption: The return value from an [optimize option](#optimize-option) listed above.
-   aMaximumDeltaTime: The maximum time after the maneuver begins to consider in computing a solution.
-   aMaximumDeltaV: The maximum delta-V to consider when computing a solution.

::: method
static WsfTargetManeuver Construct(WsfOrbitalEventCondition aCondition, WsfTargetPoint aTargetPoint, WsfOrbitalOptimizationCost aCost, double aMaximumDeltaTime, double aMaximumDeltaV)

Create a [WsfTargetManeuver](#wsftargetmaneuver) that will find a solution that minimizes the provided cost, using the following:
:::

-   aCondition: A specific `condition<WsfOrbitalEventCondition>`{.interpreted-text role="class"}.
-   aTargetPoint: A `WsfTargetPoint`{.interpreted-text role="class"} instance specifying the target of this maneuver.
-   aCost: A `WsfOrbitalOptimizationCost`{.interpreted-text role="class"} instance specifying the cost function to minimize.
-   aMaximumDeltaTime: The maximum time after the maneuver begins to consider in computing a solution.
-   aMaximumDeltaV: The maximum delta-V to consider when computing a solution.

::: method
static WsfTargetManeuver Construct(WsfOrbitalEventCondition aCondition, WsfTargetPoint aTargetPoint, double aDeltaTime)

Create a [WsfTargetManeuver](#wsftargetmaneuver) without optimizing. If all prerequisite [conditions](#conditions) are met, the intercept will occur at the specified time after the current time when executed.
:::

-   aCondition: A specific `condition<WsfOrbitalEventCondition>`{.interpreted-text role="class"}
-   aTargetPoint: A `WsfTargetPoint`{.interpreted-text role="class"} specifying the target of this maneuver.
-   aDeltaTime: The time after the maneuver begins when the rendezvous is planned to occur.

::: method
WsfTargetPoint TargetPoint()

Get the `WsfTargetPoint`{.interpreted-text role="class"} used to configure this maneuver.
:::

::: method
double Tolerance()

Get the solution tolerance to use with this maneuver. The default value is 1.0e-9.
:::

::: method
void SetTolerance(double aTolerance)

Set the solution tolerance for this maneuver to the given value.
:::