# warlock_file_menu.rst.txt.md
orphan

:   





-   <PERSON><PERSON>\... - Browse to load a `AFSIM scenario<wsf_core>`{.interpreted-text role="doc"}.
-   Recent Scenario - Select to load from recently executed `AFSIM scenarios<wsf_core>`{.interpreted-text role="doc"}.
-   Save Configuration - Save the current `application settings<warlock_user_configurations>`{.interpreted-text role="doc"} to file.
-   Load Configuration - Load a saved `application settings<warlock_user_configurations>`{.interpreted-text role="doc"} file.
-   Import Configuration Options\... - Import some features from an `application settings<warlock_user_configurations>`{.interpreted-text role="doc"} file.
-   Recent Configurations - Choose from a recently used `application settings<warlock_user_configurations>`{.interpreted-text role="doc"} file.
-   Clear Platform Options - Clear out all of the `platform options<warlock_platform_options>`{.interpreted-text role="doc"}.
-   Exit - Exit the application.