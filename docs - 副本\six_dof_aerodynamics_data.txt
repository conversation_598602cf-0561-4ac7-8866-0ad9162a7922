# six_dof_aerodynamics_data.rst.txt.md
orphan

:   



Aerodynamics data is composed of two distinct categories \-- [aero_data]() and [aero_component]() blocks. The [aero_data]() block includes the aerodynamics produced by the object without any controls input or effects from any movable surfaces/components. There is only one [aero_data]() block, although it may contain additional [aero_data]() blocks within it that serve as aero sub-modes. An RB6 object usually contains multiple [aero_component]() blocks, with one of more for each axis of control and/or for each control surface, depending on the level of detail for the model.

Each of these command blocks are detailed below.

Point-mass vehicles and rigid-body vehicles share some aerodynamic data, but RB6 aero data is generally more involved.

Both types of vehicles use lift, drag, and side force coefficient data, but these tables should be produced in different ways. While RB6 core data is produced with the control surfaces undeflected, PM6 data should be provided at the trimmed (net-zero-moment) condition.

Non-trimming devices, like spoilers and speedbrakes, are treated specially for PM6, and provide lift and/or drag coefficient increments as requested.

Beyond those tables, very little is shared. Rotation is acheived for RB6 vehicles via the deflection of several individual control surfaces, or, most simply, by a false control surface modeled as the sum of all contributions in a given axis. For example, a user could define effects for LeftElevator, RightElevator, LeftCanard, and RightCanard, or could approximate the sum effect with a pseudo-surface named StickBackSurfaces. In either case, coefficient increments for forces and moments are added to the system and contribute to translation and rotation.

PM6 vehicles use a different approach, which requires fewer pieces of data, but may also be less precise. Instead of moment coefficient data, which must be compared to reasonable mass distribution data, PM6 uses data indicating the rotation acceleration available from a control axis. In other words, if an aircraft is capable of creating a roll acceleration of 5 degrees per second per second, that information is input directly.

Similarly, stability is conferred to RB6 vehicles through moment coefficient tables. A restoring moment yields a stable aircraft, and a destabilizing moment must be managed by a well-tuned autopilot or a very attentive pilot. For PM6, this can be approximated by a stabilizing frequency. This frequency lacks a concrete physical analog, but can be considered as an inverse to a first-order lag response time. The aim of this feature is to improve the realism of projectile trajectories. Piloted vehicles may also use this feature, but will likely see little benefit.

A positive frequency is stabilizing, and so confers a \"weathervaning\" effect where the vehicle rotates to point in the direction of travel. A negative frequency is destabilizing. This is permitted, but should be used with care, if at all. The default value of 0 confers neutral stability, so a tumbling object will continue to tumble, but a projectile with no initial rotation will glide unperturbed and perform visibly unrealistically.



This block defines the primary aerodynamics for the vehicle. The data includes one or more geometric parameters as well as multiple stability derivative tables. See `P6DOF Theory<P6DOF_FM>`{.interpreted-text role="ref"} for information on how this data is used in rigid-body models. In addition, this block includes support for \"aero modes\", which allow a vehicle to have discrete aerodynamic modes, such as a retarded-fall bomb that has two aero modes \-- clean and high-drag.

::: command
aero_data \... end_aero_data

The RB6 geometric data offers two modes: wing-based or reference area. When using wing-based mode, the [wing_chord_ft](), [wing_span_ft](), and [wing_area_sqft]() should be specified. When using reference area mode, the [ref_area_sqft]() should be specified. PM6 vehicles do not incorporate chord or span information, and so require only [ref_area_sqft]().

The RB6 stability derivative tables are each a function of mach and alpha (angle-of-attack) or mach and beta (angle-of-sideslip), as appropriate. The tables affect the base, non-controls aerodynamics of the vehicle. The secondary aerodynamics consist of control surface aerodynamics and are defined using [aero_component](), which is completely separate from [aero_data]().

Additional data for PM6, such as rotation acceleration, are input as a function of Mach, and are modified on execution to account for effects from angle of attack and from altitude.

::: parsed-literal
aero_data \# for RB6 vehicles

> [wing_chord_ft]() \... [wing_span_ft]() \... [wing_area_sqft]() \... [ref_area_sqft]() \...
>
> [use_reduced_frequency]() \...
>
> [aero_center_x]() \... [aero_center_y]() \... [aero_center_z]() \...
>
> `cL_alpha_beta_mach_table`{.interpreted-text role="command"} \... `cLq_alpha_mach_table`{.interpreted-text role="command"} \... `cL_alphadot_alpha_mach_table`{.interpreted-text role="command"} \...
>
> [cd_alpha_beta_mach_table]() \...
>
> [cy_alpha_beta_mach_table]() \... [cyr_beta_mach_table]() \... [cy_betadot_beta_mach_table]() \...
>
> [cm_alpha_beta_mach_table]() \... [cmq_mach_table]() \... [cmp_mach_table]() \... [cm_alphadot_mach_table]() \...
>
> [cn_alpha_beta_mach_table]() \... [cn_betadot_mach_table]() \... [cnr_mach_table]() \... [cnp_mach_table]() \...
>
> [cl_alpha_beta_mach_table]() \... [clp_mach_table]() \... [cl_alphadot_mach_table]() \... [cl_betadot_mach_table]() \... [clr_mach_table]() \... [clq_mach_table]() \...
>
> [aero_mode]() \... end_aero_mode

end_aero_data
:::

::: parsed-literal
aero_data \# for PM6 vehicles

> [ref_area_sqft]() \...
>
> `cL_alpha_beta_mach_table`{.interpreted-text role="command"} \... [cd_alpha_beta_mach_table]() \... [cy_alpha_beta_mach_table]() \...
>
> `speedbrake_dcd_mach_table`{.interpreted-text role="command"} \... `flaps_dcd_mach_table`{.interpreted-text role="command"} \... `flaps_dcL_mach_table`{.interpreted-text role="command"} \... `spoilers_dcd_mach_table`{.interpreted-text role="command"} \... `spoilers_dcL_mach_table`{.interpreted-text role="command"} \...
>
> `maximum_roll_acceleration_mach_table`{.interpreted-text role="command"} \... `maximum_pitch_acceleration_mach_table`{.interpreted-text role="command"} \... `maximum_yaw_acceleration_mach_table`{.interpreted-text role="command"} \...
>
> `alpha_stabilizing_frequency_mach_table`{.interpreted-text role="command"} \... `beta_stabilizing_frequency_mach_table`{.interpreted-text role="command"} \... `roll_stabilizing_frequency_mach_table`{.interpreted-text role="command"} \...
>
> [aero_mode]() \... end_aero_mode

end_aero_data
:::
:::

::: command
wing_chord_ft \<real-value\>

The mean chord of the wing measured in feet. This should be used along with [wing_span_ft]() and [wing_area_sqft]().
:::

::: command
wing_span_ft \<real-value\>

The wing span of the wing measured in feet. This should be used along with [wing_chord_ft]() and [wing_area_sqft]().
:::

::: command
wing_area_sqft \<real-value\>

The area of the wing measured in square feet. This should be used along with [wing_chord_ft]() and [wing_span_ft]().
:::

::: command
ref_area_sqft \<real-value\>

The aerodynamic reference area of the vehicle measured in square feet. If this is used, [wing_chord_ft](), [wing_span_ft]() and [wing_area_sqft]() should not be used.
:::

::: command
use_reduced_frequency \<boolean-value\>

This should be true when using the \"reduced frequency\" stability derivatives. If false, the rate derivatives (e.g. `cLq_alpha_mach_table`{.interpreted-text role="command"}, `cnr_mach_table`{.interpreted-text role="command"}) will be multiplied by their corresponding angular rates. If true, the rate derivatives will instead be multiplied by a non-dimensional reduced frequency. For pitching moment and lift coefficient derivatives, the reduced frequency is calculated as the angular rate times the `wing_chord_ft`{.interpreted-text role="command"} divided by twice the speed. All other coefficient derivatives use `wing_span_ft`{.interpreted-text role="command"} instead of chord. See `P6DOF Theory<reduced_frequency>`{.interpreted-text role="ref"} for more information.
:::

------------------------------------------------------------------------

::: command
aero_center_x \<length-value\>

The aerodynamic center (x-axis) of the vehicle relative to the reference point in object coordinates. This \"aero-center\" is the \"aerodynamic reference point\" at which aerodynamic forces and moments will be calculated. The typical definition of aerodynamic center (as it relates to longitudinal stability) is the location at which the moment does not change regardless of the angle of attack. Thus, the pitching moment remains constant at that particular point. Hence, the aerodynamic center, rather than the center of pressure, is used in the analysis of longitudinal stability.
:::

::: command
aero_center_y \<length-value\>

The aerodynamic center (y-axis) of the vehicle relative to the reference point in object coordinates.
:::

::: command
aero_center_z \<length-value\>

The aerodynamic center (z-axis) of the vehicle relative to the reference point in object coordinates.
:::



> Sample 2-dimensional table:
>
>     sample_cL_alpha_mach_table
>        regular_table
>           independent_variable mach precision float 
>              0.000 0.900 1.2
>           end_independent_variable        
>           independent_variable alpha units deg 
>              0.0 18.0 20.0
>           end_independent_variable
>           dependent_variable precision float 
>              0.0 1.2 1.3 0.0 1.2 1.3 0.0 1.2 1.3
>           end_dependent_variable
>        end_regular_table
>     end_sample_cL_alpha_mach_table

::: command
cL_alpha_beta_mach_table

This is a 3-dimensional regular_table that outputs the lift coefficient (cL) based on Mach, angle of attack (alpha), and angle of sideslip (beta), with a table order of Mach-alpha-beta-value.
:::



::: command
cLq_alpha_mach_table

This is a 3-dimensional regular_table that outputs the lift coefficient (cL) based on Mach, angle of attack (alpha), and pitch rate (q), with a table order of Mach-alpha-q-value.
:::

::: command
cL_alphadot_alpha_mach_table

This is a 3-dimensional regular_table that outputs the lift coefficient (cL) based on Mach, angle of attack (alpha), and alpha-dot (the rate of change of alpha), with a table order of Mach-alpha-alphaDot-value.
:::



::: command
flaps_dcL_mach_table

This is a 1-dimensional table that outputs the lift coefficient (cL) increment from full flaps deployment based on Mach, with a table order of Mach-value.
:::

::: command
spoilers_dcL_mach_table

This is a 1-dimensional table that outputs the lift coefficient (cL) increment from full spoilers deployment based on Mach, with a table order of Mach-value.
:::



::: command
cd_alpha_beta_mach_table

This is a 3-dimensional regular_table that outputs the drag coefficient (cd) based on the Mach, angle of attack (alpha), and angle of sideslip (beta), with a table order of Mach-alpha-beta-value.
:::



::: command
flaps_dcd_mach_table

This is a 1-dimensional table that outputs the cd increment from full flaps deployment based on Mach, with a table order of Mach-value.
:::

::: command
spoilers_dcd_mach_table

This is a 1-dimensional table that outputs the cd increment from full spoilers deployment based on Mach, with a table order of Mach-value.
:::

::: command
speedbrake_dcd_mach_table

This is a 1-dimensional table that outputs the cd increment from full speedbrake deployment based on Mach, with a table order of Mach-value.
:::



::: command
cy_alpha_beta_mach_table

This is a 3-dimensional regular_table that outputs the side force coefficient (cy) based on the Mach, angle of attack (alpha), and angle of sideslip (beta), with a table order of Mach-alpha-beta-value.
:::



::: command
cyr_beta_mach_table

This is a 3-dimensional regular_table that outputs the cy based on the Mach, beta, and yaw rate (q), with a table order of Mach-beta-q-value.
:::

::: command
cy_betadot_beta_mach_table

This is a 3-dimensional regular_table that outputs the cy based on the Mach, beta, and rate of change of sideslip (beta-dot), with a table order of Mach-beta-betaDot-value.
:::



::: command
cm_alpha_beta_mach_table

This is a 3-dimensional regular_table that outputs the pitch coefficient (cm) based on the Mach, angle of attack (alpha), and angle of sideslip (beta), with a table order of Mach-alpha-beta-value.
:::

::: command
cmq_mach_table

This is a simple table that outputs the pitch damping coefficient due to pitch rate (cmq) based on Mach.
:::

::: command
cmp_mach_table

This is a simple table that outputs the pitch damping coefficient due to roll rate (cmp) based on Mach.
:::

::: command
cm_alphadot_mach_table

This is a 2-dimensional regular_table that outputs the pitch (damping) coefficient (cm) based on the Mach and the rate of change of alpha (alpha-dot), with a table order of Mach-alphaDot-value.
:::



::: command
maximum_pitch_acceleration_mach_table

This is a 1-dimensional table that outputs the maximum pitching acceleration from stick-back control at standard sea-level conditions,

:   in deg/s/s, based on Mach, with a table order of Mach-value. The output of this table is reduced with ambient air density, and is modified as a function of angle-of-attack.
:::

::: command
alpha_stabilizing_frequency_mach_table

This is a 1-dimensional table that outputs the stabilizing frequency in pitch at standard sea-level conditions, in Hz,

:   based on Mach, with a table order of Mach-value. The output of this table is reduced with ambient air density, and is modified as a function of angle-of-attack.
:::



::: command
cn_alpha_beta_mach_table

This is a 3-dimensional regular_table that outputs the yaw coefficient (cn) based on the Mach, angle of attack (alpha), and angle of sideslip (beta), with a table order of Mach-alpha-beta-value.
:::

::: command
cn_betadot_mach_table

This is a 2-dimensional regular_table that outputs the yaw coefficient (cn) based on the Mach and rate of change of sideslip angle (beta-dot), with a table order of Mach-betaDot-value.
:::

::: command
cnr_mach_table

This is a simple table that outputs the yaw damping coefficient due to yaw rate (cnr) based on Mach.
:::

::: command
cnp_mach_table

This is a simple table that outputs the yaw damping coefficient due to roll rate (cnp) based on Mach.
:::



::: command
maximum_yaw_acceleration_mach_table

This is a 1-dimensional table that outputs the maximum yawing acceleration from rudder-right control at standard sea-level conditions,

:   in deg/s/s, based on Mach, with a table order of Mach-value. The output of this table is reduced with ambient air density, and is modified as a function of sideslip.
:::

::: command
beta_stabilizing_frequency_mach_table

This is a 1-dimensional table that outputs the stabilizing frequency in yaw at standard sea-level conditions, in Hz,

:   based on Mach, with a table order of Mach-value. The output of this table is reduced with ambient air density, and is modified as a function of sideslip.
:::



::: command
cl_alpha_beta_mach_table

This is a 3-dimensional regular_table that outputs the roll coefficient (cl) based on the Mach, angle of attack (alpha), and angle of sideslip (beta), with a table order of Mach-alpha-beta-value.
:::

::: command
clp_mach_table

This is a simple table that outputs the roll damping coefficient due to roll rate (clp) based on Mach.
:::

::: command
cl_alphadot_mach_table

This is a 2-dimensional regular_table that outputs the roll coefficient (cl) based on the Mach and the rate of change of angle of attack (alpha), with a table order of Mach-alphaDot-value.
:::

::: command
cl_betadot_mach_table

This is a 2-dimensional regular_table that outputs the roll coefficient (cl) based on the Mach and the rate of change of angle of sideslip (beta), with a table order of Mach-betaDot-value.
:::

::: command
clr_mach_table

This is a simple table that outputs the roll damping coefficient due to yaw rate (clr) based on Mach.
:::

::: command
clq_mach_table

This is a simple table that outputs the roll damping coefficient due to pitch rate (clq) based on Mach.
:::



::: command
maximum_roll_acceleration_mach_table

This is a 1-dimensional table that outputs the maximum rolling acceleration from stick-right control at standard sea-level conditions,

:   in deg/s/s, based on Mach, with a table order of Mach-value. The output of this table is reduced with ambient air density, and is modified as a function of total angle-of-attack.
:::

::: command
roll_stabilizing_frequency_mach_table

This is a 1-dimensional table that outputs the stabilizing frequency in roll at standard sea-level conditions, in Hz,

:   based on Mach, with a table order of Mach-value. The output of this table is reduced with ambient air density, and is modified as a function of angle-of-attack.
:::



::: command
aero_mode \<string\> \... end_aero_mode

Aero modes may be defined to allow a vehicle to have discrete aerodynamic modes, such as a retarded-fall bomb that has two aero modes \-- clean and high-drag. Aero modes support all commands that aero data support with the exception of an aero_mode (aero_modes can not be nested within each other). Each aero mode name must be unique for that object. Aero modes are typically changed using sequencers and the `action_change_aero_mode`{.interpreted-text role="command"}.

::: parsed-literal

aero_mode \<string\>

:   \... [aero_data]() commands \...

end_aero_mode
:::
:::



Secondary aerodynamics include aerodynamic effects produced by control surfaces or other components, such as landing gear, that produce aero effects as a function of angle. For example, retractable landing gear produce significant drag when extended (angle of 90 deg) and no drag when retracted (angle of 0 deg).

::: command
aero_component \... end_aero_component

The use of aero components can be simple, such as having three \"generic\" yaw, pitch and roll aero components, or can be more complex and realistic, such as having sixteen specific components: LeftAileron, RightAileron, LeftStabilator, RightStabilator, LeftRudder, RightRudder, LeftSpeedbrake, RightSpeedbrake, LeftFlap, RightFlap, LeftSpoiler, RightSpoiler, NoseLandingGear, LeftLandingGear, RightLandingGear, and ArrestingHook. During runtime, the position of aero components is controlled by the `SixDOF_Flight_Control_System_Label`{.interpreted-text role="ref"}.

::: parsed-literal
aero_component

> [type]() \... [ref_area_sqft]() \...
>
> [use_reduced_frequency]() \...
>
> `cL_angle_alpha_mach_table`{.interpreted-text role="command"} \... [cd_angle_alpha_mach_table]() \... [cd_angle_beta_mach_table]() \... [cd_angle_mach_table]() \... [cy_angle_beta_mach_table]() \... [cm_angle_alpha_mach_table]() \... [cmq_angle_mach_table]() \... [cn_angle_beta_mach_table]() \... [cnr_angle_mach_table]() \... [cl_angle_alpha_beta_table]() \... [clp_angle_mach_table]() \... [clq_angle_mach_table]() \... [clr_angle_mach_table]() \...

end_aero_component
:::
:::

::: command
type \<string\>

This sets the name/type of the aero component. The name must be unique within the set of aero components for the given `rigid_body_vehicle_type`{.interpreted-text role="command"}.

::: warning
::: title
Warning
:::

In order for an aero_component to operate, it must be \"connected\" to `flight_controls`{.interpreted-text role="command"} via the control_surface command in `flight_controls`{.interpreted-text role="command"}.

The control_surface name and the aero_component type must match exactly and are case-sensitive.
:::
:::

::: command
ref_area_sqft \<area-value\>

This is the reference aerodynamic area for the aero component. If this is not specified, the [wing_area_sqft]() defined in [aero_data]() is used.
:::

::: command
cL_angle_alpha_mach_table

This is a 3-dimensional regular_table that outputs the lift coefficient (cL) based on the Mach, angle of attack (alpha), and control surface angle, with a table order of Mach-alpha-angle-value. Sample 3-dimensional table:

    sample_cL_angle_alpha_mach_table
       regular_table
          independent_variable mach precision float 
             0.000 0.900 1.2
          end_independent_variable         
          independent_variable alpha units deg 
             0.0 18.0 20.0
          end_independent_variable
          independent_variable angle units deg 
             -10.0 0.0 10.0
          end_independent_variable
          dependent_variable precision float 
             -1.0 0.0 1.0  -1.0 0.0 1.0  -1.0 0.0 1.0
             -1.0 0.0 1.0  -1.0 0.0 1.0  -1.0 0.0 1.0
             -1.0 0.0 1.0  -1.0 0.0 1.0  -1.0 0.0 1.0
          end_dependent_variable
       end_regular_table
    end_sample_cL_angle_alpha_mach_table
:::

::: command
cd_angle_alpha_mach_table

This is a 3-dimensional regular_table that outputs the drag coefficient (cd) based on the Mach, angle of attack (alpha), and angle of the control surface, with a table order of Mach-alpha-angle-value. This derivative should be used when drag is only a function of alpha (not beta). When this derivative is used, cd_angle_beta_mach_table and cd_angle_mach_table should not be used.
:::

::: command
cd_angle_beta_mach_table

This is a 3-dimensional regular_table that outputs the drag coefficient (cd) based on the Mach, angle of sideslip (beta), and angle of the control surface, with a table order of Mach-beta-angle-value. This derivative should be used when drag is only a function of beta (not alpha). When this derivative is used, cd_angle_alpha_mach_table and cd_angle_mach_table should not be used.
:::

::: command
cd_angle_mach_table

This is a 2-dimensional regular_table that outputs the drag coefficient (cd) based on the Mach and the angle of the control surface, with a table order of Mach-angle-value. This derivative should be used when drag is a function of both alpha and beta. (Although this function does not include either alpha or beta, it is more appropriate than either the pro-alpha or pro-beta derivatives.) When this derivative is used, cd_angle_alpha_mach_table and cd_angle_beta_mach_table should not be used.
:::

::: command
cy_angle_beta_mach_table

This is a 3-dimensional regular_table that outputs the side force coefficient (cy) based on the Mach, angle of sideslip (beta), and angle of the control surface, with a table order of Mach-beta-angle-value..
:::

::: command
cm_angle_alpha_mach_table

This is a 3-dimensional regular_table that outputs the pitch coefficient (cm) based on the Mach, angle of attack (alpha), and angle of the control surface, with a table order of Mach-alpha-angle-value.
:::

::: command
cmq_angle_mach_table

This is a 2-dimensional regular_table that outputs the pitch damping coefficient due to pitch rate (cmq) based on Mach and the angle of the control surface.
:::

::: command
cn_angle_beta_mach_table

This is a 3-dimensional regular_table that outputs the yaw coefficient (cn) based on the Mach, angle of sideslip (beta), and angle of the control surface, with a table order of Mach-beta-angle-value.
:::

::: command
cnr_angle_mach_table

This is a 2-dimensional regular_table that outputs the yaw damping coefficient due to yaw rate (cnr) based on Mach and the angle of the control surface.
:::

::: command
cl_angle_alpha_beta_table

This is a 3-dimensional regular_table that outputs the roll coefficient (cl) based on the angle of the attack (alpha), the angle of slideslip (beta) and the angle of the control surface, with a table order of alpha-beta-angle-value.
:::

::: command
clp_angle_mach_table

This is a 2-dimensional regular_table that outputs the roll damping coefficient due to roll rate (clp) based on Mach and the angle of the control surface.
:::

::: command
clq_angle_mach_table

This is a 2-dimensional regular_table that outputs the roll damping coefficient due to pitch rate (clq) based on Mach and the angle of the control surface.
:::

::: command
clr_angle_mach_table

This is a 2-dimensional regular_table that outputs the roll damping coefficient due to yaw rate (clr) based on Mach and the angle of the control surface.
:::

------------------------------------------------------------------------

Return to `six_dof_object_types`{.interpreted-text role="doc"}, `rigid_body_vehicle_type`{.interpreted-text role="doc"}, `point_mass_vehicle_type`{.interpreted-text role="doc"}