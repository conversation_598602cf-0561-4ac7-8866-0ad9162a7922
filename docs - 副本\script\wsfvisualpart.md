orphan

:   

# WsfVisualPart

## Static Methods {#static-methods .WsfVisualPart .inherits .WsfArticulatedPart}

::: method
static bool IsA_TypeOf(string aDerivedType, string aBaseType)

Returns true if the part type specified by **aDerivedType** inherits from the part type specified by **aBaseType** (compare to `WsfObject.IsA_TypeOf(aBaseType) <WsfObject.IsA_TypeOf>`{.interpreted-text role="method"}, which determines if a specific object inherits from **aBaseType**).

This method can be used when one only knows the type of the part (as a string) and does not have a reference to the actual part.
:::

## Methods

::: method
bool TurnOff()

Turns the sensor off and returns true if successful.
:::

::: method
bool TurnOn()

Turns the sensor on and returns true if successful.
:::
