# network.rst.txt.md
orphan

:   



see `Predefined_Network_Types`{.interpreted-text role="ref"}

<PERSON>ript Class `WsfNetwork`{.interpreted-text role="class"}

::: {.command block=""}
network
:::

::: parsed-literal
network \<name\> \<base-type\>

> [network_address]() \...

end_network
:::



The network command defines a user-specified network in the AFSIM communications environment. Networks are auto-generated by comm definitions in AFSIM, unless explicitly instantiated in the scenario input by using this command. All networks defined by the network command are instantiated and available for assignment of comms to these networks by using the network name, or an address if one was provided for the network. Networks not provided a specific address will be given a dynamically assigned address with CIDR value of 24 for 254 assignable comms objects.



::: command
network_address \<address\>

If specified, assigns the network to the provided address. The number of assignable objects to this address will also be defined by the address CIDR value. The address provided for this network must not collide with any other network or reserved addresses.
:::