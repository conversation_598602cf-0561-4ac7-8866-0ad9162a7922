orphan

:   

# Delta_V

**Script Type:** `WsfDeltaV_Maneuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} delta_v

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [delta_v](#delta_v) \...

end_maneuver
:::

A maneuver representing a change in the platform\'s ECI velocity.

::: command
delta_v \<frame\> \<speed-value\> \<speed-value\> \<speed-value\>

The desired change in the velocity of the platform, given in the specified orbital reference frame.

The allowed value for the reference frame are:

:   -   **inertial** for a velocity change given in the inertial frame (typically the `ECI`{.interpreted-text role="ref"} frame, but this may represent equivalent frames for other central bodies if the platform is being evolved with a central body other than the Earth);
    -   **ric** for a velocity change given in the executing platform\'s RIC frame.
:::

::: command
dv_x \<speed-value\>

The desired change in the ECI x component of the velocity.

::: note
::: title
Note
:::

This command is deprecated. Please use [delta_v](#delta_v).
:::
:::

::: command
dv_y \<speed-value\>

The desired change in the ECI y component of the velocity.

::: note
::: title
Note
:::

This command is deprecated. Please use [delta_v](#delta_v).
:::
:::

::: command
dv_z \<speed-value\>

The desired change in the ECI z component of the velocity.

::: note
::: title
Note
:::

This command is deprecated. Please use [delta_v](#delta_v).
:::
:::
