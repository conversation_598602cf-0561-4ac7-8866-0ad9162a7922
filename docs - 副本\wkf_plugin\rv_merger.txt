# rv_merger.rst.txt.md
orphan

:   



The Merge Recordings feature is found in the Tools menu.

This feature allows the user to select several recording (.aer) files from a distributed simulation and combine them into a single file.

In order to use this feature, a scenario must have XIO enabled and include XIO events in the event_pipe (enabled by default). XIO events provide the necessary information to synchronize timing and platform details between simulations. The final merged recording will not contain any XIO events, meaning it cannot be used for merging with another recording.



Click \"Add\" to select files to merge. Click \"Remove\" to remove highlighted files from the list. Click \"Browse\" to select the file to save to. Click \"Ok\" to perform the merge.

The master recording, selected from the drop-down menu, will be used for simulation date, time, and classification information.