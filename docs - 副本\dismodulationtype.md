orphan

:   

# DisModulationType {#DisModulationType}

::: DisModulationType
::: parsed-literal
`DisModulationType.SpreadSpectrum`{.interpreted-text role="method"} `DisModulationType.Major`{.interpreted-text role="method"} `DisModulationType.Detail`{.interpreted-text role="method"} `DisModulationType.System`{.interpreted-text role="method"}
:::
:::

## Overview

[DisModulationType](#dismodulationtype) is an implementation of the DIS modulation type. The modulation type is used by DIS on `DisTransmitter`{.interpreted-text role="class"} PDUs.

## Methods

::: method
int SpreadSpectrum()

Returns the spread spectrum value
:::

::: method
int Major()

Returns the major value.
:::

::: method
int Detail()

Returns the detail value.
:::

::: method
int System()

Returns the system value.
:::
