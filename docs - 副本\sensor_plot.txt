# sensor_plot.rst.txt.md
orphan

:   



::: {.command block=""}
sensor_plot
:::



sensor_plot is a WSF application that is used to evaluate sensor characteristics and interactions with user-specified geometries. It has the ability to create files that produce plots of

-   `Sensor vertical coverage <sensor_plot.vertical_coverage>`{.interpreted-text role="command"}
-   `Sensor vertical map <sensor_plot.vertical_map>`{.interpreted-text role="command"}
-   `Sensor horizontal coverage <sensor_plot.horizontal_coverage>`{.interpreted-text role="command"}
-   `Sensor horizontal map <sensor_plot.horizontal_map>`{.interpreted-text role="command"}
-   `Sensor spherical coverage <sensor_plot.spherical_map>`{.interpreted-text role="command"}
-   `Flight path analysis <sensor_plot.flight_path_analysis>`{.interpreted-text role="command"}
-   `Clutter table <sensor_plot.clutter_table>`{.interpreted-text role="command"}
-   `Defended area of a collection of sensors <sensor_plot.horizontal_map>`{.interpreted-text role="command"}
-   `Antenna patterns <sensor_plot.antenna_plot>`{.interpreted-text role="command"}



Using the stand-alone sensor_plot.exe:

    sensor_plot file <sub>1</sub> [ file<sub>2</sub> ... file<sub>n</sub> ]

For other WSF applications supporting sensor_plot:

    wsf_application.exe --sensor_plot file <sub>1</sub> [ file<sub>2</sub> ... file<sub>n</sub> ]

Where the arguments are the names of files that contain:

-   A sensor_plot input block that defines the function to be performed.
-   Sensor and platform definitions required for the function to be performed.



::: command
antenna_plot `Antenna_Plot_Commands<antenna_plot>`{.interpreted-text role="command"} end_antenna_plot

Generates files 2D constant azimuth or elevation or 3D polar plot files.

::: parsed-literal

antenna_plot

:   \... `Antenna Plot Commands<antenna_plot>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_antenna_plot
:::
:::

::: command
clutter_table `Clutter_Table_Commands<clutter_table>`{.interpreted-text role="command"} end_clutter_table

Generates a radar clutter table that is used in an WSF simulation.

::: parsed-literal

clutter_table

:   \... `Clutter Table Commands<clutter_table>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_clutter_table
:::
:::

::: command
flight_path_analysis `Flight_Path_Analysis_Commands<flight_path_analysis>`{.interpreted-text role="command"} end_flight_path_analysis

Generates a plot of variables along a flight path.

::: parsed-literal

flight_path_analysis

:   \... `Flight Path Analysis Commands<flight_path_analysis>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_flight_path_analysis
:::
:::

::: command
horizontal_map `Horizontal_Map_Commands<horizontal_map>`{.interpreted-text role="command"} end_horizontal_map

Generates a plot of variables over a matrix of down-range and cross-range values.

::: parsed-literal

horizontal_map

:   \... `Horizontal Map Commands<horizontal_map>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_horizontal_map
:::
:::

::: command
horizontal_coverage `Horizontal_Coverage_Commands<horizontal_coverage>`{.interpreted-text role="command"} end_horizontal_coverage

Generates a \'horizontal coverage diagram\' (or \'horizontal coverage envelope\') plot file.

::: parsed-literal

horizontal_coverage

:   \... `Horizontal Coverage Commands<horizontal_coverage>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_horizontal_coverage
:::
:::

::: command
spherical_map `Spherical_Map_Commands<spherical_map>`{.interpreted-text role="command"} end_spherical_map

Generates a plot of variables at a specified range over a matrix of viewing angles.

::: parsed-literal

spherical_map

:   \... `Spherical Map Commands<spherical_map>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_spherical_map
:::
:::

::: command
vertical_map `Vertical_Map_Commands<vertical_map>`{.interpreted-text role="command"} end_vertical_map

Generates a plot of variables over a matrix of altitude and ground_range values.

::: parsed-literal

vertical_map

:   \... `Vertical Map Commands<vertical_map>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_vertical_map
:::
:::

::: command
vertical_coverage `Vertical_Coverage_Commands<vertical_coverage>`{.interpreted-text role="command"} end_vertical_coverage

Generates a \'vertical coverage diagram\' (or \'vertical coverage envelope\') plot file.

::: parsed-literal

vertical_coverage

:   \... `Vertical Coverage Commands<vertical_coverage>`{.interpreted-text role="command"} \... \... `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} \...

end_vertical_coverage
:::
:::



Existing scenarios are often used to create `horizontal_map`{.interpreted-text role="command"} plots. These scenarios may include platform or platform subsystem type definitions that use capabilities that are not provided in the `sensor_plot`{.interpreted-text role="command"} executable (e.g., weapon models). In order to allow the scenario to be used without alteration, commands and dummy type definitions have been provided to simulate the availability of the unimplemented features.

The following commands provide mechanisms to ignore the specified global commands:

::: command
ignore_block \<word\>

Causes all data starting with the word \<word\> up to the word end\_\<word\> to be ignored.
:::

::: command
ignore_line \<word\>

Causes the word \<word\> and all subsequent words on the same line to be ignored.
:::

::: command
ignore_word \<word\>

Causes the word \<word\> to be ignored.
:::

The following dummy types are provided:

-   WSF_DUMMY_COMM (a dummy `comm`{.interpreted-text role="command"} definition)
-   WSF_DUMMY_MOVER (a dummy `mover`{.interpreted-text role="command"} definition)
-   WSF_DUMMY_PROCESSOR (a dummy `processor`{.interpreted-text role="command"} definition)
-   WSF_DUMMY_SENSOR (a dummy `sensor`{.interpreted-text role="command"} definition)
-   WSF_DUMMY_WEAPON (a dummy `weapon`{.interpreted-text role="command"} definition)
-   WSF_DUMMY_WEAPON_EFFECTS (a dummy `weapon_effects`{.interpreted-text role="command"} definition).