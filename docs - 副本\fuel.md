orphan

:   

# fuel

see `Predefined_Fuel_Types`{.interpreted-text role="ref"}

Script Class: `WsfFuel`{.interpreted-text role="class"}

::: {.command block=""}
fuel \... end_fuel
:::

\# 1. Define a fuel type (occurs outside a platform or platform_type command)

::: parsed-literal

fuel \<new-type\> \<base-type\>

:   \... `Platform_Part_Commands`{.interpreted-text role="ref"} \...

    [maximum_quantity]() \... [initial_quantity]() \... [reserve_quantity]() \... [mode]() \...

    [on_bingo]() \... end_on_bingo [on_empty]() \... end_on_empty [on_refuel]() \... end_on_refuel [on_reserve]() \... end_on_reserve

    \... type-specific fuel commands \...

end_fuel
:::

\# 2. Instantiate a fuel object on a new platform type:

::: parsed-literal

platform_type \...

:   

    fuel \<type\>

    :   \... desired attributes and commands \...

    end_fuel

end_platform
:::

\# 3. Instantiate a (not previously existing) fuel object on a platform instance:

::: parsed-literal

platform \...

:   

    add fuel \<type\>

    :   \... desired attributes and commands \...

    end_fuel

end_platform
:::

\# 4. Modify a (previously existing) fuel object on a platform instance:

::: parsed-literal

platform \...

:   

    edit fuel

    :   \... additional/changed/overwritten attributes and commands \...

    end_fuel

end_platform
:::

## Overview

A fuel object defines the rate at which fuel is expended on the platform. For each `platform`{.interpreted-text role="command"}, total mass is assumed to be the sum of empty mass, fuel mass, and payload mass. Empty mass is generally a fixed quantity, payload varies perhaps only at discrete events, but fuel quantities are consumed continuously. The `fuel`{.interpreted-text role="command"} object is called whenever the platform moves and updates the amount of fuel expended. The resulting fuel amount is supplied back to the platform, so that total mass is readily available for Newtonian dynamics and other such calculations.

::: note
::: title
Note
:::

If a platform does not have a `fuel`{.interpreted-text role="command"} object, run-time fuel computations will not be performed, but a fixed fuel quantity may still be supplied as `platform`{.interpreted-text role="command"} input.
:::

## Commands {#Fuel.Commands}

::: command
maximum_quantity \<mass-value\>

Defines the maximum quantity of fuel that can be carried.

**Default** infinity
:::

::: command
initial_quantity \<mass-value\>

Defines the initial quantity of fuel.

**Default** 0 kg
:::

::: command
reserve_quantity \<mass-value\>

Defines the threshold such that when the quantity of fuel remaining falls below this value, the platform is considered to be operating on reserves. If an [on_reserve]() block is defined, it will be executed when this state is reached.

**Default** 0 kg
:::

::: command
bingo_quantity \<mass-value\>

Defines the threshold such that when the quantity of fuel remaining falls below this value, the platform is considered to in BINGO. If an [on_bingo]() block is defined, it will be executed when this state is reached.

**Default** 0 kg
:::

::: command
mode \<mode-name\>

Specifies the name of the mode to be used for fuel types that support modes.
:::

## Script Interface

In each of the following scripts, the following variables will be predefined:

-   `WsfFuel`{.interpreted-text role="class"} this; // This fuel object
-   `WsfPlatform`{.interpreted-text role="class"} PLATFORM; // The platform containing this fuel object
-   double TIME_NOW; // The current simulation time

::: command
on_bingo \... \<script-definition\> \... end_on_bingo

Defines a script to be executed when the quantity of fuel remaining falls below the threshold defined by [bingo_quantity]().
:::

::: command
on_empty \... \<script-definition\> \... end_on_empty

Defines a script to be executed when all fuel has been expended.
:::

::: command
on_reserve \... \<script-definition\> \... end_on_reserve

Defines a script to be executed when the quantity of fuel remaining falls below the threshold defined by [reserve_quantity]().
:::

::: command
on_refuel \... \<script-definition\> \... end_on_refuel

Defines a script to be executed when a refueling operation has been completed.
:::
