orphan

:   

# WsfSpaceOrientation

::: {.WsfSpaceOrientation .inherits .WsfObject}
Specify one of the standard `orientation types<attitude_controller.orientation>`{.interpreted-text role="command"} to be passed as an argument to `WsfSpaceMover.SetOrientation`{.interpreted-text role="method"}.
:::

## Static Methods

::: method
WsfSpaceOrientation NONE()

No orientation constraints will be applied.
:::

::: method
WsfSpaceOrientation NADIR_WITH_ECI_VELOCITY_CONSTRAINT()

Specify z-axis nadir orientation with `ECI <ECI>`{.interpreted-text role="ref"} x-axis velocity constraint.
:::

::: method
WsfSpaceOrientation NADIR_WITH_ECEF_VELOCITY_CONSTRAINT()

Specify z-axis nadir orientation with ECEF (`WCS <WCS>`{.interpreted-text role="ref"}) x-axis velocity.
:::

::: method
WsfSpaceOrientation SOLAR_WITH_NADIR_CONSTRAINT()

Specify z-axis Solar orientation with nadir constraint.
:::

::: method
WsfSpaceOrientation NADIR_WITH_SOLAR_CONSTRAINT()

Specify z-axis Nadir orientation with solar constraint.
:::

::: method
WsfSpaceOrientation ECI_VELOCITY_WITH_NADIR_CONSTRAINT()

Specify x-axis `ECI <ECI>`{.interpreted-text role="ref"} velocity orientation with z-axis nadir constraint.
:::

::: method
WsfSpaceOrientation ECI_VELOCITY_WITH_SOLAR_CONSTRAINT()

Specify x-axis `ECI <ECI>`{.interpreted-text role="ref"} velocity orientation with z-axis solar constraint.
:::

::: method
WsfSpaceOrientation ENTITY_WITH_SOLAR_CONSTRAINT(WsfPlatform aEntity) WsfSpaceOrientation ENTITY_WITH_SOLAR_CONSTRAINT(WsfTrackId aTrackId)

Specify x-axis Resident entity (platform or track) orientation with z-axis solar constraint.

::: note
::: title
Note
:::

If pointing to a track, the provided track id must reference a valid track with a valid location in the parent platform\'s `master track list<WsfPlatform.MasterTrackList>`{.interpreted-text role="method"}.
:::
:::

::: method
WsfSpaceOrientation ENTITY_WITH_NADIR_CONSTRAINT(WsfPlatform aEntity) WsfSpaceOrientation ENTITY_WITH_NADIR_CONSTRAINT(WsfTrackId aTrackId)

Specify x-axis Resident entity (platform or track) orientation with z-axis nadir constraint.

::: note
::: title
Note
:::

If pointing to a track, the provided track id must reference a valid track with a valid location in the parent platform\'s `master track list<WsfPlatform.MasterTrackList>`{.interpreted-text role="method"}.
:::
:::

::: method
WsfSpaceOrientation ENTITY_WITH_ORBIT_PLANE_CONSTRAINT(WsfPlatform aEntity) WsfSpaceOrientation ENTITY_WITH_ORBIT_PLANE_CONSTRAINT(WsfTrackId aTrackId)

Specify x-axis Resident entity (platform or track) orientation with z-axis orbit plane constraint.

::: note
::: title
Note
:::

If pointing to a track, the provided track id must reference a valid track with a valid location in the parent platform\'s `master track list<WsfPlatform.MasterTrackList>`{.interpreted-text role="method"}.
:::
:::

::: method
WsfSpaceOrientation POINT_WITH_ORBIT_PLANE_CONSTRAINT(WsfGeoPoint aOrientation)

Specify x-axis geo-point orientation with z-axis orbit plane constraint.
:::

## Methods

::: method
void SwapAxes()

Swap the default pointing axis and constraint axis.

::: note
::: title
Note
:::

For example, if `WsfSpaceOrientation.NADIR_WITH_ECI_VELOCITY_CONSTRAINT`{.interpreted-text role="method"} is specified and SwapAxes is called, the x-axis will point at the nadir, and the z-axis will point in the direction of the velocity constraint.
:::
:::
