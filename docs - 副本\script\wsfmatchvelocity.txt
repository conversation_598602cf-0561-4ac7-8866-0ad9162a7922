# wsfmatchvelocity.rst.txt.md
orphan

:   



::: {.WsfMatchVelocity .inherits .WsfOrbitalManeuver}
Input type: `match_velocity<../match_velocity>`{.interpreted-text role="doc"}
:::

[WsfMatchVelocity](#wsfmatchvelocity) matches the velocity contained in a `local track<WsfLocalTrack>`{.interpreted-text role="class"}.

> ::: note
> ::: title
> Note
> :::
>
> If the location of the maneuvering platform is different from the projected target track location, the velocity vector will be rotated into the frame of the maneuvering platform to compensate for differences in the local coordinate system.
> :::

::: method
static WsfMatchVelocity Construct(WsfOrbitalEventCondition aCondition, WsfTrackId aTrackId)

Static method to create a [WsfMatchVelocity](#wsfmatchvelocity) object, matching the velocity contained in the local track with the given `WsfTrackId.`{.interpreted-text role="class"}
:::