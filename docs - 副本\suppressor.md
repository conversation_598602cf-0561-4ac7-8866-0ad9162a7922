orphan

:   

::: demo
suppressor
:::

| This directory contains a conversion of a major demonstration case from
| Suppressor 6.0 as it was converted by <PERSON> and others.
| 
| The main input files are:

>   ----------------------- --------------------------------------------------
>   bomb_demo.txt           The \'bomb_demo\' run input (like the MOD file).
>
>   bomb_demo_laydown.txt   The \'bomb_demo\' laydown (like the SDB file).
>   ----------------------- --------------------------------------------------

| The \'type\' definitions that would have been present in the Suppressor TDB files
| are in the directories
| 
|    DISRUPTORS
|    PATTERNS
|    PLATFORMS
|    SENSORS
|    WEAPONS
| 
| To run the scenario, set bomb_demo.txt as the startup file.

# Common Conversion Items

| - By default a SUPPRESSOR sensor reports IFF (see SNR-CHARACTERISTICS) while
|   WSF defaults to not reporting IFF. There every sensor that does not have a
|   NO-IFF in the SNR-CHARACTERISTICS should specify \'reports_iff\' in the WSF
|   sensor definition.
| 
| - By default WSF drops the local track when all contributors have dropped
|   (either explicitly by track drop messages or implicitly through raw track
|   purging).
| 
|   On the other hand, SUPPRESSOR continues to maintain the track even in the
|   absence of contributors UNTIL the TIME-BEFORE-DROP expires. To prevent WSF
|   from dropping the track when it no longer has contributors, we must add the
|   the following:
| 
|     track_manager
|       uncorrelated_track_drops off
|     end_track_manager
| 
| - By default SUPPRESSOR performs velocity changes uniformly over the distance
|   between two waypoints. WSF will do the same thing if \'maximum_linear\_
|   acceleration\' is NOT specified. Therefore, if uniform velocity changes are
|   desired, do NOT specify a \'maximum_linear_acceleration\' for the mover.
