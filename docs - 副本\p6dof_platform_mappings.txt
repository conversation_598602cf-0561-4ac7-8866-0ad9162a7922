# p6dof_platform_mappings.rst.txt.md
orphan

:   



P6DOF objects may contain P6DOF subobjects that do not have a `platform`{.interpreted-text role="command"} until they are jettison/released from their parent P6DOF object. For example, a fighter aircraft may contain several missile P6DOF subobjects. Such subobjects will lack a platform until they are jettisoned, even though they may contribute mass property and aerodynamic effects to the parent while captive. When such subobjects are jettisoned, the P6DOF/Platform Mapping is consulted to determine which type of AFSIM `platform`{.interpreted-text role="command"} should be created for the P6DOF subobject. The P6DOF object for the subobject will be \'inserted\' into the `WSF_P6DOF_MOVER`{.interpreted-text role="model"} of the newly created `platform`{.interpreted-text role="command"}, automatically conveying the kinematics of the subobject to the `platform`{.interpreted-text role="command"}.

::: command
map_p6dof_object_to_platform \<p6dof_object_type_name\> \<platform_name\> \... end_map_p6dof_object_to_platform

Define a mapping between the specified p6dof_object_type and the specified platform type.
:::

Example [map_p6dof_object_to_platform]():

> ::: parsed-literal
> map_p6dof_object_to_platform
>
> > FA-LGT FA-LGT-Platform SAM-MR TestSAM_Platform F-16C F-16C
>
> end_map_p6dof_object_to_platform
> :::

Note that the same string name may be used for both the `p6dof_object_type`{.interpreted-text role="command"} and the `platform`{.interpreted-text role="command"}, if appropriate (if there is a P6DOF object type and platform with the same name).

Return to `p6dof_object_types`{.interpreted-text role="doc"}