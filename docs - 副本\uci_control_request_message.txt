# uci_control_request_message.rst.txt.md
orphan

:   





This message allows the user to request control over a system (sensor, weapon, etc).



::: method
static UCI_ControlRequestMessage Construct(UCI_Control controlType, UCI_ControlRequest controlRequestType, UCI_SystemId controleeSystemId, UCI_CapabilityId controleeCapabilityId)

The construct method creates an instance of an UCI_ControlRequestMessage, requesting to either acquire or release `control<UCI_Control>`{.interpreted-text role="class"} of the controlee capability of the given system.
:::



::: method
string UUID()

Returns the string value of the ControlRequestID Universally Unique Identifier (UUID).
:::