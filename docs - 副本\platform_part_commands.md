orphan

:   

# Platform Part Commands {#Platform_Part_Commands}

## Overview

This defines the commands that are common to \'platform parts\':

## Commands

::: block
\_.platform_part
:::

::: command
automatic_recovery_time \<time-value\>

Define the time elapsed between damage state 1.0 (complete damage) and queued recovery. Only valid if the time is set to a positive value, and part is declared [restorable](). After time elapses, the Resurrect() virtual method will be called on the part.

Default: Zero (not used)
:::

::: command
aux_data \... aux_data_commands \... end_aux_data

Define auxiliary data for the object. See the `_.aux_data`{.interpreted-text role="command"} command for the syntax of defining auxiliary data.
:::

::: command
category \<name\>

Declare that the object is a member of the specified category. This command may be specified multiple times to indicate the object is a member of multiple categories. Categories can be queried by the script language method `CategoryMemberOf("*name*") <WsfPlatformPart.CategoryMemberOf>`{.interpreted-text role="method"} of `WsfPlatformPart`{.interpreted-text role="class"}.
:::

::: command
clear_categories

Has the effect of canceling any previous [category]() commands.
:::

::: command
critical
:::

::: command
non_critical

If critical, and the part damage becomes 1.0 (broken), the platform will be removed from the simulation.

Default: non-critical
:::

::: command
damage_factor \<unit-value\>

Initial damage factor of the part. Zero is no damage, 1.0 is totally damaged (functionally broken). If damage factor is 1.0, it must be decreased toward zero before the part can become operational, but non_resurrectible will preclude this. External entities or effects may influence the damage level. It is up to the part implementation to determine how its operations are degraded in response to a non-zero damage factor.

Default: 0.0
:::

::: command
debug
:::

::: command
no_debug

Enables or disables debugging for the object. The actual effect of enabling debugging for an object is implementation-specific. Typically a lot of information will be written to standard output, to allow some control of the information displayed use [debug_level]().

Default: no_debug
:::

::: command
debug_level \<level\>

Specifies the debugging level from level \[1, 3\]. Each level normally provides for different types or quantity of debugging information to displayed standard output. Also requires that the [debug]() be set. The actual effect of enabling debugging level for an object is implementation-specific.

Default: 3
:::

::: command
group_join \<group-name\>
:::

::: command
group_leave \<group-name\>

Adds or removes the platform from the specified `group`{.interpreted-text role="command"}.
:::

::: command
is_a_type_of \<type-name\>

The type hierarchy of an object is normally defined by the objects from which the object inherits. This command allows one to define that the object is also of the specified type.
:::

::: command
broken

Part is declared non_operational, completely damaged, and non_resurrectible. Is functionally equivalent to setting the [damage_factor]() to 1.0, [non_restorable](), and [non_operational]() simultaneously. The part cannot be repaired.

Default: functional
:::

::: command
off
:::

::: command
on

Define the initial state of the object to be off or on.

Default: Depends on the type of object. Typically \'on\', but \'off\' for sensors.
:::

::: command
operational
:::

::: command
non_operational

Define the operational state of the object. An object that is operational can be turned off and on and otherwise used for operations. An object that is non_operational cannot be used until it is made operational.

Default: operational
:::

::: command
restorable
:::

::: command
non_restorable

Define the ability of the part to recover from damage factor = 1.0. If non_restorable, the part damage latches at 1.0.

Default: restorable
:::
