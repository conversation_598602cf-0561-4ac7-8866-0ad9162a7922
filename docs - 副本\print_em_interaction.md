orphan

:   

# emi-data

This is documentation for the \<**emi-data**\> field referenced in the `csv_event_output`{.interpreted-text role="command"} and `event_output`{.interpreted-text role="command"} documentation.

::: {.contents local="" depth="1"}
:::

## Possible Signatures

*The documentation has the csv_event_output signatures on separate lines for readability. The actual csv_event_output is on a single line.*

+-------------------------------------------------------------------------------+----------------------------------------------------+--------------------------------------------------------------------+
| Condition                                                                     | CSV Signature                                      | EVT Signature                                                      |
+===============================================================================+====================================================+====================================================================+
| If the EMI is one-way between transmitter and receiver                        | | \<**absolute-location-data** ~xmtr~\>,           | | Xmtr: \<**absolute-location-data**~xmtr~\>                       |
|                                                                               | | \<**absolute-location-data** ~rcvr~\>,           | | Rcvr: \<**absolute-location-data**~rcvr~\>                       |
|                                                                               | | \<**relative-location-data** ~xmtr-\>rcvr~\>,    | | Xmtr-\>Rcvr: \<**relative-location-data**~xmtr-\>rcvr~\>         |
|                                                                               | | \<**relative-location-data** ~rcvr-\>xmtr~\>,    | | Rcvr-\>Xmtr: \<**relative-location-data**~rcvr-\>xmtr~\>         |
|                                                                               | | \<**beam-data**~xmtr~\>,                         | | XmtrBeam: \<**beam-data**~xmtr~\>                                |
|                                                                               | | \<**beam-data**~rcvr~\>,                         | | RcvrBeam: \<**beam-data**~rcvr~\>                                |
|                                                                               | | \<**other-data**\>                               | | \<**other-data**\>                                               |
+-------------------------------------------------------------------------------+----------------------------------------------------+--------------------------------------------------------------------+
| If the EMI is one-way between receiver and target                             | | \<**absolute-location-data**~rcvr~\>,            | | Rcvr: \<**absolute-location-data**~rcvr~\>                       |
|                                                                               | | \<**absolute-location-data**~tgt~\>,             | | Tgt: \<**absolute-location-data**~tgt~\>                         |
|                                                                               | | \<**relative-location-data**~rcvr-\>tgt~\>,      | | Rcvr-\>Tgt: \<**relative-location-data**~rcvr-\>tgt~\>           |
|                                                                               | | \<**relative-location-data**~tgt-\>rcvr~\>,      | | Tgt-\>Rcvr: \<**relative-location-data**~tgt-\>rcvr~\>           |
|                                                                               | | \<**beam-data**~rcvr~\>,                         | | RcvrBeam: \<**beam-data**~rcvr~\>                                |
|                                                                               | | \<**other-data**\>                               | | \<**other-data**\>                                               |
+-------------------------------------------------------------------------------+----------------------------------------------------+--------------------------------------------------------------------+
| If the EMI is bistatic, two-way between transmitter, receiver, and target     | | \<**absolute-location-data**~xmtr~\>,            | | Xmtr: \<**absolute-location-data**~xmtr~\>                       |
|                                                                               | | \<**absolute-location-data**~rcvr~\>,            | | Rcvr: \<**absolute-location-data**~rcvr~\>                       |
|                                                                               | | \<**absolute-location-data**~tgt~\>,             | | Tgt: \<**absolute-location-data**~tgt~\>                         |
|                                                                               | | \<**relative-location-data**~xmtr-\>tgt~\>,      | | Xmtr-\>Tgt: \<**relative-location-data**~xmtr-\>tgt~\>           |
|                                                                               | | \<**relative-location-data**~tgt-\>xmtr~\>,      | | Tgt-\>Xmtr: \<**relative-location-data**~tgt-\>xmtr~\>           |
|                                                                               | | \<**relative-location-data**~rcvr-\>tgt~\>,      | | Rcvr-\>Tgt: \<**relative-location-data**~rcvr-\>tgt~\>           |
|                                                                               | | \<**relative-location-data**~tgt-\>rcvr~\>,      | | Tgt-\>Rcvr: \<**relative-location-data**~tgt-\>rcvr~\>           |
|                                                                               | | \<**beam-data**~xmtr~\>,                         | | XmtrBeam: \<**beam-data**~xmtr~\>                                |
|                                                                               | | \<**beam-data**~rcvr~\>,                         | | RcvrBeam: \<**beam-data**~rcvr~\>                                |
|                                                                               | | \<**other-data**\>                               | | \<**other-data**\>                                               |
+-------------------------------------------------------------------------------+----------------------------------------------------+--------------------------------------------------------------------+
| If the EMI is non-bistatic, two-way between transmitter, receiver, and target | | \<**absolute-location-data**~xmtr/rcvr~\>,       | | Xmtr/Rcvr: \<**absolute-location-data**~xmtr/rcvr~\>             |
|                                                                               | | \<**absolute-location-data**~tgt~\>,             | | Tgt: \<**absolute-location-data**~tgt~\>                         |
|                                                                               | | \<**relative-location-data**~xmtr/rcvr-\>tgt~\>, | | Xmtr/Rcvr-\>Tgt: \<**relative-location-data**~xmtr/rcvr-\>tgt~\> |
|                                                                               | | \<**relative-location-data**~tgt-\>xmtr/rcvr~\>, | | Tgt-\>Xmtr/Rcvr: \<**relative-location-data**~tgt-\>xmtr/rcvr~\> |
|                                                                               | | \<**beam-data**~xmtr/rcvr~\>,                    | | XmtrBeam: \<**beam-data**~xmtr/rcvr~\>                           |
|                                                                               | | \<**beam-data**~tgt~\>,                          | | RcvrBeam: \<**beam-data**~tgt~\>                                 |
|                                                                               | | \<**other-data**\>                               | | \<**other-data**\>                                               |
+-------------------------------------------------------------------------------+----------------------------------------------------+--------------------------------------------------------------------+

To `Top of Page<emi-data>`{.interpreted-text role="ref"}

## Signature Elements

### Expanded Compound Signature Elements

Compound signature elements encapsulate related signature elements and appear in bold. These are included to improve readability of the documentation.

  Field                            CSV                                                                                                                          EVT
  -------------------------------- ---------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  \<**absolute-location-data**\>   \<platform~type~\>, ,                                                                                                        Type: \<platform~type~\>
  \<**relative-location-data**\>   \<range~m~\>, \<brg~true~\>,\<el~true~\>, \<brg~apparent~\>,\<el~apparent~\>                                                 Range: \<range~km~\> km (\<range~nmi~\> nm) Brg: \<brg~true~\> El: \<el~true~\> Apparent: Brg: \<brg~apparent~\> El: \<el~apparent~\>
  \<**beam-data**\>                \<brg~beam\ point~\>,\<el~beam\ point~\>, \<az~beam~\>,\<el~beam~\>, \<az~beam\ EBS~\>,\<el~beam~\>,\<omega~A~\>, \<gain\>   Brg: \<brg~beam\ point~\> deg El: \<el~beam\ point~\> deg TgtRel Az: \<el~beam~\> deg El: \<el~beam~\> deg EBS Az: \<az~beam\ EBS~\> deg El: \<el~beam\ EBS~\> Omega: \<omega~A~\> deg Gain: \<log(gain)\> dB
  \<**location**~LLA~\>                                                                                                                                         
  \<**location**~ECI~\>                                                                                                                                         
  \<**orientation**\>                                                                                                                                           
  \<**velocity**~NED~\>                                                                                                                                         
  \<**velocity**~ECI~\>                                                                                                                                         
  \<**acceleration**~NED~\>                                                                                                                                     
  \<**acceleration**~ECI~\>                                                                                                                                     

\<**other-data**\> includes a variety of signature elements. It may be further sub-divided in the future.

  Field                CSV                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        EVT
  -------------------- ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  \<**other-data**\>   { ,\<log(signature~radar~)\>,\<signature~radar~\>,\<az~radar~\>,\<el~radar~\> \| ,,,, } { ,\<log(signature~optical~)\>,\<signature~optical~\>,\<az~optical~\>,\<el~optical~\>, { \<reflectivity~optical~\> } \| ,,,,, } { ,\<signature~infrared~\>,\<az~radar~\>,\<el~radar~\> { ,\<I~BR~\>,\<I~CR~\>,\<F~A~\> \| ,,, } \| ,,,,,, } { { ,\<log(F~A~)\>,\<F~A~\> \| ,, } { ,\<log(F^4^~P~)\>,\<F^4^~P~\>,\<F~P~\> \| ,,, },{ \<F~M~\> } \| ,,,,,, } { ,{ \<log(P~T~)\> },{ \<log(P~R~)\> },{ \<log(P~RN~)\> },{ \<log(P~C~)\> },{ \<log(P~I~)\> } { \<emi-component-power-data\> \| ,,, },{ \<pixel-count\> } { ,\<log(S/I)\>,{ \<log(threshold)\> } { ,\<log(S/N)\>,\<log(S/(N+C))\>,\<log(S/(N+C+J))\> \| ,,, } \| ,,,,, } \| ,,,,,,,,,,,,,, }, \<emi-component-data\>,\<emi-failures\>   { Radar_Sig: \<log(signature~radar~)\> dBsm (\<signature~radar~\> m\^2) Az: \<az~radar~\> deg El: \<el~radar~\> deg } { Optical_Sig: \<log(signature~optical~)\> dBsm (\<signature~optical~\> m\^2) Az: \<az~optical~\> deg El: \<el~optical~\> deg Reflectivity: \<reflectivity~optical~\> } { { Infrared_Sig: \<signature~infrared~\> w/sr Az: \<az~infrared~\> deg El: \<el~infrared~\> deg } { Background_radiant_intensity: \<I~BR~\> w/sr Contrast_radiant_intensity: \<I~CR~\> w/sr Transmittance: \<F~A~\> } } { { Absorption_factor: \<log(F~A~)\> dB (\<F~A~\>) } { Propagation_factor_F\^4: \<log(F^4^~P~)\> dB (\<F^4^~P~\>) F: \<F~P~\> } { Masking_Factor: \<F~M~\> } } { { Xmtd_Power: \<log(P~T~)\> dBw } { Rcvd_Power: \<log(P~R~)\> dBw } { Rcvr_Noise: \<log(P~RN~)\> dBw } { Clutter_Power: \<log(P~C~)\> dBw } { \<Label~SNR_Int~\>: \<log(P~I~)\> dBw } { \<emi-component-power-data\> } { Pixel_Count: \<pixel-count\> } { S/I: \<log(S/I)\> dB { Threshold: \<log(threshold)\> dB } { S/N: \<log(S/N)\> dB S/(N+C): \<log(S/(N+C))\> dB \<Label~SNR_Int~\>: \<log(S/(N+C+J))\> dB } } } \<emi-component-data\> \<emi-failures\>

To `Top of Page<emi-data>`{.interpreted-text role="ref"}

### Conditional Signature Elements

#### \<relative-location-data\>

  Field                                        Condition                                     Description
  -------------------------------------------- --------------------------------------------- ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  \<range~unit~\> \<brg~true~\> \<el~true~\>   \[1\]: If the range \> 0.0                    The distance between the source and destination system (in the specified units, denoted with the subscript). The direction based off the true unit vector in azimuth/elevation.
  \<brg~apparent~\> \<el~true~\>               \[1\]: AND If the earth radius scale != 1.0   The direction based off the apparent unit vector in azimuth/elevation.

#### \<beam-data\>

+-----------------------------------------------------------------------+-----------------------------------------+----------------------------------------------------------------------------------+
| Field                                                                 | Condition                               | Description                                                                      |
+=======================================================================+=========================================+==================================================================================+
| \<brg~beam\ point~\> \<el~beam\ point~\> \<brg~beam~\> \<el~beam~\> { | \[1\]: If the gain \>= 0.0              | The direction the beam is pointing in azimuth/elevation.                         |
|                                                                       |                                         |                                                                                  |
|                                                                       | \[2\]: \[1\] AND If the gain \> 0.0     | The direction of the target with respect to the beam in azimuth/elevation.       |
+-----------------------------------------------------------------------+-----------------------------------------+----------------------------------------------------------------------------------+
| \<brg~beam\ EBS~\> \<el~beam\ EBS~\> \<omega~A~\>                     | \[2\] AND If the antenna is in EBS mode | The direction of the beam with respect to the antenna face in azimuth/elevation. |
|                                                                       |                                         |                                                                                  |
|                                                                       |                                         | The solid angle that the antenna beam makes with the antenna face.               |
+-----------------------------------------------------------------------+-----------------------------------------+----------------------------------------------------------------------------------+
| \<gain\> \<log(gain)\> }                                              | \[2\]                                   | The beam gain.                                                                   |
+-----------------------------------------------------------------------+-----------------------------------------+----------------------------------------------------------------------------------+

#### \<other-data\>

+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------+
| Field                                                                                                                                                                                                                                                                            | Condition                                                                                                                                                                                                      | Description                                                        |
+==================================================================================================================================================================================================================================================================================+================================================================================================================================================================================================================+====================================================================+
| \<log(signature~radar~)\> \<signature~radar~\> \<az~radar~\> \<el~radar~\> \<log(signature~optical~)\> \<signature~optical~\> \<az~optical~\> \<el~optical~\> \<reflectivity~optical~\> \<signature~infrared~\> \<az~infrared~\> \<el~infrared~\> \<I~BR~\> \<I~CR~\> \<I~F~\> { | \[1\] If the radar signature \> 0.0                                                                                                                                                                            |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
| \<log(F~A~)\> \<F~A~\> \<log(F^4^~P~)\> \<F\^4^4^~P~\> \<F~P~\> \<F~M~\> } {                                                                                                                                                                                                     | \[2\] If the optical signature \> 0.0                                                                                                                                                                          |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
| \<log(P~T~)\> \<log(P~R~)\> \<log(P~RN~)\> \<log(P~C~)\> \<log(P~I~)\>                                                                                                                                                                                                           | \[2\] AND If the optical reflectivity \> 0.0 \[3\] If the infrared signature \> 0.0                                                                                                                            |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | \[4\] If the signal-to-noise \> 0.0                                                                                                                                                                            |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | \[5\] If the absorption factor \> 0.0 OR                                                                                                                                                                       |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | :   the propagation factor \> 0.0 OR 0.0 \<= masking factor \< 1.0                                                                                                                                             |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | \[5\] AND If the absorption factor \> 0.0                                                                                                                                                                      |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | \[5\] AND If the propagation factor \> 0.0                                                                                                                                                                     |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | \[5\] AND If 0.0 \<= masking factor \< 1.0                                                                                                                                                                     |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | \[6\] If the transmitted power \> 0.0 OR                                                                                                                                                                       |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | :   the received power \> 0.0 OR the pixel count \> 0.0 OR the signal-to-noise \> 0.0                                                                                                                          |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  | \[6\] AND If the transmitted power \> 0.0 \[6\] AND If the received power \> 0.0 \[6\] AND If the receiver noise power \> 0.0 \[6\] AND If the clutter power \> 0.0 \[6\] AND If the interference power \> 0.0 |                                                                    |
+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------+
| \<emi-component-powers-data\>                                                                                                                                                                                                                                                    | \[7\] For each emi-component                                                                                                                                                                                   | Power data associated with specific components.                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | ::: note                                                           |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | ::: title                                                          |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | Note                                                               |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | :::                                                                |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | Actual output may vary.                                            |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | :::                                                                |
+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------+
| \<count~pixel~\>                                                                                                                                                                                                                                                                 | \[8\] If the pixel count \> 0.0                                                                                                                                                                                | The approximate number of pixels the target occupies in the image. |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
| \<emi-component-data\>                                                                                                                                                                                                                                                           |                                                                                                                                                                                                                | Other data associated with specific components.                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | ::: note                                                           |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | ::: title                                                          |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | Note                                                               |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | :::                                                                |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | Actual output may vary.                                            |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | :::                                                                |
+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------+
| \<emi-failure\>                                                                                                                                                                                                                                                                  | \[9\] If any failure occurred                                                                                                                                                                                  | The failure strings, which can be one or more of the following:    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                |                                                                    |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Xmtr_Range_Limits_Exceeded                                     |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Rcvr_Range_Limits_Exceeded                                     |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Xmtr_Altitude_Limits_Exceeded                                  |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Rcvr_Altitude_Limits_Exceeded                                  |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Xmtr_Angle_Limits_Exceeded                                     |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Rcvr_Angle_Limits_Exceeded                                     |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Xmtr_Masked_By_Horizon                                         |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Rcvr_Masked_By_Horizon                                         |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Xmtr_Masked_By_Terrain                                         |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Rcvr_Masked_By_Terrain                                         |
|                                                                                                                                                                                                                                                                                  |                                                                                                                                                                                                                | -   Insufficient_Signal                                            |
+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------+

To `Top of Page<emi-data>`{.interpreted-text role="ref"}
