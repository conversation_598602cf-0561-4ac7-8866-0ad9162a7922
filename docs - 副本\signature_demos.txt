# signature_demos.rst.txt.md
orphan

:   

::: demo
signature_demos
:::

| This directory contains various examples and demonstrations of defining
| platform signatures and sensors to detect them.
| 
| Following are the top-level files to be loaded and run in the AFSIM Wizard.



| This example demonstrates using `WSF_COMPOSITE_OPTICAL_SIGNATURE`{.interpreted-text role="model"}
| to set and change the signature of a 2 stage rocket, using cone,
| cylinder, and plume shapes. The rocket is detected by
| `WSF_IRST_SENSOR`{.interpreted-text role="model"} and `WSF_EOIR_SENSOR`{.interpreted-text role="model"} sensors throughout its trajectory.



| This example demonstrates using `WSF_COMPOSITE_OPTICAL_SIGNATURE`{.interpreted-text role="model"}
| to set the signature of a simple fighter aircraft with different shapes.
| The shape impacts each fighter\'s projected areas, and thus detection events,
| with the sensor types `WSF_IRST_SENSOR`{.interpreted-text role="model"} and `WSF_EOIR_SENSOR`{.interpreted-text role="model"}.