orphan

:   

# UCI_StrikeCapabilityCommand

## Overview {#overview .UCI_StrikeCapabilityCommand}

This type holds the information given by a strike capability command.

## Static Methods

::: method
static UCI_StrikeCapabilityCommand Construct(UCI_CapabilityId aID, UCI_StrikeWeaponCommand aWeaponCommand)

Returns a \_UCI_StrikeCapabilityCommand with the given ID and weapon command.
:::

## Methods

::: method
void SetWeaponCommandType(UCI_StrikeWeaponCommand aCommand)

Sets the weapon command type to the given command.
:::
