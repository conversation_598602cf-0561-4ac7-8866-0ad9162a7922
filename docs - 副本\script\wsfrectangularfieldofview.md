orphan

:   

# WsfRectangularFieldOfView

::: {.WsfRectangularFieldOfView .inherits .WsfFieldOfView cloneable=""}
`WsfRectangularFieldOfView`{.interpreted-text role="class"} defines a `rectangular field of view<field_of_view_commands.rectangular>`{.interpreted-text role="ref"} that is used to dynamically change a sensor\'s field of view (originally defined using `antenna field of view commands<field_of_view>`{.interpreted-text role="command"}) using `WsfSensor.SetFOV`{.interpreted-text role="method"}.
:::

## Static Methods

::: method
static WsfRectangularFieldOfView Construct(double aMinAzimuth, double aMaxAzimuth, double aMinElevation, double aMaxElevation)

Returns a new `WsfRectangularFieldOfView`{.interpreted-text role="class"} object with the specified azimuth and elevation extents (in degrees).
:::

## Methods

::: method
Array\<double\> AzimuthFieldOfView()

Returns the minimum and maximum azimuth field of view extents.
:::

::: method
Array\<double\> ElevationFieldOfView()

Returns the minimum and maximum elevation field of view extents.
:::
