# wsfcyberattackparameterobject.rst.txt.md
orphan

:   





[WsfCyberAttackParameterObject](#wsfcyberattackparameterobject) is a wrapper object for auxiliary data as a formal object in the script context. It contains no functionality beyond the standard auxiliary data API.

Note that when using this object for subsequent usage with `WsfCyberAttackParameters`{.interpreted-text role="class"}, only a single attribute string name should be used for each parameter object.

