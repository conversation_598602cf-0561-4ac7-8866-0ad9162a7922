orphan

:   

# WsfCommGraphNode

## Description {#description .WsfCommGraphNode}

The `WsfCommGraphNode`{.interpreted-text role="class"} represents a node in a `WsfCommGraph`{.interpreted-text role="class"}. In the comm framework, graphs are used to represent network states, routing tables, etc. A node in such a graph represents an individual comm interface in the graph, and is uniquely identified by its address.

## Methods

::: method
WsfAddress GetAddress()

Returns the assigned address to this node in the graph.
:::

::: method
bool IsEnabled()

Returns true if this node is set to be enabled in the graph, or false if disabled. In general, most pathing decisions made in the comm framework do not consider a node (or any of its edges) if disabled.
:::
