orphan

:   

# WsfTaskProcessor

::: {.WsfTaskProcessor .inherits .WsfTaskManager}
`WsfTaskProcessor`{.interpreted-text role="class"} encapsulates the ability to query and change the state of a track.
:::

## Methods

::: method
int CountInState(string aStateName)

Return the number of tracks that are in the state indicated by \"aStateName\'.
:::

::: method
Array\<WsfLocalTrack\> TracksInState(string aStateName)

Return the list of tracks that are in the state indicated by \"aStateName\'.
:::

::: method
void EnterState(WsfTrackId aTrackId, string aStateName)

Force an immediate transition into state indicated by \"aStateName\' for the track represented by track ID \"aTrackId\'.
:::

::: method
string StateOfTrack(WsfTrackId aTrackId)

Return the state the the track indicated by \'aTrackId\' currently resides in.
:::

::: method
void SetEvaluationInterval(double aTimeInterval)

Dynamically changes the evaluation interval to the value indicated by \'aTimeInterval\' for the current task processor.
:::

::: method
void SetTransitionTime(double aTransitionTime)

Sets the time to required to transition into the next state, if a transition is to occur. This can be used to cause a delay to simulate processing or thought time.
:::
