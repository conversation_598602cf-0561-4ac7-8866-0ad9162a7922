# sensor_related_commands.rst.txt.md
orphan

:   





-   `sensor`{.interpreted-text role="command"} `Predefined_Sensor_Types`{.interpreted-text role="ref"}
-   `clutter_model`{.interpreted-text role="command"}
-   `error_model`{.interpreted-text role="command"}
-   `propagation_model`{.interpreted-text role="command"}
-   `antenna_pattern`{.interpreted-text role="command"}
-   `_.receiver`{.interpreted-text role="command"}
-   `_.transmitter`{.interpreted-text role="command"}



-   `acoustic_signature`{.interpreted-text role="command"}
-   `infrared_signature`{.interpreted-text role="command"}
-   `radar_signature`{.interpreted-text role="command"}



-   `global_environment`{.interpreted-text role="command"}
-   `line_of_sight_manager`{.interpreted-text role="command"}
-   `terrain`{.interpreted-text role="command"}