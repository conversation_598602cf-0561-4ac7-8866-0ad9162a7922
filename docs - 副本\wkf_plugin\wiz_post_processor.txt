# wiz_post_processor.rst.txt.md
orphan

:   





The Post Processor Wizard plugin is the user interface to generate `Post Processor<../post_processor>`{.interpreted-text role="doc"} configuration files and reports. The plugin is located in the \"Tools\" menu under \"Post Processor\", where the user can select which type of report to create. From there, the user can change the options for the report, including: time and latitude/longitude formatting, sub-report type, platform filtering, etc. After selecting the desired options, select \"Generate Reports\" and two files will be created: the configuration file with the currently entered settings and the corresponding report.





