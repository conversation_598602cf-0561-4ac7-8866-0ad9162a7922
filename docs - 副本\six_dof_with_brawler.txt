# six_dof_with_brawler.rst.txt.md
orphan

:   

::: demo
six_dof_with_brawler
:::

| This is a collection of demos that utilize Point-Mass (`WsfPointMassSixDOF_Mover`{.interpreted-text role="class"}) Six-Degrees-of-Freedom (SixDOF) movers and compare them to Brawler movers (`WsfBrawlerMover`{.interpreted-text role="class"}). The goal of these demos is to demonstrate how a SixDOF mover will behave given the aerodynamic and propulsion data from a Brawler mover.
| 
| The PointMassSixDOF mover used in these demos is an automated conversion from the LTE_FIGHTER Brawler demo aircraft. An FXW-to-PM6 converter tool can be found in the tools directory of AFSIM.



| This scenario presents a series of behaviors that SixDOF aircraft movers can carry out. The demo includes two aircraft. One aircraft (dark blue) performs a series of maneuvers, either using commands a Brawler mover might use, or behaviors borrowed from the air-to-air demos. The other aircraft (light blue) carries out similar (not identical) behaviors using the WsfSixDOF_Maneuver capability.
| 
| This demo exists to illustrate that while Brawler tactics can be migrated to SixDOF, SixDOF affords other maneuvering capabilities that users may find easier to build dynamic tactics with.



| This demo contains a Brawler mover and a PM6DOF mover converted from that Brawler mover. It is a simple flyout of the two vehicles responding to heading/speed/altitude commands, and serves to illustrate how the two movers may differ in navigation when using the same aerodynamic and propulsion properties.



| This demo contains two Brawler movers and two PM6DOF movers converted from the Brawler mover. It is a simple flyout of each vehicle responding to slice-turn or level-turn commands, and serves to illustrate how the two movers may differ in execution when using the same aerodynamic and propulsion properties.