# uci_post_capability_status.rst.txt.md
orphan

:   





This type is used to define the capability (mode) status for a `WSF_IRST_SENSOR`{.interpreted-text role="model"}.



::: method
UCI_CapabilityId CapabilityId()

Returns the capability ID.
:::

::: method
bool IsAvailable()

Returns true if the capability is available.
:::

::: method
bool IsDisabled()

Returns true if the capability is disabled.
:::

::: method
bool IsExpended()

Returns true if the capability is expended.
:::

::: method
bool IsFaulted()

Returns true if the capability is faulted.
:::

::: method
bool IsUnavailable()

Returns true if the capability is unavailable.
:::

::: method
bool IsTemporarilyUnavailable()

Returns true if the capability is temporarily unavailable.
:::