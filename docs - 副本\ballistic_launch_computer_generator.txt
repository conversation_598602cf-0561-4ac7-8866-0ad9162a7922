# ballistic_launch_computer_generator.rst.txt.md
orphan

:   



::: model
tool BALLISTIC_LAUNCH_COMPUTER_GENERATOR
:::

::: parsed-literal

tool BALLISTIC_LAUNCH_COMPUTER_GENERATOR

:   \... `weapon_tools`{.interpreted-text role="command"} Commands \... [launch_altitudes]() \<minimum-altitude\> \<delta-altitude\> \<number-of-altitudes\> [launch_speeds]() \<minimum-speed\> \<delta-speed\> \<number-of-speeds\> [target_altitudes]() \<minimum-altitude\> \<delta-altitude\> \<number-of-altitudes\>

end_tool
:::



`BALLISTIC_LAUNCH_COMPUTER_GENERATOR`{.interpreted-text role="model"} is a specialization of `weapon_tools`{.interpreted-text role="command"} to produce a launch computer to release unguided ballistic weapons, such as a gravity bomb. The tool will release weapons at a variety of engagement altitudes and speeds, and capture the resulting downrange travel and time of flight to impact. A launch computer definition file is created that will examine the current flight speed and altitude above the target, and anticipate a weapon release far enough uprange to drop the weapon on the target track location. (Since no lateral guidance is available in the weapon, the launching aircraft MUST pass vertically over the target.)

::: block
BALLISTIC_LAUNCH_COMPUTER_GENERATOR
:::



::: command
launch_altitudes \<minimum-altitude\> \<delta-altitude\> \<number-of-altitudes\>

Desired launcher altitudes for weapon release. Altitudes are assumed to be the height of the launcher above an ellipsoidal earth.

Default: 1000.0 meters (minimum), 1000.0 meters (delta), 10 altitudes
:::

::: command
launch_speeds \<minimum-speed\> \<delta-speed\> \<number-of-speeds\>

Speeds of launcher for weapon release.

Default: 120.0 m/s (minimum), 30.0 m/s (delta), 4 speeds
:::

::: command
target_altitudes \<minimum-altitude\> \<delta-altitude\> \<number-of-altitudes\>

Altitudes to place targets for prosecution. Altitudes are assumed to be the height of the target above an ellipsoidal earth.

Default: 0.0 meters (minimum), 50.0 meters (delta), 10 altitudes
:::