orphan

:   

# WsfSixDOF_PursueManeuver

## Overview {#overview .WsfSixDOF_PursueManeuver .inherits .WsfSixDOF_RelativeManeuver}

This relative maneuver will cause the executing platform to fly onto the target\'s path a given distance ahead of the target platform.

When it is executing, this maneuver predicts the future motion of the target platform and provides control to the executing platform that has the effect of flying toward a certain location relative to that predicted target motion. In addition to the control limits this class inherits from `WsfSixDOF_RelativeManeuver`{.interpreted-text role="class"}, the distance ahead of the target to which the executing platform will fly can be controlled by the user.

## Methods

::: method
static WsfSixDOF_PursueManeuver Construct(string aPlatformName, double aPursuitDistanceMeters)

This static method constructs a maneuver that will cause the executing platform to pursue the platform with the given name at the given lead distance.
:::

::: method
double GetPursuitDistanceMeters()

Return the lead distance of the pursuit in meters.
:::

::: method
void SetPursuitDistanceMeters(double aPursuitDistanceMeters)

Set the lead distance of the pursuit in meters.
:::

::: method
string GetTargetPlatformName()

Return the name of the platform targeted by this maneuver.
:::
