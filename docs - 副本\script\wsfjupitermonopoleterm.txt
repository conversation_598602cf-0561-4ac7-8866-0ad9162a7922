# wsfjupitermonopoleterm.rst.txt.md
orphan

:   





The `WsfJupiterMonopoleTerm`{.interpreted-text role="class"} represents the point mass gravitational field of Jupiter. See also `Jupiter Monopole <orbital_dynamics_terms.jupiter_monopole>`{.interpreted-text role="ref"}.



::: method
WsfJupiterMonopoleTerm Construct() WsfJupiterMonopoleTerm Construct(double aGravitationalParameter)

Create a term representing Jupiter\'s point mass gravitational field with the given gravitational parameter in SI units. If no gravitational parameter is specified, a default value of 1.267127678e+17 m\^3/s\^2 will be used.
:::



::: method
double GravitationalParameter()

Return the gravitational parameter of Jupiter in SI units used by this term.
:::

::: method
Vec3 JupiterPositionECI(Calendar aTime)

Return the position of Jupiter in the ECI frame used by this term at the given time.
:::