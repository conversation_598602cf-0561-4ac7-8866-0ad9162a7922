# wsfturntoheadingmaneuver.rst.txt.md
orphan

:   





This maneuver cases the platform to which it is assigned to turn to the given heading. This maneuver will set its own exit constraint (`WsfManeuverConstraint`{.interpreted-text role="class"}) to match the heading specified in the command. This can be overridden manually by setting a different exit constraint.



::: method
WsfTurnToHeadingManeuver Construct(double aHeadingDeg)

Construct a maneuver that will turn to the given heading in degrees.
:::

::: method
double GetHeading()

Return this maneuver\'s heading in degrees.
:::