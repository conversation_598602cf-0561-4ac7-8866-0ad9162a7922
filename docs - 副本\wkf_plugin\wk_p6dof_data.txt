# wk_p6dof_data.rst.txt.md
orphan

:   



::: note
::: title
Note
:::

P6DOF Mover and its associated plugins are deprecated in favor of the new SixDOF Mover and its plugins. P6DOF Data has been deprecated in favor of SixDOF Data, so it is disabled by default and must be enabled through the `Plugin Manager <../warlock_plugin_manager>`{.interpreted-text role="doc"}.
:::

The Warlock P6Dof Data plugin is responsible for populating the Platform Details with data specific to the P6Dof mover.

