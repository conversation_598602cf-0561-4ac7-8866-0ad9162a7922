# predefined_processor_types.rst.txt.md
orphan

:   



-   `WSF_DIRECTION_FINDER_PROCESSOR`{.interpreted-text role="model"}
-   `WSF_EXCHANGE_PROCESSOR`{.interpreted-text role="model"}
-   `WSF_FUSION_CENTER`{.interpreted-text role="model"}
-   `WSF_LINKED_PROCESSOR`{.interpreted-text role="model"}
-   `WSF_MESSAGE_PROCESSOR`{.interpreted-text role="model"}
-   `WSF_PERFECT_TRACKER`{.interpreted-text role="model"}
-   `WSF_SCRIPT_PROCESSOR`{.interpreted-text role="model"}
-   `WSF_STATE_MACHINE`{.interpreted-text role="model"}
-   `WSF_TRACK_PROCESSOR`{.interpreted-text role="model"}
-   `WSF_TRACK_STATE_CONTROLLER`{.interpreted-text role="model"}