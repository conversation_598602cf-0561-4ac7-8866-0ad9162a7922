orphan

:   

# Normal

::: block
orbital_maneuver_types
:::

**Script Type:** `WsfNormalManeuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} normal

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [delta_v]() \...

end_maneuver
:::

Perform a maneuver that adds delta-V in a direction normal to the plane containing the current velocity vector and ECI location vector.

::: command
delta_v \<speed-value\>

The delta-V that will be added in the normal direction to the plane containing the current velocity vector (**v**) and the ECI location vector (**r**). Positive values will be applied in the same direction as the cross product of **v** and **r**, and negative values will be applied in the opposite direction.
:::
