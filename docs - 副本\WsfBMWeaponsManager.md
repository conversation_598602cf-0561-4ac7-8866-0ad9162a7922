orphan

:   

# WsfBMWeaponsManager

::: WsfBMWeaponsManager
**Navigation:** `script_types`{.interpreted-text role="doc"}
:::

**Derives From:** `WsfProcessor`{.interpreted-text role="class"}, `WsfPlatformPart`{.interpreted-text role="class"}, `WsfObject`{.interpreted-text role="class"}

## Overview

**WsfBMWeaponsManager** is the script interface for invoking methods on the IADS C2 Weapons Manager base class. This class provides common interfaces that all HELIOS weapons manager processors derive.

## Methods

::: method
bool IsWEZLibrarySpecified()
:::

::: method
string GetWEZLibraryName()
:::

::: method
bool IsWEZTacticalRangeAero()
:::

::: method
bool IsWEZTacticalRangeMax1()
:::

::: method
bool IsWEZTacticalRangeMax2()
:::
