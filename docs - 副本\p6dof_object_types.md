orphan

:   

# p6dof_object_types

::: {.command block=""}
p6dof_object_types \... end_p6dof_object_types
:::

::: parsed-literal
p6dof_object_types

> // `P6DOF_Object_Type_Label`{.interpreted-text role="ref"} `p6dof_object_type`{.interpreted-text role="command"} \... end_p6dof_object_type
>
> // `P6DOF_Thrust_Producer_Types`{.interpreted-text role="ref"} `jet_engine_type`{.interpreted-text role="command"} \... end_jet_engine_type `ramjet_engine_type`{.interpreted-text role="command"} \... end_ramjet_engine_type `liquid_propellant_rocket_type`{.interpreted-text role="command"} \... end_liquid_propellant_rocket_type `solid_propellant_rocket_type`{.interpreted-text role="command"} \... end_solid_propellant_rocket_type
>
> // `P6DOF_Platform_Mappings`{.interpreted-text role="ref"} `map_p6dof_object_to_platform`{.interpreted-text role="command"} \... end_map_p6dof_object_to_platform
>
> // `P6DOF_Integrators_Support`{.interpreted-text role="ref"} `p6dof_integrators`{.interpreted-text role="command"} \...
>
> // `P6DOF_Environment_Support`{.interpreted-text role="ref"} `p6dof_atmosphere`{.interpreted-text role="command"} \... `p6dof_wind`{.interpreted-text role="command"} \... `p6dof_terrain`{.interpreted-text role="command"} \... `p6dof_gravity`{.interpreted-text role="command"} \...

end_p6dof_object_types
:::

## Overview

A p6dof_object_types block is used to specify various **types** of P6DOF objects, subobjects, components (such as engines), and environmental infrastructure (such as atmosphere, terrain, gravity, etc.). Once defined, a `p6dof_object_type`{.interpreted-text role="command"} can be used when defining a `WSF_P6DOF_MOVER`{.interpreted-text role="model"}.

Multiple `p6dof_object_types`{.interpreted-text role="command"} blocks may be utilized. Types defined in the `p6dof_object_types`{.interpreted-text role="command"} blocks must be unique. In addition, a type must be defined before it is referenced. As a result, the order in which types are defined is important. Types should be defined in the order in which they are referenced. Thus, if a \"F2H_Banshee\" P6Dof aircraft uses \"Westinghouse_J34\" jet engines, the engine type (`jet_engine_type`{.interpreted-text role="command"}) should be defined first, followed by the aircraft (`p6dof_object_type`{.interpreted-text role="command"}).

## Commands

See references above.
