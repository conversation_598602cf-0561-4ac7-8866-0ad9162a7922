# convert_platforms_to_json.rst.txt.md
orphan

:   



convert_platforms_to_json.py is a python script that converts AFSIM platform definitions to a JSON database to be used in the Wizard\'s Satellite Inserter tool.



convert_platforms_to_json.py \[-h\] *\<database\>* \[*\<files\>* \[*\<files\>* \...\]\]

Where:

\[-h \| \--help\] - An optional command to show help message.

*\<database\>* - The JSON database that will contain the JSON representation of the AFSIM platforms and their file locations.

*\<files\>* - A optional list of .txt files and/or directories that hold AFSIM platform definitions to be represented in database. Default: \".\" - the current directory.