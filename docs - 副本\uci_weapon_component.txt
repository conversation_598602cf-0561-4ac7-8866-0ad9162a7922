# uci_weapon_component.rst.txt.md
orphan

:   



::: model
uci_component WEAPON
:::

::: parsed-literal
uci_component \<type\> WEAPON

> Part Commands \...
>
> `uci_component`{.interpreted-text role="command"} Commands \...
>
> capability \<string\>
>
> > uuids
> >
> > :   \<string\>\*
> >
> > end_uuids
> >
> > store_category \<string\> store_type \<int\>
>
> end_capability

end_uci_component
:::



This component handles the Strike UCI messages for all weapons on a platform.



::: command
capability \<name\> \... end_capability

This block defines necessary information for each weapon of the given name, including the store category, store type, and any UUID that corresponds to this weapon.
:::



::: command
uuids \... end_uuids

Add a list of UUIDs for each quantity of the given weapon. If the number of UUIDs defined is less than the quantity of the weapon, the rest will be given randomly generated UUIDs. Conversely, if too many UUIDs are given, only those up to the quantity of the weapon will be used.
:::

::: command
store_category \<string\>

Add a store category to the weapon. Available options are \"air\", \"ground\", \"gun\", \"sea_subsurface\", \"sea_surface\", and \"sea_surface_samssm\".
:::

::: command
store_type int

Add a store type to the weapon.
:::

::: warning
::: title
Warning
:::

If the weapon\'s store category and/or the store type is not defined, the weapon will be ignored by the UCI interface. No messages to the weapon\'s group of capabilities will be handled.
:::



All message types are able to be sent via script methods, unless noted otherwise.



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_StrikeCapabilityMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval



`UCI_StrikeCapabilityStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_ControlRequestMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   Script only



`UCI_ControlRequestStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   When a ControlRequestMessage is received and handled (one per controlee in message)



`UCI_ControlStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   When a ControlRequestMessage is received and handled (one per controlee in message)
> -   On update interval



`UCI_StrikeSettingsCommandMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_SubsystemStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval



`UCI_SystemStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval
>
> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented in script
> :::