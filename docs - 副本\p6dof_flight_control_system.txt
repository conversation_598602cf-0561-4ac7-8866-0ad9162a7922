# p6dof_flight_control_system.rst.txt.md
orphan

:   



The flight control system determines how control inputs from the active pilot object are routed/mixed to move control surfaces, throttles, or other components/controls (such as landing gear, speed brakes, etc.). Many of the control surfaces serve as inputs into `aero_component`{.interpreted-text role="command"} objects.

::: {.command block=""}
flight_controls \... end_flight_controls

The [flight_controls]() block defines various control surfaces/values ([control_surface](), [control_value](), and [control_boolean]()) that control the vehicle and/or effect its performance. The [flight_controls]() block also defines various input/signal modifiers which are referenced within the control surfaces/values ([control_surface](), [control_value](), and [control_boolean]()) blocks. It is important to define [Control Signal Modifiers](#control-signal-modifiers) before they are referenced in the [Control Surfaces and Components](#control-surfaces-and-components) blocks.

::: parsed-literal
flight_controls

> // [Control Signal Modifiers](#control-signal-modifiers) [mapping_table]() \... end_mapping_table [gain_table]() \... end_gain_table [scalar_gain]() \... end_scalar_gain [clamp_gain]() \... end_clamp_gain
>
> // [Control Surfaces and Components](#control-surfaces-and-components) [control_surface]() \... end_control_surface [control_value]() \... end_control_value [control_boolean]() \... end_control_boolean

end_flight_controls
:::
:::



The control signal modifiers provide a means to modify control input \"signals\" from the active pilot object. The modifiers can be used repeatedly within the [control_surface](), [control_value](), and [control_boolean]() blocks, as needed.

::: {.command block=""}
mapping_table \... end_mapping_table

Mapping tables are used to modify a control input signal by modulating with a control value source. The control value can be a range of sources such as mach, alpha, g-load, etc. A table defines the relationship between the control value

::: parsed-literal

mapping_table \<string\>

:   [type]() \... [table_data]() \... end_table_data

end_mapping_table
:::

::: command
type \<string\>

This sets the type of the mapping table. Valid types include:

mach_mapping

:   Uses mach as the control_value

ktas_mapping

:   Uses ktas as the control_value

alpha_mapping

:   Uses alpha as the control_value

beta_mapping

:   Uses beta as the control_value

g_x_load_mapping

:   Uses g-load-x (Nx) as the control_value

g_y_load_mapping

:   Uses g-load-y (Ny) as the control_value

g_z_load_mapping

:   Uses g-load-z (Nz) as the control_value

alt_mapping

:   Uses altitude as the control_value

q_mapping

:   Uses dynamic pressure as the control_value

signal_mapping

:   Uses a signal as the control_value
:::

::: command
table_data

This table maps a signal/input to an output/value). A typical table is like this:

    table_data
       irregular_table
           independent_variable control_value precision float
           independent_variable input         precision float
           dependent_variable                 precision float
           control_value 0.0
               input   -1.0 -0.5  0.0  0.5  1.0
               values  -0.8 -0.4  0.0  0.4  0.8
           control_value 1.0
               input   -1.0 -0.5  0.0  0.5  1.0
               values  -0.8 -0.4  0.0  0.4  0.8
           control_value 2.0
               input   -1.0 -0.5  0.0  0.5  1.0
               values  -0.8 -0.4  0.0  0.4  0.8
       end_irregular_table
    end_table_data
:::
:::

::: block
flight_controls
:::

::: {.command block=""}
gain_table \... end_gain_table

This table maps a signal/input to an output/value). A typical table is like this:

::: parsed-literal
gain_table \<string\>

> [type]() \...
>
> simple_table
>
> :   
>
>     \# value gain
>
>     :   -12.0 0.0 -10.0 1.0 0.00 1.0 10.0 1.0 12.0 0.8 20.0 0.1 30.0 0.0
>
> end_simple_table

end_gain_table
:::
:::

::: block
flight_controls
:::

::: {.command block=""}
scalar_gain \... end_scalar_gain

The scalar gain consists of a name and a gain value. Once defined, a scalar gain can be used repeatedly within the [control_surface](), [control_value](), and [control_boolean]() blocks to scale a control signal.

::: parsed-literal

scalar_gain \<string\>

:   [gain]() \...

end_scalar_gain
:::

::: command
gain \<real-value\>

This sets the gain of the scalar gain.
:::
:::

::: block
flight_controls
:::

::: {.command block=""}
clamp_gain \... end_clamp_gain

Clamp gains are used to limit a signal between min/max values. The output of signal will be \"clamped\" to the minimum and maximum values.

::: parsed-literal

clamp_gain \<string\>

:   [min_clamp]() \... [max_clamp]() \...

end_clamp_gain
:::

::: command
min_clamp \<real-value\>

This sets the minimum value that will be allowed to pass the clamp gain.
:::

::: command
max_clamp \<real-value\>

This sets the maximum value that will be allowed to pass the clamp gain.
:::
:::



The [control_surface](), [control_value](), and [control_boolean]() represent control surfaces and other components on the platform that produce forces and moments or control actions on the vehicle.

::: block
flight_controls
:::

::: {.command block=""}
control_surface \... end_control_surface

This defines a \"control surface\" or other component that can produce aerodynamic effects on the platform. Control surfaces include ailerons, elevators, elevons, rudders, spoilers, speedbrakes, etc. but also include things like landing gear, which can produce drag. See [Sample Control Surface](#sample-control-surface) for an example of a control_surface block.

Each control_surface is given a name that must be unique and must \"map\" to an `aero_component`{.interpreted-text role="command"} that will produce the effect of the control_surface.

::: warning
::: title
Warning
:::

The key concept is that each control_surface in the `flight_controls`{.interpreted-text role="command"} block should be \"connected\" to a corresponding `aero_component`{.interpreted-text role="command"}.

The control_surface name and the aero_component type must match exactly and are case-sensitive.
:::

::: parsed-literal
control_surface \<name-string\>

> [min_angle]() \... [max_angle]() \... [current_angle]() \...
>
> [inputs]() \... end_inputs
>
> [angle_mapping_table]() \... end_angle_mapping_table
>
> [actuator]() \... end_actuator

end_control_surface
:::
:::

::: command
min_angle \<angle-value\>

This sets the minimum angle of this control surface.
:::

::: command
max_angle \<angle-value\>

This sets the maximum angle of this control surface.
:::

::: command
current_angle \<angle-value\>

This sets the current angle of this control surface.
:::

::: {.command block=""}
inputs \... end_inputs

Inputs provide a means to modify a \"control input/signal\" from the active pilot object. If multiple [inputs]() blocks are used, the output from each block is summed with the other [inputs]() for a given [control_surface](). This can provide a means to \"mix\" control inputs/signals to drive a particular control surface. For example, an elevon may mix signals from stick_right and stick_back inputs to allow the elevon to be used for both pitch and roll forces/moments.

::: parsed-literal

inputs

:   [control_input]().. end_control_input

end_inputs
:::
:::

::: {.command block=""}
control_input \... end_control_input

::: parsed-literal

control_input \<string\>

:   [modifier]() \...

end_control_input
:::

::: command
modifier \<string\>

This indicates which of the [Control Signal Modifiers](#control-signal-modifiers) should be used. If multiple modifier entries are present, they are performed in succession with the result from the previous modifier serving as the input of the next, forming a \"chain\" of modifiers.
:::

::: warning
::: title
Warning
:::

The key concept is that each control_input in `flight_controls`{.interpreted-text role="command"} should be \"connected\" to a control_name in `control_inputs`{.interpreted-text role="command"}.

The names must match exactly and are case-sensitive.
:::
:::

::: command
angle_mapping_table \... end_angle_mapping_table

This table provides a means to \"shape\" the mapping of input to control surface angle. In many situations, a non-linear mapping will be used, with a reduced slope near zero and increased slope near the endpoints. This often improves the \"feel\" of controls and provides more control sensitivity near the zero point. Data in the table is normalized input mapping to a control surface angle in degrees.

Sample mapping table:

    angle_mapping_table
       #input        angle_deg
       -1.00         -20.0
       0.00          0.0
       1.00          20.0
    end_angle_mapping_table

In this example, a normalized input of +/- 1 results in a linear mapping to +/- 20 degrees.
:::

::: command
actuator

This allows an simple model of an actuator.

::: parsed-literal

actuator

:   [max_positive_rate]() \... [max_negative_rate]() \... [max_angle]() \... [min_angle]() \... [current_angle]() \...

end_actuator
:::
:::

::: command
max_positive_rate \<angle-rate-value\>

This is the maximum rate that the actuator can move in the positive direction.

Default: 0.0
:::

::: command
max_negative_rate \<angle-rate-value\>

This is the maximum rate that the actuator can move in the negative direction.

Default: 0.0
:::

::: command
max_angle \<angle-value\>

This is the maximum angle to which the actuator can move.

Default: 0.0
:::

::: command
min_angle \<angle-value\>

This is the minimum angle to which the actuator can move.

Default: 0.0
:::

::: command
current_angle \<angle-value\>

This is the current angle of the actuator.

Default: 0.0
:::



This is a sample control surface listing:

    control_surface   RightElevator_TEUp
       min_angle            -20.0 deg
       max_angle            20.0 deg
       current_angle        0.0 deg

       inputs
          control_input     StickRight
             modifier       Gain_40Percent
             modifier       Clamp_PosNegOne
          end_control_input
       end_inputs
       inputs
          control_input     StickBack
             modifier       Gain_80Percent
             modifier       Clamp_PosNegOne
          end_control_input
       end_inputs

       angle_mapping_table
          #input  angle_deg
          -1.00   -20.000
          -0.90   -12.812
          -0.80   -8.744
          -0.70   -5.920
          -0.60   -3.958
          -0.50   -2.596
          -0.40   -1.650
          -0.30   -0.993
          -0.20   -0.537
          -0.10   -0.220
          -0.05   -0.100
          0.00    0.000
          0.05    0.100
          0.10    0.220
          0.20    0.537
          0.30    0.993
          0.40    1.650
          0.50    2.596
          0.60    3.958
          0.70    5.920
          0.80    8.744
          0.90    12.812
          1.00    20.000
       end_angle_mapping_table

       actuator
          max_positive_rate  60.0 deg/sec
          max_negative_rate  -60.0 deg/sec
          max_angle          20.0
          min_angle          -20.0
          current_angle      0.0
      end_actuator

    end_control_surface

::: block
flight_controls
:::

::: command
control_value

This defines a \"control value\" that can be used to drive controls such as throttles and thrust reversers or control other objects on the platform. A control value provides a range of values, constrained by the specified limits.

::: parsed-literal
control_value \<string\>

> [min_value]() \... [max_value]() \... [current_value]() \...
>
> [inputs]() \... end_inputs

end_control_value
:::
:::

::: command
min_value \<real-value\>

This is the minimum value of the control value.

Default: 0.0
:::

::: command
max_value \<real-value\>

This is the maximum value of the control value.

Default: 0.0
:::

::: command
current_angle \<real-value\>

This is the current angle of the control value.

Default: 0.0
:::

::: command
control_boolean

This defines a \"control boolean\" that can be used to drive \"button\", \"trigger\", and \"switch\" types of controls on the platform. A control boolean\'s output is either true (on) or false (off).

::: parsed-literal
control_boolean \<string\>

> [current_value]() \... [threshold_value]() \...
>
> [inputs]() \... end_inputs

end_control_boolean
:::
:::

::: command
current_value \<integer-value\>

This should be \'0\' if the control boolean is false (off) or \'1\' if the control boolean is true (on).

Default: 0
:::

::: command
threshold_value \<real-value\>

This sets the value above which a signal will be considered to be true (on).

Default: 0.5
:::

Return to `p6dof_object_types`{.interpreted-text role="doc"} or `p6dof_object_type`{.interpreted-text role="doc"}