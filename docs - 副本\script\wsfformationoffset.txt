# wsfformationoffset.rst.txt.md
orphan

:   



::: WsfFormationOffset
The `WsfFormationOffset`{.interpreted-text role="class"} represents the relative offset of a formation from its parent.
:::



::: method
WsfFormationOffset Construct(double aRange, double aBearing, double aStack, bool aWeldedWing)

Construct an offset given the range in meters, relative bearing in degrees and stack (vertical offset) in meters. If the offset is to provide for a \'welded-wing\' formation, provide true as the final argument. Note that this is only a hint, and it will be ignored if maintaining the welded-wing formation would put the platform into an untenable state.
:::

::: method
WsfFormationOffset Construct(Vec3 aOffsetVector, double aWeldedWing)

Construct an offset given a vector of three coordinates. In order these specify how far to the right, ahead of, and above the parent reference this offset would be. If the offset is to provide for a \'welded-wing\' formation, provide true as the final argument. Note that this is only a hint, and it will be ignored if maintaining the welded-wing formation would put the platform into an untenable state.
:::



::: method
double GetStack()

Get the vertical offset in meters from the reference point. A positive stack means a higher vertical position than the reference point.
:::

::: method
double GetRight()

Get the horizontal offset to the right of the reference point in meters.
:::

::: method
double GetAhead()

Get the horizontal distance ahead of the reference point in meters.
:::

::: method
double GetRelativeBearing()

Get the relative bearing of the offset from the direction of travel in degrees.
:::

::: method
double GetRange()

Get the range in horizontal of the offset from the reference point in meters.
:::

::: method
bool IsWelded()

Return if the offset is to be considered a \'welded-wing\' offset.
:::



::: method
void SetStack(double aStackInMeters)

Set the stack of the offset to the given value.
:::

::: method
void SetRight(double aRightInMeters)

Set the offset right to the given value.
:::

::: method
void SetAhead(double aAheadInMeters)

Set the offset left to the given value.
:::

::: method
void SetRangeAndBearing(double aRangeInMeters, double aBearingInDegrees)

Set the range and bearing of the offset to the given values. This will not modify the stack.
:::

::: method
void SetWelded(bool aWelded)

Set the \'welded-wing\' status of the offset to the given value.
:::



::: method
WsfFormationOffset Add(WsfFormationOffset aLHS, WsfFormationOffset aRHS)

Add the two offsets to produce a single offset that represents an offset of the first argument followed by an offset of the second. For example, providing opposite offsets as the two arguments would produce a zero offset as a result.
:::

::: method
WsfFormationOffset Subtract(WsfFormationOffset aLHS, WsfFormationOffset aRHS)

Subtract the second offset from the first, returning a new offset. The returned offset it equivalent to an offset in the direction of the first argument followed by the opposite offset of the second. For example, providing the same offset as both arguments would produce a zero offset as a result.
:::