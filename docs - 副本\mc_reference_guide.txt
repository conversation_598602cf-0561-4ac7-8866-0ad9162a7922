# mc_reference_guide.rst.txt.md
orphan

:   





-   `Start/Setup<mc_users_guide>`{.interpreted-text role="doc"}
-   Standard Templates
    -   `Vehicles<mc_standard_vehicles>`{.interpreted-text role="doc"}
    -   `Airfoils<mc_standard_airfoils>`{.interpreted-text role="doc"}
    -   `Engines<mc_standard_engines>`{.interpreted-text role="doc"}
-   `Mover Creator Theory<mc_theory>`{.interpreted-text role="doc"}



-   `Geometry<mc_geometry>`{.interpreted-text role="doc"}
-   `Aerodynamics<mc_aerodynamics>`{.interpreted-text role="doc"}
-   `Performance<mc_performance>`{.interpreted-text role="doc"}
-   `Pilots/Controls<mc_pilots_controls>`{.interpreted-text role="doc"}
-   `Autopilot<mc_autopilot>`{.interpreted-text role="doc"}
-   `Flight Test<mc_flight_test>`{.interpreted-text role="doc"}



-   `General<mc_engine_designer>`{.interpreted-text role="doc"}
-   `Jets<Jet_Engines>`{.interpreted-text role="ref"}
-   `Ramjets<Ramjet_Engines>`{.interpreted-text role="ref"}
-   `Liquid Propellant Rockets<Liquid_Propellant_Rocket_Engines>`{.interpreted-text role="ref"}
-   `Solid Propellant Rockets<Solid_Propellant_Rocket_Engines>`{.interpreted-text role="ref"}