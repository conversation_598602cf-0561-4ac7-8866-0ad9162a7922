# mover_creator.rst.txt.md
orphan

:   





Mover Creator is an AFSIM software tool that provides a convenient, easy to use, GUI-based application that helps users create AFSIM input files for air-domain movers. Using a custom-defined, graphics-based vehicle definition, users are able to model aircraft, weapons, and engines for use in AFSIM. The Mover Creator interface allows users to create a new, custom model from an existing template model (provided by Mover Creator) or users can edit a model they previous created. Mover Creator includes several modules within the tool, which assist with a variety of functions, including:

-   Modifying the vehicle geometry/configuration
-   Adjusting the control configurations
-   Tuning/modifying autopilot parameters
-   Adjusting the aerodynamics
-   Analyzing the performance of the vehicle
-   Comparing the performance of different mover types
-   Flight testing the vehicle within the AFSIM environment

Mover types currently supported in Mover Creator are `WSF_RIGID_BODY_SIX_DOF_MOVER`{.interpreted-text role="model"}, `WSF_P6DOF_MOVER`{.interpreted-text role="model"} and `WSF_GUIDED_MOVER`{.interpreted-text role="model"}. These models may be generated either individually or concurrently.

Additionally, users of `WSF_BRAWLER_MOVER`{.interpreted-text role="model"} interested in investigating `WSF_RIGID_BODY_SIX_DOF_MOVER`{.interpreted-text role="model"} functionality may find the Brawler conversion tool useful.





-   `User's Guide <mc_users_guide>`{.interpreted-text role="doc"}
-   `Reference Guide <mc_reference_guide>`{.interpreted-text role="doc"}
-   `Change Log <mc_change_log>`{.interpreted-text role="doc"}