# unit.rst.txt.md
orphan

:   



::: command
unit
:::

::: parsed-literal

unit \<name\>

:   debug detached offset \... member_platform \<platform-name\>

end_unit
:::



A unit formation can have no sub-formations, but are where the member platforms of a formation hierarchy are specified. Unit formations cannot be defined at the root level in input files, and cannot have subformations. Units can also be defined in script, see `WsfFormation`{.interpreted-text role="class"} and `WsfFormationManager`{.interpreted-text role="class"}. Units can be given commands, see `WsfFormationCommand`{.interpreted-text role="class"}.

::: {#Unit_Formation.Common_Formation_Commands}
:::



::: command
member_platform \<platform_name\>

Set the member platform of the unit formation to be the platform with the given name.
:::