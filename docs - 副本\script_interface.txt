# script_interface.rst.txt.md
orphan

:   



The script_interface block is used to configure the scripting system.

Multiple script_interface blocks may be provided. If the same subcommand is specified in multiple blocks then the last value given will be used.

    script_interface
       script_interface sub-commands 
    end_script_interface





Tells the compiler to produce a source listing and any errors that may occur during compilation (by default debug output is turned off).



Tells the compiler not to produce the source and detailed error listing. The compiler will still display a simple message if an error occurs during compilation.



Defines a script that is callable by all platforms in the simulation. See the next section on how to define a script.