# sosm_overview.rst.txt.md
orphan

:   



The Spectral Optical Sensing Model (SOSM) is a \'high-fidelity\' infrared detection model that is suitable for use in constructive and real-time mission simulation environments. The target and atmosphere models are typically represented by tables generated using offline tools such as MODTRAN and IRIMAGE.

Using the model typically involves the following steps:

-   Creating atmosphere models (See `SOSM Atmosphere Model <SOSM_atmosphere_model>`{.interpreted-text role="model"})
-   Creating sensor models (See `SOSM Sensor Model <SOSM_sensor_model>`{.interpreted-text role="ref"})
-   Creating target models (See `SOSM Target Model Table <SOSM_target_model_table>`{.interpreted-text role="ref"} and `SOSM Target Model Simple <SOSM_target_model_simple>`{.interpreted-text role="ref"})
-   Loading models into the simulation and controlling the operation (See `SOSM Interface <sosm_interface>`{.interpreted-text role="command"})



The model uses a discrete spectral representation of the atmosphere, sensor and target. Each component has three values that define the spectral limits and resolution:

-   O - The origin, which defines the first wavenumber (cm-1) in the spectrum.
-   R - The resolution, which defines the number of wavenumbers between sample points in the spectrum.
-   N - The number of samples in the spectrum.

The spectral range of a component is then

-   \[ O, O + (N-1) \* R \]

The model imposes the following restrictions on the components that interact:

-   All must have the same resolution (R)
-   Where components intersect, their sample points must be on the same wavenumbers. The best way to ensure this is to create models whose origin (O) is evenly divisible by the resolution (R).

For most applications a resolution of 20 cm-1 is used. The atmospheres provided with the standard WSF distribution are generated using this resolution, the default resolution for the sensor model is 20 cm-1 and the IRIMAGE-to-SOSM conversion utility defaults to producing output with a resolution of 20 cm-1.

In general, the atmosphere models are typically generated with a spectral range that is sufficient to cover any sensor or target that may be used with the simulation. Target models are typically created for only for the spectral windows of interest.



To do.