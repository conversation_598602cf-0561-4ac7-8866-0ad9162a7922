orphan

:   

# Hohmann Transfer

**Script Type:** `WsfHohmannTransfer`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} hohmann_transfer

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [final_semi_major_axis]() \| [final_radius]() \...

end_maneuver
:::

A maneuver representing a simple Hohmann Transfer, or orbit transfer that represents the minimum delta-V necessary to change to a co-planar circular orbit with different semi-major axis. The Hohmann transfer maneuver consists of two separate semi-major axis changes. The first maneuver moves the satellite into a transfer orbit that intersects both the initial and final orbits, and the second maneuver circularizes the final orbit. In the case of an orbit-raising Hohmann transfer, the first burn is performed at periapsis of the initial orbit, and the second burn is performed at the apoapsis of the transfer orbit. Conversely, in the case of an orbit-lowering Hohmann transfer, the first burn may occurs at apoapsis of the initial orbit, and the final maneuver occurs at periapsis of the transfer orbit.

> ::: note
> ::: title
> Note
> :::
>
> Specifying a `constraint<orbital_event.constraints>`{.interpreted-text role="ref"} on a Hohmann transfer will place an additional, initial delay on the maneuver.
> :::

::: command
final_semi_major_axis \<length-value\>
:::

::: command
final_radius \<length-value\>

The semi-major axis (radius) of the final circular orbit.
:::
