orphan

:   

# WsfGuidanceComputer

::: {.WsfGuidanceComputer .inherits .WsfProcessor}
::: {.contents local=""}
:::
:::

## Overview

WsfGuidanceComputer provides methods that can be used to dynamically alter the behavior of `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"}.

## General Methods

::: method
void EjectStage() void EjectStage(double aPreSeparationCoastTime, double aPreIgnitionCoastTime)

The first form informs the mover to stop the engines on the current stage and enter the post-burn coast phase of the stage. If not the final stage, a staging event will occur at the completion of the pre-separation coast. The next stage (if defined) will be activated and the engines will ignite at the completion of the pre-ignition coast. This is the same as calling `EjectStage(-1, -1) <WsfGuidanceComputer.EjectStage>`{.interpreted-text role="method"}.

The second form allows the caller to override the `WSF_GUIDED_MOVER.pre_separation_coast_time`{.interpreted-text role="command"} of the current stage and the `WSF_GUIDED_MOVER.pre_ignition_coast_time`{.interpreted-text role="command"} of the following stage (if defined). If the value of an argument is less than zero then its corresponding value in the mover will not be changed.
:::

::: method
void SelectPhase(string aPhaseName)

Immediately terminates the current phase and initiates the specified phase. The `WSF_GUIDANCE_COMPUTER.on_exit`{.interpreted-text role="command"} script block in the current phase is executed, then the `WSF_GUIDANCE_COMPUTER.on_entry`{.interpreted-text role="command"} script block is executed in the selected phase.
:::

::: method
string Phase()

Returns the current `phase<WSF_GUIDANCE_COMPUTER.phase>`{.interpreted-text role="command"}.
:::

::: method
void StopEngines()

This is the same as calling `EjectStage() <WsfGuidanceComputer.EjectStage>`{.interpreted-text role="method"}.
:::

## Setting Values of Phase Commands

These method provide the ability to redefine the value of the command values for a phase. They exactly parallel the equivalent commands in `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"}.

### Notes About Method Arguments and Return Values

Each of these methods has two forms:

-   The first form does not include a phase name and affects the value only for the current phase, e.g.:

    ::: parsed-literal
    SetProportionalNavigationGain(4.0);
    :::

    This form is effective ONLY when called directly or indirectly from the `WSF_GUIDANCE_COMPUTER.on_entry`{.interpreted-text role="command"} or `WSF_GUIDANCE_COMPUTER.on_update`{.interpreted-text role="command"} script of the phase to be changed.

    ::: note
    ::: title
    Note
    :::

    Calling the single argument form from within the `WSF_GUIDANCE_COMPUTER.on_exit`{.interpreted-text role="command"} script block will have no affect as it alters the phase that is being left.
    :::

-   The second form has a phase name as the first argument and affects the value only for specified phase, e.g.:

    ::: parsed-literal
    SetProportionalNavigationGain(\"TERMINAL\", 5.0);
    :::

    These can be used in any script within the processor.

The return value indicates if the call was successful or false if it failed for some reason.

### General Subcommands

::: method
bool SetGuidanceDelay(double aValue) bool SetGuidanceDelay(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.guidance_delay`{.interpreted-text role="command"} (in seconds).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

### Aimpoint Selection Subcommands

::: method
bool SetGuidanceTarget(string aValue) bool SetGuidanceTarget(string aPhaseName, string aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.guidance_target`{.interpreted-text role="command"} (\"truth\", \"perception\", \"predicted_intercept\", \"default\").

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetAllowRouteFollowing(bool aValue) bool SetAllowRouteFollowing(string aPhaseName, bool aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.allow_route_following`{.interpreted-text role="command"} (true or false).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetAimpointAltitudeOffset(double aValue) bool SetAimpointAltitudeOffset(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.aimpoint_altitude_offset`{.interpreted-text role="command"} (meters).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetAimpointAzimuthOffset(double aValue, string aDirection) bool SetAimpointAzimuthOffset(string aPhaseName, double aValue, string aDirection)

Sets the value of `WSF_GUIDANCE_COMPUTER.aimpoint_azimuth_offset`{.interpreted-text role="command"} (degrees). aDirection must be \"left\", \"right\" or \"either\".

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetAimpointRangeOffset(double aValue) bool SetAimpointRangeOffset(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.aimpoint_range_offset`{.interpreted-text role="command"} (meters).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetAimpointEvaluationInterval(double aValue) bool SetAimpointEvaluationInterval(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.aimpoint_evaluation_interval`{.interpreted-text role="command"} (seconds).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

### Navigation Subcommands

::: method
bool SetProportionalNavigationGain(double aValue) bool SetProportionalNavigationGain(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.proportional_navigation_gain`{.interpreted-text role="command"} (unitless).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetProportionalNavigationLimitAngle(double aValue) bool SetProportionalNavigationLimitAngle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.proportional_navigation_limit_angle`{.interpreted-text role="command"} (degrees).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetProportionalNavigationMethod(double aValue) bool SetProportionalNavigationMethod(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.proportional_navigation_method`{.interpreted-text role="command"} (\"pure\" or \"augmented\").

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetVelocityPursuitGain(double aValue) bool SetVelocityPursuitGain(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.velocity_pursuit_gain`{.interpreted-text role="command"} (unitless).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

### Trajectory Shaping Subcommands

::: method
bool SetGeeBias(double aValue) bool SetGeeBias(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.g_bias`{.interpreted-text role="command"} (unitless).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetLateralGeeBias(double aValue) bool SetLateralGeeBias(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.lateral_g_bias`{.interpreted-text role="command"} (unitless).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool ClearCommandedAltitude() bool ClearCommandedAltitude(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_altitude`{.interpreted-text role="command"}.

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool ClearCommandedSpeed() bool ClearCommandedSpeed(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_speed`{.interpreted-text role="command"} or `WSF_GUIDANCE_COMPUTER.commanded_mach`{.interpreted-text role="command"}.

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool ClearCommandedFlightPathAngle() bool ClearCommandedFlightPathAngle(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_flight_path_angle`{.interpreted-text role="command"}.

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool ClearCommandedThrottle() bool ClearCommandedThrottle(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_throttle`{.interpreted-text role="command"} (resumes the default throttle control in the mover).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetCommandedAltitude(double aValue) bool SetCommandedAltitude(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_altitude`{.interpreted-text role="command"} (meters above mean sea level).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetCommandedAltitudeAGL(double aValue) bool SetCommandedAltitudeAGL(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_altitude`{.interpreted-text role="command"} (meters above ground level).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetCommandedAzimuthOffset(double aValue) bool SetCommandedAzimuthOffset(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_azimuth_offset`{.interpreted-text role="command"} (degrees).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetCommandedFlightPathAngle(double aValue) bool SetCommandedFlightPathAngle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_flight_path_angle`{.interpreted-text role="command"} (degrees). Use `ClearCommandedFlightPathAngle() <WsfGuidanceComputer.ClearCommandedFlightPathAngle>`{.interpreted-text role="method"} to disable the commanded flight path angle.

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetCommandedMach(double aValue) bool SetCommandedMach(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_mach`{.interpreted-text role="command"} (unitless Mach number). Use `ClearCommandedSpeed() <WsfGuidanceComputer.ClearCommandedSpeed>`{.interpreted-text role="method"} to disable the commanded speed.

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetCommandedSpeed(double aValue) bool SetCommandedSpeed(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_speed`{.interpreted-text role="command"} (meters/second). Use `ClearCommandedSpeed() <WsfGuidanceComputer.ClearCommandedSpeed>`{.interpreted-text role="method"} to disable the commanded speed.

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetCommandedThrottle(double aValue) bool SetCommandedThrottle(string aPhaseName, double aValue)

Set the value of `WSF_GUIDANCE_COMPUTER.commanded_throttle`{.interpreted-text role="command"} in the range \[0..1\].

A value of 0 effectively stops the engines. The engines should be restarted by calling `ClearCommandedThrottle() <WsfGuidanceComputer.ClearCommandedThrottle>`{.interpreted-text role="method"}.

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

### Limiting Subcommands

::: method
bool SetMaximumCommandedGees(double aValue) bool SetMaximumCommandedGees(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_commanded_g`{.interpreted-text role="command"} (G\'s: i.e.: a value of 1.0 is 1 g).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetMaximumAscentRate(double aValue) bool SetMaximumAscentRate(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_ascent_rate`{.interpreted-text role="command"} (meters/second).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetMaximumDescentRate(double aValue) bool SetMaximumDescentRate(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_descent_rate`{.interpreted-text role="command"} (meters/second).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetMaximumPitchAngle(double aValue) bool SetMaximumPitchAngle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_pitch_angle`{.interpreted-text role="command"} (degrees).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

::: method
bool SetPitchChangeGain(double aValue) bool SetPitchChangeGain(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.pitch_change_gain`{.interpreted-text role="command"} (unitless).

::: note
::: title
Note
:::

See [Notes About Method Arguments and Return Values](#notes-about-method-arguments-and-return-values).
:::
:::

## Examples

Script Example Within a WSF_GUIDANCE_COMPUTER:

    phase BOOST2
      on_entry
         WsfTrack targetTrack = PLATFORM.CurrentTargetTrack();
         double targetRelativeHeading = PLATFORM.RelativeHeadingOf(targetTrack);
         double targetSlantRange = PLATFORM.SlantRangeTo(targetTrack);
         if ((targetRelativeHeading < 15) && (targetSlantRange <= 1000))
         {
            SelectPhase("TERMINAL");
         }
      end_on_entry
      max_commanded_g 7 g
      commanded_flight_path_angle 20 deg
      next_phase GLIDE when phase_time > 14 sec
    end_phase
