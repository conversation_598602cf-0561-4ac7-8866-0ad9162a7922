orphan

:   

# DisEntityId {#DisEntityId}

::: DisEntityId
::: parsed-literal
`DisEntityId.Site`{.interpreted-text role="method"} `DisEntityId.Application`{.interpreted-text role="method"} `DisEntityId.Entity`{.interpreted-text role="method"}
:::
:::

## Overview

[DisEntityId](#disentityid) is an implementation of the DIS entity ID. The entity ID is used by DIS to identify individual platforms.

## Methods

::: method
int Site()

Returns the site ID.
:::

::: method
int Application()

Returns the application ID.
:::

::: method
int Entity()

Returns the entity ID.
:::
