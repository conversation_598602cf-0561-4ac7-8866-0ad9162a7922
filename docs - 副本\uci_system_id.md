orphan

:   

# UCI_SystemId

## Overview {#overview .UCI_SystemId}

This class defines a SystemId. It consists of an Universally Unique Identifier (UUID) and an optional descriptor. This type is used as an accessor since all IDs are created by the interface.

## Methods

::: method
string Descriptor()

Gets the descriptor for the ID if it exists.
:::

::: method
string UUID()

Returns the UUID of the corresponding ID.
:::
