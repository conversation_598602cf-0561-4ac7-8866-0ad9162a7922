orphan

:   

# WsfFormationManeuverLimitsCommand

::: {.WsfFormationManeuverLimitsCommand .inherits .WsfFormationCommand}
`WsfFormationManeuverLimitsCommand`{.interpreted-text role="class"} can be used to set various limits on the maneuvers that the member platforms of the formation to which it is assigned will perform. Depending on options selected, this command can also reset the limits to the default values for the mover in question. To set limits on all the members of a formation, execute this command on the top-level formation containing all of the members whose limits should be modified.
:::

## Methods

::: method
WsfFormationManeuverLimitsCommand Construct()

Construct a command that will reset the limits on the member platforms of the formation to which it is assigned. If limits are set using the other methods on this object, this command will no longer reset the limits to the default, but will instead set the selected limits, leaving the other limits as they are.
:::

::: method
WsfFormationManeuverLimitsCommand Construct(double aMaxPitchG_Load, double aMaxRollRate, double aMaxBankAngle)

Construct a command that will reset the max pitch g-load, the max roll rate (in degrees per second) and the max bank angle (in degrees) for the member platforms of the formation to which this command is assigned.
:::

::: method
void SetMaxPitchG_Load(double aMaxPitchG_Load)

Set the limit on pitch g-load that will be applied by this command. If no limit is set for this command, then the limit will not be modified on the member platform. Setting this limit has no effect on the other limits that can be set.
:::

::: method
double GetMaxPitchG_Load()

Return the limit on the pitch g-load that will be applied by this command.

::: note
::: title
Note
:::

If no pitch g-load limit has been set, the returned value will not reflect the operation of the command.
:::
:::

::: method
void SetMaxBankAngle(double aMaxBankAngle)

Set the limit on bank angle that will be applied by this command. If no limit is set for this command, then the limit will not be modified on the member platform. Setting this limit has no effect on the other limits that can be set.
:::

::: method
double GetMaxBankAngle()

Return the limit on max bank angle in degrees that will be applied by this command.

::: note
::: title
Note
:::

If no bank angle limit has been set, the returned value will not reflect the operation of the command.
:::
:::

::: method
void SetMaxRollRate(double aMaxRollRate)

Set the limit on roll rate that will be applied by this command. If no limit is set for this command, then the limit will not be modified on the member platform. Setting this limit has no effect on the other limits that can be set.
:::

::: method
double GetMaxRollRate()

Return the limit on roll rate in degrees per second that will be applied by this command.

::: note
::: title
Note
:::

If no roll rate limit has been set, the returned value will not reflect the operation of the command.
:::
:::
