orphan

:   

# Wizard

## Overview

Wizard is a tool that simplifies the process of creating and editing scenarios by providing a variety of tools such as syntax highlighting, auto-completion, context-sensitive command documentation, and a laydown editor.

-   `User's Guide <wizard_users_guide>`{.interpreted-text role="doc"}
-   `Reference Guide <wizard_reference_guide>`{.interpreted-text role="doc"}
-   `Grammar Guide <wizard_grammar_guide>`{.interpreted-text role="doc"}
-   `Change Log <wizard_change_log>`{.interpreted-text role="doc"}
