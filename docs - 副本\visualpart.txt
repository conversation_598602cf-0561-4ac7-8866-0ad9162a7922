# visualpart.rst.txt.md
orphan

:   



Script Class: `WsfVisualPart`{.interpreted-text role="class"}

::: {.command block=""}
visual_part \... end_visual_part

::: parsed-literal
\# Single mode sensor definition

[visual_part](#visual_part) *\<name\>* *WSF_VISUAL_PART*

:   \... `Platform_Part_Commands`{.interpreted-text role="ref"} \... \... `Articulated_Part_Commands`{.interpreted-text role="ref"} \...

end_visual_part
:::
:::



A [visual_part](#visual_part) provides the ability for a platform to represent articulated parts that are not defined by existing `comm`{.interpreted-text role="command"} or `sensor`{.interpreted-text role="command"}.

Visual parts may be published over DIS, by using the `dis_interface.articulated_part`{.interpreted-text role="command"} command in the `dis_interface`{.interpreted-text role="command"}.