orphan

:   

# WsfIntegratingSpaceMover

::: {.WsfIntegratingSpaceMover .inherits .WsfSpaceMover}
**Input type:** `WSF_INTEGRATING_SPACE_MOVER`{.interpreted-text role="model"}
:::

## Overview

A `WsfIntegratingSpaceMover`{.interpreted-text role="class"} uses a numerical integrator (`WsfOrbitalIntegrator`{.interpreted-text role="class"}) to advance the kinematics state of a platform according to the specified orbital dynamics (`WsfOrbitalDynamics`{.interpreted-text role="class"}).

## Orbital Dynamics Methods

::: method
WsfOrbitalDynamics OrbitalDynamics()

Return a copy of the orbital dynamics in use by this mover.
:::

::: method
void SetOrbitalDynamics(WsfOrbitalDynamics aDynamics)

Set the orbital dynamics for this mover. It is an error to attempt to change dynamics while the mover is currently propagating, so this method will fail in that case.
:::

## Orbital Integrator Methods

::: method
WsfOrbitalIntegrator OrbitalIntegrator()

Return a copy of the orbital integrator in use by this mover.
:::

::: method
void SetOrbitalIntegrator(WsfOrbitalIntegrator aIntegrator)

Set the orbital integrator for this mover. It is an error to attempt to change integrators while the mover is currently propagating, so this method will fail in that case.
:::
