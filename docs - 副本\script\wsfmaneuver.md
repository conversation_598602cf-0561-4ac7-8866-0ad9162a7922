orphan

:   

# WsfManeuver

## Overview {#overview .WsfManeuver}

The `WsfManeuver`{.interpreted-text role="class"} is an object representing a maneuver that a `WsfP6DOF_Mover`{.interpreted-text role="class"} can execute that changes the state of that mover.

Created maneuvers will not cause anything to occur until they are assigned to a specific `WsfP6DOF_Mover`{.interpreted-text role="class"} through either `WsfP6DOF_Mover.ExecuteManeuver`{.interpreted-text role="method"} or `WsfP6DOF_Mover.ExecuteManeuverSequence`{.interpreted-text role="method"}. The entry and exit constraints provide further control over the exact execution of a maneuver. These constraints, of type `WsfManeuverConstraint`{.interpreted-text role="class"}, give the conditions that are required to be met for the maneuver to begin executing (the entry constraint), and the conditions that must be met for a maneuver that has finished its execution to be marked complete (the exit condition). In isolation the entry and exit constraints are of more limited utility; in the context of a `WsfManeuverSequence`{.interpreted-text role="class"} the entry and exit constraints become central in obtaining complex behaviors.

## General Methods

::: method
bool GetDebug()

Returns if the maneuver is set to produce extra debugging output.
:::

::: method
void SetDebug(bool aDebug)

Enable or disable extra debugging output.

::: note
::: title
Note
:::

In some cases debugging output can produce a significant amount of output.
:::
:::

::: method
WsfP6DOF_Mover GetMover()

Return the `WsfP6DOF_Mover`{.interpreted-text role="class"} to which this maneuver is assigned. If this maneuver has not yet been assigned, this will return an invalid object.
:::

::: method
void Cancel()

Cancel this maneuver if it is assigned and has not completed. If it has not been assigned, or has completed, this method will do nothing.
:::

::: method
double GetUrgency()

Get the urgency of the maneuver. The urgency of a maneuver is a parameter that controls the manner of execution of some maneuvers. The use of this parameter is up to the individual maneuver; see the documentation for specific maneuvers for details.
:::

::: method
void SetUrgency(double aUrgency)

Set the urgency of this maneuver.
:::

::: method
string GetManeuverType()

Return a string giving the type of this maneuver.
:::

::: method
bool IsSequence()

Return true if this maneuver is a sequence.
:::

## Constraint Methods

::: method
WsfManeuverConstraint GetEntryConstraint()

Return the entry constraint for this maneuver.
:::

::: method
void SetEntryConstraint(WsfManeuverConstraint aConstraint)

Set the entry constraint for this maneuver.
:::

::: method
WsfManeuverConstraint GetExitConstraint()

Return the exit constraint for this maneuver.
:::

::: method
void SetExitConstraint(WsfManeuverConstraint aConstraint)

Set the exit constraint for this maneuver.
:::

## Status Methods

::: method
bool IsAssigned()

Return true if this maneuver has been assigned to a `WsfP6DOF_Mover`{.interpreted-text role="class"}.
:::

::: method
bool IsPending()

Returns true if this maneuver is pending. A maneuver is pending if it has been assigned, but the entry constraint has not yet been met. If the maneuver was not given an entry constraint, it will never be pending.
:::

::: method
bool IsExecuting()

Returns true if this maneuver is executing. A maneuver is executing if it has been assigned, and the entry constraint is satisfied.
:::

::: method
bool IsCompleting()

Returns true if a maneuver is completing. A maneuver is completing if it has been assigned, it is no longer executing, but the exit constraint is not satisfied.
:::

::: method
bool IsCompleted()

Returns true if this maneuver is completed. A maneuver is completed it has been assigned, is no longer executing, and the exit constraint is satisfied.
:::

::: method
bool IsCanceled()

Returns true if this maneuver was canceled. A canceled maneuver was halted before it reached the completed state. A completed maneuver cannot be canceled, nor can an unassigned maneuver.
:::

## Available Maneuvers

### Elemental Maneuvers

Elemental maneuvers are the simplest available maneuvers, all of which set one feature of the motion of the assigned platform. These maneuvers all operate by interacting with the autopilot on the `WsfP6DOF_Mover`{.interpreted-text role="class"} to which the maneuver is assigned, so all of these should be thought of as setting target motions, which may eventually be fulfilled by the autopilot. By stringing together these maneuvers with careful use of the set of `WsfManeuverConstraint`{.interpreted-text role="class"} types, one can produce arbitrarily complex motions.

-   `WsfFlightPathAngleManeuver`{.interpreted-text role="class"}
-   `WsfHoldCurrentSpeedManeuver`{.interpreted-text role="class"}
-   `WsfPitchAngleManeuver`{.interpreted-text role="class"}
-   `WsfPitchDeltaManeuver`{.interpreted-text role="class"}
-   `WsfPitchGLoadManeuver`{.interpreted-text role="class"}
-   `WsfPitchRateManeuver`{.interpreted-text role="class"}
-   `WsfRollAngleManeuver`{.interpreted-text role="class"}
-   `WsfRollDeltaManeuver`{.interpreted-text role="class"}
-   `WsfRollRateManeuver`{.interpreted-text role="class"}
-   `WsfScriptedManeuver`{.interpreted-text role="class"}
-   `WsfSpeedKCAS_Maneuver`{.interpreted-text role="class"}
-   `WsfSpeedKTAS_Maneuver`{.interpreted-text role="class"}
-   `WsfSpeedMachManeuver`{.interpreted-text role="class"}
-   `WsfTurnToHeadingManeuver`{.interpreted-text role="class"}

### Relative Maneuvers

All maneuvers affect changes to the platform to which they are assigned. A relative maneuver\'s changes depend not only on the state of the platform to which it is assigned, but also on some other platform in the simulation. Relative maneuvers control the executing platform to create a specific situation for that platform relative to a target platform. See `WsfRelativeManeuver`{.interpreted-text role="class"} for more details on features of all relative maneuvers.

-   `WsfPursueManeuver`{.interpreted-text role="class"}
