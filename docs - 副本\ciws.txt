# ciws.rst.txt.md
orphan

:   

::: demo
ciws
:::

| A large part of this was taken from the `gun_engagement`{.interpreted-text role="demo"} demo. Changes were:

-   The gun was placed on a ship.
-   The geometric sensor replaced with a ciws fire control radar, red_ciws_radar_1 (sigma az/el errors can be changed)
-   A variance was added to the muzzle velocity of the 30mm round (variance changed in weapons/guns/30mm_bullet.txt)
-   An observer (observer_bullseye.txt) can be turned on to view the shot pattern.
-   The radar is set to a frame time of 100 hz.
-   The state machine is set up to run faster than the 100 hz to accomplish the firing rate of 70 rounds per sec.