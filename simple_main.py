#!/usr/bin/env python3
"""
简化版AFSIM Platform Agent命令行界面
专注于脚本解释、参数查询和脚本生成
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.enhanced_platform_agent import SimplePlatformAgent


def setup_logging(level: str = "INFO"):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('simple_agent.log', encoding='utf-8')
        ]
    )


def init_agent(config_path: str = None) -> SimplePlatformAgent:
    """初始化Agent"""
    print("🚀 正在初始化AFSIM Platform Agent...")
    
    agent = SimplePlatformAgent(config_path)
    
    if not agent.initialize():
        print("❌ Agent初始化失败")
        sys.exit(1)
    
    print("✅ Agent初始化成功")
    return agent


def interactive_mode(agent: SimplePlatformAgent):
    """交互模式"""
    print("\n🎯 AFSIM Platform Agent - 交互模式")
    print("=" * 60)
    print("💡 我可以帮您：")
    print("   • 解释AFSIM脚本代码")
    print("   • 查询飞机技术参数")
    print("   • 生成飞机AFSIM脚本")
    print("   • 回答平台相关问题")
    print("\n📝 输入 'help' 查看帮助，'quit' 退出")
    print("=" * 60)
    
    while True:
        try:
            user_input = input("\n🤖 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            elif user_input.lower() == 'help':
                show_help(agent)
                continue
            elif user_input.lower() == 'status':
                show_status(agent)
                continue
            elif user_input.lower() == 'clear':
                agent.clear_history()
                print("🧹 对话历史已清空")
                continue
            elif user_input.lower() == 'aircraft':
                show_aircraft_list(agent)
                continue
            elif user_input.lower() == 'example':
                show_example_script()
                continue
            elif not user_input:
                continue
            
            print("\n🤔 处理中...")
            response = agent.chat(user_input)
            print(f"\n🤖 助手:\n{response}")
            
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")


def show_help(agent: SimplePlatformAgent):
    """显示帮助信息"""
    aircraft_list = ", ".join(agent.get_available_aircraft())
    
    print("""
📚 可用命令：
  help          - 显示此帮助信息
  status        - 显示Agent状态
  clear         - 清空对话历史
  aircraft      - 显示可用飞机列表
  example       - 显示示例脚本
  quit/exit/q   - 退出程序

💡 功能示例：

1️⃣ 脚本解释 - 直接粘贴AFSIM脚本：
   platform BBB WSF_PLATFORM 
        side blue
        icon f-15
        add mover WSF_FORMATION_FLYER 
             position 34:31:34.58n 113:28:09.47e altitude 2000 m
             speed 100 m/s
        end_mover  
   end_platform

2️⃣ 飞机参数查询：
   • "F-18参数"
   • "F-22技术信息"
   • "F-35性能数据"

3️⃣ 脚本生成：
   • "生成F-18脚本"
   • "生成F-22脚本"
   • "生成F-35脚本"

4️⃣ 参数查询：
   • "altitude参数是什么意思？"
   • "platform命令有哪些参数？"
   • "mover组件如何配置？"

🛩️ 可用飞机型号：""" + aircraft_list)


def show_status(agent: SimplePlatformAgent):
    """显示Agent状态"""
    status = agent.get_status()
    print(f"""
📊 Agent状态信息：
   初始化状态: {'✅' if status['initialized'] else '❌'}
   对话轮数: {status['conversation_turns']}
   可用飞机: {len(status['available_aircraft'])} 种
   支持功能: {', '.join(status['features'])}
   
   基础Agent状态:
   索引文件数: {status['base_agent_status'].get('indexed_files', 0)}
   向量数据库: {'✅' if status['base_agent_status'].get('initialized', False) else '❌'}
""")


def show_aircraft_list(agent: SimplePlatformAgent):
    """显示可用飞机列表"""
    aircraft_list = agent.get_available_aircraft()
    print("\n🛩️ 可用飞机型号：")
    for i, aircraft in enumerate(aircraft_list, 1):
        print(f"   {i}. {aircraft}")
    print(f"\n总计: {len(aircraft_list)} 种飞机")


def show_example_script():
    """显示示例脚本"""
    example = """
📝 示例AFSIM脚本：

platform BBB WSF_PLATFORM 
     side blue
     icon f-15
     add mover WSF_FORMATION_FLYER 
          position 34:31:34.58n 113:28:09.47e altitude 2000 m
          speed 100 m/s
          lead_aircraft leader
          offset_forward_from_lead 1 au
     end_mover  
     position 34:29:24.66n 113:26:46.51e altitude 2000 m
end_platform

💡 您可以复制上面的脚本，然后粘贴到输入框中，我会为您详细解释每个参数的含义。
"""
    print(example)


def demo_mode(agent: SimplePlatformAgent):
    """演示模式"""
    print("\n🎬 演示模式")
    print("=" * 50)
    
    demo_items = [
        {
            "title": "1. 脚本解释演示",
            "input": """platform demo_fighter WSF_PLATFORM 
     side blue
     icon f-16
     position 35:00:00n 118:00:00w altitude 5000 m
end_platform""",
            "description": "解释一个简单的平台脚本"
        },
        {
            "title": "2. 飞机参数查询演示", 
            "input": "F-18参数",
            "description": "查询F-18战斗机的技术参数"
        },
        {
            "title": "3. 脚本生成演示",
            "input": "生成F-22脚本",
            "description": "基于真实数据生成F-22脚本"
        },
        {
            "title": "4. 参数查询演示",
            "input": "altitude参数是什么意思？",
            "description": "查询特定参数的含义"
        }
    ]
    
    for item in demo_items:
        print(f"\n{item['title']}")
        print(f"描述: {item['description']}")
        print(f"输入: {item['input']}")
        print("-" * 30)
        
        try:
            response = agent.chat(item['input'])
            # 截取前300字符显示
            short_response = response[:300] + "..." if len(response) > 300 else response
            print(f"输出: {short_response}")
        except Exception as e:
            print(f"错误: {e}")
        
        input("\n按回车继续下一个演示...")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化版AFSIM Platform Agent")
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    parser.add_argument('--mode', default='interactive',
                       choices=['interactive', 'demo'],
                       help='运行模式')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ 请设置DEEPSEEK_API_KEY环境变量")
        print("   export DEEPSEEK_API_KEY='your_api_key'")
        sys.exit(1)
    
    # 初始化Agent
    agent = init_agent(args.config)
    
    # 运行对应模式
    if args.mode == 'interactive':
        interactive_mode(agent)
    elif args.mode == 'demo':
        demo_mode(agent)


if __name__ == '__main__':
    main()
