# mystic_reference_guide.rst.txt.md
orphan

:   



The Mystic Reference Guide lists the features included within the application organized into various categories.



-   `Command Line Options<mystic_command_line>`{.interpreted-text role="doc"}
-   `Start-up Dialog<mystic_start_dialog>`{.interpreted-text role="doc"}



Most of the Dialogs and Toolbars can be accessed through the `View <mystic_view_menu>`{.interpreted-text role="doc"} menu. A few are accessed through the `Context<mystic_platform_context_menus>`{.interpreted-text role="doc"} menu.



The following items control the appearance of Mystic.

-   `Configuration Files<mystic_user_configurations>`{.interpreted-text role="doc"}
-   `Map Definitions<mystic_map_definitions>`{.interpreted-text role="doc"}
-   `Model Configuration<wkf_model_definitions>`{.interpreted-text role="doc"}
-   `Platform Options<mystic_platform_options>`{.interpreted-text role="doc"}
-   `Plug-in Manager<mystic_plugin_manager>`{.interpreted-text role="doc"}
-   `Preferences<mystic_preferences>`{.interpreted-text role="doc"}



The following items describe interactions with Platforms not mentioned in the above sections.

-   `Context Menus<mystic_platform_context_menus>`{.interpreted-text role="doc"}



The following is a comprehensive list of functionality provided by Mystic extensions.

::: {.toctree glob="" titlesonly=""}
wkf_plugin/[rv]()\*
:::