# change_semi_major_axis.rst.txt.md
orphan

:   



Script Type: `WsfChangeSemiMajorAxis`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} change_semi_major_axis

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [semi_major_axis]() \| [apoapsis_altitude]() \| [periapsis_altitude]() \...

end_maneuver
:::

Change the semi-major axis of an the orbit to the specified value. The apoapsis or periapsis altitude can also be specified instead of the semi-major axis value.

::: command
semi_major_axis \<length-value\>

The desired semi-major axis of the final orbit.
:::

::: command
apoapsis_altitude \<length-value\>

The desired altitude of the final orbit\'s apoapsis.
:::

::: command
periapsis_altitude \<length-value\>

The desired altitude of the final orbit\'s periapsis.
:::