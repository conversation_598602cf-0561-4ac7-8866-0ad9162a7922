# orbital_maneuvering_models.rst.txt.md
orphan

:   

::: {#orbital_maneuvering_models}
:::



::: {#orbital_maneuvering_models.maneuver_update_interval}
::: command
maneuver_update_interval \<time-value\>

The update interval at which `finite<orbital_event.finite>`{.interpreted-text role="ref"} maneuvering events are evaluated.

Default 1.0 s
:::
:::





::: parsed-literal

maneuvering simple

:   [delta_v]() \... [maximum_acceleration]() \... [maneuver_update_interval]() \...

end_maneuvering
:::

The default orbital maneuver model. delta-V is provided explicitly as an input (instead of providing mass properties and thrust). Finite maneuvers can be limited by a maximum acceleration. This model is representative of ion thrusters, and can be used when management of a specific delta-V budget is the primary consideration.

::: command
delta_v \<speed-value\>

The total amount of \"delta-V\" available to the parent platform for performing maneuvers.

Default $10^{12}~\rm{m}/\rm{s}$.
:::

::: command
maximum_acceleration \<acceleration-value\>

The maximum acceleration available to perform `finite<orbital_event.finite>`{.interpreted-text role="ref"} maneuvers.

Default $1000~\rm{m}/\rm{s}^2$.
:::



::: parsed-literal

maneuvering rocket

:   `stage commands<orbital_maneuvering_models.stage_commands>`{.interpreted-text role="ref"} \... [maneuver_update_interval]() \...

end_maneuvering
:::

The rocket orbital maneuver model expends delta-V according to the Tsiolkovsky rocket equation. Mass properties and thrust must be provided (see `stage commands<orbital_maneuvering_models.stage_commands>`{.interpreted-text role="ref"}, below, for a description of valid stage inputs.

The rocket may be specified in more than one stage. In that case the mass properties and thrust must be provided for each stage separately. The first stage defined is the first one used, and it would be the first jettisoned using the `perform staging<perform_staging>`{.interpreted-text role="doc"}.

For example, the following definition describes the Russian Proton \"Breeze-M\" booster and payload. It is defined in three stages: the first stage describes the auxiliary propellant tank (APT), that is jettisoned after two or three initial maneuvers; the second stage describes the remainder of the booster\'s mass and propulsion; and the third stage describes a payload with the capability of performing on-orbit maneuvers:

::: parsed-literal

maneuvering rocket

:   

    stage // breeze-M APT

    :   specific_impulse 326 seconds thrust 19620 newtons empty_mass 1125 kg fuel_mass 10920 kg

    end_stage stage // breeze-M upper stage specific_impulse 326 seconds thrust 19620 newtons empty_mass 1125 kg fuel_mass 8300 kg end_stage stage // payload with mr-107 aerojet small thrusters specific_impulse 236 s thrust 1028 newtons // assume x4 empty_mass 2000 kg fuel_mass 500 kg // guess end_stage

end_maneuvering
:::



::: parsed-literal

stage

:   [specific_impulse]() \... [thrust]() \... [burn_rate]() \... [exhaust_velocity]() \... [initial_mass]() \| [total_mass]() \... [final_mass]() \| [empty_mass]() \... [propellant_mass]() \| [fuel_mass]() \...

end_stage
:::

Define one or more stages to be used by the `rocket<orbital_maneuvering_models.rocket>`{.interpreted-text role="ref"} maneuvering model.

::: command
specific_impulse \<time-value\>

Provide the specific impulse of the stage in vacuum, typically expressed in seconds.

::: note
::: title
Note
:::

The specific impulse `I_{sp}`{.interpreted-text role=":math"} is related to the `exhaust velocity<exhaust_velocity>`{.interpreted-text role="command"}, `v_e`{.interpreted-text role=":math"}, by the acceleration of gravity at sea level, $g_0$, as follows: $I_{sp} = v_e / g_0$.
:::
:::

::: command
thrust \<force-value\>

Provide the total thrust ($F_t$) of the stage.
:::

::: command
burn_rate \<mass-flow-value\>

Provide the burn (fuel depletion) rate ($\dot{m}$).
:::

::: command
exhaust_velocity \<speed-value\>

Provide the propellant exhaust velocity ($v_e$).
:::

::: command
initial_mass \<mass-value\>
:::

::: command
total_mass \<mass-value\>

Specify the initial_mass ($m_i$), defined to be the [propellant_mass]() plus the [final_mass]().
:::

::: command
final_mass \<mass-value\>
:::

::: command
empty_mass \<mass-value\>

Specify the final (empty) mass ($m_f$), defined to be the [initial_mass]() minus the [propellant_mass]().
:::

::: command
propellant_mass \<mass-value\>
:::

::: command
fuel_mass \<mass-value\>

Specify the propellant mass, defined to be the [initial_mass]() minus the [final_mass]().
:::

::: note
::: title
Note
:::

The inputs [total_mass](), [empty_mass](), and [fuel_mass]() can be also be used to specify mass properties for stages, in order to remain consistent with the inputs for `WSF_GUIDED_MOVER`{.interpreted-text role="model"}. It is understood that [fuel_mass]() represents all propellant (fuel and oxidizer) in the stage.
:::



The `rocket<orbital_maneuvering_models.rocket>`{.interpreted-text role="ref"} maneuvering model is governed by the Tsiolkovsky rocket equation. Applying this equation to a single maneuver:

$$\Delta{v} = v_e ln \frac{m_0}{m_1}$$

where $\Delta{v}$ is the magnitude of the change in velocity of the rocket, $v_e$ is the [exhaust_velocity](), $m_0$ is the initial mass before execution of the maneuver, and $m_1$ is the final mass after execution of the maneuver.

Defining a propellant mass $m_p$ expended in the maneuver of duration $\Delta{t}$, it is clear that

$$m_p \equiv m_0 - m_1 = \dot{m} \Delta{t}$$

and equivalently,

$$m_1 = m_0 - \dot{m} \Delta{t}$$

Then, rewriting the rocket equation to remove $m_1$, and manipulating to solve for the maneuver duration $\Delta{t}$:

$$\Delta{t} = \frac{m_0}{\dot{m}}(1 - \exp{\frac{-\Delta{v}}{v_e}})$$

When performing a maneuver, checks are made to ensure that a sufficient fuel supply is available. As the maneuver is performed, propellant is deleted and the stage\'s mass properties are updated.