# wsfformationchangeleadsubcommand.rst.txt.md
orphan

:   



::: {.WsfFormationChangeLeadSubCommand .inherits .WsfFormationCommand}
`WsfFormationChangeLeadSubCommand`{.interpreted-text role="class"} will change the lead sub-formation of the formation to which the command is assigned. After the change in leader, the attached members of the formation will update their station keeping to account for the new leader.
:::



::: method
WsfFormationChangeLeadSubCommand Construct(string aNewLeadName)

Construct a command that will set the lead sub-formation to the sub-formation with the given relative name for the formation to which the command is assigned.
:::

::: method
string GetNewLeaderName()

Return the relative name that would be the new lead sub-formation for the formation to which this command is assigned.
:::