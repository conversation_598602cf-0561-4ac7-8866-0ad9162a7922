"""
AFSIM Platform Agent - 中文总结和翻译模块
将Platform参数和技术文档转换为中文说明
"""

import os
import logging
import json
from typing import List, Dict, Any, Optional
import yaml
from openai import OpenAI

logger = logging.getLogger(__name__)


class ChineseSummarizer:
    """中文总结器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化中文总结器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.client = None
        self._initialize_llm()
        
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            from pathlib import Path
            config_path = Path(__file__).parent.parent / "config" / "config.yaml"
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'llm': {
                'provider': 'deepseek',
                'deepseek': {
                    'api_key': '',
                    'base_url': 'https://api.deepseek.com/v1',
                    'model': 'deepseek-chat',
                    'temperature': 0.1,
                    'max_tokens': 4000
                }
            },
            'translation': {
                'enable_translation': True,
                'target_language': 'zh',
                'summary': {
                    'max_length': 500,
                    'min_length': 100,
                    'enable_bullet_points': True
                }
            }
        }
    
    def _initialize_llm(self):
        """初始化LLM客户端"""
        try:
            llm_config = self.config['llm']
            provider = llm_config.get('provider', 'deepseek')
            
            if provider == 'deepseek':
                config = llm_config['deepseek']
                api_key = config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')
                
                if not api_key:
                    logger.warning("未设置DeepSeek API Key，请在环境变量DEEPSEEK_API_KEY中设置")
                    return
                
                self.client = OpenAI(
                    api_key=api_key,
                    base_url=config['base_url']
                )
                self.model = config['model']
                self.temperature = config['temperature']
                self.max_tokens = config['max_tokens']
                
                logger.info("DeepSeek客户端初始化成功")
                
            else:
                logger.error(f"不支持的LLM提供商: {provider}")
                
        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}")
    
    def summarize_platform_parameters(self, parameters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        总结Platform参数
        
        Args:
            parameters: 参数列表
            
        Returns:
            Dict: 总结结果
        """
        try:
            if not self.client:
                return {"error": "LLM客户端未初始化"}
            
            if not parameters:
                return {"summary": "未找到Platform参数", "parameters": []}
            
            # 构建参数信息
            param_info = []
            for param in parameters:
                param_info.append({
                    "名称": param.get('name', ''),
                    "类型": param.get('type', ''),
                    "描述": param.get('description', ''),
                    "上下文": param.get('context', '')[:200] + "..." if len(param.get('context', '')) > 200 else param.get('context', '')
                })
            
            # 构建提示词
            prompt = self._build_parameter_summary_prompt(param_info)
            
            # 调用LLM
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的AFSIM技术文档分析师，擅长将英文技术参数转换为清晰的中文说明。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            summary_text = response.choices[0].message.content
            
            # 解析总结结果
            result = self._parse_summary_response(summary_text, param_info)
            
            logger.info(f"成功总结 {len(parameters)} 个Platform参数")
            return result
            
        except Exception as e:
            logger.error(f"总结Platform参数失败: {e}")
            return {"error": str(e)}
    
    def _build_parameter_summary_prompt(self, param_info: List[Dict[str, Any]]) -> str:
        """构建参数总结提示词"""
        prompt = """请分析以下AFSIM Platform参数，并提供中文总结：

参数列表：
"""
        for i, param in enumerate(param_info, 1):
            prompt += f"""
{i}. 参数名称: {param['名称']}
   参数类型: {param['类型']}
   英文描述: {param['描述']}
   上下文: {param['上下文']}
"""
        
        prompt += """
请按以下格式提供总结：

## 总体概述
[用2-3句话概述这些参数的主要用途和功能]

## 参数分类
[将参数按功能分类，如：位置参数、配置参数、行为参数等]

## 详细说明
[对每个参数提供中文名称、用途说明和使用注意事项]

## 使用建议
[提供参数使用的最佳实践建议]

请确保：
1. 使用简洁明了的中文
2. 技术术语保持准确性
3. 提供实用的使用指导
4. 突出重要参数和常用参数
"""
        return prompt
    
    def _parse_summary_response(self, summary_text: str, param_info: List[Dict[str, Any]]) -> Dict[str, Any]:
        """解析总结响应"""
        return {
            "summary": summary_text,
            "parameter_count": len(param_info),
            "parameters": param_info,
            "timestamp": self._get_timestamp()
        }
    
    def summarize_document_content(self, content: str, doc_title: str = "") -> Dict[str, Any]:
        """
        总结文档内容
        
        Args:
            content: 文档内容
            doc_title: 文档标题
            
        Returns:
            Dict: 总结结果
        """
        try:
            if not self.client:
                return {"error": "LLM客户端未初始化"}
            
            if not content.strip():
                return {"summary": "文档内容为空", "content": ""}
            
            # 限制内容长度
            max_content_length = 3000
            if len(content) > max_content_length:
                content = content[:max_content_length] + "..."
            
            # 构建提示词
            prompt = self._build_document_summary_prompt(content, doc_title)
            
            # 调用LLM
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的AFSIM技术文档分析师，擅长将英文技术文档转换为清晰的中文总结。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            summary_text = response.choices[0].message.content
            
            result = {
                "summary": summary_text,
                "original_title": doc_title,
                "content_length": len(content),
                "timestamp": self._get_timestamp()
            }
            
            logger.info(f"成功总结文档: {doc_title}")
            return result
            
        except Exception as e:
            logger.error(f"总结文档内容失败: {e}")
            return {"error": str(e)}
    
    def _build_document_summary_prompt(self, content: str, doc_title: str) -> str:
        """构建文档总结提示词"""
        prompt = f"""请分析以下AFSIM技术文档并提供中文总结：

文档标题: {doc_title}

文档内容:
{content}

请按以下格式提供总结：

## 文档概述
[用2-3句话概述文档的主要内容和用途]

## 核心要点
[列出文档中的关键技术要点，使用要点形式]

## 重要参数/配置
[如果文档包含参数或配置信息，请列出重要的参数]

## 使用场景
[说明这些内容在AFSIM中的典型使用场景]

## 注意事项
[提醒用户需要注意的重要事项]

请确保：
1. 使用简洁明了的中文
2. 保持技术术语的准确性
3. 突出实用性信息
4. 适合技术人员阅读
"""
        return prompt
    
    def translate_text(self, text: str, target_language: str = "zh") -> str:
        """
        翻译文本
        
        Args:
            text: 要翻译的文本
            target_language: 目标语言
            
        Returns:
            str: 翻译结果
        """
        try:
            if not self.client:
                return text
            
            if not text.strip():
                return text
            
            # 构建翻译提示词
            prompt = f"""请将以下英文技术文档翻译为中文，保持技术术语的准确性：

原文：
{text}

翻译要求：
1. 保持技术术语的专业性
2. 使用简洁明了的中文表达
3. 保持原文的结构和格式
4. 对于专有名词，可以保留英文并在括号中提供中文解释
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的技术文档翻译专家，擅长英中翻译。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=self.max_tokens
            )
            
            translated_text = response.choices[0].message.content
            logger.info("文本翻译完成")
            return translated_text
            
        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return text
    
    def generate_parameter_usage_guide(self, parameters: List[Dict[str, Any]]) -> str:
        """
        生成参数使用指南
        
        Args:
            parameters: 参数列表
            
        Returns:
            str: 使用指南
        """
        try:
            if not self.client or not parameters:
                return "无法生成使用指南"
            
            # 构建参数信息
            param_list = []
            for param in parameters:
                param_list.append(f"- {param.get('name', '')}: {param.get('description', '')}")
            
            param_text = "\n".join(param_list)
            
            prompt = f"""基于以下AFSIM Platform参数，生成一个实用的中文使用指南：

参数列表：
{param_text}

请生成一个包含以下内容的使用指南：

1. 参数分类和优先级
2. 常用参数组合
3. 配置示例
4. 最佳实践
5. 常见问题和解决方案

请使用清晰的中文表达，适合技术人员参考使用。
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个AFSIM专家，擅长编写技术指南。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            guide = response.choices[0].message.content
            logger.info("参数使用指南生成完成")
            return guide
            
        except Exception as e:
            logger.error(f"生成使用指南失败: {e}")
            return "生成使用指南时发生错误"
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
