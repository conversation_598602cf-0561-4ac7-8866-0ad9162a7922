# wsfimage.rst.txt.md
orphan

:   



::: {.WsfImage cloneable=""}
WsfImage is typically contained with a `WsfImageMessage`{.interpreted-text role="class"} or `WsfVideoMessage`{.interpreted-text role="class"} and represents the product of an imaging sensor.
:::



These methods pertain to the image as a whole.

::: method
double ImageTime() double Time()

Returns the time the image was produced.
:::

::: method
int ImageNumber() int Number()

Returns the image number of the image within a stream of images. For an image that represents a frame in a video stream, this would be the frame number within the stream.
:::

::: method
void SetImageNumber(int aNumber) void SetNumber(int aNumber)

Set the image number of the image within a stream of images.
:::

::: method
int StreamNumber()

Returns the stream number of the image. For a static image, this number is incremented for each image. For a video stream this number is incremented whenever the sensor is turned on.
:::

::: method
void SetStreamNumber(int aNumber)

Set the stream number that contains the image.
:::

::: method
WsfPlatform Originator()

Returns the platform containing the originating sensor.

::: note
::: title
Note
:::

Use with caution. The platform may have been deleted.
:::
:::

::: method
int OriginatorIndex()

Returns the platform index of the platform that contained the sensor that produced the image.
:::

::: method
WsfGeoPoint OriginatorLocation()

Returns the location of the platform that contained the sensor that produced the image.
:::

::: method
string SensorName()

The name of the sensor that produced the image.
:::

::: method
string SensorType()

The type of the sensor that produced the image.
:::

::: method
string SensorMode()

The mode in which the originating sensor was operating at the time it produced the image.
:::

::: method
WsfGeoPoint CenterLocation()

Returns the approximate location of the center of the image.

::: note
::: title
Note
:::

This may be zero if it is not defined by the sensor implementation.
:::
:::

::: method
double Width() double Height()

Returns the width and height, respectively, of the image in pixels.

::: note
::: title
Note
:::

This may be zero if it is not defined by the sensor implementation.
:::
:::

::: method
double WidthResolution() double HeightResolution()

Returns the width and height of an image pixel in meters.

::: note
::: title
Note
:::

These may be zero if it is not defined by the sensor implementation.
:::
:::

::: method
double CollectionFactor()

A value of less than 1 indicates that the collection time was not sufficient to achieve the desired resolution, while a value greater than 1 indicates the collection time was greater than that needed to achieve the desired resolution.

The return value will be 1.0 if the sensor did not define a value.

::: note
::: title
Note
:::

Be sure to allow some tolerance when checking this value for 1.0 as the achieved resolution may differ a slight amount from the requested value due to timing and other numerical issues.
:::
:::

::: method
double NoiseLevel()

Returns the noise level of the producing sensor.

::: note
::: title
Note
:::

This may be zero if it is not defined by the sensor implementation.
:::
:::

::: method
double BackgroundLevel()

Returns the signal level of the \'background.\'

::: note
::: title
Note
:::

This may be zero if it is not defined by the sensor implementation.
:::
:::

::: method
double MinimumLevel()

Returns the signal level that corresponds to a [PixelIntensity]()() of zero.
:::

::: method
double MaximumLevel()

Returns the signal level that corresponds to a [PixelIntensity]()() of one.
:::

::: method
int ObjectCount()

Returns the number of objects seen in the image.
:::



These methods are used to obtain information about a specific object in the image.

::: note
::: title
Note
:::

Unless otherwise noted, the methods take an object index value (aIndex) that represents the object whose information is to be retrieved. This value must be in the range:: 0 \<= aIndex \< ObjectCount()
:::

::: method
WsfGeoPoint Location(int aIndex)

Returns the perceived location of the specified object in the image.
:::

::: method
double SignalLevel(int aIndex)

Returns the signal level of the specified object in the image.

::: note
::: title
Note
:::

This may be zero if it is not defined by the sensor implementation.
:::
:::

::: method
double PixelCount(int aIndex)

Returns the approximate number of pixels occupied by the specified object in the image.
:::

::: method
double PixelIntensity(int aIndex)

Returns the approximate intensity of the specified object in the image. The return value is in the range \[0, 1\], where a value of zero corresponds to a signal level of returned by [MinimumLevel]()() and a value of one corresponds to a signal level returned by [MaximumLevel]()().
:::

::: method
int TruthIndex(int aIndex)

Returns the truth platform index of the specified object in the image.
:::

::: method
string TruthName(int aIndex)

Returns the truth platform name of the specified object in the image.
:::

::: method
string TruthType(int aIndex)

Returns the truth type of the specified object in the image.
:::

::: method
string TruthSide(int aIndex)

Returns the truth side of the specified object in the image.
:::