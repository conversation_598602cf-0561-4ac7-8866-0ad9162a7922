# mystic_file_menu.rst.txt.md
orphan

:   





-   Open\... - Browse to open an `event_pipe`{.interpreted-text role="command"} file.
-   Open Recent - Choose from a recently opened `event_pipe`{.interpreted-text role="command"} file.
-   Save Configuration - Save the current `application settings<mystic_user_configurations>`{.interpreted-text role="doc"} to file.
-   Load Configuration - Load a saved `application settings<mystic_user_configurations>`{.interpreted-text role="doc"} file.
-   Import Configuration Options\... - Import some features from an `application settings<mystic_user_configurations>`{.interpreted-text role="doc"} file.
-   Recent Configurations - Choose from a recently used `application settings<mystic_user_configurations>`{.interpreted-text role="doc"} file.
-   Clear Platform Options - Clear out all of the `platform options<mystic_platform_options>`{.interpreted-text role="doc"}.
-   Exit - Exit the application.