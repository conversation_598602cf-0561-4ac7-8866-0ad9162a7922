orphan

:   

# WsfBMDisseminateC2

::: WsfBMDisseminateC2
**Navigation:** `script_types`{.interpreted-text role="doc"}
:::

**Derives From:** `WsfProcessor`{.interpreted-text role="class"}, `WsfPlatformPart`{.interpreted-text role="class"}, `WsfObject`{.interpreted-text role="class"}

## Overview

**WsfBMDisseminateC2** is the script interface for invoking methods on the IADS C2 disseminate C\^2 base class. This class provides common interfaces that all IADS C2 dissemination processors derive.

## Methods

::: method
void SetAssetManager(WSF_BATTLE_MANAGER battle_manager)

Couples the asset manager to the dissemination processor responsible for handling outgoing messages for the processor. **Required Initialization**

Note: Sets asset manager reference to the asset manager referenced by the battle manager.
:::

::: method
void Run()

Runs the dissemination processors main message retrieval loop for pulling messages from the battle manager.
:::

::: method
void ClearOutgoingMessages()

Clears messages retrieved from the last call to Run() and that have not been pulled with a variant of the GetOutgoingMessages\* interface.
:::

::: method
Array\<WsfBMAssignmentMessage\> GetOutgoingAssignmentMessages()

Retrieves outgoing assignment messages with proper heading information based on the Dissemination C\^2 routing settings. These messages will then need to be sent by the dissemination processor within AFSIM script.
:::

::: method
Array\<WsfBMAssignmentStatusMessage\> GetOutgoingAssignmentStatusMessages()

Retrieves outgoing assignment status messages with proper heading information based on the Dissemination C\^2 routing settings. These messages will then need to be sent by the dissemination processor within AFSIM script.
:::

::: method
Array\<WsfBMStatusMessage\> GetOutgoingStatusMessages()

Retrieves outgoing status messages with proper heading information based on the Dissemination C\^2 routing settings. These messages will then need to be sent by the dissemination processor within AFSIM script.
:::

::: method
Array\<WsfBMTrackMessage\> GetOutgoingTrackMessages()

Retrieves outgoing track messages with proper heading information based on the Dissemination C\^2 routing settings. These messages will then need to be sent by the dissemination processor within AFSIM script.
:::
