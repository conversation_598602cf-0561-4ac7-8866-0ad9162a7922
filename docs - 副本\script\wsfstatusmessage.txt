# wsfstatusmessage.rst.txt.md
orphan

:   





::: method
string Status()

Return the status.
:::

::: method
void SetStatus(string aStatus)

Set the status.
:::

::: method
WsfTrackId RequestId()

Return the track Id to which the status applies.
:::

::: method
void SetRequestId(WsfTrackId aRequestId)

Set the track Id to which the status applies.
:::

::: method
string SystemName()

Return the system name to which the status applies.
:::

::: method
void SetSystemName(string aSystemName)

Set the system name to which the status applies.
:::

::: method
WsfPlatform Platform()

Return the platform to which the status applies.
:::

::: method
void SetPlatform(WsfPlatform aPlatform)

Set the platform to which the status applies.
:::