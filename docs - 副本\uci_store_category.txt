# uci_store_category.rst.txt.md
orphan

:   





This is a helper class to define a StoreCategory in a `UCI_DMPI_Message`{.interpreted-text role="class"}.



::: method
UCI_StoreCategory AIR()

Return a store category of type UCI_AIR.
:::

::: method
UCI_StoreCategory GROUND()

Return a store category of type UCI_GROUND.
:::

::: method
UCI_StoreCategory GUN()

Return a store category of type UCI_GUN.
:::

::: method
UCI_StoreCategory SEA_SUBSURFACE()

Return a store category of type UCI_SEA_SUBSURFACE.
:::

::: method
UCI_StoreCategory SEA_SURFACE()

Return a store category of type UCI_SEA_SURFACE.
:::

::: method
UCI_StoreCategory SEA_SURFACE_SAMSSM()

Return a store category of type UCI_SEA_SURFACE_SAMSSM.
:::