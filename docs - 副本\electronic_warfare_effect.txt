# electronic_warfare_effect.rst.txt.md
orphan

:   



::: {.command block=""}
electronic_warfare_effect
:::

EW Effect Aggregation: `Electronic_Warfare_Effect_Aggregation`{.interpreted-text role="ref"}

::: parsed-literal
\# Defining a new type: electronic_warfare_effect *\<name\>* *\<type-name\>* [debug]() [target_protection_type]() \... [allowed_target_set]() \... [rejected_target_set]() end_electronic_warfare_effect

\# Adding or editing an instance inside an \'electronic_warfare_technique\' or \'technique\' block: \... effect *\<name\>* *\[\<type-name\>\]* [debug]() [target_protection_type]() \... [allowed_target_set]() \... [rejected_target_set]() end_effect \...
:::

-   \<name\> : Name of the Electronic Warfare effect type or instance to be created.
-   \<type-name\> : Name of an existing or predefined Electronic Warfare effect type whose definition will be used as the initial definition of the new type or instance.



An electronic_warfare_effect provides the ability to define the effect(s) associated with a specific `electronic_warfare_technique`{.interpreted-text role="command"}.



::: command
debug

Specifies to use a debug mode to output debug data to the standard output.

Default false or off
:::

::: command
target_protection_type \<target-protection-type\>

Specifies the target protection type to use for an EA effect. Sets the jamming power based on target protection type specified.

\<target-protection-type\> : A string input that defines the target protection type to apply, valid values are listed in the table below:

>   ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
>   \<target-protection-type\>   Description
>   ---------------------------- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
>   \"all_targets\"          Default value. All targets will be allowed and protected against. If this is defined then the rejected_target_list and [allowed_target_set]() if set will be checked also.
>
>   \"self_protect\"         Only the jamming target owning the `_.transmitter`{.interpreted-text role="command"} will be protected by this effect.
>
>   \"non_self_protect\"     Only other targets besides the jamming platform will be protected by this effect.
>   ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Default all_targets
:::

::: note
::: title
Note
:::

Precedence order of target allow/rejection is as follows: [target_protection_type]() -\> rejected_target_list -\> allowed_target_list. The first one to fail (i.e., reject the target and not allow the effect) will cause the next one to not be evaluated.
:::

::: command
allowed_target_set \... end_allowed_target_set
:::

Specifies the allowed target set that an EA effect will be applied to, all other targets will have their jamming power set to 0.0 watts.

::: note
::: title
Note
:::

Precedence order of target allow/rejection is as follows: [target_protection_type]() -\> rejected_target_list -\> allowed_target_list. The first one to fail (i.e., reject the target and not allow the effect) will cause the next one to not be evaluated.
:::

::: command
rejected_target_set \... end_allowed_target_set
:::

Specifies the rejected target set that an EA effect will not be applied to and have their jamming power set to 0.0 watts. All other targets will be allowed.

::: note
::: title
Note
:::

Precedence order of target allow/rejection is as follows: [target_protection_type]() -\> rejected_target_list -\> allowed_target_list. The first one to fail (i.e., reject the target and not allow the effect) will cause the next one to not be evaluated.
:::