# change_attitude.rst.txt.md
orphan

:   



Script Type: `WsfChangeAttitude`{.interpreted-text role="class"}

::: parsed-literal

`event<orbital_event.maneuver>`{.interpreted-text role="ref"} change_attitude

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [orientation]() \...

end_event
:::

Change the `orientation type<attitude_controller.orientation>`{.interpreted-text role="command"} to which the `attitude controller<attitude_controller>`{.interpreted-text role="command"} orients the parent platform.