orphan

:   

# UCI_StrikeCommandStatusMessage

## Overview {#overview .UCI_StrikeCommandStatusMessage .inherits .UCI_Message}

This message holds the status for strike commands.

## Methods

::: method
void CommandUUID()

Returns the UUID of the command.
:::

::: method
bool IsAccepted()

Returns true if the command state equals UCI_ACCEPTED, returns false otherwise.
:::

::: method
bool IsReceived()

Returns true if the command state equals UCI_RECEIVED, returns false otherwise.
:::

::: method
bool IsRejected()

Returns true if the command state equals UCI_REJECTED, returns false otherwise.
:::

::: method
bool IsRemoved()

Returns true if the command state equals UCI_REMOVED, returns false otherwise.
:::
