orphan

:   

# Predefined Launch Computer Types {#Predefined_Launch_Computer_Types}

Base type `launch_computer`{.interpreted-text role="command"}

-   `WSF_AIR_TO_AIR_LAUNCH_COMPUTER`{.interpreted-text role="model"}
-   `WSF_ATG_LAUNCH_COMPUTER`{.interpreted-text role="model"}
-   `WSF_BALLISTIC_LAUNCH_COMPUTER`{.interpreted-text role="model"}
-   `WSF_BALLISTIC_MISSILE_LAUNCH_COMPUTER`{.interpreted-text role="model"}
-   `WSF_ORBITAL_LAUNCH_COMPUTER`{.interpreted-text role="model"}
-   `WSF_FIRES_LAUNCH_COMPUTER`{.interpreted-text role="model"}
-   `WSF_SAM_LAUNCH_COMPUTER`{.interpreted-text role="model"}
-   `WSF_SCRIPT_LAUNCH_COMPUTER`{.interpreted-text role="model"}
