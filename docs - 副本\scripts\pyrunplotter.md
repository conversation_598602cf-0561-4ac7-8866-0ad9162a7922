orphan

:   

# pyrunplotter

pyrunplotter.py is a Python3 script intended to be used for comparing the difference between multiple runs. It is intended to be used in conjunction with `multiresolution_multirun_table`{.interpreted-text role="command"} to compare differences between models used in a multiresolution context. Plots can be generated of platform positions, sensor parameters such as SNR, and simulation level quantities such as platform counts. Several demos (listed below) illustrate its usage.

This script uses `pymystic<docs/pymystic:pymystic>`{.interpreted-text role="ref"} to parse event files generated by Mission and depends on `numpy` and `matplotlib` (\> 3.4.0).

## API Documentation

::: automodule
pyrunplotter
:::

::: {.autoclass members=""}
Analyzer
:::

## demo-plot2dPosition.py

This example script will plot the positions of 4 platforms, as well as their route\'s waypoints, on a 2D graph by their latitude and longitude.

Output will look like this:

![image](../images/2dpositionplot.png)

## demo-plot3dPosition.py

This example will plot the same positions as the example above, but in 3D based on their WCS coordinates.

Output will look like this:

![image](../images/3dpositionplot.png)

## demo-plotAltitude.py

This example will plot the altitude of 2 satellites over time.

Output will look like this:

![image](../images/altitudeplot.png)

## demo-plotAmmoQuantity.py

This example will plot the ammo quantity of 4 different weapons against time.

Output will look like this:

![image](../images/ammoplot.png)

## demo-plotPlatformCounts.py

This example will create a stacked plot of the number of platforms on each side over time. The colors on the graph correspond to the side of the platform.

Output will look like this:

![image](../images/platformcountplot.png)

## demo-compareAltitude.py

This example will show the relative altitudes of the platforms shown in `demo-plotAltitude.py` above.

Output will look like this:

![image](../images/plotcomparealtitude.png)

## demo-compareDistance.py

This example will show the distances between platforms which follow the same route but use different flight models.

Output will look like this:

![image](../images/plotcomparedistance.png)

## demo-compareFuelLevels.py

This example will show the relative fuel levels between the same platforms as the previous example.

Output will look like this:

![image](../images/plotcomparefuel.png)

## demo-compareOpticalSignatureDetection.py

This example will show the difference between detection probabilities for 3 different signatures. This uses one of the signatures as a base and compares the other 2 signatures against the base.

Output will look like this:

![image](../images/plotcomparepd.png)
