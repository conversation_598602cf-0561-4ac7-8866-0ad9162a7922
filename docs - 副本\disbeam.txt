# disbeam.rst.txt.md
orphan

:   



::: DisBeam
::: parsed-literal
`DisBeam.GetDataLength`{.interpreted-text role="method"} `DisBeam.GetBeamId`{.interpreted-text role="method"} `DisBeam.GetIndex`{.interpreted-text role="method"} `DisBeam.GetFrequency`{.interpreted-text role="method"} `DisBeam.GetFrequencyRange`{.interpreted-text role="method"} `DisBeam.GetEffectiveRadiatedPower`{.interpreted-text role="method"} `DisBeam.GetPulseRepetitionFrequency`{.interpreted-text role="method"} `DisBeam.GetPulseWidth`{.interpreted-text role="method"} `DisBeam.GetAzimuthCenter`{.interpreted-text role="method"} `DisBeam.GetAzimuthSweep`{.interpreted-text role="method"} `DisBeam.GetElevationCenter`{.interpreted-text role="method"} `DisBeam.GetElevationSweep`{.interpreted-text role="method"} `DisBeam.GetSweepSync`{.interpreted-text role="method"} `DisBeam.GetFunction`{.interpreted-text role="method"} `DisBeam.GetTrackJamCount`{.interpreted-text role="method"} `DisBeam.GetHighDensityTrackJam`{.interpreted-text role="method"} `DisBeam.GetJammingModeSequence`{.interpreted-text role="method"}
:::
:::



[DisBeam](#disbeam) is an implementation of the DIS Beam.



::: method
int GetDataLength()

Returns the length of the DIS Beam structure.
:::

::: method
int GetBeamId()

Returns the DIS ID associated with the beam.
:::

::: method
int GetIndex()

Returns an index associated with the beam in the DIS Emissions PDU.
:::

::: method
double GetFrequency()

Returns the frequency of the beam.
:::

::: method
double GetFrequencyRange()

Returns the frequency range of the DIS beam.
:::

::: method
double GetEffectiveRadiatedPower()

Returns the effective radiated power of the beam.
:::

::: method
double GetPulseRepetitionFrequency()

Returns the pulse repetition frequency of the beam.
:::

::: method
double GetPulseWidth()

Returns the pulse width of the beam.
:::

::: method
double GetAzimuthCenter()

Returns the center line azimuth of the beam.
:::

::: method
double GetAzimuthSweep()

Returns the sweep angle of the beam along azimuth.
:::

::: method
double GetElevationCenter()

Returns the beam\'s center angle in elevation
:::

::: method
double GetElevationSweep()

Returns the sweep angle of the beam along elevation.
:::

::: method
double GetSweepSync()

Returns the beam\'s sweep synchronization value.
:::

::: method
int GetFunction()

Returns the beam\'s function value.
:::

::: method
int GetTrackJamCount()

Returns the number of tracks or jams occurring on the beam.
:::

::: method
int GetHighDensityTrackJam()

Returns the high density track jam value.
:::

::: method
int GetJammingModeSequence()

Returns the jamming mode sequence value.
:::