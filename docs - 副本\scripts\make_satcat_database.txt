# make_satcat_database.rst.txt.md
orphan

:   



make_satcat_database.py is a python script that converts raw SATCAT satellite definitions to both AFSIM platform definitions and a JSON database to be used in the Wizard\'s Satellite Inserter tool.



make_satcat_database.py \[-h \| \--help\] *\<data\>* *\<new_database\>* *\<new_definitions_file\>*

Where:

\[-h \| \--help\] - An optional command to show help message.

*\<data\>* - The raw SATCAT data. The most up-to-date SATCAT data can be found at [SATCAT](https://www.celestrak.com/pub/satcat.txt).

*\<new_database\>* - The JSON database that will contain the JSON representation of the SATCAT satellites and their file locations.

*\<new_definitions_file\>* - A .txt file that holds the SATCAT satellites\' AFSIM platform definitions.