orphan

:   

# MTT (Multi-Target Tracker) {#MTT}

## Overview {#MTT.Overview}

The Multi-Target Tracker (MTT) is a tracker produced by <PERSON> of Bogdon & Associates. It is the tracker included in mission-level simulation SUPPRESSOR, version 7+. It was recoded in C and integrated into AFSIM.

## Configuration {#MTT.Configuration}

The following is a summary of valid inputs for the MTT, along with default values. If no input is provided for a particular configuration input, the default will be used. Documentation will be added for these inputs as they become available, as there is currently no detailed reference available for the MTT. These block inputs must be placed within a `track_manager.tracker_type`{.interpreted-text role="command"} block of a `track_manager`{.interpreted-text role="command"}, and the `track_manager.tracker_type`{.interpreted-text role="command"} must be set to **mtt**.

    track_drop_times
       embryonic_track               <time-value> (30.0 s)
       candidate_track               <time-value> (30.0 s)
       active_track                  <time-value> (60.0 s)
       vertical_channel_active_track <time-value> (60.0 s)
       report_delay_for_active_track <time-value> ( 0.0 s)
    end_track_drop_times

    process_model_one_sigma_errors_candidate_track
       x_dir_accel <accel-value> (9.0 m/s2)
       y_dir_accel <accel-value> (9.0 m/s2)
    end_process_model_one_sigma_errors_candidate_track

    process_model_one_sigma_errors_and_parameters_active_track
       x_dir_accel_straight_flight_model <accel-value>    (0.09 m/s2)
       y_dir_accel_straight_flight_model <accel-value>    (0.09 m/s2)
       x_dir_accel_turning_flight_model  <accel-value>    ( 8.0 m/s2)
       y_dir_accel_turning_flight_model  <accel-value>    ( 8.0 m/s2)
       vertical_velocity                 <velocity-value> (6.25 m/s )
       decorrelation_time_vert_velocity  <time-value>     (20.0   s )
    end_process_model_one_sigma_errors_and_parameters_active_track

    one_sigma_state_error_thresholds
       velocity_limit_to_promote_embryonic_track  <velocity-value> (700.0 m/s)
       velocity_error_to_promote_embryonic_track  <velocity-value> (150.0 m/s)
       position_error_to_promote_candidate_track  <distance-value> (500.0 m  )
       velocity_error_to_promote_candidate_track  <velocity-value> ( 40.0 m/s)
       position_error_to_promote_vertical_channel <distance-value> (500.0 m  )
       velocity_error_to_promote_vertical_channel <velocity-value> ( 40.0 m/s)
    end_one_sigma_state_error_thresholds

    state_error_covariance_matrix_condition_number_thresholds
       promote_track_in_horizontal_channel <value> (2.0e+4)
       promote_track_in_vertical_channel   <value> (1.0e+10)
    end_state_error_covariance_matrix_condition_number_thresholds

    state_variance_limit_active_track
       max_std_dev_straight_flight_model <distance-value> (800.0 m)
       max_std_dev_turning_flight_model  <distance-value> (800.0 m)
       min_std_dev_straight_flight_model <distance-value> (400.0 m)
       min_std_dev_turning_flight_model  <distance-value> (400.0 m)
    end_state_variance_limit_active_track

    mode_transition_probability_matrix
       straight_to_straight_flight <value> (0.70)
       straight_to_turning_flight  <value> (0.30)
       turning_to_turning_flight   <value> (0.30)
       turning_to_straight_flight  <value> (0.70)
    end_mode_transition_probability_matrix

    probability_of_falsely_rejecting_correlation
       measurement_to_track <value> (1.0e-20)
       track_to_track       <value> (1.0e-20)
    end_probability_of_falsely_rejecting_correlation

    consecutive_single_source_hits_to_promote_track <value> (0)

    track_correlation_cylinder
       cylinder_height   <distance-value> (2000 m)
       cylinder_diameter <distance-value> (2000 m)
    end_track_correlation_cylinder

    mttc_track_fusion <"all_sources" | "initial_source_only"> (all_sources)
