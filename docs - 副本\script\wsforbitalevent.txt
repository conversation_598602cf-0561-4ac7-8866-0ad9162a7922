# wsforbitalevent.rst.txt.md
orphan

:   



::: WsfOrbitalEvent
`WsfOrbitalEvents<WsfOrbitalEvent>`{.interpreted-text role="class"} are be passed to a `WsfSpaceMover`{.interpreted-text role="class"} to perform specific actions, such as changing the orbit of a platform using maneuvering. The event may either be executed directly, using `WsfSpaceMover.ExecuteEvent`{.interpreted-text role="method"}, or as part of a `mission sequence<WsfOrbitalMissionSequence>`{.interpreted-text role="class"}, using `WsfSpaceMover.ExecuteMissionSequence`{.interpreted-text role="method"}. Each `script orbital event<available_orbital_events>`{.interpreted-text role="ref"} type is meant to be configured with a `WsfOrbitalEventCondition`{.interpreted-text role="class"} object, passed in the object\'s *Construct* method.
:::

::: note
::: title
Note
:::

Orbital events are considered to be *finite* (i.e., executing over a finite time), or *impulsive* (i.e., executing instantaneously), and they conditionally execute based on the provided orbital constraint (see `WsfOrbitalEventCondition`{.interpreted-text role="class"}). Finite events execute at the provided update rate in `SetUpdateInterval<WsfOrbitalEvent.SetUpdateInterval>`{.interpreted-text role="method"} (otherwise, at the default update interval of one second), until complete. If a duration is also set using the `SetDuration<WsfOrbitalEvent.SetDuration>`{.interpreted-text role="method"} method, the event will execute for that duration, if possible (depending on the event type).
:::





::: {#orbital_maneuver_type_strings}
:::



::: {#orbital_event_type_strings}
:::



::: method
WsfOrbitalEventCondition Condition()

Returns the condition required for the maneuver to execute.
:::

::: method
string ConditionType()

Returns the event\'s execution condition type string.
:::

::: method
double Duration()

Returns the duration of a maneuver.
:::

::: method
string EventType()
:::

::: method
string ManeuverType()

Returns the type of event.
:::

::: method
bool IsComplete()

Returns true if the maneuver has completed, otherwise false.
:::

::: method
bool IsFinite()

Returns true if the maneuver is finite, otherwise false.
:::

::: method
void SetRelativeTime(double aRelativeTime)

Sets the amount of time until the maneuver executes when the condition is `WsfOrbitalEventCondition.AT_RELATIVE_TIME`{.interpreted-text role="method"}.
:::

::: method
double Radius()

Returns the final radius of the maneuver when the co
:::

::: method
double RelativeTime()

Returns the relative amount of time for the maneuver to execute when the condition is `WsfOrbitalEventCondition.AT_RELATIVE_TIME`{.interpreted-text role="method"}.
:::

::: method
void SetCondition(WsfOrbitalEventCondition aCondition)

Sets the condition required for the maneuver to execute.
:::

::: method
void SetDuration(double aDuration)

Sets the nominal duration for which the event will execute. If the duration is greater than zero, the event will be considered *finite*. This method will do nothing if the provided duration is less than 0.
:::

::: method
void SetFinite(bool aIsFinite)

Sets whether the event is to be considered finite.
:::

::: note
::: title
Note
:::

By default, orbital events are impulsive (not finite).
:::

::: method
void SetUpdateInterval(double aUpdateInterval)

Sets the update interval for the event at which *finite* events are executed.
:::

::: note
::: title
Note
:::

The default update interval is one second.
:::

::: method
double TimeToConstraint()

Returns the amount of time until the event executes (i.e., its execution condition is satisfied).
:::