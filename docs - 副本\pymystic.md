orphan

:   

::: {.automodule members="" special-members="__init__"}
pymystic
:::

# example_message_list.py

This example script will output a list of times and message types, and when an entity state message is encountered it will also write the name of the relevant platform.

Output will look like this:

    28797.0 MsgEntityState platformA
    28798.0 MsgEntityState platformB
    28798.0 MsgEntityState platformA
    28799.0 MsgEntityState platformB
    28800.001953125 :  MsgPlatformStatus
    28800.001953125 :  MsgPlatformStatus

# example_graph.py

This example requires graphviz and the graphviz python modules. It will query command-chain relationships from the MsgPlatformInfo messages and use them to build the entire scenario\'s command-chain graph. It will then render this graph into a PDF.

Output will look like this:

![image](images/pymystic_graph.png)

# example_bigreport.py

This example will read from a local `aerlist.txt` file containing a list of AER filenames (users will need to generate this). It will read the execution data message from each file and report the version of AFSIM that is was executed in, and the time-stamp of the execution.

Output will look like this:

    TESTING  /AFSIM/test_scenarios/scen1/scen1.aer
       ***unversioned***
    TESTING  /AFSIM/training/developer/labs/solution/comm/data/comm_exercise.aer
       version:   mission 2.4.0.190626
       executed:  2019-06-28 09:09:48
    TESTING  /AFSIM/training/developer/labs/solution/comm/data/comm_exercise2.aer
       version:   mission 2.4.0.190626
       executed:  2019-06-28 09:09:52
    TESTING  /AFSIM/training/user/basic/scenarios/solutions/11/Floridistan/output/jacksonabad.aer
       version:   Warlock 2.5.0.191029
       executed:  2019-10-31 11:50:59

# example_print_message_data.py

The purpose of this example is to demonstrate recursive processing of messages represented by nested dictionaries.

The script takes an AER file as a parameter, and reads each message from the input file, printing the data from each message to the console. The `FormatMessage` function recursively formats messages containing nested dictionaries.

Output will look like this:

    id:                                                1
    msgtype:                                           MsgEntityState
    simTime:                                           0.0
    simIndex:                                          0
    state:
          platformIndex:                             38
          damageFactor:                              0.0
          locationWCS:
                   x:                                 -2356075.8866955335
                   y:                                 -3743698.25337908
                   z:                                 4579620.857159804
          velocityWCSValid:                          False
          accelerationWCSValid:                      False
          orientationWCSValid:                       True
          orientationWCS:
                   x:                                 1.0090789794921875
                   y:                                 -0.7646905779838562
                   z:                                 3.1415927410125732
          fuelCurrentValid:                          True
          fuelCurrent:                               0.0
