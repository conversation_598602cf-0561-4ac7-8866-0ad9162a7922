# wk_wsfprompt.rst.txt.md
orphan

:   



An AFSIM script can create customizable dialogs within Warlock to display to the user by using the `WsfPrompt`{.interpreted-text role="class"} Display methods. The user can configure button names and the scripts each button executes, as well as the title and message displayed in the dialog. If desired, the prompt can pause the simulation until the operator makes a selection.

The methods in the `WsfPrompt`{.interpreted-text role="class"} script class may be invoked as follows:

    WsfPrompt.Display("title", "message", {"script_1", "script_2", ... , "script_n"}, {"button_1", "button_2", ... , "button_n"});

If the user does not want a button to execute a script, an empty string should be used for the script name. Only scripts with no arguments are supported.

The image below shows a simple use case.

