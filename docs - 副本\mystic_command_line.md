orphan

:   

# Command Line - Mystic

Mystic may optionally take an `AER file<event_pipe>`{.interpreted-text role="command"} to load at start-up.

  Command Line Options   
  ---------------------- ---------------------------------------------------------------------------------------------
  -?, -h, -help          Display command line `options<mystic_help>`{.interpreted-text role="doc"} and quit
  -cf \<filename\>       Use the specified configuration file, modifications will be saved to specified file
  -icf \<filename\>      Imports the specified configuration file, modifications will not be saved to specified file
  -console               Enables output to the console window
