orphan

:   

# route_network

::: {.command block=""}
route_network \... end_route_network
:::

::: parsed-literal

route_network *\<route-network-name\>*

:   \# Repeat as required to specify routes in network `route`{.interpreted-text role="command"} \... end_route

    \# Repeat as required to specify routes in network [use_route]() *\<route-name\>*

    \# Test to solve the shortest path for routes in network [test]()

    \# Test to solve the shortest path between 2 nodes [test_nodes]() *\<from-node-id\>* *\<to-node-id\>*

    \# Generate debugging output [verbose]()

end_route_network
:::

## Overview

A [route_network](#route_network) is typically used to represent a network of roads, airways, or waterways. It is generally used by objects such as `air_traffic`{.interpreted-text role="command"}, `road_traffic`{.interpreted-text role="command"}, and `sea_traffic`{.interpreted-text role="command"} to define the paths on which the platforms navigate or move.

::: note
::: title
Note
:::

A `route`{.interpreted-text role="command"} must have at least two waypoints defined to be added to the [route_network](#route_network).
:::

## Commands

::: command
use_route \<route-name\>

Include the route type with the specified name as part of the route definition. Repeat as required to specify the route network.
:::

::: command
test

During initialization, *test* all possible paths between each `route.node_id`{.interpreted-text role="command"} in all `route`{.interpreted-text role="command"}\'s to solve the shortest path. A count of *Nodes* in the [route_network](#route_network) tested is output to the console. See [Example 1 - route_network with test command](#example-1---route_network-with-test-command).

::: note
::: title
Note
:::

A *Warning* is provided in the output when the shortest path cannot be solved between each start and end node path tested. See [Example 2 - route_network with test command - cannot solve the shortest path](#example-2---route_network-with-test-command---cannot-solve-the-shortest-path).
:::

::: tip
::: title
Tip
:::

Use [verbose]() to generate additional debugging data when using the [test]() command.
:::
:::

::: command
test_nodes \<from-node-id\> \<to-node-id\>

During initialization, test all routes to solve the shortest path between *\<from-node-id\>* and *\<to-node-id\>* (specified as integers).

The following data is output to the console:

-   From: \<from-node-id\>
-   To: \<to-node-id\>
-   Cost: (computed cost value of the tested path)
-   Path: (list of `route.node_id`{.interpreted-text role="command"}\'s included in the tested path)

See [Example 3 - route_network with test_nodes command](#example-3---route_network-with-test_nodes-command).

::: note
::: title
Note
:::

When the path cannot be solved, *Cost: = -1* and *Path:* is empty.
:::
:::

::: command
verbose

Generates additional debug data when used with the [test]() command. The following data is output to the console for each path tested:

-   From: \<from-node-id\>
-   To: \<to-node-id\>
-   Cost: (computed cost value of the tested path)
-   Path: (list of `route.node_id`{.interpreted-text role="command"}\'s included in the tested path)

See [Example 4 - route_network with test and verbose commands](#example-4---route_network-with-test-and-verbose-commands).

::: note
::: title
Note
:::

The shortest path cannot be solved when a disconnection is detected in the route(s) to the target node. When the shortest path cannot be solved, a *Warning* is provided, *Cost: = -1* and *Path: = No path could be found.* See [Example 5 - test command with verbose - cannot solve the shortest path](#example-5---test-command-with-verbose---cannot-solve-the-shortest-path).
:::
:::

## Examples

### Example 1 - route_network with test command

### Example 2 - route_network with test command - cannot solve the shortest path

### Example 3 - route_network with test_nodes command

### Example 4 - route_network with test and verbose commands

### Example 5 - test command with verbose - cannot solve the shortest path

