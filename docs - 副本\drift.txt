# drift.rst.txt.md
orphan

:   



Script Type: `WsfDriftManeuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} drift

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [drift_rate]() \... [delta_time]() \... [maximum_delta_time]() \... [maximum_delta_v]() \... [optimize_time]() \... [optimize_delta_v]() \... [optimize_cost]() \... [tolerance]() \...

end_maneuver
:::

A drift maneuver will transfer the executing platform from an initial circular orbit to a final circular orbit with a relative drift rate. Because these two orbits do not intersect, the transfer is made via an intermediate orbit similar to those in targeting-class maneuvers (e.g., `rendezvous`{.interpreted-text role="doc"}). As such, this maneuver also has options for specifying details of the transfer, allowing the user to set time or delta-V related constraints, and to specify optimization for time taken, or delta-V cost.

::: note
::: title
Note
:::

Only the constraints `relative_time`{.interpreted-text role="command"}, `ascending_node`{.interpreted-text role="command"}, `descending_node`{.interpreted-text role="command"}, `eclipse_entry`{.interpreted-text role="command"} or `eclipse_exit`{.interpreted-text role="command"} are supported for this maneuver.
:::

::: command
drift_rate \<angle-rate-value\>

Specify the rate of drift between the initial and final orbits.
:::