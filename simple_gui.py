#!/usr/bin/env python3
"""
简单的AFSIM Platform Agent GUI界面
支持脚本解释、参数查询和脚本生成
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.enhanced_platform_agent import SimplePlatformAgent


class AFSIMAgentGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("AFSIM Platform Agent - 简化版")
        self.root.geometry("1000x700")
        
        # 初始化Agent
        self.agent = SimplePlatformAgent()
        self.agent_initialized = False
        
        self.setup_ui()
        self.init_agent()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="AFSIM Platform Agent", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 左侧功能面板
        left_frame = ttk.LabelFrame(main_frame, text="功能选择", padding="5")
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 功能按钮
        ttk.Button(left_frame, text="脚本解释", 
                  command=self.show_script_explanation).pack(fill=tk.X, pady=2)
        ttk.Button(left_frame, text="参数查询", 
                  command=self.show_parameter_query).pack(fill=tk.X, pady=2)
        ttk.Button(left_frame, text="飞机信息", 
                  command=self.show_aircraft_info).pack(fill=tk.X, pady=2)
        ttk.Button(left_frame, text="脚本生成", 
                  command=self.show_script_generation).pack(fill=tk.X, pady=2)
        
        ttk.Separator(left_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # 可用飞机列表
        ttk.Label(left_frame, text="可用飞机:").pack(anchor=tk.W)
        aircraft_frame = ttk.Frame(left_frame)
        aircraft_frame.pack(fill=tk.X, pady=5)
        
        self.aircraft_listbox = tk.Listbox(aircraft_frame, height=6)
        self.aircraft_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        aircraft_scroll = ttk.Scrollbar(aircraft_frame, orient=tk.VERTICAL, 
                                       command=self.aircraft_listbox.yview)
        aircraft_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        self.aircraft_listbox.config(yscrollcommand=aircraft_scroll.set)
        
        # 状态信息
        ttk.Separator(left_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        ttk.Button(left_frame, text="查看状态", 
                  command=self.show_status).pack(fill=tk.X, pady=2)
        ttk.Button(left_frame, text="清空历史", 
                  command=self.clear_history).pack(fill=tk.X, pady=2)
        
        # 右侧主要工作区
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # 输入区域
        input_frame = ttk.LabelFrame(right_frame, text="输入", padding="5")
        input_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        
        self.input_text = scrolledtext.ScrolledText(input_frame, height=8, wrap=tk.WORD)
        self.input_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 输入按钮
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="发送", 
                  command=self.process_input).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空输入", 
                  command=self.clear_input).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="示例脚本", 
                  command=self.load_example_script).pack(side=tk.LEFT)
        
        # 输出区域
        output_frame = ttk.LabelFrame(right_frame, text="输出", padding="5")
        output_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
        
        self.output_text = scrolledtext.ScrolledText(output_frame, wrap=tk.WORD, state=tk.DISABLED)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("正在初始化...")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def init_agent(self):
        """在后台线程中初始化Agent"""
        def init_worker():
            try:
                if self.agent.initialize():
                    self.agent_initialized = True
                    self.status_var.set("Agent初始化成功 - 就绪")
                    self.update_aircraft_list()
                    self.append_output("🎯 AFSIM Platform Agent 已就绪！\n\n")
                    self.append_output("💡 您可以：\n")
                    self.append_output("• 输入AFSIM脚本进行解释\n")
                    self.append_output("• 查询飞机参数（如：F-18参数）\n")
                    self.append_output("• 生成飞机脚本（如：生成F-18脚本）\n")
                    self.append_output("• 询问平台相关问题\n\n")
                else:
                    self.status_var.set("Agent初始化失败")
                    self.append_output("❌ Agent初始化失败\n")
            except Exception as e:
                self.status_var.set(f"初始化错误: {str(e)}")
                self.append_output(f"❌ 初始化错误: {str(e)}\n")
        
        threading.Thread(target=init_worker, daemon=True).start()
    
    def update_aircraft_list(self):
        """更新飞机列表"""
        if self.agent_initialized:
            aircraft_list = self.agent.get_available_aircraft()
            self.aircraft_listbox.delete(0, tk.END)
            for aircraft in aircraft_list:
                self.aircraft_listbox.insert(tk.END, aircraft)
    
    def append_output(self, text):
        """添加输出文本"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, text)
        self.output_text.see(tk.END)
        self.output_text.config(state=tk.DISABLED)
    
    def clear_output(self):
        """清空输出"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
    
    def clear_input(self):
        """清空输入"""
        self.input_text.delete(1.0, tk.END)
    
    def process_input(self):
        """处理用户输入"""
        if not self.agent_initialized:
            messagebox.showwarning("警告", "Agent尚未初始化完成，请稍候")
            return
        
        user_input = self.input_text.get(1.0, tk.END).strip()
        if not user_input:
            return
        
        self.append_output(f"🤖 用户: {user_input}\n\n")
        self.status_var.set("处理中...")
        
        def process_worker():
            try:
                response = self.agent.chat(user_input)
                self.append_output(f"🤖 助手: {response}\n\n")
                self.append_output("-" * 50 + "\n\n")
                self.status_var.set("处理完成")
            except Exception as e:
                self.append_output(f"❌ 处理错误: {str(e)}\n\n")
                self.status_var.set("处理失败")
        
        threading.Thread(target=process_worker, daemon=True).start()
    
    def load_example_script(self):
        """加载示例脚本"""
        example_script = """platform BBB WSF_PLATFORM 
     side blue
     icon f-15
     add mover WSF_FORMATION_FLYER 
          position 34:31:34.58n 113:28:09.47e altitude 2000 m
          speed 100 m/s
          lead_aircraft leader
          offset_forward_from_lead 1 au
     end_mover  
     position 34:29:24.66n 113:26:46.51e altitude 2000 m
end_platform"""
        
        self.input_text.delete(1.0, tk.END)
        self.input_text.insert(1.0, example_script)
    
    def show_script_explanation(self):
        """显示脚本解释功能说明"""
        self.clear_output()
        self.append_output("📝 脚本解释功能\n\n")
        self.append_output("在输入框中粘贴AFSIM平台脚本，我将为您详细解释每个参数的含义。\n\n")
        self.append_output("点击'示例脚本'按钮可以加载一个示例进行测试。\n\n")
    
    def show_parameter_query(self):
        """显示参数查询功能说明"""
        self.clear_output()
        self.append_output("🔍 参数查询功能\n\n")
        self.append_output("您可以询问AFSIM平台的各种参数，例如：\n")
        self.append_output("• 'altitude参数是什么意思？'\n")
        self.append_output("• 'platform命令有哪些参数？'\n")
        self.append_output("• 'mover组件如何配置？'\n\n")
    
    def show_aircraft_info(self):
        """显示飞机信息功能说明"""
        self.clear_output()
        self.append_output("✈️ 飞机信息功能\n\n")
        self.append_output("查询军用飞机的详细技术参数，例如：\n")
        self.append_output("• 'F-18参数'\n")
        self.append_output("• 'F-22信息'\n")
        self.append_output("• 'F-35技术参数'\n\n")
        self.append_output("左侧列表显示了所有可用的飞机型号。\n\n")
    
    def show_script_generation(self):
        """显示脚本生成功能说明"""
        self.clear_output()
        self.append_output("🚀 脚本生成功能\n\n")
        self.append_output("基于真实飞机数据生成AFSIM平台脚本，例如：\n")
        self.append_output("• '生成F-18脚本'\n")
        self.append_output("• '生成F-22脚本'\n")
        self.append_output("• '生成F-35脚本'\n\n")
        self.append_output("生成的脚本包含真实的技术参数和详细说明。\n\n")
    
    def show_status(self):
        """显示Agent状态"""
        if not self.agent_initialized:
            messagebox.showinfo("状态", "Agent尚未初始化完成")
            return
        
        status = self.agent.get_status()
        status_text = f"""Agent状态信息:

初始化状态: {'✅' if status['initialized'] else '❌'}
对话轮数: {status['conversation_turns']}
可用飞机: {len(status['available_aircraft'])} 种
支持功能: {', '.join(status['features'])}

基础Agent状态:
索引文件数: {status['base_agent_status'].get('indexed_files', 0)}
向量数据库: {'✅' if status['base_agent_status'].get('initialized', False) else '❌'}
"""
        messagebox.showinfo("Agent状态", status_text)
    
    def clear_history(self):
        """清空对话历史"""
        if self.agent_initialized:
            self.agent.clear_history()
            self.clear_output()
            self.append_output("🧹 对话历史已清空\n\n")
            self.status_var.set("历史已清空")
        else:
            messagebox.showwarning("警告", "Agent尚未初始化")


def main():
    """主函数"""
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        messagebox.showerror("错误", "请设置DEEPSEEK_API_KEY环境变量")
        return
    
    root = tk.Tk()
    app = AFSIMAgentGUI(root)
    root.mainloop()


if __name__ == '__main__':
    main()
