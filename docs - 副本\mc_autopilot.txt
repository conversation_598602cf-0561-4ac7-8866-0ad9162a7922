# mc_autopilot.rst.txt.md
orphan

:   



::: note
::: title
Note
:::

This page is disabled for vehicles with a cNO_CONTROL configuration.
:::

::: note
::: title
Note
:::

This page is disabled if only a Guided Mover is being generated.
:::

Mover Creator along with <PERSON><PERSON> give users the ability to setup and tune the control PIDs of the vehicle\'s piloting systems. There are five sections to the *Autopilot* page.

-   [PID Auto-Tuning Options](#pid-auto-tuning-options)
-   [Genetic Algorithm Options](#genetic-algorithm-options)
-   [Pilots/Guidance](#pilotsguidance) control setup/tuning
-   [Hardware Autopilot](#hardware-autopilot) control setup/tuning
-   [G-Load Limits](#g-load-limits) options
-   3D rendering of the vehicle





The *PID Auto-Tuning Options* section of the Autopilot page allows users to define the minimum and maximum boundaries for Kp, Ki and Kd. It also provides the user the ability to set the command run time, error and overshoot weights. All of these user defined settings are copied into Warlock and used by the `P6DOF Tuner<wkf_plugin/wk_p6dof_tuner>`{.interpreted-text role="doc"} and `<PERSON><PERSON><PERSON><PERSON><wkf_plugin/wk_six_dof_tuner>`{.interpreted-text role="doc"} plugins. The PIDs available to tune are:

-   Alpha\*
-   Vertical Speed
-   Pitch Angle
-   Pitch Rate
-   Flight Path Angle
-   Delta Pitch
-   Altitude
-   Beta\*
-   Yaw Rate
-   Yaw Heading
-   Roll Rate\*
-   Delta Roll
-   Bank Angle
-   Roll Heading
-   Forward Acceleration
-   Speed
-   Taxi Forward Acceleration\*
-   Taxi Speed\*
-   Taxi Heading\*
-   Taxi Yaw Rate\*

\*Not available for PM6DOF vehicles.





The user can set the values used by the Tuner plugins\' genetic algorithm. The genetic algorithm is used to help find the optimal values for the PIDs.





Once the user is satisfied with his or her values setup in the *PID Auto-Tuning Options* and *Genetic Algorithm Options* sections, they need to run the actual Tuner to get a tuned pilot. The list of pilot/guidance types to be tuned appears to the left of the *Setup/Tune Controls* button. The available pilots/guidance list was defined by the user on the `Pilots/Controls page <mc_pilots_controls>`{.interpreted-text role="doc"}. To proceed with tuning the PIDs the user presses the *Setup/Tune Controls* button found in the Pilots/Guidance and Hardware Autopilot sections.



Once the user presses the *Setup/Tune Controls* button Warlock will be launched and present the user with either the P6DOF Tuner of the SixDOF Tuner, depending on which vehicle type is selected. For helpful tips on tuning a vehicle, visit the \"Best Practices\" section in the `P6DOF Tuner<wkf_plugin/wk_p6dof_tuner>`{.interpreted-text role="doc"} documentation.

Mover Creator uses the P6DOF Tuner settings to configure tuning for both P6DOF and SixDOF vehicles. (Eventually, this will be replaced by the SixDOF Tuner settings as P6DOF is phased out of support.) The user can verify that his or her PID settings are being used in Warlock by selecting *Options -\> Preferences* and selecting *P6DOF Tuner* in the preferences dialog.

Once the user has tuned the PIDs, he or she needs to press the *Save And Exit* button in Warlock. This will save the values for the tuned PIDs of this vehicle. The autopilot configuration, along with the tuned PID values that were saved from Warlock can be found in the *autopilot_config.txt* script file located in the *\\resources\\data\\mover_creator\\AFSIM_Scripts\\\<vehicle_name\>\\p6dof_types\\\<vehicle_type\>\\\<vehicle_name\>\\controls\\* directory where \<vehicle_name\> is the name the user gave the vehicle, and \<vehicle_type\> is either *aircraft* or *weapons*.





The user can copy these tuned PID values to the *Hardware Autopilot* by pressing the *Copy Data to Autopilot (Below)* button in this section. This will copy the values from *autopilot_config.txt* to the *hw_autopilot_config.txt* file located in the directory mentioned above.



The user can copy these tuned PID values to the other *Pilots/Guidance* by pressing the *Copy Data to Pilot (Above)* button in this section. This will copy the values from *hw_autopilot_config.txt* to the *autopilot_config.txt* file located in the directory mentioned above.



::: note
::: title
Note
:::

These settings may also be set in the `Pilots/Controls<mc_pilots_controls>`{.interpreted-text role="doc"} page.
:::



This section allows the user to specify the g-load limits. Once either the \"Pilots/Guidance\" or \"Hardware Autopilot\" button is pressed, a dialog will appear where the user can enter the desired values as shown below.



Editing these values is equivalent to changing `pitch_gload_min`{.interpreted-text role="command"}, `pitch_gload_max`{.interpreted-text role="command"}, and `yaw_gload_max`{.interpreted-text role="command"}, respectively.



From this page the user can navigate to one of the following pages via the tabs or by clicking the *Next* or *Previous* buttons.

-   `Start/Setup<mc_users_guide>`{.interpreted-text role="doc"}
-   `Pilots/Controls<mc_pilots_controls>`{.interpreted-text role="doc"}
-   `Geometry<mc_geometry>`{.interpreted-text role="doc"}
-   `Aerodynamics<mc_aerodynamics>`{.interpreted-text role="doc"}
-   `Performance<mc_performance>`{.interpreted-text role="doc"}
-   `Flight Test<mc_flight_test>`{.interpreted-text role="doc"}