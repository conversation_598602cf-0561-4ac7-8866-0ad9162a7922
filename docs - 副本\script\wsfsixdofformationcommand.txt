# wsfsixdofformationcommand.rst.txt.md
orphan

:   



::: WsfSixDOF_FormationCommand
Commands are the means by which a formation can be made to take actions. For a command to be executed it must be assigned to a formation. Commands can be constrained to execute only once a `WsfSixDOF_FormationCommandConstraint`{.interpreted-text role="class"} is satisfied. When executed by a `WsfSixDOF_Formation`{.interpreted-text role="class"}, a command may be assigned to that formation causing it to perform some action, it may be transformed and forwarded to the sub-formations of that formation, or both.
:::



::: method
bool IsInstantaneous()

Return if the command is an instantaneous command. Such commands only need to be executed once before they complete.
:::

::: method
bool IsSequence()

Return if the command is a sequence of commands. See `WsfSixDOF_FormationCommandSequence`{.interpreted-text role="class"}.
:::

::: method
bool IsAssigned()

Return if the command is assigned to a formation.
:::

::: method
bool IsPending()

Return if the command is pending. A pending command has been assigned, but has not yet satisfied its constraint.
:::

::: method
bool IsExecuting()

Return if the command is executing. An executing command has satisfied its constraint, but has yet to finish the action of the command itself.
:::

::: method
bool IsCompleted()

Return if the command is complete. Once a command has executed all of its work, it becomes complete. Once a command reaches this status it will no longer be updated.
:::

::: method
bool IsCanceling()

Return if the command is in the process of being canceled.
:::

::: method
bool IsCanceled()

Return if the command has been canceled. Once the command reaches this status it will no longer be updated.
:::

::: method
bool IsDropped()

Return if the command has been dropped. A dropped command differs from a canceled command in that a dropped command will be completely removed from the simulation, while a canceled command remains in the simulation with the canceled status.
:::

::: method
bool IsRunning()

Return if the command is still running. A running command might be pending, executing, or canceling.
:::

::: method
bool GetDebug()

Return if extra debugging output has been enabled on this command.
:::

::: method
void SetDebug(bool aEnableDebugOutput)

Turn on or off extra debugging output for this command.
:::

::: method
WsfSixDOF_Formation GetFormation()

Return the formation to which this command has been assigned. This can return an invalid object if this command is unassigned.
:::

::: method
void Cancel()

Cancel an incomplete command.
:::

::: method
WsfSixDOF_FormationCommandConstraint GetConstraint()

Return the constraint on execution of this command. This can return an invalid object if there is no constraint on the command.
:::

::: method
void SetConstraint(WsfSixDOF_FormationCommandConstraint aConstraint)

Set the constraint on execution for this command.
:::



`WsfSixDOF_FormationAddSubCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationAttachCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationChangeLeadSubCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationChangeOffsetCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationCommandSequence`{.interpreted-text role="class"}

`WsfSixDOF_FormationDetachCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationDisbandCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationManeuverLimitsCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationRemoveSubCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationRenameCommand`{.interpreted-text role="class"}

`WsfSixDOF_FormationTurnToHeadingCommand`{.interpreted-text role="class"}