orphan

:   

# iff_mapping

::: {.command block=""}
iff_mapping \... end_iff_mapping

::: parsed-literal

[iff_mapping](#iff_mapping)

:   

    [side]() *\<side-name\_\>* \... *\<side-name\>* \| \[default\]

    :   [reports]() \... [for_side]() \... [reports]() \... [for_category]() \... [reports]() \... [by_default]()

    end_side

end_iff_mapping
:::
:::

## Overview

Provides the capability to define IFF reporting behavior for all entities in the simulation.

## Commands

::: command
side \<side-name\> \... \<side-name\> \| \[default\]

One or more sides for which the IFF mapping rules apply. If **default** is specified then the IFF mapping rules apply to all sides not explicitly defined.
:::

::: command
side-name

The \'side\' (\'team\' or \'affiliation\') for which the IFF mapping applies. Refers to blue, red, country or team name.
:::

::: command
reports \[friend \| foe \| neutral\]

Indicates the IFF status to report for the given side, category, or by default.
:::

::: command
for_side \<side-name\>

Indicates the IFF mapping between sides.
:::

::: command
for_category \<category-name\>

Indicates the IFF mapping between platforms from a given side and platforms from a given category.
:::

::: command
by_default

Indicates the IFF mapping between platforms form a given side and all sides and categories not explicitly mapped.
:::

An example of IFF mapping entries - Sides \'blue\', \'green\', and all other sides in the simulation except \'white\' reports side \'red\' as foe, any platforms in \'myCategory\' as friend, and all other sides and categories as neutral. Side \'white\' reports side \'red\' as friend and with all other sides and categories as foe.:

    iff_mapping
      side blue green default
         reports foe     for_side     red
         reports friend  for_category myCategory
         reports neutral by_default
      end_side
      side white
         reports friend for_side red
      end_side
    end_iff_mapping
