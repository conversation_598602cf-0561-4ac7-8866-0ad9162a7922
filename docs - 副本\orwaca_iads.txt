# orwaca_iads.rst.txt.md
orphan

:   

::: demo
orwaca_iads
:::

| UNCLASSIFIED
| Any resemblance or approximations to actual weapon system capabilities
| in this demo are purely accidental. The intent of this DEMONSTRATION is
| to illustrate how to setup an integrated air defense system (IADS).

| There are two start up files.
| The startup file \"orwaca_northern_area.txt\" only has the sams in the states of Washington and Oregon.
| The startup file \"orwaca_full_area.txt\" has the additional SAMs located in the state of California

| You must use the new icons that have \"\_icon\" in the name or all of the platforms will look like stop signs when
| looking at output files in Mystic.

| The \"orwaca_full_iads_coverage.JPG\" image file (located in jpg subdirectory) shows the geographical locations
| of the sam sites.
| The extremely long range sams (ELR_SAMS) are 175 nautical mile missiles (RED rings)
| The long range sams (LR_SAMS) are 100 nautical mile missiles (ORANGE rings)
| The medium range sams (MR_SAMS) are 50 nautical mile missiles (GREEN rings)
| The SR_SAMS are 10 nautical mile missiles (VIOLET rings) (max alt of 15kft) (they don\'t do so well past 7 nautical miles so can be defeated at longer kinematic ranges)
| The SR_SAMS (telars) are self commanded and do not do cooperative engagements like the longer range sams.

| The TBM shooting at Denver can be adjusted to shoot at any time during the simulation run.
| This can be accomplished by adjusting the times in platforms/tbm_launcher_vehicle.txt (for every tbm launcher) OR
| adjusting the platform variables in the scenario file \"tbm_route_launch.txt\".
| This is just an example to show some scripting capability. The pause time in the route has to be adjusted accordingly.