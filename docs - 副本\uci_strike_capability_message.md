orphan

:   

# UCI_StrikeCapabilityMessage

## Overview {#overview .UCI_StrikeCapabilityMessage .inherits .UCI_Message}

This message gives the current capabilities (weapons) of the owning platform.

## Methods

::: method
UCI_StrikeCapability Capability(int capabilityIndex)

Returns the capability at the indexed value.
:::

::: method
int Size()

Returns the number of capabilities available in the message.
:::
