orphan

:   

# UCI_StrikeCapabilityStatusMessage

## Overview {#overview .UCI_StrikeCapabilityStatusMessage .inherits .UCI_Message}

This message gives the status of each of the capabilities (weapons) for a platform.

## Methods

::: method
UCI_CapabilityStatus CapabilityStatus(int aIndex)

Returns the capability status at aIndex.
:::

::: method
int Size()

Returns the size of the CapabilityStatus.
:::
