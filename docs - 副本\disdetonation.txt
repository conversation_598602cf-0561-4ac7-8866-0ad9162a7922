# disdetonation.rst.txt.md
orphan

:   



::: {.DisDetonation .inherits .DisPdu}
::: parsed-literal
`DisDetonation.FiringEntity`{.interpreted-text role="method"} `DisDetonation.TargetEntity`{.interpreted-text role="method"} `DisDetonation.WeaponEntity`{.interpreted-text role="method"} `DisDetonation.Event`{.interpreted-text role="method"} `DisDetonation.Velocity`{.interpreted-text role="method"} `DisDetonation.Location`{.interpreted-text role="method"} `DisDetonation.WeaponType`{.interpreted-text role="method"} `DisDetonation.Warhead`{.interpreted-text role="method"} `DisDetonation.Fuse`{.interpreted-text role="method"} `DisDetonation.Quantity`{.interpreted-text role="method"} `DisDetonation.Rate`{.interpreted-text role="method"} `DisDetonation.DetonationResult`{.interpreted-text role="method"} `DisDetonation.ImpactLocation`{.interpreted-text role="method"}
:::
:::



[DisDetonation](#disdetonation) is an implementation of the DIS detonation PDU. Detonation PDUs are used to indicate that a munition has detonated.



::: method
DisEntityId FiringEntity()

Returns the ID of the platform that fired the munition.
:::

::: method
DisEntityId TargetEntity()

Returns the ID of the platform that is fired upon.
:::

::: method
DisEntityId WeaponEntity()

Returns the ID of the platform representing the munition.
:::

::: method
DisEventId Event()

Returns the event ID. The event ID on the detonation PDU should be the same as one on the `DisFire`{.interpreted-text role="class"}.
:::

::: method
Array\<double\> Velocity()

Returns the velocity of the munition at the time of detonation. The velocity is in meters per second with respect to earth centric coordinates.
:::

::: method
Array\<double\> Location()

Returns the location of the munition at the time of detonation. The location is in earth centered meters.
:::

::: method
DisEntityType WeaponType()

Return the type of munition.
:::

::: method
int Warhead()

Returns the warhead ID.
:::

::: method
int Fuse()

Returns the fuse ID.
:::

::: method
int Quantity()

Returns the quantity of munitions.
:::

::: method
int Rate()

Returns the rate of detonation.
:::

::: method
int DetonationResult()

Returns the predicted detonation result ID.
:::

::: method
Array\<double\> ImpactLocation()

Returns the location of impact in the target\'s body coordinate system.
:::