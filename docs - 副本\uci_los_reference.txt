# uci_los_reference.rst.txt.md
orphan

:   





This type indicates the reference types for the given azimuth and elevation.



::: method
static UCI_LOS_Reference BODY()

Returns an instance of the [UCI_LOS_Reference](#uci_los_reference) with the BODY value. Cues the part to the specified azimuth and elevation relative to the uncued orientation of the part.
:::

::: method
static UCI_LOS_Reference INERTIAL()

Returns an instance of the [UCI_LOS_Reference](#uci_los_reference) with the INERTIAL value. The part will cue to the specified azimuth and elevation relative to the horizontal plane (no pitch or roll) of the host platform.
:::