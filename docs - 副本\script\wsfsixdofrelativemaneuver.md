orphan

:   

# WsfSixDOF_RelativeManeuver

## Overview {#overview .WsfSixDOF_RelativeManeuver .inherits .WsfSixDOF_Maneuver}

A relative maneuver is any maneuver that will control the executing platform in a way that refers to another platform in the simulation. Like other kinds of maneuvers, a relative maneuver supports entry constraints so the start of a relative maneuver can be made dependent on any available condition provided by `WsfManeuverConstraint`{.interpreted-text role="class"}. Unlike other kinds of maneuvers, a relative maneuver does not support exit constraints. Instead, a relative maneuver continues to execute, trying to achieve the desired relationship with respect to the target platform. In general the target platform will be changing its state, and so the controls enacted by this maneuver must continually update so that the executing platform can achieve its goal. To end a relative maneuver, the maneuver must be explicitly canceled, or replaced by another maneuver.

Users can control the aggressiveness of the relative maneuver by setting limits on the g-load and speed that the relative maneuver will command. These limits are distinct from those defined in the autopilot for the `WsfSixDOF_Mover`{.interpreted-text role="class"} that is executing this maneuver.

## Methods

::: method
double GetG_LoadMax()

Return the maximum g-load that this maneuver will command for the executing platform. The default value of this limit is effectively infinite.
:::

::: method
void SetG_LoadMax(double aG_LoadMax)

Set the maximum g-load that this maneuver will command for the executing platform.
:::

::: method
double GetG_LoadMin()

Return the minimum g-load that this maneuver will command for the executing platform. The default value of this limit is effectively negative infinity.
:::

::: method
void SetG_LoadMin(double aG_LoadMin)

Set the minimum g-load that this maneuver will command for the executing platform.
:::

::: method
double GetSpeedKTAS_Max()

Return the maximum speed (as knots true airspeed) that this maneuver will command for the executing platform. The default value of this limit is effectively infinite.
:::

::: method
void SetSpeedKTAS_Max(double aSpeedKTAS_Max)

Set the maximum speed (as knots true airspeed) that this maneuver will command for the executing platform.
:::

::: method
double GetSpeedKTAS_Min()

Return the minimum speed (as knots true airspeed) that this maneuver will command for the executing platform. The default value of this limit is zero.
:::

::: method
void SetSpeedKTAS_Min(double aSpeedKTAS_Min)

Set the minimum speed (as knots true airspeed) that this maneuver will command for the executing platform.
:::

::: method
double GetSpeedMpsMax()

Return the maximum speed (in meters per second) that this maneuver will command for the executing platform. The default value of this limit is effectively infinite.
:::

::: method
void SetSpeedMpsMax(double aSpeedMpsMax)

Set the maximum speed (in meters per second) that this maneuver will command for the executing platform.
:::

::: method
double GetSpeedMpsMin()

Return the minimum speed (in meters per second) that this maneuver will command for the executing platform. The default value of this limit is zero.
:::

::: method
void SetSpeedMpsMin(double aSpeedMpsMin)

Set the minimum speed (in meters per second) that this maneuver will command for the executing platform.
:::

## Available Relative Maneuvers

The following relative maneuvers are available:

-   `WsfSixDOF_PursueManeuver`{.interpreted-text role="class"}
