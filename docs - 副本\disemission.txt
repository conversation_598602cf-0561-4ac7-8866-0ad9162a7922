# disemission.rst.txt.md
orphan

:   



::: {.DisEmission .inherits .DisPdu}
::: parsed-literal
`DisEmission.EmitterId`{.interpreted-text role="method"} `DisEmission.EventId`{.interpreted-text role="method"} `DisEmission.StateUpdate`{.interpreted-text role="method"} `DisEmission.SystemCount`{.interpreted-text role="method"} `DisEmission.FirstSystem`{.interpreted-text role="method"} `DisEmission.NextSystem`{.interpreted-text role="method"}
:::
:::



[DisEmission](#disemission) is an implementation of the DIS emission PDU. Emission PDUs are used to communicate information about radio emissions from a platform.



::: method
DisEntityId EmitterId()

Returns the ID of the subject platform.
:::

::: method
DisEventId EventId()

Returns the ID associated with the emission event.
:::

::: method
int StateUpdate()

Returns the state update value.
:::

::: method
int SystemCount()

Returns the number of `DisSystem`{.interpreted-text role="class"} associated with the PDU.
:::

::: method
DisSystem FirstSystem()

Returns the first system.
:::

::: method
DisSystem NextSystem()

Returns the system after the one previously returned by FirstSystem or NextSystem.
:::