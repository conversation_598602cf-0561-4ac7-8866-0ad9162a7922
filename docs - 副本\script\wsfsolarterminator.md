orphan

:   

# WsfSolarTerminator

::: WsfSolarTerminator
`WsfSolarTerminator`{.interpreted-text role="class"} provides a number of utilities to query locations and platforms as to their position relative to the Solar terminator.
:::

## Static Methods

These methods are all static and may be invoked as follows:

    double twilight = WsfSolarTerminator.TWILIGHT_CIVIL();
    bool isDay = WsfSolarTerminator.GetPhaseOfDay(lat, lon, calendar, twilight) == WsfSolarTerminator.PHASE_DAY();

::: method
static int GetPhaseOfDay(double aLatDeg, double aLonDeg, Calendar aTime, double aLimitDeg)

Return the phase of the day (day, night, twilight) for the given point on the Earth\'s surface at the given time. The definition of twilight extends from 90 deg 50\' away from the direction to the Sun out to the angle given as the fourth argument. The return value of this method will be equal to one of the following: `WsfSolarTerminator.PHASE_DAY`{.interpreted-text role="method"}, `WsfSolarTerminator.PHASE_TWILIGHT`{.interpreted-text role="method"} or `WsfSolarTerminator.PHASE_NIGHT`{.interpreted-text role="method"}.
:::

::: method
static int PHASE_DAY()

Return the integer value used to represent day in return values from `WsfSolarTerminator.GetPhaseOfDay`{.interpreted-text role="method"}.
:::

::: method
static int PHASE_TWILIGHT()

Return the integer value used to represent twilight in return values from `WsfSolarTerminator.GetPhaseOfDay`{.interpreted-text role="method"}.
:::

::: method
static int PHASE_NIGHT()

Return the integer value used to represent night in return values from `WsfSolarTerminator.GetPhaseOfDay`{.interpreted-text role="method"}.
:::

::: method
static double TWILIGHT_CIVIL()

Return the angle limit in degrees of civil twilight (96 deg). This is for use with `WsfSolarTerminator.GetPhaseOfDay`{.interpreted-text role="method"}.
:::

::: method
static double TWILIGHT_NAUTICAL()

Return the angle limit in degrees of nautical twilight (102 deg). This is for use with `WsfSolarTerminator.GetPhaseOfDay`{.interpreted-text role="method"}.
:::

::: method
static double TWILIGHT_ASTRONOMICAL()

Return the angle limit in degrees of astronomical twilight (108 deg). This is for use with `WsfSolarTerminator.GetPhaseOfDay`{.interpreted-text role="method"}.
:::

::: method
static int PlatformSolarIllumination(WsfPlatform aPlatform)

Return a qualitative assessment of how much solar illumination the given platform is receiving. The possible return values are: 0 if the platform is not part of a simulation; equal to `WsfSolarTerminator.ILLUMINATED`{.interpreted-text role="method"} if the platform is in full sunlight; equal to `WsfSolarTerminator.EARTH_PENUMBRA`{.interpreted-text role="method"} if the sun is partially hidden behind the horizon; or `WsfSolarTerminator.EARTH_UMBRA`{.interpreted-text role="method"} if the sun is completely hidden behind the horizon.

::: note
::: title
Note
:::

This method does not account for atmospheric refraction or terrain.
:::
:::

::: method
static int ILLUMINATED()

Return the integer value used to represent full illumination in return values from `WsfSolarTerminator.PlatformSolarIllumination`{.interpreted-text role="method"}.
:::

::: method
static int EARTH_PENUMBRA()

Return the integer value used to represent the platform being in Earth\'s penumbra in return values from `WsfSolarTerminator.PlatformSolarIllumination`{.interpreted-text role="method"}.
:::

::: method
static int EARTH_UMBRA()

Return the integer value used to represent the platform being in Earth\'s umbra in return values from `WsfSolarTerminator.PlatformSolarIllumination`{.interpreted-text role="method"}.
:::
