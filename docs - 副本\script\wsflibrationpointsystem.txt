# wsflibrationpointsystem.rst.txt.md
orphan

:   





The `WsfLibrationPointSystem`{.interpreted-text role="class"} represents the available primary-secondary object pairs supported by the `WsfLibrationPoint`{.interpreted-text role="class"}.



::: method
WsfLibrationPointSystem EARTH_MOON()

Return a `WsfLibrationPointSystem`{.interpreted-text role="class"} instance representing the Earth-Moon system.
:::

::: method
WsfLibrationPointSystem SUN_EARTH()

Return a `WsfLibrationPointSystem`{.interpreted-text role="class"} instance representing the Sun-Earth system.
:::

::: method
WsfLibrationPointSystem SUN_JUPITER()

Return a `WsfLibrationPointSystem`{.interpreted-text role="class"} instance representing the Sun-Jupiter system.
:::