# wsfp6dof_mover.rst.txt.md
orphan

:   



::: {.WsfP6DOF_Mover .inherits .WsfMover arrow=""}
Input type: `WsfP6DOF_Mover`{.interpreted-text role="class"}
:::

::: {.contents local=""}
:::



::: method
double GetAltitude()

Returns the current altitude (meters above sea level).
:::

::: method
double GetHeading()

Returns the current heading in degrees.
:::

::: method
double GetPitch()

Returns the current pitch angle in degrees.
:::

::: method
double GetRoll()

Returns the current roll angle in degrees.
:::

::: method
double GetVerticalSpeed()

Returns the current vertical speed (m/sec).
:::

::: method
double GetFlightPathAngle()

Returns the current flight path angle in degrees.
:::

::: method
double GetYawRate()

Returns the current body yaw rate in rad/sec.
:::

::: method
double GetPitchRate()

Returns the current body pitch rate in rad/sec.
:::

::: method
double GetRollRate()

Returns the current body roll rate in rad/sec.
:::

::: method
double GetGLoad()

Returns the current g-load (in gees) for the vehicle.
:::

::: method
double GetNx()

Returns the current acceleration (in body coordinates) in the forward direction (in gees).
:::

::: method
double GetNy()

Returns the current acceleration (in body coordinates) in the right-side direction (in gees).
:::

::: method
double GetNz()

Returns the current acceleration (in body coordinates) in the downward direction (in gees).
:::

::: method
double GetKCAS()

Returns the current airspeed in knots calibrated airspeed.
:::

::: method
double GetKIAS()

Returns the current airspeed in knots indicated airspeed.
:::

::: method
double GetKTAS()

Returns the current airspeed in knots true airspeed.
:::

::: method
double GetMach()

Returns the current airspeed in Mach.
:::

::: method
double GetDynamicPressure()

Returns the current dynamic pressure (lbs/ft\^2).
:::



::: method
double GetTotalWeight()

Returns the total weight/mass (kg) for the vehicle, including fuel weight.
:::

::: method
double GetCurrentWeight()

Returns the current weight/mass (kg) for the vehicle, including fuel weight.
:::

::: method
double GetEmptyWeight()

Returns the empty weight/mass (kg) for the vehicle (no fuel or weapons).
:::

::: method
double GetCgX()

Returns the x component of the current center of gravity (meters) relative to the reference point of the vehicle.
:::

::: method
double GetCgY()

Returns the y component of the current center of gravity (meters) relative to the reference point of the vehicle.
:::

::: method
double GetCgZ()

Returns the z component of the current center of gravity (meters) relative to the reference point of the vehicle.
:::



::: method
void StartupEngines(double aSimtime)

Start up the engines at aSimtime (in seconds). If aSimtime is less than the current sim time, the engines will be started immediately.
:::

::: method
void ShutdownEngines(double aSimtime)

Shutdown the engines at aSimtime_sec (in seconds). If aSimtime is less than the current sim time, the engines will be shutdown immediately.
:::

::: method
bool IsProducingThrust()

Returns true if the P6DOF model is producing thrust.
:::

::: method
double GetTotalThrust()

Returns the current thrust (Newtons) of all engines on the vehicle. It does not include subobjects.
:::

::: method
double GetEngineThrust(string aEngineName)

Returns the current thrust (Newtons) of for the specified engine.
:::

::: method
bool GetAfterburnerOn()

Returns true if an afterburner is on.
:::

::: method
double GetEngineFuelFlowRate(string aEngineName)

Returns the current total fuel flow (kg/sec) for the specified engine.
:::

::: method
bool GetEngineAfterburnerOn(string aEngineName)

Returns true if the afterburner is on for the specified engine.
:::

::: method
bool SetFuelFeed(string aEngineName, string aFuelTankName)

Sets the feed tank from which the specified engine will draw fuel. Returns true if the selection is valid.
:::

::: method
bool SetFuelFeedAllEngines(string aFuelTankName)

Sets the feed tank from which all engines will draw fuel. Returns true if the selection is valid.
:::



::: method
void AddFuel(double aFuelMass)

Add fuel (in kg) to fuel tanks defined in the p6dof_mover definition. This will split the fuel evenly among all fuel tanks, and it is limited by the fuel capacity of each tank. For more information on fuel and fuel tanks for P6DOF models, see `P6DOF_Propulsion_System_Label`{.interpreted-text role="ref"}
:::

::: method
double GetTotalFuelCapacity()

Returns the total fuel capacity (kg) in all fuel tanks.
:::

::: method
double GetInternalFuelCapacity()

Returns the total fuel capacity (kg) in the internal fuel tanks.
:::

::: method
double GetExternalFuelCapacity()

Returns the total fuel capacity (kg) in all external fuel tanks.
:::

::: method
double GetTotalFuelRemaining()

Returns the total fuel remaining (kg) in all fuel tanks.
:::

::: method
double GetInternalFuelRemaining()

Returns the fuel remaining (kg) in the internal fuel tanks.
:::

::: method
double GetExternalFuelRemaining()

Returns the total fuel remaining (kg) in the external fuel tanks.
:::

::: method
double GetFuelTankCapacity(string aTankName)

Returns the fuel capacity (kg) of the tank with the specified name. If no tank with the name exists, it returns 0.
:::

::: method
double GetFuelInTank(string aTankName)

Returns the current amount of fuel (kg) in the tank with the specified name. If no tank with the name exists, it returns 0.
:::

::: method
void SetFuelInTank(string aTankName, double aFuelMass)

Sets the amount of fuel present in the fuel tank named aTankName, to aFuelMass (in kg). If a fuel tank by the name aTankName is not found, this function does nothing. If the fuel quantity is greater than the capacity for the fuel tank, it is limited to the capacity of the fuel tank.
:::

::: method
double GetTotalFuelFlowRate()

Returns the current total fuel flow (kg/sec) of all engines on the vehicle. It does not include subobjects.
:::

::: method
bool AddFuelTransfer(string aFuelTransferName, string aSourceTankName, string aTargetTankName)

Adds a new fuel transfer between the source tank and the target tank using the specified fuel transfer name, which must be unique on this platform. Returns true if the transfer is created.
:::

::: method
bool RemoveFuelTransfer(string aFuelTransferName)

Removes the specified fuel transfer (by name). Returns true if the transfer is removed.
:::

::: method
void SetJokerFuelState(double aFuelMass)

Sets the Joker fuel state in kg.
:::

::: method
double GetJokerFuelState()

Returns the Joker fuel state in kg.
:::

::: method
bool GetJokerFuelReached()

Returns true if the Joker fuel state has been reached (total fuel is less than Joker fuel state).
:::

::: method
void SetBingoFuelState(double aFuelMass)

Sets the Bingo fuel state in kg.
:::

::: method
double GetBingoFuelState()

Returns the Bingo fuel state in kg.
:::

::: method
bool GetBingoFuelReached()

Returns true if the Bingo fuel state has been reached (total fuel is less than Bingo fuel state).
:::



::: method
void RetractLandingGear()

Retract the landing gear. P6DOF includes the drag from extended landing gear.
:::

::: method
void LowerLandingGear()

Extend the landing gear.
:::

::: method
double GetLandingGearPosition()

Returns the angle (deg) of the landing gear. Zero is retracted and 90 deg is full down and locked. The angle is the average of all gear (left main gear, right main gear, and nose gear).
:::

::: method
bool WeightOnWheels()

Returns true if any landing gear is compressed due to weight being on it. This can be used to detect when an aircraft is airborne (when taking off) or when an aircraft has touched down (when landing).
:::

::: method
bool WeightOnNoseWheel()

Returns true if the nose landing gear is compressed due to weight being on the nose gear.
:::

::: method
void SetEnableNWS(bool aEnable)

Enable or disable nose wheel steering (NWS). When NWS is active, the nose wheel will be able to rotate through a larger angle than when NWS is inactive, allowing a tighter turn radius. Nose-wheel steering is often used when taxiing, but should typically be removed prior to the takeoff roll and before landing.
:::

::: method
double GetNoseWheelAngle()

Returns the angle (deg) of the nose wheel steering. Zero is centered, positive is to the right and negative is to the left.
:::



These functions allow script control of ground operations. In most cases, EnableDirectBraking should be called before calling these functions. When there is no further need for these functions, ReleaseDirectBraking should be called (unless the parking brake is set, in which case EnableDirectBraking should remain active, to hold the brakes).

::: method
void EnableDirectBraking()

This must be called to allow direct control of wheel brakes, overriding any autopilot control of brakes. If no further direct control of wheel brakes is required, ReleaseDirectBraking should be called.
:::

::: method
void SetParkingBrake()

Set the \"parking brake\", which applies full braking to both wheel brakes of the landing gear of the P6DOF mover.
:::

::: method
void ApplyLeftGearBrake(double aNormalizedBrakingValue)

Apply the specified braking level to the left wheel brake on the landing gear of the P6DOF mover. Values range from 0.0 (no braking) to 1.0 (full braking).
:::

::: method
void ApplyRightGearBrake(double aNormalizedBrakingValue)

Apply the specified braking level to the right wheel brake on the landing gear of the P6DOF mover. Values range from 0.0 (no braking) to 1.0 (full braking).
:::

::: method
void ReleaseWheelBrakes()

Release all wheel brakes.
:::

::: method
void ReleaseDirectBraking()

This disables direct control of wheel brakes, restoring control to the autopilot. It essentially \"cancels\" the EnableDirectBraking function.
:::



The functions provide support to apply an external force to the vehicle or to remove such a force.

::: method
void ApplyExternalForce(double aMagnitude, double aAngle)

This method applies a force with magnitude equal to aMagnitude (in lbs) with an angle of aAngle (deg) to the nose wheel of the model. This method is only intended to be used on ground operations and is useful to simulate pushing back from the gate or towing an aircraft. It can also be used to simulate a catapult on an aircraft carrier. An angle of 0 deg represents a force being pushed against the nose wheel such that the plane will back up, while a force at an angle of 180 will make the aircraft roll forward. If the model does not have landing gear, this function will not do anything. The force is applied until `WsfP6DOF_Mover.RemoveExternalForce`{.interpreted-text role="method"} is called.
:::

::: method
void RemoveExternalForce()

Removes any external force being applied to the P6DOF mover. For more information, see `WsfP6DOF_Mover.ApplyExternalForce`{.interpreted-text role="method"}
:::



::: method
bool ActivateSequencer(string aSequencerName)

Activate the sequencer specified by aSequencerName. If the sequencer is not found, it will return false. For more information on sequencers, see `P6DOF_Sequencers_Label`{.interpreted-text role="ref"}
:::



::: method
WsfManeuver GetCurrentManeuver()

This will return the current maneuver. This could be either a maneuver that is executing, or one that is waiting for either its entry or exit constraint to become satisfied. If there is no current maneuver, then this will return an invalid object.
:::

::: method
WsfManeuverSequence GetManeuverSequence()

This will return the maneuver sequence that this mover is executing. If there is no maneuver sequence being executed, this will return an invalid object.
:::

::: method
void ExecuteManeuver(WsfManeuver aManeuver)

This will execute the provided maneuver. If this mover is currently executing a maneuver, then that maneuver will be canceled and the given maneuver will be executed instead.
:::

::: method
void ExecuteManeuverSequence(WsfManeuverSequence aSequence)

This will execute the given maneuver sequence. If this mover is currently executing a maneuver sequence, then that sequence will be canceled, and the provided sequence will be executed instead.
:::

::: method
void CancelManeuvers()

This will cancel any currently executing maneuvers.
:::



P6DOF autopilots typically utilize three \"channels\" for control \-- vertical, lateral, and speed channels. Each channel is independent, but the state of the air vehicle can effect all three channels and each channel may influence the other channels. Recognizing that these three channels are at the heart of autopilot functions can help users better understand how autopilot commands might interact between channels. Autopilot control methods are presented below.

::: method
void EnableAutopilot()

This will \"enable/activate\" the autopilot. An autopilot must be enabled in order to function.
:::

::: method
void SetAutopilotAltitude(double aAltitude)

This will command the autopilot to climb/dive to the specified altitude (in feet). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotVerticalSpeed(double aVerticalSpeed)

This will command the autopilot to attain and hold the vertical speed (in ft/min). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotPitchAngle(double aPitchAngle)

This will command the autopilot to attain and hold the specified pitch angle (in degrees). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotFlightPathAngle(double aFlightPathAngle)

This will command the autopilot to climb/dive at the specified flight path angle (in degrees). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotPitchRate(double aPitchRate)

This will command the autopilot to attain and hold the specified pitch rate (in deg/sec). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotDeltaPitch(double aDeltaPitchAngle)

This will command the autopilot to pitch up (positive) or down (negative) by the specified angle (in degrees).
:::

::: method
void SetPitchGLoad(double aGLoad)

This will command the autopilot to attain and hold the specified pitch g-load (in gees). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotRollAngle(double aRollAngle)

This will command the autopilot to attain and hold the specified roll/bank angle (in degrees). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotRollRate(double aRollRate)

This will command the autopilot to attain and hold the specified roll rate (in deg/sec). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotDeltaRoll(double aDeltaRollAngle)

This will command the autopilot to roll right (positive) or left (negative) by the specified angle (in degrees).
:::

::: method
void SetAutopilotSpeedKCAS(double aSpeed_KCAS)

This will command the autopilot to attain and hold the specified speed in knots calibrated airspeed (KCAS, which is similar to knots indicated airspeed, KIAS). KCAS and KIAS are effected by altitude (and air density). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotSpeedKIAS(double aSpeed_KIAS)

This will command the autopilot to attain and hold the specified speed in knots indicated airspeed (KIAS, which is similar to knots calibrated airspeed, KCAS). KCAS and KIAS are effected by altitude (and air density). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotSpeedKTAS(double aSpeed_KTAS)

This will command the autopilot to attain and hold the specified speed in knots true airspeed (KTAS). This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetAutopilotSpeedMach(double aSpeed_Mach)

This will command the autopilot to attain and hold the specified speed in Mach. This will not directly effect what the other autopilot channels are doing.
:::

::: method
void SetTaxiMode(bool aEnable)

Enable or disable \"taxi mode\". Taxi mode should be enabled when an aircraft is operating on the ground so that the autopilot can utilize the appropriate controls (rudder instead of stick right, for example) when performing ground operations.
:::

::: method
void SetTaxiRadius()

Set the minimum radius for turns when in taxi mode.
:::

::: method
void SetAutopilotLateralWaypointMode()

This will command the autopilot to set the lateral channel to waypoint mode.
:::

::: method
void SetAutopilotVerticalWaypointMode()

This will command the autopilot to set the vertical channel to waypoint mode.
:::

::: method
void SetAutopilotSpeedWaypointMode()

This will command the autopilot to set the speed channel to waypoint mode.
:::

::: method
void SetAutopilotWaypointMode()

This will command the autopilot to set all three channels to waypoint mode.
:::

::: method
void SetAutopilotNoControl()

This will command the autopilot to \"zero\" all controls, which will center the stick and rudder and pull back throttle(s) to zero (idle). It is similar to `WsfP6DOF_Mover.EnableControls`{.interpreted-text role="method"} but is commanding the autopilot, rather than the controls themselves.
:::



::: method
string GetActivePilot()

Returns the active pilot as a string (i.e. \"Synthetic\").
:::

::: method
void ActivateManualPilot()

This will activate the manual pilot. If no manual pilot exists, there will be no change.
:::

::: method
void ActivateSyntheticPilot()

This will activate the synthetic pilot. If no synthetic pilot exists, there will be no change.
:::

::: method
void ActivateHardwareAutopilot()

This will activate the hardware autopilot. If no hardware autopilot exists, there will be no change.
:::

::: method
void ActivateGuidanceAutopilot()

This will activate the guidance autopilot. If no guidance autopilot exists, there will be no change.
:::



::: method
string GetAutopilotLateralMode()

Returns the autopilot\'s lateral mode as a string (i.e. \"Waypoint\").
:::

::: method
double GetAutopilotLateralModeValue()

Returns the value of the autopilot\'s current lateral mode.
:::

::: method
string GetAutopilotVerticalMode()

Returns the autopilot\'s vertical mode as a string (i.e. \"Waypoint\").
:::

::: method
double GetAutopilotVerticalModeValue()

Returns the value of the autopilot\'s current vertical mode.
:::

::: method
string GetAutopilotSpeedMode()

Returns the autopilot\'s speed mode as a string (i.e. \"Waypoint\").
:::

::: method
double GetAutopilotSpeedModeValue()

Returns the value of the autopilot\'s current speed mode.
:::



::: method
double GetPitchGLoadMin()

Returns the minimum pitch g-load.
:::

::: method
double GetPitchGLoadMax()

Returns the maximum pitch g-load.
:::

::: method
double GetAlphaMin()

Returns the minimum angle of attack (alpha) in degrees.
:::

::: method
double GetAlphaMax()

Returns the maximum angle of attack (alpha) in degrees.
:::

::: method
double GetPitchRateMin()

Returns the minimum pitch rate (deg/s).
:::

::: method
double GetPitchRateMax()

Returns the maximum pitch rate (deg/s).
:::

::: method
double GetVerticalSpeedMin()

Returns the minimum vertical speed (ft/min).
:::

::: method
double GetVerticalSpeedMax()

Returns the maximum vertical speed (ft/min).
:::

::: method
double GetYawGLoadMax()

Returns the maximum yaw g-load.
:::

::: method
double GetBetaMax()

Returns the maximum sideslip (beta) in degrees.
:::

::: method
double GetYawRateMax()

Returns the maximum yaw rate (deg/s).
:::

::: method
double GetRollRateMax()

Returns the maximum roll rate (deg/s).
:::

::: method
double GetBankAngleMax()

Returns the maximum bank angle in degrees.
:::

::: method
double GetForwardAccelMin()

Returns the minimum forward acceleration (in g\'s).
:::

::: method
double GetForwardAccelMax()

Returns the maximum forward acceleration (in g\'s).
:::

::: method
double GetTaxiSpeedMax()

Returns the maximum taxi speed in (ft/sec).
:::

::: method
double GetTaxiYawRateMax()

Returns the maximum yaw rate (deg/s) when in taxi mode.
:::



These functions set various limits on autopilot control. This allows an autopilot to operate in a more aggressive manner (with increased limits) or a slower/calmer manner (with reduced limits).

::: method
void SetPitchGLoadMin(double aGees)

Sets the minimum pitch g-load.
:::

::: method
void SetPitchGLoadMax(double aGees)

Sets the maximum pitch g-load.
:::

::: method
void SetAlphaMin(double aAngle)

Sets the minimum angle of attack (alpha) in degrees.
:::

::: method
void SetAlphaMax(double aAngle)

Sets the maximum angle of attack (alpha) in degrees.
:::

::: method
void SetPitchRateMin(double aRate)

Sets the minimum pitch rate (deg/s).
:::

::: method
void SetPitchRateMax(double aRate)

Sets the maximum pitch rate (deg/s).
:::

::: method
void SetVerticalSpeedMin(double aSpeed)

Sets the minimum vertical speed (ft/min).
:::

::: method
void SetVerticalSpeedMax(double aSpeed)

Sets the maximum vertical speed (ft/min).
:::

::: method
void SetYawGLoadMax(double aGees)

Sets the maximum yaw g-load.
:::

::: method
void SetBetaMax(double aAngle)

Sets the maximum sideslip (beta) in degrees.
:::

::: method
void SetYawRateMax(double aRate)

Sets the maximum yaw rate (deg/s).
:::

::: method
void SetRollRateMax(double aRate)

Sets the maximum roll rate (deg/s).
:::

::: method
void SetBankAngleMax(double aAngle)

Sets the maximum bank angle in degrees.
:::

::: method
void SetForwardAccelMin(double aGees)

Sets the minimum forward acceleration (in g\'s).
:::

::: method
void SetForwardAccelMax(double aGees)

Sets the maximum forward acceleration (in g\'s).
:::

::: method
void SetTaxiSpeedMax(double aSpeed)

This will limit the autopilot to the specified maximum taxi speed in (ft/sec).
:::

::: method
void SetTaxiYawRateMax(double aRate)

Sets the maximum yaw rate (deg/s) when in taxi mode.
:::

::: method
void SetTurnRollInMultiplier(double aValue)

Sets the turn roll-in multiplier value. This can shorten (less than than 1) or extend (greater than 1) the time/distance used when turning when following a route (waypoints).
:::

::: method
double GetCurrentTurnRollInMultiplier()

Returns the current/active turn roll-in multiplier value.
:::

::: method
double GetDefaultTurnRollInMultiplier()

Returns the default/nominal turn roll-in multiplier value.
:::

::: method
void SetRouteAllowableAngleError(double aAngle)

Sets the allowable heading error (in radians) when following routes.
:::

::: method
double GetCurrentRouteAllowableAngleError()

Returns the current/active route allowable heading error (in radians).
:::

::: method
double GetDefaultRouteAllowableAngleError()

Returns the default/nominal route allowable heading error (in radians).
:::



This allows any temporary autopilot limits to revert back to nominal/default values.

::: method
void RevertLimitsToDefaults()

Revert all autopilot \"limit\" values back to the value they were at the start of scenario.
:::



::: method
bool GetEngineIsOperating()

Returns true if at least one engine is operating.
:::

::: method
bool GetEngineIsSmoking()

Returns true if at least one engine is smoking in some manner.
:::

::: method
bool GetEngineAfterburnerIsOn()

Returns true if at least one engine has a visible afterburner plume.
:::

::: method
bool GetContrailTrailingEffect()

Returns true if the platform is producing visible contrails.
:::

::: method
bool GetRocketSmokeTrailingEffect()

Returns true if the platform is producing a rocket smoke trail.
:::

::: method
bool GetDamageSmokeTrailingEffect()

Returns true if the platform is producing a damage smoke trail.
:::

::: method
bool GetLaunchFlashSmokeIsPresent()

Returns true if there is launch smoke on/near the platform.
:::

::: method
bool GetRisingSmokePlumeIsPresent()

Returns true if there is rising smoke coming from the platform.
:::

::: method
bool GetFlamesArePresent()

Returns true if there is are flames on/near the platform.
:::

::: method
bool GetIsLightlyDamaged()

Returns true if the platform is lightly damaged.
:::

::: method
bool GetIsHeavilyDamaged()

Returns true if the platform is heavily damaged.
:::



::: method
void SetLightDamage()

This sets a light damage level on the mover.
:::

::: method
void SetHeavyDamage()

This sets a heavy damage level on the mover.
:::

::: method
void SetDestroyed()

This will make the vehicle fall out of control. It allows a realistic crash motion rather than simply removing a dead vehicle. When destroyed, the vehicle will fall ballistically (using a zero pitch g-load), roll inverted, cut power to idle, and extend speedbrakes (if available) to slow the vehicle as it crashes.
:::



::: method
void EnableThrustVectoring(bool aEnable)

Controls whether thrust vectoring is on or off. Thrust vectoring is off by default. If thrust vectoring is not supported on the P6DOF model, this function does nothing.
:::

::: method
void SetThrustReverserPosition(double aPosition)

Sets the thrust reverser position. Valid values for aPosition are \[0,1\] \-- 0 is normal (non-reverse) thrust and 1 is full reverse thrust. If there are no thrust reversers on the P6DOF model, this function does nothing.
:::

::: method
void EnableControls(bool aEnable)

This enables/disables control inputs from any source (autopilot, external manual pilot, P6Net interface, etc.). Controls are enabled by default, so this command is typically used to disable controls (using false). This is often used to allow a weapon to drop ballistically and without control inputs as it is released from the carrying aircraft and then calling EnableControls(true) to establish control input after the weapon is safely clear of the aircraft.
:::

::: method
void TakeDirectControlInput()

This must be called before using SetDirectControlInputs. If no further direct control is required, ReleaseDirectControlInput should be called.
:::

::: method
void ReleaseDirectControlInput()

This should be called if no further direct control is required. It essentially \"cancels\" the TakeDirectControlInput function.
:::

::: method
void SetDirectControlInputs(double aStickBack, double aStickRight, double aRudderRight, double aThrottles)

This sets the controls for the control stick (aStickBack and aStickRight), rudder pedals (aRudderRight) and throttle levers (aThrottles). Control stick and rudder pedal values range from -1 (full left or forward) to zero (neutral) to +1 (full right or back). Throttle values range from 0 (idle) to 1 (non-augmented, military power) to 2 (full afterburner). This function requires that TakeDirectControlInput be called prior to use.
:::

::: method
void EnableDirectThrottleInput()

This allows direct throttle input, overriding any autopilot control of the throttle.
:::

::: method
void MoveThrottleToIdle()

Sets and holds the throttle to idle power. EnableDirectThrottleInput must be called to allow this control.
:::

::: method
void MoveThrottleToFull()

Sets and holds the throttle to military (MIL) power. EnableDirectThrottleInput must be called to allow this control.
:::

::: method
void MoveThrottleToMilitary()

Sets and holds the throttle to military (MIL) power. EnableDirectThrottleInput must be called to allow this control.
:::

::: method
void MoveThrottleToAfterburner()

Sets and holds the throttle to full afterburner (AB) power. EnableDirectThrottleInput must be called to allow this control.
:::

::: method
void ReleaseDirectThrottleInput()

This releases direct throttle input, re-establishing autopilot control of the throttle.
:::

::: method
void EnableDirectSpeedBrakeInput()

This allows direct speed brake input, overriding any autopilot control of the speed brake.
:::

::: method
void OpenSpeedBrake()

Sets the speed brake to the fully open position. EnableDirectSpeedBrakeInput must be called to allow this control.
:::

::: method
void CloseSpeedBrake()

Sets the speed brake to the fully closed position. EnableDirectSpeedBrakeInput must be called to allow this control.
:::

::: method
void ReleaseDirectSpeedBrakeInput()

This releases direct speed brake input, re-establishing autopilot control of the speed brake.
:::

::: method
double GetSpeedBrakePosition()

Returns the angle (deg) of the speed brake(s). Zero is retracted and a positive value is extended.
:::

::: method
void SetFlapsPosition(double aPosition)

Sets the flaps position. Valid values for aPosition are \[0,1\] \-- 0 is retracted and 1 is fully extended. If there are no flaps on the P6DOF model, this function does nothing.
:::

::: method
double GetFlapsPosition()

Returns the angle (deg) of the flaps. A positive value is trailing edge down.
:::

::: method
void SetSpoilersPosition(double aPosition)

Sets the spoilers position. Valid values for aPosition are \[0,1\] \-- 0 is retracted and 1 is fully extended. If there are no spoilers on the P6DOF model, this function does nothing.
:::

::: method
double GetSpoilersPosition()

Returns the angle (deg) of the spoilers. Zero is retracted and a positive value is extended.
:::

These provide the flight control inputs.

::: method
double GetStickRightPosition()

Returns the current stick right control input position (between 0 and 1).
:::

::: method
double GetStickBackPosition()

Returns the current stick back control input position (between 0 and 1).
:::

::: method
double GetRudderRightPosition()

Returns the current rudder right control input position (between 0 and 1).
:::

::: method
double GetThrottleMilitaryPosition()

Returns the current military throttle control input position (between 0 and 1).
:::

::: method
double GetThrottleAfterburnerPosition()

Returns the current afterburner throttle control input position (between 0 and 1).
:::

::: method
double GetThrustReverserPosition()

Returns the current thrust reverser control input position (between 0 and 1).
:::

::: method
double GetSpeedBrakeControlPosition()

Returns the current speed brake control input position (between 0 and 1).
:::

::: method
double GetFlapsLeverPosition()

Returns the current position of the flaps lever (between 0 and 1).
:::

::: method
double GetSpoilersLeverPosition()

Returns the current position of the spoilers lever (between 0 and 1).
:::

::: method
double GetLandingGearLeverPosition()

Returns the current position of the landing gear lever (between 0 and 1).
:::



::: method
double GetAngleOfControlSurface(string aControlSurfaceName)

Returns the current angle (deg) of the specified control surface.
:::

::: method
Array\<string\> GetListOfControlSurfaceNames()

Returns a list of control surface names for the vehicle.
:::

::: method
double GetAileronsBothPosition()

Returns the angle (deg) of the ailerons. A positive value is trailing edge up on the right aileron and trailing edge down on the left aileron.
:::

::: method
double GetStabilatorLeftPosition()

Returns the angle (deg) of the left stabilator. A positive value is trailing edge up.
:::

::: method
double GetStabilatorRightPosition()

Returns the angle (deg) of the left stabilator. A positive value is trailing edge up.
:::

::: method
double GetRudderPosition()

Returns the angle (deg) of the rudder(s). A positive value is trailing edge right.
:::



::: method
double GetLift()

Returns the current lift force in Newtons.
:::

::: method
double GetDrag()

Returns the current drag force in Newtons.
:::

::: method
double GetSideForce()

Returns the current side force in Newtons.
:::

::: method
double GetThrustMagnitude()

Returns the magnitude of the total thrust in Newtons.
:::

::: method
double GetYawMoment()

Returns the current yaw moment in Newton\*meters.
:::

::: method
double GetPitchMoment()

Returns the current pitch moment in Newton\*meters.
:::

::: method
double GetRollMoment()

Returns the current roll moment in Newton\*meters.
:::



::: method
double GetAlpha()

Returns the current angle of attack in degrees.
:::

::: method
double GetAlphaDot()

Returns the current angle of attack rate in degrees/sec.
:::

::: method
double GetBeta()

Returns the current angle of sideslip in degrees.
:::

::: method
double GetBetaDot()

Returns the current angle of sideslip rate in degrees/sec.
:::

::: method
double GetCLMaxAtMach(double aMach)

This will return the CLmax (maximum lift coefficient) at the specified Mach.
:::

::: method
double GetAlphaAtCLMaxAtMach_deg(double aMach)

This will return the angle of attack (alpha) in degrees at CLmax (maximum lift coefficient) at the specified Mach.
:::

::: method
double MaxPotentialManeuverGLoad()

This will return the maximum number of g\'s that the P6DOF mover could achieve with the current conditions (speed and altitude).
:::



These functions assist with calculating and converting airspeed/Mach values.

::: method
double CalcKtasFromKcas(double aAltitude, double aKcas)

Returns the equivalent knots true airspeed (KTAS) given an altitude in ft and a knots calibrated airspeed (KCAS).
:::

::: method
double CalcKtasFromMach(double aAltitude, double aMach)

Returns the equivalent knots true airspeed (KTAS) given an altitude in ft and a Mach.
:::

::: method
double CalcKtasFromFps(double aFps)

Returns the equivalent knots true airspeed (KTAS) given a speed value in ft/sec.
:::

::: method
double CalcKcasFromKtas(double aAltitude, double aKtas)

Returns the equivalent knots calibrated airspeed (KCAS) given an altitude in ft and a knots true airspeed (KTAS).
:::

::: method
double CalcKcasFromMach(double aAltitude, double aMach)

Returns the equivalent knots calibrated airspeed (KCAS) given an altitude in ft and a Mach.
:::

::: method
double CalcKcasFromFps(double aAltitude, double aFps)

Returns the equivalent knots calibrated airspeed (KCAS) given an altitude in ft and a speed value in ft/sec.
:::

::: method
double CalcMachFromKcas(double aAltitude, double aKcas)

Returns the equivalent Mach given an altitude in ft and a knots calibrated airspeed (KCAS).
:::

::: method
double CalcMachFromKtas(double aAltitude, double aKtas)

Returns the equivalent Mach given an altitude in ft and a knots true airspeed (KTAS).
:::

::: method
double CalcMachFromFps(double aAltitude, double aFps)

Returns the equivalent Mach given an altitude in ft and a speed value in ft/sec.
:::

::: method
double CalcFpsFromKcas(double aAltitude, double aKcas)

Returns the equivalent speed in ft/sec given an altitude in feet and a knots calibrated airspeed (KCAS).
:::

::: method
double CalcFpsFromKtas(double aKtas)

Returns the equivalent speed in ft/sec given a knots true airspeed (KTAS).
:::

::: method
double CalcFpsFromMach(double aAltitude, double aMach)

Returns the equivalent speed in ft/sec given an altitude in ft and a Mach.
:::

::: method
double CalcDynamicPressue_lbft2(double aAltitude, double aFps)

Returns the dynamic pressure in lbs/ft\^2 given an altitude in ft and a speed value in ft/sec.
:::

::: method
double CalcFpsFromAltitudeDynamicPressure(double aAltitude, double aPressure)

Returns a speed in ft/sec given an altitude in ft and a pressure value in lbs/ft\^2.
:::