orphan

:   

# UCI_EntityId

## Overview {#overview .UCI_EntityId}

This class defines an EntityId. It consists of a Universally Unique Identifier (UUID) and an optional descriptor. This type is used as an accessor since all IDs are created by the interface.

## Methods

::: method
string Descriptor()

Gets the descriptor for the ID if it exists.
:::

::: method
string UUID()

Returns the UUID of the corresponding ID.
:::
