# six_dof.rst.txt.md
orphan

:   

::: demo
six_dof
:::

| This is a collection of demos that utilize Rigid-Body (`WsfRigidBodySixDOF_Mover`{.interpreted-text role="class"}) and Point-Mass (`WsfPointMassSixDOF_Mover`{.interpreted-text role="class"}) Six-Degrees-of-Freedom (SixDOF) movers. The goal of these demos is to show features and capabilities of the SixDOF movers and how they can be used in other scenarios.
| 
| The collection also includes some simple support scenarios to help create SixDOF models and analyze their performance. Most demos are based on the previous P6DOF demos, highlighting how these new movers can provide similar functionality.



| These scenarios demonstrate five examples of bombing using SixDOF movers, including the use of sequencers to drop bombs.
| 
| The first example uses a heavy bomber (Rolling Thunder) at very low altitude that drops a series of high-drag bombs to destroy a runway.
| 
| The second example uses a heavy bomber (Arc Light) at high altitude that drops a series of low-drag bombs to destroy another runway. This aircraft is in the contrail band and will produce contrails in the Warlock and Mystic tools.
| 
| The third example uses a flying-wing bomber (Ghost) at medium altitude that drops a series of guided-bombs (representing GPS-guided bombs) that use pre-briefed tracks to attack point targets at the air base.
| 
| The fourth example uses a tactical fighter (Viper 1) to drop low-drag bombs using a dive bombing approach. This shows the use of a single, nested sequencer to drop multiple bombs with a single call.
| 
| The fifth (and last) example also uses a tactical fighter (Viper 2) to drop low-drag bombs with a dive bombing approach. In this case, however, it uses individual sequencers to drop bombs individually.



| This scenario demonstrates full flight operations including taxi, takeoff, script-commanded maneuvers, route following, approach, landing/touchdown, roll-out, and taxi to the ramp.
| 
| Two aircraft begin the demo parked in the Quick Reaction Alert (QRA) area at Honolulu International Airport. The lead aircraft begins to taxi and pauses to test brakes. The leader then taxis to the runway, with the wingman taxiing slightly behind. The leader lines up on the left side of the runway and the wingman lines up on the right side. A formation takeoff (simulated) is performed with a steep climb-out. The aircraft then perform a route orbit.
| 
| The leader then descends and makes an approach and landing with the wingman slightly behind. Both aircraft taxi back to the ramp and park.
| 
| This scenario demonstrates the ability of SixDOF to support a realistic scramble (and recovery) by two interceptors, and the ability of the RB6 variant to taxi, take off, and land.



| These scenarios demonstrate the use of four air-launched cruise missiles from a heavy bomber.
| 
| The scenario begins with the bomber avoiding detection by flying a low-level (200 ft) profile. It then pops up to 1000 ft and launches four cruise missiles, with a five second interval between each missile.
| 
| The missiles each fly several waypoints en-route to their targets, with all impacting nearly simultaneously (all detonating within less than a 2 sec period).
| 
| Each cruise missile also performs a pitch-up terminal maneuver, which makes them a more difficult target for AAA.



| These scenarios demonstrate various features of the formation capability supported by the `WSF_SIX_DOF_MOVER`{.interpreted-text role="model"}. This scenario involves one tanker and a four-ship formation that takes on fuel before heading off toward their mission.
| 
| This scenario demonstrates the formation command system, the two kinds of station keeping available, and the flexibility of the defined formations.



| These scenarios demonstrate several missile engagements.
| 
| The first engagement, south of Oahu, involves two short-range (SR) air-to-air missiles (AAMs). The blue fighter fires two SR-AAMs at the red target aircraft. These include a nominal SR-AAM configuration along with a variant that uses thrust-vector control (TVC). This direct comparison dramatically shows how TVC can increase the agility of a missile, and how SixDOF movers can perform high-alpha maneuvers that 3DOF guided movers cannot emulate. Note, too, that the RB6 and PM6 missiles achieve their skid in different ways. The RB6 whips into orientation, while the PM6 with its lower-order dynamics pulls smoothly into position, but struggles to reduce its turning radius. There are advantages and disadvantages to both, depending on the application.
| 
| The second engagement, also in the south, involves a medium-range (MR) air-to-air missile. The same blue fighter fires a MR-AAM at another red target aircraft. Note that the MR-AAM uses a loft maneuver to increase its range.
| 
| A third engagement, further to the east, involves a long-range (LR) air-to-air missile. This missile consists of two stages, the first of which is discarded after its propellant is spent.
| 
| The fourth engagement, in the northwest, involves a surface-to-air missile (SAM). A red SAM detects aircraft, but does not identify them. The red rules of engagement (ROE) allow engagement of any aircraft in its sector, so a SAM is launched. The missile guides to its target, which was actually \"white air\" \-- a commercial airliner. One airliner is defenseless and is shot down. The second airliner is (unrealistically) equipped with self-defense munitions and is able to intercept the SAM and turn away.



| These scenarios demonstrate the pursuit maneuver capability available for SixDOF movers.
| 
| As an alternative to constructing a custom algorithm for tracking a moving target, a mover can be assigned a `WsfSixDOF_PursueManeuver`{.interpreted-text role="class"}. This is a specialized relative maneuver which aims to maintain a certain trailing distance to another platform.



| This scenario demonstrates the basic use of formations for platforms with SixDOF movers, including formations with a mix of RB6 and PM6 movers. This covers formation creation via both input and script, as well as the formation command system.



| These scenarios present a range of tactical maneuvers that SixDOF aircraft movers can perform, defined totally in script functions. The demo includes two aircraft.
| 
| The first aircraft (red) performs a series of maneuvers. The second aircraft (blue) pursues the first aircraft.



| These scenarios are an alternate take on TacticalManeuvers.txt.
| 
| This version of the tactical maneuvers demo set uses `WsfSixDOF_Maneuver`{.interpreted-text role="class"} objects and sequences to produce the same sequence of maneuvers as in the original demos.



| In these scenarios, an FA-LGT aircraft is launched using a Zero Length Launch (ZELL) technique. ZELL was developed during the 1950\'s to allow aircraft to takeoff without the use of a runway.
| ZELL involves attaching a large solid-propellant rocket to the bottom of a fighter. The rocket is ignited and provides thrust to accelerate the fighter to flying speed before the rocket is jettisoned.
| 
| This demo shows how a SixDOF sub-object can command itself to be launched from a parent object. The ZELL launch platform has a sub-object fighter of type FA-LGT-ZLL. The launcher itself is a PM6 object, launching a PM6 sub-object in one variant, and an RB6 sub-object in the other.
| 
| A sequencer on the fighter is activated 5 seconds after the FA-LGT-ZLL platform is instantiated. This sequencer ignites the attached rocket engine and separates the fighter from the launcher. Three seconds later, after the rocket burns out, the rocket is jettisoned from the fighter.



| This scenario demonstrates the use of unguided rockets using SixDOF movers. The rockets in this example are similar to Zuni rockets.
| 
| An aircraft begins the demo in level flight and bunts over into a shallow dive, lines up on the target (tank) and fires a salvo of 16 rockets from a right and left \"weapon\", representing four quad-tubes under the wings. After firing the rockets, the aircraft pulls away and the rockets destroy the target.



| This scenario uses two aircraft flying the same route but the second aircraft has two 300 gallon external fuel tanks attached to it. Both aircraft have full internal fuel and the second aircraft is adjusted to have 250 lbs of fuel in each external tank. Each aircraft maintains the same speed.
| 
| After 5 minutes, the second aircraft uses a sequencer to drop the external tanks, reducing the drag and weight on the aircraft. The tanks then fall realistically down to the ground.



| This scenario uses two aircraft flying straight and level at 10000ft. One aircraft has 5000 lbs of internal fuel while the second aircraft has 5000 lbs fuel internally plus another 4400lbs of fuel in two external fuel tanks.
| 
| The aircraft fly until they run out of fuel, then data is output including the range and endurance of each flight.



| This scenario demonstrates the ground launch and subsequent orbit of a space vehicle. The launch vehicle begins its flight inverted, expends its booster stage, then rights itself. A liquid rocket motor is used on the orbiter in order to preserve fuel when transitioning between the initial launch phase and the orbiting phase. The resulting orbit is elliptical but stable.
| 
| Two launch vehicles are observed: one with a traditional eastward launch trajectory, and the other pointing west, counter to Earth\'s rotation. While the eastward-pointing system is able to achieve a stable orbit in part due to Earth\'s rotation, the same effects work against the westward system\'s ability to gain altitude and eventually results in its descent.



| This scenario shows how differently the SixDOF movers will fly as compared to each other and to an air mover. In this demo, there is one air mover, three RB6 movers, and three PM6 movers, with all aircraft flying the same route, but with different bank angle limits and with \"follow_vertical_track\" enabled/disabled.
| 
| The red aircraft (pm_mover_1 and rb_mover_1) have no max bank angle limits set and have follow_vertical_track set.
| The blue aircraft (pm_mover_2 and rb_mover_2) have max bank angle limits set for some turns and have follow_vertical_track set.
| The yellow aircraft (pm_mover_3 and rb_mover_3) have max bank angle limits set for some turns and does not have follow_vertical_track set.
| The green aircraft (air_mover) is a `WSF_AIR_MOVER`{.interpreted-text role="model"}.



| This scenario is intended to provide a rough approximation of level flight range for SixDOF AAM models during model file creation. This is accomplished by controlling the angle of attack (alpha) of the missile to make it fly level. No autopilot/guidance object is required. When the minimum termination speed is reached, details about flight time and range will be provided.
| 
| To use this scenario, simply change the included model file path and the PLATFORM_TYPE to the desired model.



| These scenarios show a medium range Surface-to-Air Missile (SAM) launched at multiple launch angles. The missiles fly a ballistic trajectory (no autopilot/guidance controls). The maximum range launch angle will be computed and displayed along with other data about the max range flight.
| 
| This scenario can be modified to test other missiles. Simply change the included model file path and the PLATFORM_TYPE to the desired model.



| This demo generates a flight envelope for a SixDOF model. (Flight envelopes are often referred to by using the slang term, \"dog house plot\".)
| 
| A flight envelope shows the minimum and maximum speed that the aircraft can fly under sustained conditions as a function of altitude. Flight envelopes may be generated under different conditions, such as maximum (military) power, augmented (afterburner) power, or other conditions such as flap settings or customized weapons/fuel load-outs (external fuel tanks and weapons will create drag and all weapons and fuel will effect mass properties, such as weight and rotational inertia as well as center-of-gravity). SixDOF models consider all of these effects in their kinematic calculations.
| 
| This test is set up to use the PM6 FA-LGT model, but can be easily changed to use another model by changing the included platform type and the PLATFORM_TYPE variable.
| 
| This test will generate up to three different files:
| 
| MilEnvelope.csv : CSV file which represents the flight envelope for the model using MIL power.
| 
| ABEnvelope.csv : CSV file which represents the flight envelope for the model using AB power.
| 
| DotPlot.csv : CSV file that contains three columns of data. One includes points at which the aircraft can fly and be controlled, a second column for points with the aircraft can fly but can not be controlled, and a final column for points where the aircraft can not fly. When these three columns are put on a scatter plot as different series, the plot shows information similar to the flight envelope.



| This demo generates a fuel burn table for a SixDOF model, given a starting fuel load. Fuel load is specified in loadout percentage. Burn is output in pounds/hour.
| 
| This test uses internal testing functions to output the data. The output data will be 0.0 for any flight condition where the vehicle cannot sustain steady level flight. This can happen if the condition is below the vehicle\'s stall speed or above Vmax for a given altitude.
| 
| This test is set up to use the rigid-body FA-LGT model, but can be easily changed to use another aircraft model by changing the included vehicle type and the PLATFORM_TYPE variable.
| 
| This test will generate a 2D CSV file, named according to the vehicle and the fuel fraction requested. For example, using the FA-LGT_RB6 at 50% fuel produces FA-LGT_RB6_50percent_FuelBurn.csv. Columns are defined by airspeed in the specified units, and rows are defined by altitude (ft MSL).



| This scenario determines the maximum range and maximum flight time (endurance) for a SixDOF aircraft.
| 
| This test is set up to use the RB6 FA-LGT model, but can be easily changed to use another model by changing the included model type and the PLATFORM_TYPE variables.
| 
| This test can execute a single run at a given altitude and speed, or execute multiple runs at a variety of altitudes and/or speeds. This is controlled by modifying the variables in the script_variables block.



| This scenario tests the system performance as SixDOF entities run in order to estimate the maximum number of SixDOF entities that the system can handle while maintaining realtime performance, or inversely, the speed of constructive execution of a single-aircraft scenario.



| This scenario can be used to generate an autopilot_support_tables.txt file that is used with aircraft and missiles that use SixDOF autopilot/guidance. The tabular data defines angle of attack (alpha) and lift coefficient (CL) limits as well a control data that is used for feed-forward (control bias) in the autopilot PID-based (Proportional, Integral, Derivative) feedback control system used in the SixDOF autopilot. These are helpful for users creating their own SixDOF models.
| 
| To use this scenario, change the included model file path and the PLATFORM_TYPE to the desired model, and run the scenario to generate the autopilot_support_tables.txt file. Copy the autopilot_support_tables.txt to model\'s directory. Where both a PM6 and RB6 vehicle are available to represent the same system, use the output for the RB6 for best results. This will often provide more realistic limits for the autopilot.



| These scenarios provide direct comparisons between PM6 and RB6 movers. In each case, a target aircraft is pursued by a PM6 (F-16 icon) and a RB6 (F-15 icon). This shows that both movers provide similar results, but sometimes subtle differences can result in notable variations.