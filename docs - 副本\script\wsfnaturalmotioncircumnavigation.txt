# wsfnaturalmotioncircumnavigation.rst.txt.md
orphan

:   



::: {.WsfNaturalMotionCircumnavigation .inherits .WsfOrbitalManeuver}
Input type: `natural_motion_circumnavigation<../natural_motion_circumnavigation>`{.interpreted-text role="doc"}
:::

`WsfNaturalMotionCircumnavigation`{.interpreted-text role="class"} is used to place the executing platform, called the chaser, into a relative orbit around another platform, called the target or chief. The chaser will first transfer to the vicinity of the chief, and then insert into the natural motion circumnavigation. See `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"} for details.

Several conditions must be met for the transfer orbit to be successful:

::: {#conditions}
-   The `target point<WsfTargetPoint>`{.interpreted-text role="class"} must reference a valid `WsfLocalTrack`{.interpreted-text role="class"} with both valid location and velocity;

-   The transfer orbit can only be hyperbolic if the mover executing the maneuver supports hyperbolic propagation;

-   The transfer orbit must not intersect earth;

-   When optimizing, a valid solution must exist for the provided optimization option;

-   The expended energy for the transfer must be less than the available delta-v.

    ::: note
    ::: title
    Note
    :::

    The targeting algorithm is direct (less than one orbit). Phasing orbits are not considered.
    :::
:::



::: method
static WsfNaturalMotionCircumnavigation Construct(WsfOrbitalEventCondition aCondition, WsfTargetPoint aTargetPoint, int aOptimizeOption, double aMaximumDeltaTime, double aMaximumDeltaV, double aOrbitSize)

Create a `WsfNaturalMotionCircumnavigation`{.interpreted-text role="class"} with the intent of finding an optimal transfer solution in delta-V or time, using the following:

-   aCondition: A specific `WsfOrbitalEventCondition`{.interpreted-text role="class"}.
-   aTargetPoint: A `WsfTargetPoint`{.interpreted-text role="class"} instance specifying the target of this maneuver. This target must be a track target, and should not specify any position or velocity offsets, offset time, or lag time.
-   aOptimizeOption: The return value from a `WsfNaturalMotionCircumnavigation.EARLIEST_TIME`{.interpreted-text role="method"} or `WsfNaturalMotionCircumnavigation.LEAST_DELTA_V`{.interpreted-text role="method"}.
-   aMaximumDeltaTime: The maximum time in seconds after the maneuver begins to consider in computing a solution.
-   aMaximumDeltaV: The maximum delta-V in meters per second to consider when computing a solution.
-   aOrbitSize: The size in meters of the semi-major-axis of the relative orbit of the chaser around the chief. The provided value must be greater than zero.
:::

::: method
static WsfNaturalMotionCircumnavigation Construct(WsfOrbitalEventCondition aCondition, WsfTargetPoint aTargetPoint, WsfOrbitalOptimizationCost aCost, double aMaximumDeltaTime, double aMaximumDeltaV, double aOrbitSize)

Create a `WsfNaturalMotionCircumnavigation`{.interpreted-text role="class"} with the intent of finding an transfer solution minimizing the provided cost function, using the following:

-   aCondition: A specific `WsfOrbitalEventCondition`{.interpreted-text role="class"}.
-   aTargetPoint: A `WsfTargetPoint`{.interpreted-text role="class"} instance specifying the target of this maneuver. This target must be a track target, and should not specify any position or velocity offsets, offset time, or lag time.
-   aCost: A `WsfOrbitalOptimizationCost`{.interpreted-text role="class"} instance specifying the cost function to minimize.
-   aMaximumDeltaTime: The maximum time in seconds after the maneuver begins to consider in computing a solution.
-   aMaximumDeltaV: The maximum delta-V in meters per second to consider when computing a solution.
-   aOrbitSize: The size in meters of the semi-major-axis of the relative orbit of the chaser around the chief. The provided value must be greater than zero.
:::

::: method
static WsfNaturalMotionCircumnavigation Construct(WsfOrbitalEventCondition aCondition, WsfTargetPoint aTargetPoint, double aDeltaTime, double aOrbitSize)

Create a `WsfNaturalMotionCircumnavigation`{.interpreted-text role="class"} without optimizing the transfer. If all prerequisite [conditions](#conditions) are met, the transfer will take the specified time to complete once the maneuver begins.

-   aCondition: A specific `WsfOrbitalEventCondition`{.interpreted-text role="class"}
-   aTargetPoint: A `WsfTargetPoint`{.interpreted-text role="class"} instance specifying the target of this maneuver. This target must be a track target, and should not specify any position or velocity offsets, offset time, or lag time.
-   aDeltaTime: The time after the maneuver begins when the transfer is planned to complete.
-   aOrbitSize: The size in meters of the semi-major-axis of the relative orbit of the chaser around the chief. The provided value must be greater than zero.
:::

::: method
int EARLIEST_TIME()

Attempt to optimize the maneuver such that it will execute at the earliest possible time, up to delta-t and delta-V constraints provided during construction.
:::

::: method
int LEAST_DELTA_V()

Attempt to optimize the maneuver such that it will execute with the least possible delta-v, up to delta-t and delta-V constraints provided during construction.
:::

::: method
double OrbitSize()

Return the relative semi-major-axis in meters of the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}).
:::

::: method
double OrbitPhase()

Return the phase of the insertion point in degrees for the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}). Unless `WsfNaturalMotionCircumnavigation.SetOrbitPhase`{.interpreted-text role="method"} has been called on this object, this will return the default value of 0.
:::

::: method
double OutOfPlaneAmplitude()

Return the out-of-plane amplitude in meters for the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}). Unless `WsfNaturalMotionCircumnavigation.SetOutOfPlaneAmplitude`{.interpreted-text role="method"} has been called on this object, this will return the default value of 0.
:::

::: method
double OutOfPlanePhase()

Return the out-of-plane phase in degrees for the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}). Unless `WsfNaturalMotionCircumnavigation.SetOutOfPlanePhase`{.interpreted-text role="method"} has been called on this object, this will return the default value of 0.
:::

::: method
void SetOrbitSize(double aOrbitSize)

Set the relative semi-major-axis in meters of the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}). The provided size must be greater than zero.
:::

::: method
void SetOrbitPhase(double aOrbitPhase)

Set the phase of the insertion point in degrees for the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}).
:::

::: method
void SetOutOfPlaneAmplitude(double aOutOfPlaneAmplitude)

Set the out-of-plane amplitude in meters for the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}). The provided value must be non-negative.
:::

::: method
void SetOutOfPlanePhase(double aOutOfPlanePhase)

Set the out-of-plane phase in degrees for the resulting natural motion circumnavigation (see `Natural Motion Circumnavigation Details <../nmc_details>`{.interpreted-text role="doc"}).
:::