# dissystem.rst.txt.md
orphan

:   



::: DisSystem
::: parsed-literal
`DisSystem.GetBeam`{.interpreted-text role="method"} `DisSystem.GetDataLength`{.interpreted-text role="method"} `DisSystem.GetBeamCount`{.interpreted-text role="method"} `DisSystem.GetEmitterName`{.interpreted-text role="method"} `DisSystem.GetFunction`{.interpreted-text role="method"} `DisSystem.GetEmitterId`{.interpreted-text role="method"} `DisSystem.GetLocation`{.interpreted-text role="method"}
:::
:::



[DisSystem](#dissystem) is an implementation of the DIS System.



::: method
DisBeam GetBeam(int aBeamId)

Returns the `DisBeam`{.interpreted-text role="class"} associated with aBeamId on this system.
:::

::: method
int GetDataLength()

Returns the data length of the DIS System structure.
:::

::: method
int GetBeamCount()

Returns the number of beams associated with the system.
:::

::: method
int GetEmitterName()

Returns an index name associated with this system.
:::

::: method
int GetFunction()

Returns the function associated with this system.
:::

::: method
int GetEmitterId()

Returns an emitter ID associated with the system.
:::

::: method
Array\<double\> GetLocation()

Returns the offset of this system in the parent platform\'s coordinate system.
:::