# wsfcommandchain.rst.txt.md
orphan

:   





::: method
WsfPlatform Commander()

Return the platform\'s commander.
:::

::: method
string CommanderName()

Return the name of the platform\'s commander.
:::

::: method
WsfPlatformList Peers()

Return a platform list of peers.
:::

::: method
WsfPlatformList Subordinates()

Return a platform list of subordinates.
:::

::: method
int PeerCount()

Return the number of peers in the command chain.
:::

::: method
WsfPlatform PeerEntry(int aIndex)

Return the peer at the specified index.
:::

::: method
int SubordinateCount()

Return the number of subordinates in the command chain.
:::

::: method
WsfPlatform SubordinateEntry(int aIndex)

Return the subordinates at the specified index.
:::

::: method
string Name()

Return the name of the command chain.
:::