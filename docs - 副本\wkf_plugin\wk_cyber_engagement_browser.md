orphan

:   

# Cyber Engagement Browser - Warlock

![image](../images/wk_CyberEngagement.png)

The Cyber Engagement Browser provides a table that displays currently perceived cyber engagements. For each engagement, the attack type, attacker, attack team, victim, and result of the engagement is shown.

The user has the ability to remove, or purge, stale events after a period of time by right-clicking the events in the table, then clicking Delete.

## Preferences

![image](../images/wk_cyber_engagement_browser_preferences.png)

The user can change the purge time in the Preferences dialog under Cyber Engagement Browser. By default, the table will not purge stale events.
