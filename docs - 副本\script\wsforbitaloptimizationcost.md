orphan

:   

# WsfOrbitalOptimizationCost

::: WsfOrbitalOptimizationCost
`WsfOrbitalOptimizationCost`{.interpreted-text role="class"} is used to specify alternate cost functions for orbital targeting operations. This class is a base class from which all cost classes will derive. The available optimization costs are:
:::

-   `WsfOrbitalBlendedCost`{.interpreted-text role="class"}.

## Methods

::: method
double Cost(double aDeltaT, double aDeltaV)

Compute the value of the cost function for the provided **aDeltaT**, in seconds, and **aDeltaV**, in meters per second.
:::
