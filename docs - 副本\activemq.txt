# activemq.rst.txt.md
orphan

:   





ActiveMQ is the middleware in charge of handling OMS_UCI messages.



To download ActiveMQ, go to [https://activemq.apache.org/components/classic/download/]{.title-ref} and select the desired version. Once downloaded, follow the instructions located here: [https://activemq.apache.org/getting-started]{.title-ref}. These instructions describe the steps to run ActiveMQ.

::: note
::: title
Note
:::

ActiveMQ must be started before the plugin can be used.
:::



To connect a simulation to ActiveMQ, a Reference CAL configuration file is required. The CAL configuration file is set using the environment variable CAL_ACTIVEMQ_CONFIG. This file configures all platform and component Universally Unique Identifiers (UUIDs), so the simulation can send messages/commands to the correct platforms, sensors, etc.

Example configuration file:

::: parsed-literal
\<?xml version=\"1.0\" encoding=\"UTF-8\"?\> \<!\--Sample XML file generated by XMLSpy v2012 rel. 2 sp1 (x64) (<http://www.altova.com)-->\> \<RefCppCalConfiguration xmlns:xsi=\"<http://www.w3.org/2001/XMLSchema-instance>\"\> \<ActiveMQBroker\>tcp://127.0.0.1:61616\</ActiveMQBroker\> \<ActiveMQNoEchoMode\>1\</ActiveMQNoEchoMode\> \<ReaderQueueMax\>1000\</ReaderQueueMax\> \<ReaderSleepMicroseconds\>1000\</ReaderSleepMicroseconds\>

> \<ServiceList\>
>
> :   
>
>     \<Service Name=\"\<disMvr\>1:1:1\" UUID=\"CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC\"\>
>
>     :   \<System Label=\"uci\" UUID=\"AA020000-0000-0000-0000-000000000000\"/\> \<Domain ID=\"10\"/\>
>
>     \</Service\> \<Service Name=\"sense\" UUID=\"CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCD\"\> \<System Label=\"uci\" UUID=\"AA020000-0000-0000-0000-000000000000\"/\> \<Domain ID=\"10\"/\> \</Service\>
>
> \</ServiceList\>

\</RefCppCalConfiguration\>
:::

::: warning
::: title
Warning
:::

Every platform that can communicate using UCI must have a corresponding Service block in the ServiceList.
:::