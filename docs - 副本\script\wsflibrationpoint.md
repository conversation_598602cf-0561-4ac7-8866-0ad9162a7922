orphan

:   

# WsfLibrationPoint

## Overview {#overview .WsfLibrationPoint}

The `WsfLibrationPoint`{.interpreted-text role="class"} can be used to query the position and velocity of the various libration points (as enumerated by the `WsfLibrationPointSelection`{.interpreted-text role="class"} class), for the various primary-secondary systems available (as enumerated by the `WsfLibrationPointSystem`{.interpreted-text role="class"} class).

In addition to providing the position and velocity of libration points, this class can also transform positions and velocities to and from the rotating frame (denoted RF). This frame has its origin at the position of the primary body (e.g., the Earth in the Earth-Moon system), an x-axis pointing toward the secondary body, a z-axis parallel to the orbital angular momentum of the secondary (i.e., normal to the orbital plane), and a y-axis that completes the right-handed triple.

## Static Methods

::: method
WsfLibrationPoint Construct(WsfLibrationPointSystem aSystem)

This method constructs a `WsfLibrationPoint`{.interpreted-text role="class"} object for the given system.
:::

## Methods

::: method
WsfLibrationPointSystem System()

Return the system of this libration point.
:::

::: method
Vec3 LocationECI(WsfLibrationPointSelection aSelectedPoint, Calendar aEpoch)

Return the ECI position of the selected libration point at the specified epoch.
:::

::: method
Vec3 VelocityECI(WsfLibrationPointSelection aSelectedPoint, Calendar aEpoch)

Return the ECI velocity of the selected libration point at the specified epoch.
:::

::: method
Vec3 TransformECI_PositionToRF(Calendar aEpoch, Vec3 aPositionECI)

Transform the given position in the ECI frame into the rotating frame at the given epoch.
:::

::: method
Vec3 TransformECI_VelocityToRF(Calendar aEpoch, Vec3 aPositionECI, Vec3 aVelocityECI)

Transform the given velocity in the ECI frame at the given ECI position at the given epoch into the rotating frame.
:::

::: method
Vec3 TransformRF_PositionToECI(Calendar aEpoch, Vec3 aPositionRF)

Transform the given position in the rotating frame into the ECI frame at the given epoch.
:::

::: method
Vec3 TransformRF_VelocityToECI(Calendar aEpoch, Vec3 aPositionRF, Vec3 aVelocityRF)

Transform the given velocity in the rotating frame at the given position at the given epoch into the ECI frame.
:::
