# wsfpitchratemaneuver.rst.txt.md
orphan

:   





This maneuver sets the target pitch rate of the platform to which it is assigned. This maneuver is done executing as soon as the target is set.



::: method
WsfPitchRateManeuver Construct(double aPitchRateDegPerSec)

Construct a maneuver that will set the target pitch rate of the platform to which the maneuver is assigned.
:::

::: method
double GetPitchRate()

Return this maneuver\'s target pitch rate in degrees per second.
:::