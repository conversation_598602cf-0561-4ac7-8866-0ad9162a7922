# sensor_plot_antenna_plot.rst.txt.md
orphan

:   



Navigation: `sensor_plot`{.interpreted-text role="command"} \> antenna_plot

::: {.command block=""}
antenna_plot \... end_antenna_plot

::: parsed-literal

antenna_plot

:   `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"} [pattern_name]() \... [axes]() \... [azimuth_range]() \... [azimuth_step]() \... [elevation_range]() \... [elevation_step]() \... [tilt_angle]() \... [frequency]() \... [polarization]() \... [electronic_beam_steering]() \... [electronic_beam_steering_loss_exponent]() \... [azimuth_steering_angle]() \... [elevation_steering_angle]() \... [output_file]() \... [gnuplot_file]() \... [header_line_1]() \... [header_line_2]() \... [header_line_3]() \... [output_column_limit]() \...

end_antenna_plot
:::
:::



> The `antenna_plot`{.interpreted-text role="command"} command is used to generate a file that contains the gain of an `antenna_pattern`{.interpreted-text role="command"} sample at selected points within specified spherical limits. This plot produces two output formats. One, specified by the [output_file]() command, generates a table that can be loaded and viewed with the CME tool, SigView (load \"threat assessment\" feature). The other file, specified with the [gnuplot_file]() command, produces an output file suitable for use with gnuplot.



::: command
pattern_name \<pattern-name\>

Specifies the name of the pattern to be processed. This must be the name of an antenna pattern defined with the `antenna_pattern`{.interpreted-text role="command"} command.

Default none - this is a required value.
:::

::: command
axes \[ vertical \| horizontal \| both \]

Specifies the type of data file to be produced:

-   vertical - A vertical sampling at 0 degrees azimuth.
-   horizontal - A horizontal sampling at 0 degrees elevation.
-   both - A horizontal and vertical sampling.

Default vertical
:::

::: command
azimuth_range \<min-angle-value\> \<max-angle-value\>

Specifies the azimuth limits for axes horizontal or axes both.

Default -180 degrees 180 degrees
:::

::: command
azimuth_step \<step-angle-value\>

Specifies the increment between azimuth samples for axes horizontal or axes both.

Default 1 degree
:::

::: command
elevation_range \<min-angle-value\> \<max-angle-value\>

Specifies the elevation limits for axes vertical or axes both.

Default -90 degrees 90 degrees
:::

::: command
elevation_step \<step-angle-value\>

Specifies the increment between elevation samples for axes vertical or axes both.

Default 1 degree
:::

::: command
tilt_angle \<tilt-angle-value\>

Specifies the elevation tilt angle between \[-90 90\] degrees of the antenna pattern with respect to the normal of the pattern.

Default 0 degrees
:::

::: command
frequency \<frequency-value\>

Specifies the frequency which the pattern is to be plotted for.

Default 0.0 Hz
:::

::: command
polarization \[ horizontal \| vertical \| slant_45 \| slant_135 \| left_circular \| right_circular \| default \]

Specifies the polarization which the pattern is to be plotted for.

Default default
:::

::: command
electronic_beam_steering \[ none \| azimuth \| elevation \| both \| azimuth_and_elevation \]

Specifies that the subsystem uses an electronically-steered antenna that is capable of being electronically steered in the specified directions. When specified, the antenna gain is a function of the cosine of the angle relative to the array center. When enabled, the command affects the returned beamwidths of `WSF_RADAR_SENSOR.error_model_parameters.azimuth_beamwidth`{.interpreted-text role="command"} and `WSF_RADAR_SENSOR.error_model_parameters.elevation_beamwidth`{.interpreted-text role="command"}.

-   none - The beam is not electronically steered.
-   azimuth - The beam is electronically steered in azimuth.
-   elevation - The beam is electronically steered in elevation.
-   both or azimuth_and_elevation - The beam is electronically steered in both azimuth and elevation.

Default none
:::

::: command
electronic_beam_steering_loss_exponent \<value\>

Specifies the exponent that will be used in the beam steering loss computation.
:::

::: command
azimuth_steering_angle \<steer-angle-value\>

Specifies the azimuth steering angle between \[-180 180\] degrees of a steered array antenna pattern with respect to the normal of the pattern.

Default 0 degrees
:::

::: command
elevation_steering_angle \<steer-angle-value\>

Specifies the elevation steering angle between \[-90 90\] degrees of a steered array antenna pattern with respect to the normal of the pattern.

Default 0 degrees
:::

::: command
output_file \<file-name\>

Specifies the name to which the output is to be written.

Default none - this is a required value.
:::

::: command
gnuplot_file \<file-name\>

Specifies the name to which the gnuplot output is to be written.
:::

::: command
header_line_1 \<text\>
:::

::: command
header_line_2 \<text\>
:::

::: command
header_line_3 \<text\>

Specifies the text to be contained in the first three lines of the output file when axes both is specified.

Default all header lines are blank.
:::

::: command
output_column_limit \<integer\>

Specifies the maximum number of columns per physical line in the output file when axes both is specified.

Default 100

::: note
::: title
Note
:::

If the file is to be imported into a spreadsheet such as Microsoft Excel, this value should be set so that the rows do not have to be split into multiple physical lines.
:::
:::