orphan

:   

# DisPdu {#DisPdu}

::: DisPdu
::: parsed-literal
`DisComment`{.interpreted-text role="class"} `DisDetonation`{.interpreted-text role="class"} `DisEmission`{.interpreted-text role="class"} `DisEntityState`{.interpreted-text role="class"} `DisFire`{.interpreted-text role="class"} `DisReceiver`{.interpreted-text role="class"} `DisTransmitter`{.interpreted-text role="class"}
:::
:::

::: parsed-literal
`DisPdu.ProtocolVersion`{.interpreted-text role="method"} `DisPdu.Exercise`{.interpreted-text role="method"} `DisPdu.PduType`{.interpreted-text role="method"} `DisPdu.ProtocolFamily`{.interpreted-text role="method"} `DisPdu.TimeStamp`{.interpreted-text role="method"} `DisPdu.BaseLength`{.interpreted-text role="method"} `DisPdu.Length`{.interpreted-text role="method"}
:::

## Overview

[DisPdu](#dispdu) is an implementation of a generic DIS PDU. PDUs are used to communicate information between simulations on a DIS network.

## Methods

::: method
int ProtocolVersion()

Returns the DIS Protocol version.
:::

::: method
int Exercise()

Returns the DIS exercise ID.
:::

::: method
int PduType()

Returns the type of PDU.

| 1. `DisEntityState`{.interpreted-text role="class"}
| 2. `DisFire`{.interpreted-text role="class"}
| 3. `DisDetonation`{.interpreted-text role="class"}
| 22. `DisComment`{.interpreted-text role="class"}
| 23. `DisEmission`{.interpreted-text role="class"}
| 25. `DisTransmitter`{.interpreted-text role="class"}
| 27. `DisReceiver`{.interpreted-text role="class"}
:::

::: method
int ProtocolFamily()

Returns the DIS Protocol family.
:::

::: method
int TimeStamp()

Returns the timestamp for the PDU.
:::

::: method
int BaseLength()

Returns the length of the generic DIS PDU, excluding type-specific information.
:::

::: method
int Length()

Returns the length of the entire DIS PDU, including type-specific information.
:::
