orphan

:   

# electronic_warfare_technique

::: {.command block=""}
electronic_warfare_technique
:::

::: parsed-literal
\# Defining a new type: electronic_warfare_technique *\<name\>* *\<type-name\>* [debug]() effect *\<effect-name\>* *\[\<effect-type-name\>\]* \... `Electronic Warfare Effect Commands <electronic_warfare_effect.Commands>`{.interpreted-text role="ref"} \... end_effect end_electronic_warfare_technique

\# Adding or editing an instance inside an `electronic_warfare`{.interpreted-text role="command"}, `electronic_attack`{.interpreted-text role="command"} or `electronic_protect`{.interpreted-text role="command"} block: \... technique *\<name\>* *\[\<type-name\>\]* [debug]() effect *\<effect-name\>* *\[\<effect-type-name\>\]* \... `Electronic Warfare Effect Commands <electronic_warfare_effect.Commands>`{.interpreted-text role="ref"} \... end_effect end_technique \...
:::

*\<name\>* : Name of the new Electronic Warfare technique type or instance to be created.

*\<type-name\>* : Name of an existing or predefined type Electronic Warfare technique type, whose definition will be used as the initial definition of the new type.

## Overview

An electronic_warfare_technique provides the ability to define Electronic Warfare techniques with associated `electronic_warfare_effect`{.interpreted-text role="command"}. Multiple effect definitions are allowed.

## Commands {#Electronic_warfare_technique.Commands}

::: command
debug

Specifies to use a debug mode to output debug data to the standard output.

**Default** false or off
:::

::: command
effect \<effect-name\> \[\<effect-type-name\>\]\... end_effect

Defines the uniquely named effect for this technique and its derived type, if required.

*\<effect-name\>* : A string input of the effect\'s unique name.

*\<effect-type-name\>* : A string input of the effect\'s type. If editing an instance name already defined this input is not required.
:::
