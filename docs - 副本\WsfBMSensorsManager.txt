# WsfBMSensorsManager.rst.txt.md
orphan

:   



::: WsfBMSensorsManager
Navigation: `script_types`{.interpreted-text role="doc"}
:::

Derives From: `WsfProcessor`{.interpreted-text role="class"}, `WsfPlatformPart`{.interpreted-text role="class"}, `WsfObject`{.interpreted-text role="class"}



WsfBMSensorsManager is the script interface for invoking methods on the IADS C2 Sensors Manager base class. This class provides common interfaces that all IADS C2 sensors manager processors derive.

