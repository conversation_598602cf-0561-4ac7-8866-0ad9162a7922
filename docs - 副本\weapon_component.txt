# weapon_component.rst.txt.md
orphan

:   



::: model
uci_component WEAPON
:::

::: parsed-literal
uci_component \<type\> WEAPON

> Part Commands \...
>
> `uci_component`{.interpreted-text role="command"} Commands \...

end_uci_component
:::



This component handles the Strike UCI messages for all weapons on a platform.





All message types are able to be sent via script methods, unless noted otherwise.



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_StrikeCapabilityMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval



`UCI_StrikeCapabilityStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_ControlRequestMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   Script only



`UCI_ControlRequestStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   When a ControlRequestMessage is received and handled (one per controlee in message)



`UCI_ControlStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   When a ControlRequestMessage is received and handled (one per controlee in message)
> -   On update interval



`UCI_StrikeSettingsCommandMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::



`UCI_SubsystemStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval



`UCI_SystemStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval
>
> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented in script
> :::