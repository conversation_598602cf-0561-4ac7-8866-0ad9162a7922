orphan

:   

# WsfTangentManeuver

::: {.WsfTangentManeuver .inherits .WsfOrbitalManeuver}
**Input type:** `tangent<../tangent>`{.interpreted-text role="doc"}
:::

[WsfTangentManeuver](#wsftangentmaneuver) can be used to change a delta V tangential to a platforms current orbit.

::: method
static WsfTangentManeuver Construct(WsfOrbitalEventCondition aCondition, double aDeltaV)

Static method to create a [WsfTangentManeuver](#wsftangentmaneuver) using a specific condition and a delta-V (m/s) tangent to the current velocity vector of the platform.
:::
