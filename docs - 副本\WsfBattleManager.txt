# WsfBattleManager.rst.txt.md
orphan

:   



::: WsfBattleManager
Navigation: `script_types`{.interpreted-text role="doc"}
:::

Derives From: `WsfProcessor`{.interpreted-text role="class"}, `WsfPlatformPart`{.interpreted-text role="class"}, `WsfObject`{.interpreted-text role="class"}



WsfBattleManager is the script interface for invoking methods on the IADS C2 battle manager base class. This class provides common interfaces that all IADS C2 battle managers derive.



::: method
void RunModel()

Runs the model for the current update cycle. This generally means that the battle managers will run their threat to assignment loops possibly creating pending outgoing messages for a C^2^ Dissemination processor to dispatch.
:::

::: method
bool HasCommitAuthority()

Returns true if the battle manager has been scripted with commit_authority on, otherwise false.
:::