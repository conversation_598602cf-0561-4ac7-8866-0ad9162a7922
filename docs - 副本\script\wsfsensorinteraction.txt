# wsfsensorinteraction.rst.txt.md
orphan

:   





::: method
bool Detected()

Returns \'true\' if sensor EM interaction resulted in a detection of the target. (Pd \>= Required Pd) and there were no other failure reasons.
:::

::: method
string ModeName()

Returns the name of the sensor mode used during the EM interaction.
:::

::: method
int BeamNumber()

Returns the index of the beam within the beam list that resulted in the best probability of detection for the EM interaction.
:::

::: method
double Pd()

Returns the probability of detection.
:::

::: method
double RequiredPd()

Returns the probability of detection required for detection.
:::