orphan

:   

::: demo
aea_iads
:::

| This directory contains the necessary files to test the EW effects
| in a scenario similar to the iads_demo baseline scenario.
| 
| The SOJ and EW_Radar have EA and EP techniques, respectively, with them
| to aid in the testing and verification of these techniques.
| 
| The strike.txt file is the normal run input file to run the simulation
| as a single application.
| 
| To execute, open \"strike.txt\" in Wizard and execute using the mission
| application. The replay file will written to the output directory for
| opening by My<PERSON>.
| 
| Also included are two realtime run files strike-rt.txt and iads-rt.txt
| that can be run separately to test the jamming and EW effects over DIS.
| To run both concurrently, open two Wizard applications one with
| \"iads-rt.txt\" and one with \"strike-rt.txt\" and execute using the mission
| application. Each WSF application communicate over DIS. The applications
| can be run on separate hosts or on the same host. The file dis_data.txt
| provides the DIS Enumeration mapping for the entity types, emitter types,
| and EW technique types.
| 
| To visually monitor the progress of the realtime sims, Warlock can be opened
| and then setup to monitor multicast address ***********, port 3225 via input file containing a dis_interface block.
