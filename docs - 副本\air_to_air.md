orphan

:   

::: demo
air_to_air
:::

| This is a collection of several demos that demonstrate air-to-air combat using the `WSF_SA_PROCESSOR`{.interpreted-text role="model"} and `Advanced Behavior Trees (ABTs)<advanced_behavior_tree>`{.interpreted-text role="doc"}.
| 
| The following demos are included (all demos use `WSF_BRAWLER_MOVER`{.interpreted-text role="model"}, unless otherwise indicated):

-   

    1v1

    :   -   This is a simple one-versus-one engagement

-   

    1v1_initial_prediction

    :   -   This is a simple scenario illustrating a set of predictions from the `WSF_SA_PROCESSOR`{.interpreted-text role="model"}

-   

    2v2

    :   -   This is a two-versus-two engagement

-   

    2v2_am

    :   -   This is a two-versus-two engagement (uses `WSF_AIR_MOVER`{.interpreted-text role="model"})

-   

    2v2_six_dof

    :   -   This is a two-versus-two engagement (uses `WSF_POINT_MASS_SIX_DOF_MOVER`{.interpreted-text role="model"})

-   

    hvaa_dca

    :   -   This is a HVAA-P/DCA mission

-   

    escort

    :   -   This is a bomber escort mission

# 1v1.txt

| This scenario demonstrates a one-versus-one, head-to-head engagement (1v1) between two fighters.

# 1v1_initial_prediction.txt

| This scenario demonstrates the basic prediction capabilities of the `WSF_SA_PROCESSOR`{.interpreted-text role="model"}. Each different prediction will draw its own line segment (using `WsfDraw`{.interpreted-text role="class"}) onto the Map Display illustrating the movement of the aircraft being predicted.

# 2v2.txt

| This scenario demonstrates a two-versus-two engagement (2v2) between two blue and two red fighters.

# 2v2_am.txt

| This scenario demonstrates a two-versus-two (2v2) engagement, but this uses `WSF_AIR_MOVER`{.interpreted-text role="model"} for the movers instead of `WSF_BRAWLER_MOVER`{.interpreted-text role="model"}.

# 2v2_six_dof.txt

| This scenario demonstrates a two-versus-two (2v2) engagement, but this uses `WSF_POINT_MASS_SIX_DOF_MOVER`{.interpreted-text role="model"} for the movers instead of `WSF_BRAWLER_MOVER`{.interpreted-text role="model"}.

# hvaa_dca.txt

| This scenario demonstrates a High-Value Airborne Asset (HVAA) Protection / Defensive Counter-Air (DCA) mission with four blue fighters on Combat Air Patrol (CAP) defending a HVAA (AWACS) versus six red fighters that are seeking to shoot down the AWACS.

# escort.txt

| This scenario demonstrates an escort of a single bomber by four blue fighters. Six red fighters will attempt to shoot down the bomber.

::: warning
::: title
Warning
:::

This demo does not work with Linux, due to a problem with the `WSF_BRAWLER_MOVER`{.interpreted-text role="model"}.
:::
