# acoustic_signature.rst.txt.md
orphan

:   



::: {.command block=""}
acoustic_signature \<signature-name\> \... end_acoustic_signature

::: parsed-literal

acoustic_signature \<signature-name\>

:   data_reference_range \<length-value\>

    state \<state-name\> \| default

    :   

        spectrum_data

        :   freq \<frequency-value\> noise_pressure \<noise-pressure-value\> freq \<frequency-value\> noise_pressure \<noise-pressure-value\> \...

        end_spectrum_data

    state \...

end_acoustic_signature
:::

\<signature-name\>

:   Name to be given to the signature. This must be unique among all acoustic signatures. A particular signature may be redefined, with the last definition being used.
:::



acoustic_signature defines an acoustic signature for a platform type. It is used when an acoustic sensor attempts to detect a platform. The acoustic signature consists of one or more tables where each table defines the signature that applies when the platform is in a particular \'state\'. A \'state\' represents a condition such as \'landing-gear-down\'.



::: command
data_reference_range \<length-value\>

The reference range that the following acoustic data was calculated or measured at. This value is used to adjust the following acoustic data to a zero slant range value.
:::

::: command
state \[ \<state-name\> \| default \]

Indicates that the following table definition will be used when the platform is in the signature state \<state-name\>. If default is specified then the subsequent table will be used if the platform is in a signature state that does not match any of the states defined in the signature.

If a state command is not specified then the signature has one signature table that applies to all signature states.
:::

::: command
freq \<frequency-value\>

1/3 octave center frequency of signature value.

::: note
::: title
Note
:::

These must occur in increasing order within a single state.
:::
:::

::: command
noise_pressure \<noise-pressure-value\>

Acoustic noise pressure (dB/20 uPa) at specified 1/3 octave center frequency.
:::