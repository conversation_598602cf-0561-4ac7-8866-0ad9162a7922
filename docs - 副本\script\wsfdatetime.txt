# wsfdatetime.rst.txt.md
orphan

:   



::: {.WsfDateTime .inherits .Object}
WsfDateTime is a utility to represent absolute date and time (see `Calendar`{.interpreted-text role="class"}) within the simulation.
:::



::: method
static Calendar CurrentTime()

Returns a `Calendar`{.interpreted-text role="class"} object representing the current date and time of the simulation.
:::

::: method
static Calendar StartTime()

Returns a `Calendar`{.interpreted-text role="class"} object representing the starting date and time of the simulation.
:::

::: method
static Calendar SimulationTime(double aSimTime)

Returns a `Calendar`{.interpreted-text role="class"} object representing the date and time at the specified elapsed simulation time.
:::