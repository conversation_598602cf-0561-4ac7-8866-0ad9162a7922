# target.rst.txt.md
orphan

:   



Script Type: `WsfTargetManeuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} target

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [delta_time]() \... [maximum_delta_time]() \... [maximum_delta_v]() \... [optimize_time]() \... [optimize_delta_v]() \... [optimize_cost]() \... [tolerance]() \... `target_specification`{.interpreted-text role="doc"} \...

end_maneuver
:::

::: block
target_maneuver
:::

Perform a maneuver to target (intercept with) the given platform. Options are provided to optimize when the maneuver occurs, either at the earliest possible time, or with minimum delta-V expended during that time.

> ::: note
> ::: title
> Note
> :::
>
> The script version of this maneuver, `WsfTargetManeuver`{.interpreted-text role="class"}, is used dynamically to target track locations.
> :::
>
> ::: note
> ::: title
> Note
> :::
>
> Several conditions must be met before the maneuver can succeed. These conditions include the following:
> :::
>
> -   The platform must be valid at the start of the simulation.
> -   The transfer orbit can only be hyperbolic if the mover executing the maneuver supports hyperbolic propagation.
> -   The transfer orbit must not intersect earth.
> -   When optimizing, a valid solution must exist for the provided optimization option ([optimize_time]() or [optimize_delta_v]()).
> -   The expended energy for the transfer must be less than the available delta-v.