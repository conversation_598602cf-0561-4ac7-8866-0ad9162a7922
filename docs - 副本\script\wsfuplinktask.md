orphan

:   

# WsfUplinkTask

::: {.WsfUplinkTask .inherits .WsfTask}
`WsfUplinkTask`{.interpreted-text role="class"} is a `WsfTask`{.interpreted-text role="class"} for uplinking a track to another platform.
:::

## Static Methods

::: method
WsfUplinkTask Create(string aTaskType)

Returns a new `WsfUplinkTask`{.interpreted-text role="class"} with the given task type.
:::

::: method
WsfUplinkTask Create(string aTaskType, string aAssignee)

Returns a new `WsfUplinkTask`{.interpreted-text role="class"} with the given task type and assignee platform name.
:::

## Methods

::: method
string UplinkDestination()

Returns the platform name of the platform to receive the uplinked tracks.
:::

::: method
void UplinkDestination(string aUplinkDestination)

Sets the platform name of the platform to receive the uplinked tracks.
:::

::: method
string UplinkComm()

Returns the value for uplink comm, which if set overrides the `WSF_TASK_PROCESSOR.uplink_comm`{.interpreted-text role="command"} parameter in `WSF_TASK_PROCESSOR`{.interpreted-text role="model"}.
:::

::: method
void UplinkComm(string aUplinkComm)

Sets the value for uplink comm, which if set overrides the `WSF_TASK_PROCESSOR.uplink_comm`{.interpreted-text role="command"} parameter in `WSF_TASK_PROCESSOR`{.interpreted-text role="model"}.
:::

::: method
string UplinkSource()

Returns the value for uplink source, which if set overrides the `WSF_TASK_PROCESSOR.uplink_source`{.interpreted-text role="command"} parameter in `WSF_TASK_PROCESSOR`{.interpreted-text role="model"}.
:::

::: method
void UplinkSource(string aUplinkSource)

Sets the value for uplink source, which if set overrides the `WSF_TASK_PROCESSOR.uplink_source`{.interpreted-text role="command"} parameter in `WSF_TASK_PROCESSOR`{.interpreted-text role="model"}.
:::
