#!/usr/bin/env python3
"""
增强版AFSIM Platform Agent主程序
支持智能对话、记忆功能和脚本生成
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.enhanced_platform_agent import EnhancedPlatformAgent


def setup_logging(level: str = "INFO"):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('enhanced_agent.log', encoding='utf-8')
        ]
    )


def init_agent(config_path: str = None) -> EnhancedPlatformAgent:
    """初始化增强版Agent"""
    print("🚀 正在初始化增强版AFSIM Platform Agent...")
    
    agent = EnhancedPlatformAgent(config_path)
    
    if not agent.initialize():
        print("❌ Agent初始化失败")
        sys.exit(1)
    
    print("✅ 增强版Agent初始化成功")
    return agent


def interactive_chat(agent: EnhancedPlatformAgent):
    """交互式对话模式"""
    print("\n🎯 进入智能对话模式")
    print("=" * 60)
    print("💡 我是您的AFSIM平台脚本助手，可以帮您：")
    print("   • 查询平台参数和配置信息")
    print("   • 生成F-22、F-35、B-2等平台脚本")
    print("   • 解释AFSIM相关概念和用法")
    print("   • 提供最佳实践建议")
    print("\n📝 输入 'help' 查看帮助，'quit' 退出")
    print("=" * 60)
    
    while True:
        try:
            user_input = input("\n🤖 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！感谢使用AFSIM Platform Agent")
                break
            elif user_input.lower() == 'help':
                show_help()
                continue
            elif user_input.lower() == 'status':
                show_status(agent)
                continue
            elif user_input.lower() == 'clear':
                agent.clear_memory()
                print("🧹 对话记忆已清空")
                continue
            elif user_input.lower().startswith('save '):
                filepath = user_input[5:].strip()
                agent.save_conversation(filepath)
                continue
            elif user_input.lower().startswith('load '):
                filepath = user_input[5:].strip()
                agent.load_conversation(filepath)
                continue
            elif user_input.lower() == 'templates':
                show_templates(agent)
                continue
            elif not user_input:
                continue
            
            print("\n🤔 思考中...")
            response = agent.chat(user_input)
            print(f"\n🤖 助手: {response}")
            
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")


def show_help():
    """显示帮助信息"""
    print("""
📚 可用命令：
  help          - 显示此帮助信息
  status        - 显示Agent状态
  clear         - 清空对话记忆
  templates     - 显示可用平台模板
  save <file>   - 保存对话历史
  load <file>   - 加载对话历史
  quit/exit/q   - 退出程序

💡 示例问题：
  • "F-22的platform脚本应该怎么写？"
  • "platform命令有哪些参数？"
  • "生成一个B-2轰炸机的脚本"
  • "altitude参数是什么意思？"
  • "如何设置平台的雷达特征？"
""")


def show_status(agent: EnhancedPlatformAgent):
    """显示Agent状态"""
    status = agent.get_status()
    print("\n📊 Agent状态:")
    print(f"   增强版Agent: {'✅' if status['enhanced_agent_initialized'] else '❌'}")
    print(f"   对话记忆条数: {status['memory_messages']}")
    print(f"   可用工具数: {status['tools_count']}")
    print(f"   平台模板数: {len(status['available_templates'])}")
    
    if 'base_agent_status' in status and status['base_agent_status']:
        base_status = status['base_agent_status']
        print(f"   基础Agent: {'✅' if base_status.get('initialized', False) else '❌'}")
        print(f"   索引文件数: {base_status.get('indexed_files', 0)}")


def show_templates(agent: EnhancedPlatformAgent):
    """显示可用模板"""
    templates = agent.list_platform_templates()
    print("\n🛩️ 可用平台模板:")
    for template in templates:
        template_data = agent.get_platform_template(template)
        print(f"   • {template}: {template_data.get('description', 'No description')}")


def demo_mode(agent: EnhancedPlatformAgent):
    """演示模式"""
    print("\n🎬 演示模式")
    print("=" * 50)
    
    demo_questions = [
        "F-22的platform脚本应该怎么写？",
        "platform命令有哪些主要参数？",
        "生成一个F-35战斗机的脚本",
        "altitude参数是什么意思？"
    ]
    
    for i, question in enumerate(demo_questions, 1):
        print(f"\n{i}. 问题: {question}")
        print("   回答:", end=" ")
        
        try:
            response = agent.chat(question)
            # 截取前200字符显示
            short_response = response[:200] + "..." if len(response) > 200 else response
            print(short_response)
        except Exception as e:
            print(f"错误: {e}")
        
        input("   按回车继续...")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强版AFSIM Platform Agent")
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    parser.add_argument('--mode', default='interactive',
                       choices=['interactive', 'demo'],
                       help='运行模式')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ 请设置DEEPSEEK_API_KEY环境变量")
        sys.exit(1)
    
    # 初始化Agent
    agent = init_agent(args.config)
    
    # 运行对应模式
    if args.mode == 'interactive':
        interactive_chat(agent)
    elif args.mode == 'demo':
        demo_mode(agent)


if __name__ == '__main__':
    main()
