# wk_platform_part_browser.rst.txt.md
orphan

:   



{.align-right height="600px"}

The Warlock Platform Part Browser is responsible for displaying the `platform parts<_.platform_part>`{.interpreted-text role="command"} on the selected platform. The browser shows different properties depending on the type of platform part being displayed.

All platform parts show the following properties in the browser:

  -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Platform Part Properties   
  -------------------------- ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Debug                      A check box dictating whether or not to display `_.platform_part.debug`{.interpreted-text role="command"} information.

  On                         A check box dictating whether or not the part is `_.platform_part.on`{.interpreted-text role="command"}

  Operational                A check box dictating whether or not the part is `_.platform_part.operational`{.interpreted-text role="command"}. When this checkbox is unchecked, the part is turned off and cannot be turned back on until this checkbox is checked again.

  Broken                     Shows whether or not the part is `_.platform_part.broken`{.interpreted-text role="command"}.
  -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------



Articulated parts (ie. weapons, comms, and sensors) contribute additional properties to the part browser:

  -----------------------------------------------------------------------------------------------------------------------------------------------
  Articulated Part Properties          
  ------------------------------------ ----------------------------------------------------------------------------------------------------------
  Slew Mode                            The `slew mode<_.articulated_part.slew_mode>`{.interpreted-text role="command"} of the articulated part.

  Roll                                 The `_.articulated_part.roll`{.interpreted-text role="command"} of the articulated part.

  Pitch                                The `_.articulated_part.pitch`{.interpreted-text role="command"} of the articulated part.

  Yaw                                  The `_.articulated_part.yaw`{.interpreted-text role="command"} of the articulated part.

  Tilt                                 The `_.articulated_part.tilt`{.interpreted-text role="command"} of the articulated part.
  -----------------------------------------------------------------------------------------------------------------------------------------------



If the articulated part is any type of sensor (ie. radar, acoustic, optical, etc), the following properties will be displayed for the sensor:

  --------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Sensor Properties   
  ------------------- ------------------------------------------------------------------------------------------------------------------------------------------------
  Current Mode        The current mode of the sensor

  Modes               A list of all of the sensor\'s `modes<sensor.mode>`{.interpreted-text role="command"}.

  Tracks              A list of the platform names for which the sensor has a track. If the sensor has no tracks, or does not produce tracks, \"none\" is displayed.
  --------------------------------------------------------------------------------------------------------------------------------------------------------------------



If the sensor of interest is a radar, the Part Browser displays additional properties for both the receiver and transmitter.

  --------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Radar Receiver Properties   
  --------------------------- ----------------------------------------------------------------------------------------------------------------------------------------------
  Range, Min                  The `minimum range<_.antenna_commands.minimum_range>`{.interpreted-text role="command"} at which the radar can interact with another object.

  Range, Max                  The `maximum range<_.antenna_commands.maximum_range>`{.interpreted-text role="command"} at which the radar can interact with another object.

  Elevation, Min              The minimum angle of the `elevation field of view<_.antenna_commands.elevation_field_of_view>`{.interpreted-text role="command"}.

  Elevation, Max              The maximum angle of the `elevation field of view<_.antenna_commands.elevation_field_of_view>`{.interpreted-text role="command"}.

  Azimuth, Min                The minimum angle of the `azimuth field of view<_.antenna_commands.azimuth_field_of_view>`{.interpreted-text role="command"}.

  Azimuth, Max                The maximum angle of the `azimuth field of view<_.antenna_commands.azimuth_field_of_view>`{.interpreted-text role="command"}.

  Bandwidth                   The operating `_.receiver.bandwidth`{.interpreted-text role="command"} of the radar.

  Noise Power                 The `noise power<_.receiver.noise_power>`{.interpreted-text role="command"} of the radar.

  Detection Threshold         The `detection threshold<_.receiver.detection_threshold>`{.interpreted-text role="command"} of the radar signal.
  --------------------------------------------------------------------------------------------------------------------------------------------------------------------------

| 

  ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Radar Transmitter Properties   
  ------------------------------ ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Peak Power                     The `peak power<_.transmitter.power>`{.interpreted-text role="command"} output of the transmitter

  Average Power                  The average power for the currently set frequency

  Frequency                      The current operating `frequency<_.transmitter.frequency>`{.interpreted-text role="command"} of the transmitter

  Pulse Width                    The average `pulse width<_.transmitter.pulse_width>`{.interpreted-text role="command"} for a pulsed transmitter

  Pulse Repetition Interval      The `pulse repetition interval<_.transmitter.pulse_repetition_interval>`{.interpreted-text role="command"}, for a pulsed transmitter. If the `pulse width<_.transmitter.pulse_width>`{.interpreted-text role="command"} is zero, then the pulse repetition interval is not specified.
  ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------