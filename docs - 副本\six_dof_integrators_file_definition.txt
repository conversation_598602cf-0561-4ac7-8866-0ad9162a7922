# six_dof_integrators_file_definition.rst.txt.md
orphan

:   



The SixDOF integrators file is very simple. A single input line is used for each desired integrator.

The file format is as follows:

    integrators

      create_integrator  STANDARD_RIGID_BODY_INTEGRATOR

    end_integrators

This should typically include the default integrator (case-sensitive, STANDARD_RIGID_BODY_INTEGRATOR for Rigid-Body 6DOF and STANDARD_POINT_MASS_INTEGRATOR for Point-Mass 6DOF) unless all objects in the scenario use other integrators.

Return to `six_dof_object_types`{.interpreted-text role="command"} or `docs/six_dof_object_types:SixDOF Integrators Support`{.interpreted-text role="ref"}