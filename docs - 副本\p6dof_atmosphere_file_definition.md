orphan

:   

# P6DOF Atmosphere File Definition {#P6DOF_Atmosphere_File_Definition}

A P6DOF atmosphere file defines five atmospheric properties:

-   Density versus altitude
-   Pressure versus altitude
-   Temperature versus altitude
-   Speed of sound versus altitude
-   Altitude band where contrailing may occur (optional)

The density, pressure, temperature, and speed of sound properties are defined in tables with altitude/data pairings. Altitude is measured in feet and data units are slugs/cu-ft (density), lbs/sq-ft (pressure), deg-Rankine (temperature), and ft/sec (speed of sound). Format is as follows:

    p6dof_atmosphere

      density_altitude_table 
       # altitude_ft  density_slugs_ft3
         0            0.002377170
         1000         0.002308390
         ...          ...
         400000       0.000000000
      end_density_altitude_table

      pressure_altitude_table 
       # altitude_ft  pressure_lbs_ft2
         0            2116.2300
         1000         2040.8600
         ...          ...
         400000       0.0000
      end_pressure_altitude_table

      temperature_altitude_table
       # altitude_ft  rankine_deg
         0            518.67
         1000         515.10
         ...          ...
         400000       336.50
      end_temperature_altitude_table

      sonic_speed_altitude_table
       # altitude_ft  sonic_speed_ft_s
         0            1116.45
         1000         1112.61
         ...          ...
         400000       899.27
      end_sonic_speed_altitude_table

      contrail_min_altitude_ ...
      contrail_max_altitude_ ...

    end_p6dof_atmosphere

For the tables, altitude must be listed in increasing values. It is recommended that the altitudes in the tables reach well beyond the maximum altitude of any P6DOF objects that will be used in the scenario. The practical limit of the atmosphere is between 300,000 to 400,000 feet, but the tables may go higher, if needed.

If contrailing conditions are present, the altitude band should be defined and must include both min and max altitudes, as follows:

::: command
contrail_min_altitude \<length-value\>

This is the lowest altitude at which contrailing will occur.
:::

::: command
contrail_max_altitude \<length-value\>

This is the highest altitude at which contrailing will occur.
:::

Return to `p6dof_object_types`{.interpreted-text role="doc"} or `P6DOF_Environment_Support`{.interpreted-text role="ref"}
