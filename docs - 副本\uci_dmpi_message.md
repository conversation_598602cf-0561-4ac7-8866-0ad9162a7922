orphan

:   

# UCI_DMPI_Message

## Overview {#overview .UCI_DMPI_Message .inherits .UCI_Message}

This message represents a single Designated/Desired Mean Point of Impact (DMPI) for a weapon/store. The DMPI series of messages are designed to be used collectively to support a variety of use cases by separating data content into independent messages aligned with typical use case actors.

## Static Methods

::: method
UCI_DMPI_Message Construct(double aLatitude, double aLongitude, double aAltitude) UCI_DMPI_Message Construct(UCI_DMPI_ID aDMPI_ID, double aLatitude, double aLongitude, double aAltitude)

Construct a UCI_DMPI_Message for the given aLatitude, aLongitude, and aAltitude. If a `UCI_DMPI_ID`{.interpreted-text role="class"} is not given, a random ID will be generated.
:::

## Methods

::: method
UCI_DMPI_ID DMPI_ID()

Get the DMPI_ID of the message.
:::

::: method
void SetWeaponeering(int aStoreType, UCI_StoreCategory aStoreCategory, int aStoreQuantity)

Set the weaponeering category for the DMPI message.
:::
