orphan

:   

# ballistic_types

::: {.command block=""}
ballistic_types .. end_balistic_types
:::

::: parsed-literal

[ballistic_types](#ballistic_types)

:   [use_fires_extrapolation]() \<boolean\> [target_data]() \<type\> [aero]() \<aero-type\> [mass]() \<mass-value\> end_target_data

end_ballistic_types
:::

## Overview

Ballistic_types defines a list of platform types that should be treated as ballistic projectiles. This means that BallisticPaths can be generated for these types, and special WsfDraw effects are generated for WeaponFired and WeaponTerminated events.

Specify the WSF platform types that should be treated as ballistic types. For example:

    ballistic_types
       FIRES_60MM_MORTAR
       FIRES_107MM_ROCKET
       FIRES_120MM_ARTILLERY
    end_ballistic_types

## Commands

::: block
ballistic_types
:::

::: command
use_fires_extrapolation \<boolean\>

When creating BallisticPaths, directs whether to extrapolate using an existing WSF_FIRES_MOVER, if present. If not present, or if the flag is set false, the path will be computed from [target_data](), if available.

**Default** true
:::

::: command
target_data \<type\> \... end_target_data

Provide mass and aero characteristics associated with the given ballistic type. These data are needed to calculate BallisticPaths when the use_fires_extrapolation flag is disabled. For example:

    ballistic_types
       ...
       target_data FIRES_122MM_ROCKET
          aero 122mm_ROCKET_AERO
          mass 66 kg
       end_target_data
       ...
    end_ballistic_types

If one defines ballistic target data, it is not necessary to list the type again on a separate line.
:::

::: block
ballistic_types.target_data
:::

::: command
aero \<aero-type\>

Provide aero characteristics associated with the given type.
:::

::: command
mass \<mass-value\>

Provide a mass to be associated with the given type.
:::
