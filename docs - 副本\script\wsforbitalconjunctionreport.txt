# wsforbitalconjunctionreport.rst.txt.md
orphan

:   



::: WsfOrbitalConjunctionReport
`WsfOrbitalConjunctionReport`{.interpreted-text role="class"} contains the details of conjunctions predicted by a `WSF_ORBITAL_CONJUNCTION_PROCESSOR`{.interpreted-text role="model"}.
:::



::: method
double MissDistance()

Return the predicted distance in meters between the two objects at the time of closest approach.
:::

::: method
double RelativeVelocity()

Return the predicted relative velocity in meters per second of the two objects at the point of closest approach.
:::

::: method
double StartTime()

Return the predicted time in seconds when the two objects enter within their mutual exclusion zone.
:::

::: method
double EndTime()

Return the predicted time in seconds when the two objects leave their mutual exclusion zone.
:::

::: method
double MinimumTime()

Return the predicted time in seconds of the point of closest approach of the two objects.
:::

::: method
double MaximumProbability()

Return the predicted maximum probability of the conjunction.
:::

::: method
string Primary()

Return a text identifier of the primary object suffering the predicted conjunction.
:::

::: method
string Secondary()

Return a text identifier of the secondary object suffering the predicted conjunction.
:::