# alternate_locations_demo.rst.txt.md
orphan

:   

::: demo
alternate_locations
:::

| Often a threat platform\'s location is not concretely known from intelligence data.
| The threat may be frequently moved, may be a movable platform that relocates easily,
| or there may simply be a lack of data. All of these issues cause there to be an
| uncertainty in the placement of a platform at the start of a scenario run. It is
| necessary to implement some functionality such that platforms can have multiple
| alternate locations possible before the platform is instantiated for a given run of
| a simulation.



| This scenario provides examples of how to utilize the `alternate_locations`{.interpreted-text role="command"}
| functionality within AFSIM. Platforms can be given a probabilistic choice of locations
| at which to be initialized in the beginning of a scenario. Additionally these locations
| do not have to be absolute but can be relative to other platforms.