# wsfgroup.rst.txt.md
orphan

:   



::: {.WsfGroup .inherits .WsfObject}
WsfGroup is an aggregation of `platform`{.interpreted-text role="command"} and platform part (`sensor`{.interpreted-text role="command"}, `processor`{.interpreted-text role="command"}, `comm`{.interpreted-text role="command"}) objects.
:::



Accessor methods allow access to group members .. method:: Array\<string\> MembersByName()

> Returns a list of member names

::: method
Array\<WsfObject\> Members()

Returns a list containing all members of the group. Each returned `WsfObject`{.interpreted-text role="class"} can be either a `WsfPlatform`{.interpreted-text role="class"} or a `WsfPlatformPart`{.interpreted-text role="class"}.
:::

::: method
int Size()

Returns the number of members in the group.
:::

::: method
WsfObject Member(int aIndex)

Returns a member of the group by index. The specified index should be in the range \[0, Size). You can use the bracket operator \"\[ \]\" instead of Member (e.g., myGroup\[0\] instead of myGroup.Member(0))
:::



Group methods allow changes to the structure of the group

::: method
void AddMember(WsfPlatform aPlatform) void AddMember(WsfPlatformPart aPlatformPart)

Adds the given platform or platform part to the group
:::

::: method
void RemoveMember(WsfPlatform aPlatform) void RemoveMember(WsfPlatformPart aPlatformPart)

Adds the given platform or platform part to the group
:::

::: method
static WsfGroup CreateGroup(string aGroupName, string aGroupType)

Creates a group with the given name using a base group type or an already defined group as a template.
:::

::: method
static WsfGroup Group(string aGroupName)

Returns a WsfGroup object matching the given name
:::

::: method
static Array\<string\> Groups()

Returns a list of the names of all groups in the simulation.
:::





    group group_template WSF_GROUP
       *group properties go here*
    end_group

    PLATFORM.GroupJoin("dynamic_group", "group_template");
    Array<string> groups = WsfGroup.Groups();
    foreach(string groupName in groups)
    {
       WsfGroup group = WsfGroup.Group(groupName);
       if( group.IsValid() )
       {
          writeln( "Group: " + groupName );
          foreach( string memberName in group.Members() )
          {
             writeln( "     " + memberName );
          }
       }
    }