# wsfem_xmtr.rst.txt.md
orphan

:   



::: {.WsfEM_Xmtr .inherits .WsfEM_XmtrRcvr}
WsfEM_Xmtr is the transmitter of electromagnetic radiation associated with a `WsfEM_Interaction`{.interpreted-text role="class"} and defined by the `_.transmitter`{.interpreted-text role="command"} block.
:::

::: note
::: title
Note
:::

Use the `WsfSensor.Xmtr`{.interpreted-text role="method"}, `WsfComm.Xmtr`{.interpreted-text role="method"}, or `WsfEM_Interaction.Xmtr`{.interpreted-text role="method"} script methods to access the `WsfEM_Xmtr`{.interpreted-text role="class"}.
:::



::: method
double AlternateFrequency(int aId)

Returns the alternate EM frequency (Hz) for the specified Id (a.k.a index).
:::

::: method
double AlternateFrequencyCount()

Returns the alternate frequency count.
:::

::: method
Map\<int, double\> AlternateFrequencyMap()

Returns the current Id to EM frequency (Hz) Map, with the key value being the Id (a.k.a index) and value being the EM frequency (Hz).
:::

::: method
int AlternateFrequencyIndex()

Returns the index of the current alternate frequency.
:::

::: method
int BeamNumber()

Returns the identifying beam number of this transmitter, starting at 1.

::: note
::: title
Note
:::

BeamNumber is simply an alias for `WsfEM_XmtrRcvr.Index`{.interpreted-text role="method"}
:::
:::

::: method
double Power() double Power(double aFrequency)

Returns the average power (W) for the currently set frequency (Hz), or at the specified frequency (Hz) if supplied.
:::

::: method
void SetPower(double aPower) void SetPower(double aPower, double aFrequency)

Sets the peak power (W) for all frequencies, or at the specified frequency (Hz) if supplied.
:::

::: method
double PeakPower() double PeakPower(double aFrequency)

Returns the peak power (W) (i.e. duty cycle not accounted for) for the currently set frequency (Hz), or at the specified frequency (Hz) if supplied.
:::

::: method
double DutyCycle()

Returns the duty cycle.
:::

::: method
void SetDutyCycle(double aDutyCycle)

Sets the duty cycle. Will affect average power calculations as specified in `_.transmitter.duty_cycle`{.interpreted-text role="command"}.
:::

::: method
double PulseRepetitionInterval() double PulseRepetitionInterval(int aNum)

Returns the average PRI (sec) (i.e. aNum = 0) or PRI (sec) at specified PRI number \[1..N\] if supplied.
:::

::: method
void SetPulseRepetitionInterval(double aPRI) void SetPulseRepetitionInterval(double aPRI, int aNum)

Sets the average PRI (sec) (i.e. aNum = 0) or PRI (sec) at specified PRI number from \[1..N\] if supplied.
:::

::: method
double PulseRepetitionIntervalCount()

Returns the PRI count.
:::

::: method
Array\<double\> PulseRepetitionIntervals()

Returns the list of PRIs (sec).
:::

::: method
double PulseRepetitionFrequency() double PulseRepetitionFrequency(int aIndex)

Returns the average PRF (Hz) (i.e. aNum = 0) or PRF (Hz) at specified PRF number \[1..N\] if supplied.
:::

::: method
void SetPulseRepetitionFrequency(double aPRF) void SetPulseRepetitionFrequency(double aPRF, int aNum)

Sets the average PRF (Hz) (i.e. aNum = 0) or PRF (Hz) at specified PRF number from \[1..N\] if supplied.
:::

::: method
double PulseRepetitionFrequencyCount()

Returns the PRF count.
:::

::: method
Array\<double\> PulseRepetitionFrequencys()

Returns the list of PRFs (Hz).
:::

::: method
double PulseWidth()

Returns the pulse width (sec). If 0.0 then it is a continuous wave transmitter.
:::

::: method
void SetPulseWidth(double aPW)

Set the pulse width (sec). If 0.0 then it is a continuous wave transmitter.
:::

::: method
double PulseCompressionRatio()

Returns the compression ratio, representing the ratio of pulse widths before and after matched filtering.
:::

::: method
void SetPulseCompressionRatio(double aPulseRatio)

Sets the pulse compression ratio for this transmitter.
:::