orphan

:   

# WsfFormationDisbandCommand

::: {.WsfFormationDisbandCommand .inherits .WsfFormationCommand}
`WsfFormationDisbandCommand`{.interpreted-text role="class"} will destroy the formation to which it is assigned. The formation and all of its descendants will not longer exist. This does not change the state of any member platforms, which will continue in their present state.
:::

This command can only be assigned to top-level formations.

## Methods

::: method
WsfFormationDisbandCommand Construct()

Create a command that will disband the formation to which it is assigned.
:::
