orphan

:   

# simdis_interface

::: {.command block=""}
simdis_interface \... end_simdis_interface
:::

::: parsed-literal

[simdis_interface](#simdis_interface)

:   [file]() \<string\> [hit_icon]() \<string\> [kill_icon]() \<string\> [event_duration]() \<time-value\> [enable_beams]() \<boolean\> [default_beam_color]() \<string\> [beam_color]() \<sensor_type\> \<string\> [reference_lla]() \<latitude\> \<longitude\> \<altitude\> [define_fhn]() \<side\> \<FN\>

end_simdis_interface
:::

## Overview

Configures the SIMDIS interface for WSF. [SIMDIS](http://simdis.nrl.navy.mil) is a Navy developed simulation visualization. The simdis_interface allows the simulation to output to the SIMDIS ASCII Scenario Input (ASI) format.

## Connection Commands

::: command
file \<string\>

Causes the simulation to create and write to a SIMDIS ASI file. Setting this value to NULL will disable SIMDIS output.
:::

## Configuration Commands

::: command
file \<string\>

Specifies the asi output filename.
:::

::: command
hit_icon \<string\>

Sets the icon that will display in SIMDIS on a weapon hit. Setting this value to NULL will disable hit icons.

**Default** splash.
:::

::: command
kill_icon \<string\>

Sets the icon that will display in SIMDIS on a platform kill. Setting this value to NULL will disable kill icons.

**Default** expl-1.
:::

::: command
event_duration \<time-value\>

Sets the duration of the hit and kill icons in SIMDIS.

**Default** 1 second.
:::

::: command
enable_beams \<boolean\>

Enables or disables track beams in SIMDIS.

**Default** true.
:::

::: command
default_beam_color \<string\>

This sets the default track beam color in SIMDIS. These values may either be color names, or hexadecimal values (0xAABBGGRR)

**Default** yellow
:::

::: command
beam_color \<sensor_type\> \<string\>

This sets the color of track beams generated by a specific sensor type in SIMDIS. This will override the default_beam_color. The values may either be color names, or hexadecimal values (0xAABBGGRR). Beams with zero alpha will not be drawn (ie. 0x00000000).
:::

::: command
reference_lla \<latitude\> \<longitude\> `\<altitude\> <length-units>`{.interpreted-text role="argtype"}

This sets the camera starting location in SIMDIS.

**Default** 0n, 0e, 0m MSL.
:::

::: command
define_fhn \<side\> \<FN\>

This sets the friendly/hostile/neutral association with the given side. May be input multiple time for each side.

**Default** blue - friendly, red - hostile, other sides - neutral.
:::
