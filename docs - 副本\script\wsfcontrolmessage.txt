# wsfcontrolmessage.rst.txt.md
orphan

:   





::: method
string Function()

Return the command function.
:::

::: method
void SetFunction(string aFunction)

Set the function of the message.

::: note
::: title
Note
:::

This function is equivalent to setting the message `subtype<WsfMessage.SetSubType>`{.interpreted-text role="method"}.
:::
:::

::: method
WsfTrackId RequestId()

Return the track Id of the current request.
:::

::: method
void SetRequestId(WsfTrackId aTrackId)

Set request to the given track Id.
:::

::: method
string Resource()

Return the name of the resource that is associated with the command. A command may or may not have an associated resource depending on its function. The return value will be an empty string if there is no associated resource.
:::

::: method
void SetResource(string aResource)

Set the resource to be associated with the message
:::

::: method
WsfTrack Track()

Return the track that is associated with the command. A command may or may not have an associated track depending on its function. The return value will have a track Id that is null (Track().TrackId().IsNull() will be true) if there is no associated track.
:::

::: method
void SetTrack(WsfTrack aTrack)

Set the track to be associated with the message.
:::

::: method
WsfRoute Route()

Return the route that is associated with the command. A command may or may not have an associated route depending on its function.
:::

::: method
void SetRoute(WsfRoute aRoute) void SetRoute(string aRouteName)

Set the specified route to be associated with the message.
:::