orphan

:   

# Network Preferences - Warlock

![image](images/warlock_network_prefs.png)

The Network Preferences page has the settings for a multicast network that Warlock plugins can use to share information not handled by `DIS<dis_interface>`{.interpreted-text role="command"} or `XIO<xio_interface>`{.interpreted-text role="command"}.

-   Address - This is the multicast address. All connected sims should use the same address.
-   Interface - This is the address of the local port. This list is set by scanning network interfaces. The \'+\' button adds a new interface that was not detected automatically.
-   Port - This is the port used for sending and receiving data. All connected sims should use the same port.

## Affected Features

..include:: wk_network_extension.txt
