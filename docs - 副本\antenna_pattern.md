orphan

:   

# antenna_pattern

::: {.command block=""}
antenna_pattern \... end_antenna_pattern
:::

::: parsed-literal

antenna_pattern *\<pattern-name\>*

:   

    `<pattern-type-name> <antenna_pattern.antenna_pattern_types>`{.interpreted-text role="ref"}

    :   [Common Commands](#common-commands) \... [Available Antenna Patterns](#available-antenna-patterns) Commands \...

end_antenna_pattern
:::

## Overview

antenna_pattern is used in `_.transmitter`{.interpreted-text role="command"} and `_.receiver`{.interpreted-text role="command"} commands to define the gain of an antenna for communication and sensor devices.

**\<pattern-name\>** Specifies the name of the antenna pattern.

::: {#antenna_pattern.antenna_pattern_types}
**\<pattern-type-name\>** Specifies one of the [Available Antenna Patterns](#available-antenna-patterns):
:::

## Common Commands

The following commands can be used in any of the antenna pattern definitions:

::: command
minimum_gain \<db-ratio-value\>

The minimum gain that will be returned.

**Default:** -300 db
:::

::: command
gain_adjustment \<db-ratio-value\>

An adjustment factor to be applied to the raw gain. This is especially useful if one wants to reuse a pattern that has been defined by a file (such as the `antenna_pattern.pattern_table`{.interpreted-text role="ref"}) and simply scale the definition.

**Default:** 1.0 (no adjustment)

::: note
::: title
Note
:::

This [gain_adjustment]() AND [gain_adjustment_table]() can be used together. The results are additive in logarithmic space (multiplicative in linear space).
:::
:::

::: {.command block=""}
gain_adjustment_table \... end_gain_adjustment_table

This command provides the means to define a frequency-dependent adjustment to the gain. The points define a curve on a plot whose x-axis is the log_10 of the frequency and the y-axis is adjustment factor in dB. Linear interpolation is used to derive the values for intermediate frequencies. Signals whose frequencies are outside the range of the table use the value from the appropriate endpoint (i.e., extrapolation is not performed).

The format of the table is:

::: parsed-literal
gain_adjustment_table frequency \<frequency-value\> `\<db-ratio-1> <db-ratio-value>`{.interpreted-text role="argtype"} frequency \<frequency-value\> `\<db-ratio-2> <db-ratio-value>`{.interpreted-text role="argtype"} \... frequency \<frequency-value\> `\<db-ratio-n> <db-ratio-value>`{.interpreted-text role="argtype"} end_gain_adjustment_table
:::

The following rules must be observed:

-   The entries must be in order monotonically increasing frequency.
-   There must be at least two entries, except that if no entries are provided then it is treated as though the table is not provided.

::: note
::: title
Note
:::

This [gain_adjustment]() AND [gain_adjustment_table]() can be used together. The results are additive in logarithmic space (multiplicative in linear space).
:::
:::

## Available Antenna Patterns

### Azimuth/Elevation Table {#antenna_pattern.pattern_table}

::: parsed-literal

antenna_pattern *\<pattern-name\>*

:   

    pattern_table

    :   \# `Azimuth-elevation_Table_Definition`{.interpreted-text role="ref"}

        [azimuth_beamwidth]() \<angle-value\> [elevation_beamwidth]() \<angle-value\>

        \# [Common Commands](#common-commands)

        [minimum_gain]() \<db-ratio-value\>

        [gain_adjustment]() \<db-ratio-value\> [gain_adjustment_table]() \... end_gain_adjustment_table

end_antenna_pattern
:::

Defines a pattern using any of the standard `Azimuth-elevation_Table_Definition`{.interpreted-text role="ref"} formats.

::: block
antenna_pattern.pattern_table
:::

::: command
azimuth_beamwidth \<angle-value\>

Defines the width of the beam in azimuth.

**Default:** none - must be provided
:::

::: command
elevation_beamwidth \<angle-value\>

Defines the width of the beam in elevation.

**Default:** none - must be provided
:::

### Uniform or Constant Pattern {#antenna_pattern.uniform_pattern}

::: parsed-literal

[antenna_pattern](#antenna_pattern) *\<pattern-name\>*

:   

    uniform_pattern

    :   [peak_gain]() \<db-ratio-value\> [azimuth_beamwidth]() \<angle-value\> [elevation_beamwidth]() \<angle-value\>

        \# [Common Commands](#common-commands)

        [minimum_gain]() \<db-ratio-value\> [gain_adjustment]() \<db-ratio-value\> [gain_adjustment_table]() \... end_gain_adjustment_table

end_antenna_pattern
:::

Defines a pattern whose gain is the *peak_gain* within the specified beamwidth limits and *minimum_gain* everywhere else.

::: block
antenna_pattern.uniform_pattern
:::

::: command
peak_gain \<db-ratio-value\>

Defines the peak gain of the antenna referenced to a perfect isotropic antenna.

**Default:** 1 db
:::

::: command
azimuth_beamwidth \<angle-value\>

Defines the width of the beam in azimuth.

**Default:** 180 deg
:::

::: command
elevation_beamwidth \<angle-value\>

Defines the width of the beam in elevation.

**Default:** 90 deg
:::

### Circular sine(x)/x Pattern {#antenna_pattern.circular_pattern}

::: parsed-literal

antenna_pattern *\<pattern-name\>*

:   

    circular_pattern

    :   [peak_gain]() \<db-ratio-value\> [beamwidth]() \<angle-value\>

        \# [Common Commands](#common-commands)

        [minimum_gain]() \<db-ratio-value\> [gain_adjustment]() \<db-ratio-value\> [gain_adjustment_table]() \... end_gain_adjustment_table

end_antenna_pattern
:::

Defines a sine(x)/x pattern with circular symmetry.

::: block
antenna_pattern.circular_pattern
:::

::: command
peak_gain \<db-ratio-value\>

Defines the peak gain of the antenna referenced to a perfect isotropic antenna.

**Default:** 1 db
:::

::: command
beamwidth \<angle-value\>

Defines the half-power beamwidth (The angle subtended by points at which the gain becomes one-half the peak gain).
:::

### Rectangular sine(x)/x Pattern {#antenna_pattern.rectangular_pattern}

::: parsed-literal

antenna_pattern *\<pattern-name\>*

:   

    rectangular_pattern

    :   [peak_gain]() \<db-ratio-value\> [azimuth_beamwidth]() \<angle-value\> [elevation_beamwidth]() \<angle-value\>

        \# [Common Commands](#common-commands)

        [minimum_gain]() \<db-ratio-value\> [gain_adjustment]() \<db-ratio-value\> [gain_adjustment_table]() \... end_gain_adjustment_table

end_antenna_pattern
:::

Defines a sine(x)/x pattern where the azimuth and elevation beamwidths do not have to be the same.

::: block
antenna_pattern.rectangular_pattern
:::

::: command
peak_gain \<db-ratio-value\>

Defines the peak gain of the antenna referenced to a perfect isotropic antenna.

**Default:** 1 db
:::

::: command
azimuth_beamwidth \<angle-value\>

Defines the half-power beamwidth in azimuth (The angle subtended by points at which the gain becomes one-half the peak gain).
:::

::: command
elevation_beamwidth \<angle-value\>

Defines the half-power beamwidth in elevation (The angle subtended by points at which the gain becomes one-half the peak gain).
:::

### Cosecant Pattern {#antenna_pattern.cosecant_squared_pattern}

::: parsed-literal

antenna_pattern *\<pattern-name\>*

:   

    cosecant_squared_pattern

    :   [peak_gain]() \<db-ratio-value\> [azimuth_beamwidth]() \<angle-value\> [elevation_beamwidth]() \<angle-value\> [minimum_elevation_for_peak_gain]() \<angle-value\> [elevation_of_peak/csc2_boundary]() \<angle-value\> [maximum_elevation_for_csc2]() \<angle-value\>

        \# [Common Commands](#common-commands)

        [minimum_gain]() \<db-ratio-value\> [gain_adjustment]() \<db-ratio-value\> [gain_adjustment_table]() \... end_gain_adjustment_table

end_antenna_pattern
:::

Defines a antenna pattern that will:

> -   Use a sin x/x pattern for elevation angles less than [minimum_elevation_for_peak_gain]().
> -   Use the peak gain from \[ [minimum_elevation_for_peak_gain](), [elevation_of_peak/csc2_boundary]() \]
> -   Use a csc\^2 pattern from \[ [elevation_of_peak/csc2_boundary](), [maximum_elevation_for_csc2]() \]
> -   Use a sin x/x pattern for angles above [maximum_elevation_for_csc2]().

::: block
antenna_pattern.cosecant_squared_pattern
:::

::: command
peak_gain \<db-ratio-value\>

Defines the peak gain of the antenna referenced to a perfect isotropic antenna.

**Default:** 1.0 db
:::

::: command
azimuth_beamwidth \<angle-value\>

Defines the half-power beamwidth in azimuth (The angle subtended by points at which the gain becomes one-half the peak gain). This is used to the determine the azimuth-dependent portion of the gain.
:::

::: command
elevation_beamwidth \<angle-value\>

Defines the half-power beamwidth in elevation (The angle subtended by points at which the gain becomes one-half the peak gain). This is used to determine the elevation-dependent port of the gain when the elevation angle is above or below the cosecant_squared region.
:::

::: command
minimum_elevation_for_peak_gain \<angle-value\>

Defines the minimum elevation angle for the [peak_gain]() value.

::: note
::: title
Note
:::

A non-zero value result in issues for radars which slew or scan using a non-peak gain at 0 degrees, i.e. the elevation cueing/scanning angle.
:::
:::

::: command
elevation_of_peak/csc2_boundary \<angle-value\>

Defines the elevation angle for the peak cosecant-squared boundary value.
:::

::: command
maximum_elevation_for_csc2 \<angle-value\>

Defines the maximum elevation angle for the [peak_gain]() value.
:::
