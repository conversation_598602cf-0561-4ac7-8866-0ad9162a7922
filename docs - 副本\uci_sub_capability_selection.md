orphan

:   

# UCI_SubCapabilitySelection

## Overview {#overview .UCI_SubCapabilitySelection}

This type indicates the subcapability and its details being commanded.

## Static Methods

::: method
static UCI_SubCapabilitySelection Construct(UCI_SubCapabilityDetails capabilityDetails)

Creates a [UCI_SubCapabilitySelection](#uci_subcapabilityselection) with the new details for the capability.
:::

## Methods

::: method
void PushBack(UCI_SubCapabilityDetails capabilityDetails)

Adds capabilityDetails to the [UCI_SubCapabilitySelection](#uci_subcapabilityselection).
:::
