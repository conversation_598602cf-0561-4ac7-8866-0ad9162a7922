# script_demos.rst.txt.md
orphan

:   

::: demo
script_demos
:::



| This is a basic script example that demonstrates some the features
| of the scripting language and how to use scripting in an WSF component.
| 
| The \'Comprehensive Script Demo\' (script_demo_2.txt) builds upon this example.
| 
| In this scenario we have:
| a) an air platform.
| 
| The air platform will fly a route and print its configuration using a script.



| This is a script example that demonstrates many of the features
| of the scripting language. It is not a lesson in tactics.
| 
| In this scenario we have:
| a) an air platform with a \'perfect\' ESM device which gives the precise
|    location of an emitter.
| b) another air platform that is acting as a strike platform.
| c) a ground platform that is an enemy threat radar.
| 
| The air platform will detect the threat radar and send a track to the striker
| which will drop bombs on it. The route is already planned for the striker.



| This example demonstrates the syntax of the relative geometry method.



| Demonstrates the various mover script commands available to users.



| This is a script example that demonstrates many of the features
| of the scripting language. It is not a lesson in tactics.
| 
| It is similar to Script Demo 2, however, it incorporates logic to \"fly to\" a
| target location and bomb the target. The route to the target is not
| hard coded as in Script Demo 2.
| 
| In this scenario we have:
| a) an air platform with a \'perfect\' ESM device which gives the precise
|    location of an emitter.
| b) another air platform that is acting as a strike platform.
| c) a ground platform that is an enemy threat radar which turns on after 5
|    min.
| d) In addition, a processor is added to the weapon to print information about
|    the trajectory on the way to the target.
| 
| The air platform will detect the threat radar and send a track to the striker
| which will drop bombs on it.