# uci_strike_weapon_command.rst.txt.md
orphan

:   





This type is used to set the command type for `UCI_StrikeCapabilityCommand`{.interpreted-text role="class"}.



::: method
static UCI_StrikeWeaponCommand AssignDMPI_ID(UCI_DMPI_ID aDMPI_ID)

Returns a \_UCI_StrikeWeaponCommand with aDMPI_ID assigned.
:::

::: method
static UCI_StrikeWeaponCommand SelectForJettison(bool aSelected)

Returns a \_UCI_StrikeWeaponCommand with the SelectForJettison set to the selected value.
:::

::: method
static UCI_StrikeWeaponCommand SelectForKeyLoad(bool aSelected)

Returns a \_UCI_StrikeWeaponCommand with the SelectForKeyLoad set to the selected value.
:::

::: method
static UCI_StrikeWeaponCommand SelectForRelease(bool aSelected)

Returns a \_UCI_StrikeWeaponCommand with the SelectForRelease set to the selected value.
:::

::: method
static UCI_StrikeWeaponCommand WeaponArm(bool aArmed)

Returns a \_UCI_StrikeWeaponCommand with the WeaponArm set to the selected value.
:::