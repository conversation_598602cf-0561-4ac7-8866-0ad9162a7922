# aircraft_signature_parameters.rst.txt.md
orphan

:   





::: {.command block=""}
aircraft_signature_parameters \... aircraft_signature_parameters

::: parsed-literal

aircraft_signature_parameters

:   // [Required Commands](#required-commands) [side]() \... // [Optional Commands](#optional-commands) [fuselage_frontal_area]() \... [wing_area]() \... [tail_area]() \... [engine_inlet_area]() \... [engine_exit_area]() \... [sweep_angle]() \... [signature_reduction_factor_overall]() \... [signature_reduction_factor_nose]() \... [nose_signature_reduction_angle]() \...

end_aircraft_signature_parameters
:::
:::



::: command
side \[red ; blue\]

Specifies the side for which the aircraft signature parameters apply. The available options are \'red\' and \'blue\'.

The [side]() command determines the default signature parameters. These can be overwritten with the [Optional Commands](#optional-commands).

For example:

    # An aircraft of side blue will have the default signature parameters, except for the sweep angle.
    aircraft_signature_parameters
       side          blue
       sweep_angle   40 degrees
    aircraft_signature_parameters
:::



The commands in this section are used to determine the expected detection range of the target aircraft ($R_d$). An effective cross sectional area is computed ($A_{eff}$), which is used in an empirically-determined equation to obtain the detection range. The equation used is fairly simplistic and is used in the absence of more accurate aircraft signatures. Future releases will include more sophisticated mechanisms to accurately define radar signatures. Each command corresponds to a variable in the equations below. The following table defines the mapping between equation variable names and command names (or a description if no command exists):

  command/description                      variable
  ---------------------------------------- -----------
  [fuselage_frontal_area]()                $A_f$
  [wing_area]()                            $A_w$
  [tail_area]()                            $A_t$
  [engine_inlet_area]()                    $A_i$
  [engine_exit_area]()                     $A_e$
  [sweep_angle]()                          $\omega$
  [signature_reduction_factor_overall]()   $f_o$
  [signature_reduction_factor_nose]()      $f_n$
  azimuth angle                            $\phi$
  elevation angle                          $\theta$
  range constant                           $k$
  effective cross sectional area           $A_{eff}$
  expected detection range                 $R_d$

The expected detection range is computed using Equation `detection_range`{.interpreted-text role="eq"}. The range constant $k$ is determined such that a $10$ $m^2$ target produces a detection range of $200$ $km$. This results in a $k$ value of $1.6e20$ $m^2$.

[$$R_d = (k \cdot A_{eff})^{1/4}$$]{label="detection_range"}

The effective radar cross section $A_{eff}$ is defined by the following equation:

[$$A_{eff} = f_o \cdot f_n \cdot [A_f + A_w \sin{\theta} + A_t \sin{\theta} \sin{\phi} + A_i \cos({\max({\theta,\phi})}) + A_e \cos({\max({\theta,\pi - \phi})}) + A_w \cos{\theta} \cos^4({\phi - \omega}) ]$$]{label="a_eff"}



::: command
fuselage_frontal_area \<area-value\>

The frontal area of the aircraft fuselage (projection on the y-z plane) to be used when determining the radar cross section. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 3.0 m\^2
:::

::: command
wing_area \<area-value\>

The wing area of the aircraft to be used when determining the radar cross section. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 30.0 m\^2
:::

::: command
tail_area \<area-value\>

The area of the aircraft\'s vertical tail to be used when determining the radar cross section. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 4.0 m\^2
:::

::: command
engine_inlet_area \<area-value\>

The inlet area of the aircraft\'s engine to be used when determining the radar cross section. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 2.0 m\^2
:::

::: command
engine_exit_area \<area-value\>

The exit area of the aircraft\'s engine to be used when determining the radar cross section. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 2.0 m\^2
:::

::: command
sweep_angle \<angle-value\>

The sweep angle of the aircraft\'s wing to be used when determining the radar cross section. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 45 degrees
:::



::: command
signature_reduction_factor_overall \<real\>

The overall reduction factor of the signature. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 1.0
:::

::: command
signature_reduction_factor_nose \<real\>

The reduction factor of the signature due to the aircraft\'s nose shape. This factor is only used if [nose_signature_reduction_angle]() is more than the azimuth and more than the elevation. See Equation `a_eff`{.interpreted-text role="eq"}.

Default: 1.0
:::

::: command
nose_signature_reduction_angle \<angle-value\>

The nose angle used to determine [signature_reduction_factor_nose](). If [nose_signature_reduction_angle]() is more than the azimuth and more than the elevation, then the effective radar cross section will be multiplied by [signature_reduction_factor_nose]().

Default: 45 degrees
:::