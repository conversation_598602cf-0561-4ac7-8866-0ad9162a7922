orphan

:   

# launch_computer

See `Predefined_Launch_Computer_Types`{.interpreted-text role="ref"}

Script Class: `WsfLaunchComputer`{.interpreted-text role="class"}

::: {.command block=""}
launch_computer
:::

::: parsed-literal

launch_computer \<name\> \<base-type\>

:   [debug]() [no_debug]() \... `WSF_LAUNCH_COMPUTER`{.interpreted-text role="model"} Commands \...

end_launch_computer
:::

**\<name\>**

:   Name of the new launch computer to be created.

**\<base-type\>**

:   Name of an existing user-defined launch computer type, `WSF_LAUNCH_COMPUTER`{.interpreted-text role="model"} or one of the `Predefined_Launch_Computer_Types`{.interpreted-text role="ref"} whose definition will form initial definition of the new type.

## Overview

This command defines a launch computer type which can be used by a `weapon`{.interpreted-text role="command"} to decide if the conditions are favorable to a specific weapon - target engagement, and if so some information to guide employment of the weapon.

During an integrated battle, when many resources may potentially be used to strike the same target, one key consideration is an estimate of how quickly a given resource can strike. So a primary function that each type of launch computer must provide is an estimate of time-to-intercept a target track. If the engagement conditions are completely unfavorable, a huge value is returned, indicating that no engagement is advised. It would then be up to the battle manager to intelligently choose between resources that provide an acceptably rapid time-to-intercept, with an acceptable probability of success.

::: note
::: title
Note
:::

This probably should be called a \'weapon_computer\' or \'weapon_engagement_computer\' because \'launch_computer\' implies the existence of a launched entity. This would not be true for things like directed energy weapons.
:::

## Commands

::: command
debug

Enables debug printouts during run time.
:::

::: command
no_debug

Disables debug printouts during run time.
:::
