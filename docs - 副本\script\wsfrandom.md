orphan

:   

# WsfRandom

::: WsfRandom
Every script has access to an instance of **WsfRandom** named **RANDOM**. To use a method from this class within a script, simply make method calls on the **RANDOM** variable.
:::

For instance:

    RANDOM.Uniform(3.0, 5.0);

::: note
::: title
Note
:::

The following random functions use the same random number generator as the simulation\'s main random number generator and as such may affect repeatability. To use a separate, repeatable random number generator see `Math`{.interpreted-text role="class"} or `WsfRandomVariable`{.interpreted-text role="class"}.
:::

## Methods

::: note
::: title
Note
:::

Multiple versions of each method with different numbers of arguments allow for default values of the distribution parameters to be used when none are specified.
:::

::: method
double Uniform() double Uniform(double aMinValue, double aMaxValue)

Return a random number from a uniform distribution within the specified range.

-   **aMinValue** Minimum value of range, default is 0.0.
-   **aMaxValue** Maximum value of range, default is 1.0.
:::

::: method
bool <PERSON>() bool <PERSON>(double aP)

Returns a random true or false result using a <PERSON><PERSON><PERSON> distribution.

-   **aP** The probability of returning a true result, default is 0.5.
:::

::: method
int Binomial() int Binomial(int aT) int Binomial(int aT, double aP)

Returns a random number using a Binomial distribution.

-   **aT** The number of trials, default is 1.
-   **aP** The probability of a trial generating true, default is 0.5.
:::

::: method
int NegativeBinomial() int NegativeBinomial(int aK) int NegativeBinomial(int aK, double aP)

Returns a random number using a Negative Binomial distribution.

-   **aK** The number of desired true outcomes, default is 1.
-   **aP** The probability of a trial generating true, default is 0.5.
:::

::: method
int Geometric() int Geometric(double aP)

Returns a random number using a Geometric distribution, equivalent to the Negative Binomial distribution with aK = 1.

-   **aP** The probability of a trial generating true, default is 0.5.
:::

::: method
int Poisson() int Poisson(double aMean)

Returns a random number according to a Poisson distribution.

-   **aMean** The mean number of occurrences of the event, default is 1.0.
:::

::: method
double Exponential() double Exponential(double aLambda)

Returns a random number according to an exponential distribution. This function is useful for drawing a random number associated with the waiting times between independent Poisson-random events (such as in bank queuing or radioactive decay).

-   **aLambda** Defines the rate of events per unit, default is 1.0.
:::

::: method
double Gamma() double Gamma(double aAlpha) double Gamma(double aAlpha, double aBeta)

Returns a random number using a Gamma distribution.

-   **aAlpha** The shape parameter, default is 1.0.
-   **aBeta** The scale parameter, default is 1.0.
:::

::: method
double Weibull() double Weibull(double aA) double Weibull(double aA, double aB)

Returns a random number using a Weibull distribution.

-   **aA** The shape parameter, default is 1.0.
-   **aB** The scale parameter, default is 1.0.
:::

::: method
double ExtremeValue() double ExtremeValue(double aA) double ExtremeValue(double aA, double aB)

Returns a random number using an ExtremeValue distribution.

-   **aA** The location parameter, default is 1.0.
-   **aB** The scale parameter, default is 0.0.
:::

::: method
double Gaussian() double Gaussian(double aMean) double Gaussian(double aMean, double aStdDev) double Normal() double Normal(double aMean) double Normal(double aMean, double aStdDev)

Return a random number from a normal (or Gaussian) distribution with the specified mean and standard deviation.

-   **aMean** The mean distribution parameter, default is 0.0.
-   **aStdDev** The standard deviation parameter, default is 1.0.
:::

::: method
double LogNormal() double LogNormal(double aM) double LogNormal(double aM, double aS)

Return a random number from a normal distribution with the specified mean and standard deviation.

-   **aM** The log-mean distribution parameter, default is 0.0.
-   **aS** The log-deviation distribution parameter, default is 1.0.
:::

::: method
double ChiSquared() double ChiSquared(double aN)

Returns a random number using a ChiSquared distribution.

-   **aN** The degrees of freedom parameter, default is 1.0.
:::

::: method
double Cauchy() double Cauchy(double aA) double Cauchy(double aA, double aB)

Returns a random number using a Cauchy distribution.

-   **aA** The location parameter, default is 0.0.
-   **aB** The scale parameter, default is 1.0.
:::

::: method
double FisherF() double FisherF(double aM) double FisherF(double aM, double aN)

Returns a random number using a FisherF distribtution.

-   **aM** The first degree of freedom parameter, default is 1.0.
-   **aN** The second degree of freedom parameter, default is 1.0.
:::

::: method
double StudentT() double StudentT(double aN)

Returns a random number using a StudentT distribution with the specified degrees of freedom.

-   **aN** The degrees of freedom, default is 1
:::

::: method
double Rayleigh(double aRadius)

Returns a random number using a Rayleigh distribution using the specified radius.
:::
