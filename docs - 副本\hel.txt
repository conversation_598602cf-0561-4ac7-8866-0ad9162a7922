# hel.rst.txt.md
orphan

:   

::: demo
hel
:::

| HEL atmospheric propagation library, intersection mesh library, and HEL demos.



| Currently the atmospheric data must be \"included\" in the WSF input stream in order to
| utilize the HEL models. One does this by inserting the following line into one\'s input file:
| 
| include_once \$(MY_PATH)%/coefficients.txt,
| 
| where MY_PATH is the path to the location of the coefficients.txt file, for example:
| 
| define_path_variable MY_PATH c:swdevwsf_uhel_demoatmosphere.
| 
| For detailed information about supported atmosphere models and wavelengths,
| see either the ./atmosphere/AAA_README.txt file or the WSF documentation command entry for \"fluence_model.\"



| Currently the only two intersection meshes defined are for a RED_ADV_FIGHTER_2, and
| short range air to air IR missile. These are to be included on target platforms, using the intersection
| processor (see WSF documentation). It is hoped that more meshes will be added to this library
| in the future.
| 
| Note: If one cannot use an intersection mesh and intersection processor, intersections
| will be computed generically, either as a head-on incidence, or using the incidence
| angle to compute a cosine roll off.



| Megawatt-class ground-based HEL engages and destroys a hypersonic vehicle passing overhead.
| This demo includes a scripted time delay, meant to simulate the HEL\'s locking onto the target



| BLUE_MULTIROLE_FIGHTER_1EF W/HEL engages an air target (red_adv_fighter_2) A HEL-equipped blue player chases a red
| player, intending to shoot the IRST (region on the red_adv_fighter_2 intersection mesh).
| The script continually checks the target to see if the IRST is occluded.
| Initially it is occluded. so there is no shot until the red player maneuvers
| and the IRST becomes visible.



| This is a nearly identical test case with the above. The difference is that we are
| using a WSF_BEAM_DIRECTOR and WSF_CUED_LASER_WEAPON instead of the WSF_LASER_WEAPON of
| the more simple example above. Intersection mesh tests are done transparently by the
| beam director model.



| This demo is still in development. It is meant to set up static test cases for
| each viable HEL mission.