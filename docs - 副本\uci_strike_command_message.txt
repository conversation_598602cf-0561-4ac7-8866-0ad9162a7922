# uci_strike_command_message.rst.txt.md
orphan

:   



::: {.UCI_StrikeCommandMessage .inherits .UCI_Message}
:::



This message allows the user to command a weapon via UCI messaging.



::: method
static UCI_StrikeCommandMessage Construct(UCI_StrikeCommand aStrikeCommand)

Returns a UCI_StrikeCommandMessage with the given UCI_StrikeCommand.
:::



::: method
void PushBack(UCI_StrikeCommand aStrikeCommand)

Adds a command to the the \_UCI_StrikeCommandMessage.
:::

::: method
UCI_CommandId CommandUUID(int aIndex)

Returns the command ID at the given index. If the index is less than 0 or greater than the number of capability commands in the message, it returns null.
:::