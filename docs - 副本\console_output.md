orphan

:   

# console_output

::: {.command block=""}
console_output \... end_console_output

::: parsed-literal

console_output

:   [file]() \<file-name\> \... end_file

    [preset]() \[ none \| low \| medium \| high \]

    [fatal]() \<boolean-value\> [error]() \<boolean-value\> [warning]() \<boolean-value\> [info]() \<boolean-value\> [debug]() \<boolean-value\> [developer]() \<boolean-value\> [type]() \<string\> \<boolean-value\>

    [single_line]() \<boolean-value\> [force_flushing]() \<boolean-value\>

end_console_output
:::
:::

## Overview

The `console_output`{.interpreted-text role="command"} block allows the user to specify the content and format of the console output.

The following types of messages may be displayed in the console:

  ----------- ------------------------------------------------------------------------------------------------------------------------------
  fatal       Indicates that the simulation has encountered an unrecoverable error.
  error       Indicates that an error has occurred. The simulation may continue to run, but any results after this point may not be valid.
  warning     Indicates that something unexpected has occurred.
  info        General information about the simulation\'s state.
  debug       Any information enabled by debug flags.
  developer   Information intended for developers.
  ----------- ------------------------------------------------------------------------------------------------------------------------------

  : **Console Output Types**

Additional types may be specified, but require WSF plugins written specifically to take advantage of this feature.

Each of the different message types can be written via script using variations of `writeln<__BUILTIN__.writeln>`{.interpreted-text role="method"}.

::: note
::: title
Note
:::

These settings only take effect after the entire scenario has been loaded. Until then, the console uses the default settings.
:::

## Commands

::: command
file \<file-name\> \... end_file

Specifies a file to write output to. As many different files with different settings may be specified as needed. The file block may contain any of the commands from the [console_output](#console_output) block, except for other file blocks.
:::

::: command
preset \[ none \| low \| medium \| high \]

Overrides the current output types with the preset.

-   none = (no output)
-   low = fatal, error, warning
-   medium = fatal, error, warning, info
-   high = fatal, error, warning, info, debug
:::

::: command
fatal \<boolean-value\>

Enables or disables fatal output. Fatal output is prefixed with `***** FATAL:`.

**Default** on
:::

::: command
error \<boolean-value\>

Enables or disables error output. Error output is prefixed with `***** ERROR:`.

**Default** on
:::

::: command
warning \<boolean-value\>

Enables or disables warning output. Warning output is prefixed with `***** WARNING:`.

**Default** on
:::

::: command
info \<boolean-value\>

Enables or disables info output. Info output has no prefix.

**Default** on
:::

::: command
debug \<boolean-value\>

Enables or disables debug output. Debug output has no prefix.

**Default** on

::: note
::: title
Note
:::

In order for script debug messages to be output, `_.script_commands.script_debug_writes`{.interpreted-text role="command"} must be enabled.
:::
:::

::: command
developer \<boolean-value\>

Enables or disables developer output. Developer output is prefixed with `***** DEVELOPER:`.

**Default** off
:::

::: command
type \<string\> \<boolean-value\>

Enables or disables the output of a custom type. Custom type output has no prefix, unless it is paired with a type that has one. In general, this command is only useful if a WSF plugin is written specifically to take advantage of it.

**Default** off
:::

::: command
single_line \<boolean-value\>

Sets the output to single-line or multi-line format. In multi-line format, each piece of information is displayed on a separate line. Nested information is indented from the previous line. Example:

    ***** WARNING: Failed FireSalvo for track.
        T = 700
        Platform: sam-1
        Weapon: sam
        Track Id: sam-1.2

Single-line format is intended to be as compact as possible. All related information is shown on the same line and separated by punctuation. Example:

    ***** WARNING: Failed FireSalvo for track. T = 700; Platform: sam-1; Weapon: sam; Track Id: sam-1.2;

**Default** off (multi-line)
:::

::: command
force_flushing \<boolean-value\>

Enables or disables automatic flushing of output to the console after every log entry.

Whenever logging output is occurring at a very fast pace, the I/O flushing at the end of every log entry can cause a significant bottleneck to overall performance of the application.

Regardless of the value of this field, all fatal, error, and warning messages will always be flushed immediately.

**Default** on

::: note
::: title
Note
:::

If forced flushing is turned off, and the executable crashes, it\'s possible for some logged entries to be lost and not appear in the console/file.
:::
:::
