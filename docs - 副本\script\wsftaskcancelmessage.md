orphan

:   

# WsfTaskCancelMessage

::: {.WsfTaskCancelMessage .inherits .WsfMessage}
`WsfTaskCancelMessage`{.interpreted-text role="class"} is sent by `WsfTaskManager::CancelTask <WsfTaskManager.CancelTask>`{.interpreted-text role="method"} method when a task assignment is canceled. It should normally be processed by the receiving task manager but can be examined by a script method that handles messages.
:::

## Methods

::: method
WsfPlatform Assigner()

Returns the platform that assigned the task.
:::

::: method
string AssignerName()

Returns the name of the platform that assigned the task.
:::

::: method
WsfPlatform Assignee()

Returns the platform that was assigned the task.
:::

::: method
string AssigneeName()

Returns the name of the platform that was assigned the task.
:::

::: method
WsfTrackId TrackId()

Returns the track Id associated with the task.
:::

::: method
string TaskType()

Returns the task type.
:::

::: method
string ResourceName()

Returns the name of the resource that was specified to be used in the task.
:::
