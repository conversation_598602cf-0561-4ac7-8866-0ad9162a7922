orphan

:   

# WsfSixDOF_FormationCommandConstraint

## Creation Methods {#creation-methods .WsfSixDOF_FormationCommandConstraint}

::: method
WsfSixDOF_FormationCommandConstraint AT_TIME(double aSimTime)

Create an absolute time command constraint. A `WsfSixDOF_FormationCommand`{.interpreted-text role="class"} given the constraint that results from this method will begin executing at the given simulation time.
:::

::: method
WsfSixDOF_FormationCommandConstraint AT_RELATIVE_TIME(double aTimeToStart)

Create a relative time command constraint. A `WsfSixDOF_FormationCommand`{.interpreted-text role="class"} given the constraint that results from this method will begin executing after the given number of seconds past the time at which the command is assigned to a formation.
:::

## Other Methods

::: method
double GetConstraintTime()

Return the time at which the constraint will be satisfied. If this constraint was created with AT_TIME(), this will be the absolute time. If this constraint was created with AT_RELATIVE_TIME(), this will return the relative time if the command has not been assigned, and the absolute time if the command has been assigned.
:::

::: method
double SetConstraintTime()

Set the absolute time for the constraint. If this constraint was created with AT_RELATIVE_TIME(), after calling this method, it will be as if it had been created with AT_TIME().
:::
