orphan

:   

# UCI_POST_CapabilityCommand

## Overview {#overview .UCI_POST_CapabilityCommand}

This command type will create new activities for the controlled `WSF_IRST_SENSOR`{.interpreted-text role="model"}.

## Static Methods

::: method
static UCI_POST_CapabilityCommand Construct(UCI_CapabilityId commandedCapabilityId)

This method will create an instance of a [UCI_POST_CapabilityCommand](#uci_post_capabilitycommand) that, if approved, will create a new activity for the given capability to accomplish. The CommandID will be randomly generated upon creation.
:::

## Methods

::: method
void SetPointing(UCI_Pointing pointing)

This method will set the indicated position or location that the sensor will be commanded to point to.
:::
