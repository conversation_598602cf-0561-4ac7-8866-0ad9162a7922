# rv_routes.rst.txt.md
orphan

:   



Platforms\' routes may be optionally communicated from AFSIM in the `event_pipe`{.interpreted-text role="command"}. Global routes will not be written to the file.

Enabling routes in the `platform options<../mystic_platform_options>`{.interpreted-text role="doc"} will display the current route. Routes may change dynamically over the course of the playback.



Right clicking on a platform will provide an option `Show Route Information` which will display a summary of the platform\'s route\'s waypoints.

