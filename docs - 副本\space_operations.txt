# space_operations.rst.txt.md
orphan

:   

::: demo
space_operations
:::

| This directory contains demos that illustrate various space operations.
| The following top-level startup files demonstrate the various operations as indicated:



| A demonstration of generalized attitude control, which allows pointing
| the satellite or oriented platform part such as a sensor or comm device,
| along a particular vector to a target (e.g., another platform, nadir, the sun, etc).
| A 3D satellite model is used as a visual reference. A comm device, sensor,
| and solar panel (`WSF_VISUAL_PART<visual_part>`{.interpreted-text role="command"}) are placed on an AFSIM platform
| in the same orientations as they appear on the 3D model.
| Using tethering and pointing vectors in results
| visualization one can see that the satellite is pointing
| its sensor panels, comm device, or sensor, respectively, as commanded.



| Demonstrates how clouds can disrupt line of sight for various sensors.
| A representation of the Space Surveillance Network is modeled. Telescopes cannot make
| detections of satellites when obscured by clouds, but radars can.
| This demo uses the AFSIM `noise_cloud`{.interpreted-text role="command"} feature.



| This scenario demonstrates an avoidance maneuver. A commander on the ground
| tracks spacecraft for possible conjunctions with a satellite of interest,
| sending a warning message when a conjunction is predicted.
| This demonstrates both the conjunction assessment processor, and the
| ability to script spacecraft maneuvers.



| This scenario demonstrates a network of sensors tracking objects in space,
| all while a commander on the ground is analyzing the resulting tracks for
| the possibility of conjunctions. The scenario is arranged so that there
| are two conjunctions: one during a period of active tracking leading to
| better estimates of the conjunction characteristics, and one not during
| active tracking that demonstrates the ability of the conjunction assessment
| to incorporate imperfect measurements.

| This scenario also demonstrates the ability to setup a conjunction via input
| files, as well as the orbit determination filter and orbit determination
| fusion method.



| Launches a Delta IV Medium rocket that deploys a satellite into low-Earth orbit.
| 
| The subdirectory \'launch_vehicles\' contains subdirectories for prototype launch vehicles.
| Each vehicle has its own directory.



| This example demonstrates the ability to remotely detect the addition
| of stages onto a launch vehicle. The addition of a new stage is modeled
| by changing the optical signature of the launch vehicle. A constellation of
| satellites with EOIR sensors detect the change and send the data down to
| a commander for processing. The data is communicated through a network that
| models the Iridium communication network. The commander then processes the
| images by determining the pixel count and noting any changes as the addition
| of a stage.

| Parameters for the optical signature of each stage as well as the times
| stages are to be added can be set in the file setup_launch_detection.txt.



| This example demonstrates a scenario in which a launch vehicle experiences a
| staging failure and is subsequently interdicted. The probability of a staging
| failure can be set by changing the `WSF_GUIDED_MOVER.ignition_failure_probability`{.interpreted-text role="command"} parameter
| in the delta_iv_m_staging_failure.txt file. If a staging failure occurs, the
| launch vehicle follows a ballistic trajectory and an intercept solution is
| then computed using the processor defined in the
| anti_ballistic_missile_processor.txt file. For more information, see the
| `ballistic_missile_shootdown`{.interpreted-text role="demo"} demo.



| This example sets up a scenario to intercept launch telemetry and set up a
| subsequent track. A launch vehicle reports telemetry back to the launch
| facility while a ship off the coast intercepts the telemetry and sends the
| data to radars in order to set up a track.

| The two radars located in the continental United States are part of the Space
| Surveillance Network (SSN): Millstone Observatory and FPS85. The third radar is
| on a ship in the Atlantic.



| A demonstration using the `WSF_LASER_TRANSCEIVER`{.interpreted-text role="model"}. A subset of the planned \"starfire\"
| constellation is modeled using the `Wizard Constellation Maker<wkf_plugin/wiz_constellation_maker>`{.interpreted-text role="doc"}. The crosslinks and downlinks of the satellites
| in the constellation are lasercomms. Satellites in the same plane in the constellation have fixed network links
| with the previous and subsequent satellites in their orbits, but side crosslinks dynamically connect with
| relatively nearby satellites. Managing communication links is performed through scripting.
| Using this ad-hoc network, a platform periodically sends messages to a commander
| on the other side of the world.
| 
| This demo also highlights use of the `Constellation Maker<wkf_plugin/wiz_constellation_maker>`{.interpreted-text role="doc"} using the advanced settings.
| In order to edit the starfire constellation:
| \* From within Wizard, open the /platforms/startlink_generator.txt file (this file was previously generated with
|   the Constellation Maker using the \"Advanced\" option).
| \* Right-click at the top of the file and select \"Edit using Constellation Maker\". The constellation maker will launch.
| \* Edit fields as desired within the Constellation Maker GUI.
| \* Be sure the \"Advanced\" button was pressed, so that the \"Generator\" button is shown.
| \* Press the \"Generator\" button to write out the new constellation generation scripts.
| \* Run the file \"startlink_generator.txt\" to generate the new constellation.



| This demo reproduces the previous demo (lasercomm.txt) but uses the script
| class `WsfConstellationMaker`{.interpreted-text role="class"} to create and configure the constellation
| at run time.



| This scenario performs a complex `orbital mission sequence <orbital_mission_sequence>`{.interpreted-text role="doc"}. `Astrolabe<wkf_plugin/wiz_astrolabe>`{.interpreted-text role="doc"}, Wizard\'s space mission planning
| and design tool, can be used to load, visualize, and manipulate the sequence. This scenario, based on examples
| from the Proton Mission Planning Guide, executes a realistic set of mission events. It begins after orbit injection
| of the 4th stage and payload of a Proton rocket. A sequence of maneuvers, including tangential burns, inclination change,
| and a circularize maneuver, ultimately place the payload in a geosynchronous orbit. The Upper stage and payload
| are modeled using the AFSIM `rocket maneuvering model<orbital_maneuvering_models.rocket>`{.interpreted-text role="ref"}, according to Proton specifications.
| A staging event of the upper stage\'s auxiliary propulsion tank also demonstrates more advanced delta-v budgeting and expenditure.
| 
| Refer to the mission_design_astrolabe.txt file for instructions on exercising this scenario with Astrolabe.



| This proximity operations-related demo shows a CubeSat maneuvering around a
| Space Shuttle. Initially it moves under the shuttle, comes up in front of it,
| where it briefly pauses, then moves over the top to the back.
| It then transitions to tracing out a path in the horizontal plane,
| first along the right side to the front, then to the left side and to the back.



| This example scenario shows two satellites that suffer an accidental
| conjunction. The resulting debris from the conjunction is created using
| a model based on the NASA Standard Breakup Model.



| This example simulates the effect of electromagnetic radiation, high energy particles, and low to medium energy particles
| from a solar event on satellite orientation, radar, and geolocation. At time 2000 seconds,
| the event of a coronal mass ejection (CME, or burst of electromagnetic radiation, and high energy particles) impacting Earth
| begins. The orientation of an INTELSAT satellite is affected and it begins to tumble while still
| in orbit. An Over the Horizon Radar also becomes nonoperational during the event. As in the `GPS Denied Demo<satellite_demos>`{.interpreted-text role="demo"},
| a fighter bomber fires two missiles at a ground radar site, but instead of the missiles\' GPS receiver being jammed by the ground radar,
| they experience a degraded GPS signal due to the CME, causing them to miss their intended target by up to 100 meters. The solar event lasts
| for 4600 seconds. When the event ends, the INTELSAT satellite regains its orientation and the OTH radar becomes operational again.



| This example scenario shows a central satellite that drops a number of
| pieces of junk which it then collects again after computing the route that
| will minimize the required delta V. This scenario demonstrates scripting
| a rendezvous maneuver, and the ability to speculatively compute the
| required delta V and time to completion of a rendezvous maneuver.