# tangent.rst.txt.md
orphan

:   



Script Type: `WsfTangentManeuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} tangent

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [delta_v]() \...

end_maneuver
:::

Perform a maneuver that adds [delta_v]() in the same or opposite directions (tangent) with the current velocity vector.

> ::: note
> ::: title
> Note
> :::
>
> If the added delta-V causes the orbit to become hyperbolic, the maneuver will only execute if the space mover supports hyperbolic propagation.
> :::

::: command
delta_v \<speed-value\>

The given delta-V to be applied. Positive values will be applied in the same direction as the velocity vector, and negative values will be applied in the opposite direction.
:::