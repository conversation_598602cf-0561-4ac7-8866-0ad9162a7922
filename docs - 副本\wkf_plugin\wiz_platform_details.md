orphan

:   

# Platform Details - Wizard

The Platform Details dialog lists the selected platform\'s mutable attributes.

![image](../images/wizard_platform_details.png)

Mutable attributes include:

  -------------- --------------------------------------------------------------------------------------------------------------------------
  Side           The platform\'s `platform.side`{.interpreted-text role="command"} / team.

  Type           The platform\'s type, as defined by `platform.platform_type`{.interpreted-text role="command"}.

  Icon           The platform\'s visual representation in the `Map Display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"}.

  Latitude       The platform\'s initial latitudinal `platform.position`{.interpreted-text role="command"}.

  Longitude      The platform\'s initial longitudinal `platform.position`{.interpreted-text role="command"}.

  Altitude MSL   The platform\'s initial `platform.altitude`{.interpreted-text role="command"}.

  Heading        The platform\'s initial `platform.heading`{.interpreted-text role="command"}.

  Pitch          The platform\'s initial pitch.

  Roll           The platform\'s initial roll.
  -------------- --------------------------------------------------------------------------------------------------------------------------

To edit an attribute: double-click the attribute\'s value, type the desired value, then press enter or select a different attribute.

> ::: note
> ::: title
> Note
> :::
>
> Some platform attributes are also mutable via dropdown menu. For said attributes, a dropdown menu is expandable via a down-arrow button, located to the right of the attribute\'s value.
> :::

When an attribute is modified in the Platform Details dialog, the value is updated in the AFSIM script definition and in text.

Changes are visible in the `Map Display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"}.

Toggle the Platform Details dialog via the `View Menu<../wizard_view_menu>`{.interpreted-text role="doc"}.
