# draw.rst.txt.md
orphan

:   



::: {.command block=""}
draw
:::

::: parsed-literal

draw *\<object-type\>* *\<object-name\>*

:   [color]() \... [color2]() .. [line_size]() \... [point_size]() \...

end_draw
:::

Draws an object using `WsfDraw`{.interpreted-text role="class"}.

\<object-type\>

:   Type of object to draw. Can be one of the following:

    -   route
    -   route_network

\<object-name\>: The name of the object in the input file.



::: command
color \<color-value\>
:::

::: command
color2 \<color-value\>

Sets the primary and secondary colors used to draw the object.

Default blue and yellow, respectively
:::

::: command
layer \<layer_name\>

Sets the layer name of this draw object. Unless specified, this object will not be drawn in a layer.
:::

::: command
line_size \<size_value\>

Sets the width of the lines used to draw the object.

Default 1
:::

::: command
point_size \<size_value\>

Sets the size of the points used to draw the object.

Default 2
:::

Example:

    draw route_network road_network
      color  float 0 0 1
      color2 float .5 .5 1
      line_size 2
      point_size 1
    end_draw