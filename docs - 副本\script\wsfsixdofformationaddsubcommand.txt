# wsfsixdofformationaddsubcommand.rst.txt.md
orphan

:   



::: {.WsfSixDOF_FormationAddSubCommand .inherits .WsfSixDOF_FormationCommand}
`WsfSixDOF_FormationAddSubCommand`{.interpreted-text role="class"} can be used to add a formation as a sub-formation to which the command is assigned. This will add the given formation as a sub-formation, but it will add it in a detached state. To have the added sub-formation attach to its new parent, use `WsfSixDOF_FormationAttachCommand`{.interpreted-text role="class"}.
:::



::: method
static WsfSixDOF_FormationAddSubCommand Construct(string aQualifiedName, WsfSixDOF_FormationOffset aOffset)

Create a command that will add the formation with the given qualified name with the given offset to the formation to which this command is assigned.
:::

::: method
static WsfSixDOF_FormationAddSubCommand Construct(string aQualifiedName, string aProposedParent, WsfSixDOF_FormationOffset aOffset)

Create a command that works as above, but will instead add the formation as a sub-formation of one of the sub-formations of the formation to which the command is assigned.

For example, the following will add the formation \'lead\' as a subformation of \'yankee.alpha.one\':

    WsfSixDOF_FormationAddSubCommand cmd = WsfSixDOF_FormationAddSubCommand.Construct("lead", "alpha.one", WsfSixDOF_FormationOffset.Construct(...));
    WsfSixDOF_Formation yankee = WsfSixDOF_FormationManager.GetFormation("yankee");
    yankee.ExecuteCommand(cmd);
:::

::: method
string GetFormationToAdd()

Return the qualified name that this command will add as a sub-formation.
:::

::: method
WsfSixDOF_FormationOffset GetOffset()

Return the offset with which the sub-formation will be added.
:::