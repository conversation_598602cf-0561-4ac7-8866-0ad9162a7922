# uci_capability_status.rst.txt.md
orphan

:   





This class defines the capability (mode) status of sensors.



::: method
UCI_CapabilityId CapabilityId()

Returns the capability ID.
:::

::: method
bool IsAvailable()

Returns true if the capability is available.
:::

::: method
bool IsDisabled()

Returns true if the capability is disabled.
:::

::: method
bool IsExpended()

Returns true if the capability is expended.
:::

::: method
bool IsFaulted()

Returns true if the capability is faulted.
:::

::: method
bool IsUnavailable()

Returns true if the capability is unavailable.
:::

::: method
bool IsTemporarilyUnavailable()

Returns true if the capability is temporarily unavailable.
:::