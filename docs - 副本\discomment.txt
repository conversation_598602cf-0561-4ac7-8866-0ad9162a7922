# discomment.rst.txt.md
orphan

:   



::: {.DisComment .inherits .DisPdu}
::: parsed-literal
`DisComment.ReceivingEntity`{.interpreted-text role="method"} `DisComment.OriginatingEntity`{.interpreted-text role="method"} `DisComment.String`{.interpreted-text role="method"}
:::
:::



[DisComment](#discomment) is an implementation of the DIS comment PDU. Comment PDUs are used to pass information between platforms in the simulation environment.



::: method
DisEntityId ReceivingEntity()

Returns the ID of the platform receiving the comment.
:::

::: method
DisEntityId OriginatingEntity()

Returns the ID of the platform sending the comment.
:::

::: method
string String()

Returns the contents of the comment.
:::