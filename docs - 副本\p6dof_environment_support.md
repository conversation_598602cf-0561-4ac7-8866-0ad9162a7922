orphan

:   

# P6DOF Environment Support {#P6DOF_Environment_Support}

P6DOF objects require several environmental components to function properly. These include atmosphere, wind, terrain, and gravity. The use of specific environmental models for P6DOF-based objects provides a means to offer higher-fidelity models for these components than may be needed by other AFSIM objects. For example, a higher-fidelity atmosphere model may provide thermodynamic parameters that support aerodynamic heating analysis, while higher-fidelity terrain models may provide friction data that differentiates between a hard surface runway, dirt, and soft grass. A higher-fidelity gravity model may include mascons or other gravitational anomalies.

::: command
p6dof_atmosphere \<filename\>

Create a P6DOF atmosphere object using the specified file. Only a single p6dof_atmosphere object should be defined for a given scenario.

See `P6DOF_Atmosphere_File_Definition`{.interpreted-text role="ref"} for the definition of the atmosphere file format.
:::

::: command
p6dof_gravity \<filename\>

Create a P6DOF gravity object using the specified file. Only a single p6dof_gravity object should be defined for a given scenario.

Note: The P6DOF gravity file is a pre-planned product improvement and is not yet implemented. However, the [p6dof_gravity]() command should still be used, although the specified filename will currently have no effect.
:::

::: command
p6dof_terrain \<filename\>

Create a P6DOF terrain object using the specified file. Only a single p6dof_terrain object should be defined for a given scenario.

See `P6DOF_Terrain_File_Definition`{.interpreted-text role="ref"} for the definition of the terrain file format.
:::

::: command
p6dof_wind \<filename\>

Create a P6DOF wind object using the specified file. Only a single p6dof_wind object should be defined for a given scenario.

Note: The P6DOF wind file is a pre-planned product improvement and is not yet implemented. However, the [p6dof_wind]() command should still be used, although the specified filename will currently have no effect.
:::

Environmental component definitions are often grouped into a single file (although this is not required). The resultant **p6dof_environment.txt** file can then be brought into a scenario using an **include_once** command. In addition to the environmental commands listed above, a `p6dof_integrators`{.interpreted-text role="command"} command is also typically included in the file for convenience.

Return to `p6dof_object_types`{.interpreted-text role="doc"}
