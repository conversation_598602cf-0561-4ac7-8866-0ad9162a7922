orphan

:   

# WsfFormationRemoveSubCommand

::: {.WsfFormationRemoveSubCommand .inherits .WsfFormationCommand}
`WsfFormationRemoveSubCommand`{.interpreted-text role="class"} is a command that will remove a sub-formation from the formation to which it is assigned. This will remove the sub-formation, but not destroy it. Instead, the removed formation will now appear as a top-level formation. The members of the removed formation will have their station keeping updated to reflect the change in organization.
:::

## Methods

::: method
WsfFormationRemoveSubCommand Construct(string aRelativeName)

Construct a command that will remove the sub-formation with the given relative name from the formation to which this command is assigned.

This provided relative name does not have to indicate a direct sub-formation of the formation to which this command is assigned. For example, removing \'alpha.two\' from the \'yankee\' formation has the same effect as removing \'two\' from the \'yankee.alpha\' formation.
:::

::: method
string GetFormationToRemove()

Return the relative name of the sub-formation that will be removed from the formation to which this command is assigned.
:::
