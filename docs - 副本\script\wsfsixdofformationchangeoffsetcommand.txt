# wsfsixdofformationchangeoffsetcommand.rst.txt.md
orphan

:   



::: {.WsfSixDOF_FormationChangeOffsetCommand .inherits .WsfSixDOF_FormationCommand}
`WsfSixDOF_FormationChangeOffsetCommand`{.interpreted-text role="class"} will change the offset of the formation to which the command is assigned. This command supports a change in offset that traces out a piecewise linear path between a sequence of offsets. The time that it takes to traverse the change in offset can be specified, and each segment of the path will be given equal shares of the total time.
:::



::: method
static WsfSixDOF_FormationChangeOffsetCommand Construct()

Construct a command that will change the offset of the formation to which it is assigned.
:::

::: method
void AddOffset(WsfSixDOF_FormationOffset aOffset)

Append the given offset to the list of offset locations.
:::

::: method
int GetNumOffsets()

Return the number of offset locations in the sequence for this command.
:::

::: method
void SetTransitionTime(double aTransitionTime)

Set the time that the command will take to traverse the sequence of offsets specified by the command.
:::

::: method
double GetTransitionTime()

Get the time that the command will take to traverse the sequence of offsets specified by this command.
:::