orphan

:   

# Sensor Controller - Warlock

![image](../images/wk_SensorController.png)

The Sensor Controller plugin gives the user basic control over sensors on the selected platform. This includes turning the sensor on/off and cueing to a given azimuth/elevation (AzEl) or track. A sensor can only be cued if the following conditions are met:

> -   The sensor\'s `_.articulated_part.slew_mode`{.interpreted-text role="command"} and `sensor_mode.cue_mode`{.interpreted-text role="command"} are not set to \"fixed\"
> -   The sensor\'s platform is not externally controlled.
> -   The sensor is turned on and operational.

When using AzEl, the angles are relative to the horizontal plane (no pitch or roll) of the platform. Hovering over the AzEl edits will show the acceptable slew limits for the given sensor. The azimuth limits are defined by the minimum of the `_.articulated_part.azimuth_slew_limits`{.interpreted-text role="command"} and `sensor_mode.azimuth_cue_limits`{.interpreted-text role="command"}. The elevation limits are defined by the `_.articulated_part.elevation_slew_limits`{.interpreted-text role="command"} and the `sensor_mode.elevation_cue_limits`{.interpreted-text role="command"}.

::: warning
::: title
Warning
:::

Attempting to use the controls on a sensor that has scripting will likely have unpredictable consequences.
:::
