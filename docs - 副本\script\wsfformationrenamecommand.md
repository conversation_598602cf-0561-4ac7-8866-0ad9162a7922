orphan

:   

# WsfFormationRenameCommand

::: {.WsfFormationRenameCommand .inherits .WsfFormationCommand}
`WsfFormationRenameCommand`{.interpreted-text role="class"} will change the relative name of the formation to which it is assigned. This will only be successful if the parent of the renamed formation does not already have a sub-formation with the new name.
:::

## Methods

::: method
WsfFormationRenameCommand Construct(string aNewRelativeName)

Construct a command that will change the relative name of the formation to which it is assigned to the given.
:::

::: method
string GetNewName()

Return the new relative name that will be assigned to the formation to which this command is assigned.
:::
