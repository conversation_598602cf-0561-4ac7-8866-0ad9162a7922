orphan

:   

# WsfFlightPathAngleManeuver

## Overview {#overview .WsfFlightPathAngleManeuver .inherits .WsfManeuver}

This maneuver sets the target flight path angle of the assigned platform. This maneuver is done executing as soon as the target is set, so if there is a need to wait for the platform\'s flight path angle to reach the target value, a `WsfManeuverConstraint`{.interpreted-text role="class"} must be used.

## Methods

::: method
WsfFlightPathAngleManeuver Construct(double aFlightPathAngleDeg)

Construct a maneuver that will instruct the assigned platform to obtain the given flight path angle in degrees.
:::

::: method
double GetFlightPathAngle()

Return this maneuver\'s target flight path angle.
:::
