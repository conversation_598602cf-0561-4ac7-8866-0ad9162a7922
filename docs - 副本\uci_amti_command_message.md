orphan

:   

# UCI_AMTI_CommandMessage

## Overview {#overview .UCI_AMTI_CommandMessage .inherits .UCI_Message}

This message allows the user to command a radar via UCI messaging.

## Static Methods

::: method
static UCI_AMTI_CommandMessage Construct(UCI_AMTI_Command AMTICommand)

This method constructs an instance of an UCI_AMTI_CommandMessage.
:::

## Methods

::: method
string CommandUUID(int commandIndex)

This method returns the UUID of the command at the given index.
:::

::: method
void PushBack(UCI_AMTI_Command AMTICommand)

This method adds the given UCI_AMTI_Command to the list of commands in the message.
:::
