orphan

:   

# WsfESM_Sensor

## Methods {#methods .WsfESM_Sensor .inherits .WsfSensor}

The scripting capability in the `WSF_ESM_SENSOR`{.interpreted-text role="model"} Class allows for an analyst to modify certain parameters of an `WSF_ESM_SENSOR`{.interpreted-text role="model"} during run-time. The following command are available for `WSF_ESM_SENSOR`{.interpreted-text role="model"} types:

::: method
int FrequencyBandCount(string aMode, int aBeam)

Returns the number of frequency bands contained in the receiver for a given sensor mode and antenna beam.
:::

::: method
bool TuneFrequencyBand(bool aReset, string aMode, int aBeamNum, int aFrequencyBandNumber, double aLowerFrequencyLimit, double aUpperFrequencyLimit, double aDwellTime, double aRevisitTime)

Either adds a new frequency band or retunes an existing frequency band for a given mode-beam combination. Parameter \"aReset\" controls whether the call from script will wither ADD a new Band or RESET an existing Band. Values are either \'false\' or \'true\', which ADDS or RESETS, respectively. Frequency units are expected to be hertz, and time units are in seconds. The dwell and revisit times must follow the conventions as defined in the `WSF_ESM_SENSOR`{.interpreted-text role="model"}. If the frequency band is being added then previously defined dwell and revisit times must be of the same type, if using the scan on scan model.
:::
