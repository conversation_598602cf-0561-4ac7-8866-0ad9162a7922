orphan

:   

# Data Structure Conventions

## Naming Conventions

The established AFSIM naming conventions ensure consistency and enable users to recognize whether a name is a file or platform/sensor/pattern.

::: centered
Table 1. Naming Conventions
:::

  Pattern                                                   Convention                                                                                         Example
  --------------------------------------------------------- -------------------------------------------------------------------------------------------------- --------------------------------------------------------------------
  Names                                                     Use hyphens where hyphens are used and underscores to replace spaces or other special characters   **F18 radar** becomes **f-18_radar**
  File Name                                                 Lowercase letters                                                                                  **generic_f-18_radar.txt**
  Platform/Sensor                                           Capitalize & replace /, space, or dash with underscore                                             **Tin Shield** -\> **TIN_SHIELD**
  Name of platform, sensor, \... defined in file            Same as file name (without the suffix) in ALL UPPERCASE                                            file **generic_f-18_radar.txt** defines **GENERIC_F-18_RADAR**
  Name of components of a system (e.g., antenna patterns)   Start with the name of the system                                                                  component **radar antenna** becomes **GENERIC_F-18_RADAR_ANTENNA**
  Radar mode names                                          All uppercase                                                                                      mode **ACQUIRE**

## Directory Structure

The file repository directory tree (Table 2) illustrates the directory structure used for AFSIM input files.

The input files are organized into subdirectories under **base_types** and **\<other\>\_types** subdirectories. Drilling down, these are further separated to provide **\*patterns***,***platforms***,***sensors***,***signatures***, and***weapons**\*.

The user project directories (e.g., **iads_demo** that is delivered with AFSIM) are divided into its own directory structure that mimic those of the input file example subdirectories. These directories contain files that \'override\' the base input files. This approach allows the user to have a stable, preserved base case with known behavior that can be modified in a straightforward, consistent manner.

::: note
::: title
Note
:::

For example, if a user wants to modify the behavior of a specific weapon, a copy of the base weapon from the **base_types/weapons** subdirectory can be made to the local project **scenario/weapons** directory. Changes then can be made to the local weapon. Likewise, additional models can be copied to the project subdirectories, which will override the models in base_types.
:::

::: centered
Table 2. File Repository Directory Tree
:::

  Directory          Subdirectory   Links   Content
  ------------------ -------------- ------- --------------------------------------------------------------
  base_types                                
                     patterns               
                     platforms              
                     sensors                
                                            
                                            
                                            
                                            
                     signatures             
                     weapons                
                                            
                                            
                                            
                                            
                                            
                                            
                                            
  tools                                     Scripts that help users run their model(s) and parse output.
  \<project\>                               User scenario (iads_demo, sensor_demo, etc)
                     output                 Model output (replay, log, and event files).
                     platforms              Only present if changes were made to platforms.
                     sensors                Only present if changes were made to sensors.
                     signatures             Only present if changes were made to signatures.
                     weapons                Only present if changes were made to weapons.
  \<other\>\_types                          Override directory for base_types if applicable.
                     patterns               
                     platforms              
                     sensors                
                                            
                                            
                                            
                                            
                     signatures             
                     weapons                
                                            
                                            
                                            
                                            
                                            
                                            
                                            

::: note
::: title
Note
:::

The directory structure delivered with AFSIM is not required in order for AFSIM to execute. The convention was established over a period of time in order to have a consistent structure of the the data input files.
:::
