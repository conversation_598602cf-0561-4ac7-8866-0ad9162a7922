orphan

:   

# Wizard Reference Guide

The Wizard Reference Guide lists the features included within Wizard organized into various categories.

## Start-up

-   `Command Line Options<wizard_command_line>`{.interpreted-text role="doc"}
-   `Start-up Dialog<wizard_start_page>`{.interpreted-text role="doc"}

## Application Layout

![image](images/wizard_application_screen.png)

This image above shows the layout of the Wizard application with the major components labeled.

Many of these components are dockable and may be moved to other locations in the window, or into their own \'floating\' windows.

-   <PERSON>us - <PERSON><PERSON> Control the behavior of the application

> -   `File<wizard_file_menu>`{.interpreted-text role="doc"}
> -   `View<wizard_view_menu>`{.interpreted-text role="doc"}
> -   `Options<wizard_options_menu>`{.interpreted-text role="doc"}
> -   `Tools<wizard_tools_menu>`{.interpreted-text role="doc"}
> -   `Developer<wizard_developer_menu>`{.interpreted-text role="doc"}
> -   `Edit<wizard_edit_menu>`{.interpreted-text role="doc"}
> -   `Project<wizard_project_menu>`{.interpreted-text role="doc"}
> -   `Run<wizard_run_menu>`{.interpreted-text role="doc"}
> -   `Help<wizard_help>`{.interpreted-text role="doc"}

-   `Central View<wizard_central_display>`{.interpreted-text role="doc"}
-   `Command Chain Browser<wkf_plugin/wiz_command_chain_browser>`{.interpreted-text role="doc"}
-   `Map Layer Manager<wkf_map_layer_manager>`{.interpreted-text role="doc"}
-   `Output Dialog<wkf_plugin/wiz_output>`{.interpreted-text role="doc"}
-   `Platform Browser<wkf_plugin/wiz_platform_browser>`{.interpreted-text role="doc"}
-   `Platform Options<wizard_platform_options>`{.interpreted-text role="doc"}
-   `Project Browser<wkf_plugin/wiz_project_browser>`{.interpreted-text role="doc"}
-   Status Bar
-   `Toolbars<wizard_toolbars>`{.interpreted-text role="doc"}
-   `Type Browser<wkf_plugin/wiz_type_browser>`{.interpreted-text role="doc"}

## Dialogs, Views and Toolbars

Most of the Dialogs and Toolbars can be accessed through the `View <wizard_view_menu>`{.interpreted-text role="doc"} menu. A few are accessed through the `Context<wizard_platform_context_menus>`{.interpreted-text role="doc"} menu.

-   `Command Documentation<wizard_command_documentation>`{.interpreted-text role="doc"}

## Platforms

-   `Context Menus<wizard_platform_context_menus>`{.interpreted-text role="doc"}

## Application Configuration

-   `Configuration Files<wizard_user_configurations>`{.interpreted-text role="doc"}
-   `Preferences<wizard_preferences>`{.interpreted-text role="doc"}
-   `Platform Options<wizard_platform_options>`{.interpreted-text role="doc"}
-   `Project Settings<wizard_project_settings>`{.interpreted-text role="doc"}
-   `Map Definitions<wizard_map_definitions>`{.interpreted-text role="doc"}
-   `Model Configuration<wkf_model_definitions>`{.interpreted-text role="doc"}
-   `Plug-in Manager<wizard_plugin_manager>`{.interpreted-text role="doc"}
-   `Tool Launcher<wizard_external_tools>`{.interpreted-text role="doc"}

## Other Tools

-   `Change History<wizard_change_history>`{.interpreted-text role="doc"}
-   `Create Platform<wizard_create_platform>`{.interpreted-text role="doc"}
-   `Export Project<wizard_export_project>`{.interpreted-text role="doc"}
-   `Find Results<wizard_find_results>`{.interpreted-text role="doc"}

## References

-   `Grammar<wizard_grammar_guide>`{.interpreted-text role="doc"}

## Extended Capabilities

::: {.toctree glob="" titlesonly="" maxdepth="1"}
wkf_plugin/[wiz]()\*
:::
