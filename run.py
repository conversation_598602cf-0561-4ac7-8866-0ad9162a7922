#!/usr/bin/env python3
"""
AFSIM Platform Agent - 快速启动脚本
提供简化的启动方式和常用功能
"""

import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from core.platform_agent import AFSIMPlatformAgent


def quick_demo():
    # """快速演示"""
    # print("🎯 AFSIM Platform Agent 快速演示")
    # print("=" * 50)
    #
    # # 初始化Agent
    print("1. 初始化Agent...")
    agent = AFSIMPlatformAgent()
    if not agent.initialize():
        print("❌ 初始化失败")
        return
    print("✅ 初始化成功")
    #
    # # 检查是否有文档
    # docs_path = Path("docs")
    # if not docs_path.exists() or not list(docs_path.glob("*.md")):
    #     print("⚠️  未找到文档，请将MD文档放入docs/目录")
    #     return
    #
    # # 索引文档
    # print("\n2. 索引文档...")
    # result = agent.index_documents("docs")
    # if 'error' in result:
    #     print(f"❌ 索引失败: {result['error']}")
    #     return
    # print(f"✅ 索引完成: {result['total_chunks']} 个文档块")
    #
    # 演示查询
    print("\n3. 演示查询...")
    # queries = [
    #     "platform position parameters",
    #     "altitude heading configuration",
    #     "side faction settings"
    # ]
    queries = [
        "9"
    ]
    
    for query in queries:
        print(f"\n🔍 查询: {query}")
        result = agent.query_platform_parameters(query, top_k=3)
        if 'error' not in result:
            print(f"   找到 {result['total_results']} 个结果")
            if result['summary']:
                print(f"   总结: {result['summary'][:100]}...")
        else:
            print(f"   ❌ 查询失败: {result['error']}")
    
    # 演示脚本生成
    print("\n4. 演示脚本生成...")
    config = {
        "platform_name": "demo_fighter",
        "platform_type": "WSF_PLATFORM",
        "side": "BLUE",
        "position": {"lat": 39.9, "lon": 116.4},
        "altitude": 10000,
        "heading": 90
    }
    
    result = agent.generate_platform_script(config)
    if 'error' not in result:
        print("✅ 脚本生成成功:")
        print("   " + "\n   ".join(result['script_content'].split('\n')[:5]))
        print("   ...")
    else:
        print(f"❌ 脚本生成失败: {result['error']}")
    
    print("\n" + "=" * 50)
    print("✅ 演示完成！")
    print("使用 'python main.py interactive' 进入交互模式")


def check_environment():
    """检查环境"""
    print("🔍 环境检查")
    print("-" * 30)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    if python_version < (3, 8):
        print("⚠️  建议使用Python 3.8+")
    
    # 检查依赖包
    required_packages = [
        'chromadb',
        'sentence_transformers', 
        'openai',
        'yaml',
        'markdown',
        'beautifulsoup4'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查配置文件
    config_file = Path("config/config.yaml")
    if config_file.exists():
        print("✅ 配置文件存在")
    else:
        print("⚠️  配置文件不存在，将使用默认配置")
    
    # 检查API Key
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print("✅ DeepSeek API Key已设置")
    else:
        print("⚠️  未设置DEEPSEEK_API_KEY环境变量")
    
    # 检查文档目录
    docs_path = Path("docs")
    if docs_path.exists():
        md_files = list(docs_path.glob("*.md"))
        print(f"✅ 文档目录存在，找到 {len(md_files)} 个MD文件")
    else:
        print("⚠️  docs目录不存在")
    
    return len(missing_packages) == 0


def main():
    quick_demo()
    # """主函数"""
    # if len(sys.argv) > 1:
    #     command = sys.argv[1].lower()
    #
    #     if command == 'demo':
    #         quick_demo()
    #     elif command == 'check':
    #         check_environment()
    #     elif command == 'setup':
    #         print("运行安装程序...")
    #         os.system("python setup.py")
    #     else:
    #         print(f"未知命令: {command}")
    #         print("可用命令: demo, check, setup")
    # else:
    #     print("🎯 AFSIM Platform Agent")
    #     print("=" * 30)
    #     print("可用命令:")
    #     print("  python run.py demo   - 快速演示")
    #     print("  python run.py check  - 环境检查")
    #     print("  python run.py setup  - 运行安装程序")
    #     print("")
    #     print("主要功能:")
    #     print("  python main.py interactive  - 交互模式")
    #     print("  python main.py --help       - 查看帮助")


if __name__ == '__main__':
    main()
