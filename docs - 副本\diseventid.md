orphan

:   

# DisEventId {#DisEventId}

::: DisEventId
::: parsed-literal
`DisEventId.Site`{.interpreted-text role="method"} `DisEventId.Application`{.interpreted-text role="method"} `DisEventId.Number`{.interpreted-text role="method"}
:::
:::

## Overview

[DisEventId](#diseventid) is an implementation of the DIS event ID. The event ID is used by DIS to correlate events.

## Methods

::: method
int Site()

Returns the site ID.
:::

::: method
int Application()

Returns the application ID.
:::

::: method
int Number()

Returns an event ID.
:::
