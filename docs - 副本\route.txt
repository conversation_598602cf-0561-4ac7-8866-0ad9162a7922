# route.rst.txt.md
orphan

:   



::: {.command block=""}
route \... end_route
:::

::: parsed-literal

[route](#route)

:   \# [Commands](#commands) [navigation]() \# [Navigation Commands](#navigation-commands) [label]() \... [position]() \... [mgrs_coordinate]() \... [offset]() \... [turn_left]() \... [turn_right]() \... [turn_to_heading]() \... [goto]() \...

    > \# [Waypoint Commands](#waypoint-commands) [altitude]() \... [depth]() \... [heading]() \... [turn]() \... [speed]() \... [linear_acceleration]() \... [radial_acceleration]() \... [bank_angle_limit]() \... [turn_g_limit]() \... [climb_rate]() \... [dive_rate]() \... [pause_time]() \... [execute]() \... [extrapolate]() \... [stop]() \... [remove]() \... [switch_on_passing]() \... [switch_on_approach]() \... [distance]() \... [time]() \... [time_to_point]() \... [node_id]() \... [aux_data]() \... end_aux_data

    end_navigation

    \# `Auxiliary Data Commands <_.aux_data>`{.interpreted-text role="command"} [aux_data]() \... end_aux_data

    \# [Route Insertion Commands](#route-insertion-commands) [transform_route]() \... [transform_absolute_route]() \...

end_route
:::

::: parsed-literal
\# Define a route on a `platform`{.interpreted-text role="command"}. `platform`{.interpreted-text role="command"} \... [route](#route) \... end_route end_platform

\# Define a route type that can be referenced by the use_route command of the `platform`{.interpreted-text role="command"} or `route_network`{.interpreted-text role="command"} \# commands, or by [transform_route]() or [transform_absolute_route]() commands. \# \# These occur outside `platform`{.interpreted-text role="command"} definitions.

[route](#route) \<name\>

:   \...

end_route
:::



A route is a collection of waypoints that define a path, or route, for movers which use routes (e.g.: `WSF_AIR_MOVER`{.interpreted-text role="model"}, `WSF_GROUND_MOVER`{.interpreted-text role="model"}, `WSF_ROAD_MOVER`{.interpreted-text role="model"} and `WSF_SURFACE_MOVER`{.interpreted-text role="model"}) or to define a portion of a route within a `route_network`{.interpreted-text role="command"}.

The start of a waypoint in indicated by the presence of one of the following commands:

-   A specific latitude and longitude ([position]())
-   An offset relative to the current position ([offset]())
-   A command to turn ([turn_left](), [turn_right]() or [turn_to_heading]())
-   A command to \'goto\' another labeled waypoint ([goto]())

The definition of the waypoint continues until the next command that starts a new waypoint.

::: note
::: title
Note
:::

Parameters like speed, altitude, climb_rate, radial_acceleration, linear_acceleration, etc\... are used for all subsequent waypoints until overridden.
:::

::: block
route
:::



::: command
navigation \<navigation-commands\> \... end_navigation

Defines the block input for [Navigation Commands](#navigation-commands) used to enter the route waypoints and other navigational data.
:::

::: command
aux_data \<aux-data\> \... end_aux_data

Defines auxiliary data for a route. See `_.aux_data`{.interpreted-text role="command"} for more commands and information.
:::



A new waypoint is started when a \'navigation\' command is entered, which is a [position](), [offset](), [turn_left](), [turn_right](), [turn_to_heading](), or [goto]() command. All commands up until the next \'navigation\' command (or the end of the route) are considered part of the definition of the waypoint and define the desired speed and altitude to at the waypoint and what is to be done when that waypoint is encountered.

::: command
label \<string\>

Associates a string label with the immediately following waypoint definition. This can be used as the target of a [goto]() command.

::: note
::: title
Note
:::

This command should be immediately followed by a navigation command, as it is attached to the next waypoint.
:::
:::

::: command
position \<latitude-value\> \<longitude-value\>

Specifies the latitude and longitude of the waypoint.
:::

::: command
mgrs_coordinate \<MGRS-value\>

Specifies the coordinate of the waypoint in the Military Grid Reference System.
:::

::: command
offset \<x-offset\> \<y-offset\> \<length-units\>

Go to a point relative to the current location of the platform. Each offset waypoint is relative to the position of the previous waypoint (or platform\'s position if it is the first waypoint), and orientation is set using the heading at the first offset waypoint and remains constant for all subsequent offset waypoints. The +X axis is in the direction of the initial heading and the +Y axis is 90 degrees to the right of the initial heading.
:::

<figure>
<img src="./images/route_offset.png" alt="./images/route_offset.png" />
<figcaption>Example: Route with offset waypoints. The platform starts in the bottom left corner and the platform's heading sets the +X axis. The table shows the offset for each waypoint (how much the platform should move from the previous waypoint along the x-axis and y-axis) and the cumulative change from the starting location. The diagram shows the platform position at each waypoint.</figcaption>
</figure>

::: command
turn_left \<angle-value\>
:::

::: command
turn_right \<angle-value\>

Initiate a turn to effect the specified heading angle change.
:::

::: command
turn_to_heading \<angle-value\>

Initiate a turn to the specified absolute heading angle. The direction of the turn will be in the direction that requires the least amount of heading angle change.
:::

::: command
goto \<string\>

When this waypoint is reached, go to the waypoint with the specified label in the current route.

::: note
::: title
Note
:::

This command should follow a navigation command, as it is attached to the previous waypoint.
:::
:::



::: command
altitude \<length-value\> \[ agl \| msl \]

Specifies the altitude at the waypoint. if agl or msl is omitted, the default altitude reference is defined by the mover. msl is assumed for `WSF_AIR_MOVER`{.interpreted-text role="model"} and agl for all others.
:::

::: command
depth \<length-value\>

Specifies the sub-surface depth at the waypoint.
:::

::: command
heading \<angle-value\>

Specifies the heading at the waypoint. This is really only effective for a route with one point. If more than one waypoint is given then heading will be determined automatically.
:::

::: command
turn \[ left \| right \| shortest \]

Specifies the direction to turn if a turn is required.

Default shortest
:::

::: command
speed \<speed-value\>

Specifies the speed at the waypoint.
:::

::: command
linear_acceleration \<acceleration-value\>

Specifies the linear acceleration to be used for changing speed on the route segment that starts with this waypoint. *\<acceleration-value\>* may also be default to use the mover\'s default linear_acceleration.

Default The default linear acceleration for the mover.
:::

::: command
radial_acceleration \<acceleration-value\>

Specifies the radial acceleration to be used for turns when making heading changes on the route segment that starts with this waypoint. *\<acceleration-value\>* may also be default to use the mover\'s default radial_acceleration.

Default The default radial acceleration for the mover.

::: note
::: title
Note
:::

The radial acceleration is NOT the load factor for the aircraft. For example, if one desires a maximum load factor of n = 2 for a 2g turn, then the radial acceleration for a desired 2g turn limit would need to be set = $g * sqrt(n^2 -1)$ = 1.732g.
:::
:::

::: command
bank_angle_limit \<angle-value\>

Specifies the maximum bank angle to be used for turns when making heading changes on the route segment that starts with this waypoint. This effectively sets the radial_acceleration to $g * tan(bank\_angle\_limit)$.
:::

::: command
turn_g_limit \<acceleration-value\>

Specifies the maximum turn g-load to be used for turns when making heading changes on the route segment that starts with this waypoint. This effectively sets the radial_acceleration to $sqrt(turn\_g\_limit^2 - g^2)$.
:::

::: command
climb_rate \<speed-value\>
:::

::: command
dive_rate \<speed-value\>

Specifies the rate of climb or dive for changing altitude on the route segment that starts with this waypoint. \<speed-value\>\* may also be default to use the mover\'s default climb_rate. Note: the commands climb_rate and dive_rate are synonymous \-- specifying a dive_rate will replace a previously specified climb_rate.

Default The default climb/dive rate for the mover.
:::

::: command
maximum_flight_path_angle \<angle-value\>
:::

::: command
maximum_flight_path_angle default

Specifies the maximum flight path angle for climbs and dives that happen after this waypoint. If default is specified, the mover will use its default value.
:::

::: command
pause_time \<time-value\>

When the waypoint is reached, stop moving for the specified time.
:::

::: command
execute \<script-name\> \<callback-name\>

Specify a script or callback to be executed upon reaching the waypoint. *\<script-name\>*/*\<callback-name\>* must be the name of a \'script\' defined for the `platform`{.interpreted-text role="command"} or `platform_type <platform>`{.interpreted-text role="command"}.
:::

::: command
extrapolate
:::

::: command
stop
:::

::: command
remove

Indicates to the mover what is to be done when this waypoint is encountered and there are no more waypoints in the route. The possible actions are:

-   extrapolate - continue moving at the current speed, heading and altitude.
-   stop - stop moving.
-   remove - remove the platform from the simulation.

The default depends on the type of mover as follows:

-   extrapolate - `WSF_AIR_MOVER`{.interpreted-text role="model"}
-   stop - `WSF_GROUND_MOVER`{.interpreted-text role="model"}, `WSF_ROAD_MOVER`{.interpreted-text role="model"}, `WSF_SURFACE_MOVER`{.interpreted-text role="model"}
:::

::: command
switch_on_passing
:::

::: command
switch_on_approach

Defines the condition when the mover should declare that it has reached this waypoint and should start moving towards the next waypoint. switch_on_passing is sometimes known as \'turn long\' and causes the switch to occur when the platform passes over or along side of the waypoint. switch_on_approach is sometimes known as \'turn short\' and causes the switch to occur before the waypoint.

Default switch_on_passing.

::: note
::: title
Note
:::

This is applicable only to [position]() and [offset]() waypoints.
:::

::: note
::: title
Note
:::

switch_on_approach is applicable only if the following point is also a [position]() waypoint. The user is also responsible for ensuring the target waypoint is such that the turn can be completed properly.
:::
:::

::: command
distance \<length-value\>
:::

::: command
time \<time-value\>

If the waypoint is a [turn_left](), [turn_right]() or [turn_to_heading]() and the next waypoint is also one of the same class, this command specifies how long or how far to move until switching to the next waypoint.

::: note
::: title
Note
:::

It is an error to specify this command with a [position]() or [offset]() waypoint.
:::
:::

::: command
time_to_point \<time-value\>

If specified, the mover will change speed in attempt to reach this waypoint after the specified duration. *\<time-value\>* is the length of time it should take the platform to move from the previous waypoint to the current one. time_to_point may only be specified for [position]() waypoints.
:::

::: command
node_id \<string\>

This command is used only if the route is part of a `route_network`{.interpreted-text role="command"}. Waypoints that share the same node_id within a set of routes within a `route_network`{.interpreted-text role="command"} are assumed to intersect or connect at those points.

::: note
::: title
Note
:::

It is the responsibility of the user to ensure that waypoints with the same node_id actually have the same spatial location.
:::
:::

::: command
aux_data \<aux-data\> \... end_aux_data

Defines auxiliary data for a waypoint. See `_.aux_data`{.interpreted-text role="command"}.
:::



The following commands allow another route to be inserted at the current point within the route. This allows the creation of routes that represent patterns.

::: command
insert_route \<route-name\> \[ reference_heading \<heading\> \]
:::

::: command
insert_route \<route-name\> \<latitude\> \<longitude\> \<heading\>

Transforms the named route and inserts its waypoints into the route being defined. The named route should have already been defined as a \'route type\'. All points in the named route that were defined using the [offset]() command are transformed to a new coordinate system whose origin and orientation are defined below and then internally converted to [position]() points.

The first form should be used if the command occurs in a route in which waypoints appear before it. It uses the latitude and longitude of the preceding waypoint as the origin for the transformation coordinate system. If reference_heading was specified then it defines the orientation of the transformation coordinate system. If omitted, it will use the heading between the preceding two waypoints, or 0 if there is only one preceding waypoint.

The second form should be used if the command occurs as the first item in the route. The *\<latitude\>*, *\<longitude\>* and *\<heading\>* values specify the origin and orientation of the transformation coordinate system.

::: note
::: title
Note
:::

This command is useful for inserting patterns (e.g.: orbits, etc.) into the route.
:::
:::

::: command
insert_offset_route \<route-name\> \[ reference_heading \<heading\> \]
:::

::: command
insert_offset_route \<route-name\> \<latitude\> \<longitude\> \<heading\>

The insert_offset_route command is similar to [insert_route](). The insert_offset_route command converts offset waypoints to be relative to a single origin. This is different than offset waypoints explicitly defined in a route which are treated relative to the previous waypoint. This means a route explicitly containing offset waypoints will be a different route than one that implicitly includes those offset waypoints using the [insert_route]() command.
:::

::: command
transform_absolute_route \<route-name\> \<north-length-value\> \<east-length-value\> \<down-length-value\>

Translate the named route the specified about and insert it into the current route. Only [position]() points will be translated.
:::



::: command
transform_route \<route-name\> \[ reference_heading \<heading\> \]
:::

::: command
transform_route \<route-name\> \<latitude\> \<longitude\> \<heading\>

Transforms the named route and inserts its waypoints into the route being defined. The named route should have already been defined as a \'route type\'. All points in the named route that were defined using the [offset]() command are transformed to a new coordinate system whose origin and orientation are defined below and then internally converted to [position]() points.

The first form should be used if the command occurs in a route in which waypoints appear before it. It uses the latitude and longitude of the preceding waypoint as the origin for the transformation coordinate system. If reference_heading was specified then it defines the orientation of the transformation coordinate system. If omitted, it will use the heading between the preceding two waypoints, or 0 if there is only one preceding waypoint.

The second form should be used if the command occurs as the first item in the route. The *\<latitude\>*, *\<longitude\>* and *\<heading\>* values specify the origin and orientation of the transformation coordinate system.

::: note
::: title
Note
:::

This command is useful for inserting patterns (e.g.: orbits, etc.) into the route.
:::
:::

::: deprecated
2.9 This command will be replaced by [insert_route]().
:::