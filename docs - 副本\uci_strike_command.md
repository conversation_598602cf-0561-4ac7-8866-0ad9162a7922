orphan

:   

# UCI_StrikeCommand

## Overview {#overview .UCI_StrikeCommand}

This type holds the information given by a strike command.

## Static Methods

::: method
static UCI_StrikeCommand Construct(UCI_StrikeCapabilityCommand aCapabilityCommand)

Returns a \_UCI_StrikeCommand with the given capability command.
:::

::: method
static UCI_StrikeCommand Construct(UCI_StrikeActivityCommand aActivityCommand)

Returns a \_UCI_StrikeCommand with the given activity command.
:::
