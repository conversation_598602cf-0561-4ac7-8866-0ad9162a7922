# ripr.rst.txt.md
orphan

:   





Please see `This Conversion Guide <Convert_RIPR_To_Quantum_Guide>`{.interpreted-text role="ref"} for tips on how to convert a scenario from using jobs and RIPR Processors to using the new Quantum Tasker.



The Reactive Integrated Planning aRchitecture (RIPR) is an artificial intelligence framework that allows scenario developers to quickly create flexible agents with sophisticated behaviors. RIPR agent scripts are typically constructed with the behavior tree technology (now on all script processors in WSF); please see the `behavior_tree`{.interpreted-text role="command"} page for more info. Many commander/subordinate agent pairs make uses of the job board technology. The job board is a task allocation system and is exposed to scenario developers via the `WsfRiprProcessor`{.interpreted-text role="class"} and the `WsfRIPRJobb`{.interpreted-text role="class"} classes.

RIPR is part of the baseline WSF release and consists of the following classes:

-   `WSF_RIPR_PROCESSOR`{.interpreted-text role="model"} / `WsfRiprProcessor`{.interpreted-text role="class"}
-   `behavior_tree`{.interpreted-text role="command"} (now on `WSF_SCRIPT_PROCESSOR`{.interpreted-text role="model"})
-   `WsfRIPRJob`{.interpreted-text role="class"}



1.  Most RIPR functionality is used by writing scripts in the WSF scripting language. Refer to the `WSF_REFERENCE_GUIDE`{.interpreted-text role="ref"} for detailed information on the available scripting capabilities.
2.  The `behavior_tree`{.interpreted-text role="command"} technology is the best way to make your behavior scripts modular, reusable, & easy to edit.
3.  The job board task allocation system is accessible via the `WSF_RIPR_PROCESSOR`{.interpreted-text role="model"} class any any types that derive from it. Job boards can be used to provide dynamic, adaptable command and control to a hierarchy of intelligent agents.
4.  For an example of RIPR agents in a scenario, see *scenario_gci-cluster* in the unclassified WSF analyst repository under *ripr_training/13.gci-cluster/*. The striker platform in this scenario is a RIPR agent, it uses the aiai_processor defined in *site_types/processors/ripr_agents/aiai*.



-   `RIPR_Resource_Allocator`{.interpreted-text role="ref"} - example on how to use RIPR for coordinated jamming.
-   `RIPR_Scenarios`{.interpreted-text role="ref"} - more explanation about a scenario with RIPR agents in the command hierarchy.



Please reference the `RIPR_Trouble_Shooting_Guide`{.interpreted-text role="ref"} for steps to debug your agents.





`WSF_RIPR_PROCESSOR`{.interpreted-text role="model"} is the processor that houses the job board technology. If you want a platform or agent to use the bottom-up job job board bidding system, then they should use a `WSF_RIPR_PROCESSOR`{.interpreted-text role="model"}. Please see the `WSF_QUANTUM_TASKER_PROCESSOR`{.interpreted-text role="model"} for other options.