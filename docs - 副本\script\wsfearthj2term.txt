# wsfearthj2term.rst.txt.md
orphan

:   





The `WsfEarthJ2Term`{.interpreted-text role="class"} represents the additional acceleration due to the oblateness of the Earth. See also `Earth J2 Perturbation <orbital_dynamics_terms.earth_j2>`{.interpreted-text role="ref"}.



::: method
WsfEarthJ2Term Construct()

Construct a `WsfEarthJ2Term`{.interpreted-text role="class"} using default values of the gravitational parameter (3.986004415e+14 m\^3/s\^2), the mean radius of the Earth (6371.0 km), and $J_2$ (0.0010826267).
:::

::: method
WsfEarthJ2Term Construct(double aGravitationalParameter, double aMeanRadius, double aJ2)

Construct a `WsfEarthJ2Term`{.interpreted-text role="class"} using the given gravitational parameter in SI units, the given mean radius of the Earth in meters, and the given value for $J_2$.
:::

::: method
double DefaultJ2()

Return the default value for $J_2$ (0.0010826267).
:::



::: method
double GravitationalParameter()

Return the gravitational parameter in SI units used by this term.
:::

::: method
double MeanRadius()

Return the mean radius in meters of the Earth used by this term.
:::

::: method
double J2()

Return the value of $J_2$ used by this term.
:::

::: method
Vec3 ComputeAcceleration(double aMass, Calendar aTime, Vec3 aPosition, Vec3 aVelocity)

For the `WsfEarthJ2Term`{.interpreted-text role="class"} this method will return the zero vector; the acceleration provided by this term can only be computed when the dynamics are fully initialized and are in use by a `WsfIntegratingSpaceMover`{.interpreted-text role="class"}.
:::