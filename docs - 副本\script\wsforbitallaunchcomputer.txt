# wsforbitallaunchcomputer.rst.txt.md
orphan

:   



::: {.WsfOrbitalLaunchComputer .inherits .WsfLaunchComputer}
The WsfOrbitalLaunchComputer object provides support to `WsfWeapon`{.interpreted-text role="class"} to determine if an orbital launch vehicle can be launched, and if so, information about the launch conditions and the resulting orbit.
:::



::: method
Array\<double\> ComputeOrbitalElements(Vec3 aLocECI, Vec3 aVelECI, double aSimTime)

Returns the orbital elements for an object with the specified inertial position and velocity at the specified time.

See `WsfOrbitalLaunchComputer.OrbitalElements`{.interpreted-text role="method"} for a description of the contents of the return array.
:::



::: method
bool ComputeLEO_Insertion(double aAltitude, double aInclination)

Initiates a launch computer calculation to determine the launch parameters and orbit insertion state to achieve a low-Earth orbit at the specified altitude and inclination. If successful then t launch parameters and parameters for the resulting orbit can be retrieved using the [Data Retrieval Methods](#data-retrieval-methods) below.

Returns \'true\' if successful or \'false\' if an insertion trajectory cannot be determined.
:::



These methods return the launch parameters and orbit insertion data from the last successful call to `WsfOrbitalLaunchComputer.ComputeLEO_Insertion`{.interpreted-text role="method"}.

::: method
double LaunchHeading()

Returns the launch heading (degrees).
:::

::: method
double LauncherBearing()

Returns the launcher bearing (degrees). The `WsfArticulatedPart.SetYaw`{.interpreted-text role="method"} on the weapon should be invoked with this value prior to invoking `WsfWeapon.Fire`{.interpreted-text role="method"}.
:::

::: method
double LoftAngle()

Returns the loft angle for the booster needed for orbit insertion.
:::

::: method
double Inclination()

Returns the inclination of the resulting orbit (degrees).
:::

::: method
double InsertionTimeOfFlight()

Returns the estimate time of flight from launch until orbit insertion (seconds)
:::

::: method
WsfGeoPoint InsertionLocation()

Returns the estimated location of the launched vehicle at orbit insertion.
:::

::: method
double InsertionSpeed()

Returns the estimated speed of the launched vehicle in the inertial frame at orbit insertion (meters/second).
:::

::: method
double InsertionSpeedFixed()

Returns the estimated speed of the launched vehicle in the inertial frame at orbit insertion (meters/second).
:::

::: method
double InsertionHeading()

Returns the estimated heading of the launched vehicle in the inertial frame at orbit insertion (degrees).
:::

::: method
double InsertionPitch()

Returns the estimated pitch angle of the launched vehicle in the inertial frame at orbit insertion (degrees).
:::

::: method
double InsertionResidualDeltaV()

Returns the estimated speed the vehicle could achieve if the engines remained burning after orbit insertion (meters/second).
:::

::: method
double InsertionFuelRemaining()

Returns the estimated fuel remaining after orbit insertions (kilograms).
:::

::: method
double InsertionTimeRemaining()

Returns the estimated time remaining the engines could burn after orbit insertions (seconds).
:::

::: method
Array\<double\> OrbitalElements()

Returns the orbital elements of the resulting orbit.

-   Element 0: Semi-major axis (meters)
-   Element 1: Eccentricity
-   Element 2: Inclination (degrees)
-   Element 3: Mean anomaly (degrees)
-   Element 4: Right ascension of the ascending node (RAAN) (degrees)
-   Element 5: Argument of periapsis (degrees)
:::