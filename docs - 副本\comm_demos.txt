# comm_demos.rst.txt.md
orphan

:   

::: demo
comm
:::

| This is a collection of demos that showcase `comm`{.interpreted-text role="command"} and other related capabilities.



This simple scenario provides an example of transmitting tracks received from off-board platforms to other groups.

Scenario:

-   Platform 1 (p1) is the only platform that has their sensor ON in the simulation
-   Platform 1 (p1) can communicate tracks to platforms in FIGHTER_GROUP_1 (p1, p2)
-   Platform 2 (p2) can communicate tracks to platforms in FIGHTER_GROUP_2 (p2, p3)
-   p1 CANNOT communicate tracks to p3, but must go by way of p2



| This scenario places ad-hoc enabled platforms randomly in a constrained area, and forms
| a network based on rules defined by the ad-hoc protocol. A high velocity ad-hoc enabled
| friendly passes through the area to highlight how linkage is created and removed between
| members during runtime. An opposing aircraft moves through the area, creating tracks that
| are reported through the network based on the resulting network topology.
| 
| Network connections and resulting message transmissions are visualized using `WsfDraw`{.interpreted-text role="class"}.