orphan

:   

# UCI_StrikeActivity

## Overview {#overview .UCI_StrikeActivity}

This type holds the information given by a strike activity.

## Methods

::: method
UCI_ActivityId ActivityId()

Returns the activity\'s ID.
:::

::: method
UCI_CapabilityId CapabilityId(int aIndex)

Returns the activity\'s capability ID at the given index. If aIndex is less than 0 or greater than \_CapabilityIdSize, this returns null.
:::

::: method
int CapabilityIdSize()

Returns the number of capability IDs in the activity.
:::

::: method
bool IsReadyForRelease()

Returns true if the weapon is ready for release, returns false otherwise.
:::

::: method
bool IsSelectedForJettison()

Returns true if the weapon has been given the command to be selected for jettison, returns false otherwise.
:::

::: method
bool IsSelectedForKeyLoad()

Returns true if the weapon has been given the command to be selected for key load, returns false otherwise.
:::

::: method
bool IsSelectedForRelease()

Returns true if the weapon has been given the command to be selected for release, returns false otherwise.
:::

::: method
bool IsWeaponArmed()

Returns true if the weapon has been given the command to be armed, returns false otherwise.
:::
