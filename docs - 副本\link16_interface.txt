# link16_interface.rst.txt.md
orphan

:   





::: {.command block=""}
link16_interface \... end_link16_interface
:::

::: parsed-literal

link16_interface

:   \... Link16 interface subcommands \... j11 [allow_any_comm]() [network_enabled_weapon_type]() \... [use_time_of_target_measurement]() [wift_interval]() \... [print_messages]() end_j11

end_link16_interface
:::

This command configures the link16 interface. The absence of this command will disable the Link 16 interface.



::: command
tdl_header \<header-type\>

-   \<header-type\> : The TDL header type used when writing DIS Signal messages. This can be either 6 or 100.

Default 100
:::

::: command
swap_tdl_header_100_method \<swap-type\>

We support two different ways of transmitting TDL-100. \<swap-type\> can be fields_only or fields_then_word_boundary.

Default fields-only
:::

::: command
print_script_usage

Outputs information about the `WsfTadilJ`{.interpreted-text role="ref"} classes for scripting J-Messages.
:::

::: command
ignore_inbound_messages \<boolean-value\>

Specifies whether to ignore inbound l16 DIS messages. If this is set to *true*, WSF will not attempt to process j-messages received over DIS. This does not affect J11 messages.

Default false
:::

::: command
debug
:::

Enables debug output for the Link16 interface.

::: command
fill_with_31.7 \<boolean-value\>
:::

Enables or disables filling signal PDU\'s with 31.7 messages. If enabled, signal PDU\'s will contain enough 31.7 words to total 3, 6, or 12 words for the entire message. This option is available for compatibility with certain simulations.

Default no

::: command
include_dis_entity_id_in_j3x_tracks \<boolean-value\>
:::

This is used when converting WSF tracks to J3.X surveillance track messages. When \"true\', an extension word will be appended which contains the DIS Entity ID of the truth object represented by the track.

Default false



::: command
allow_any_comm
:::

Allow any comm device to send/receive J11 messages. In other words, a `WSF_JTIDS_TERMINAL`{.interpreted-text role="model"} is NOT required.

::: command
network_enabled_weapon_type \<weapon-platform-type\> \<integer-value\>
:::

This is used to map the platform type of a weapon to an integer value used in the \"Type of NEW\" field in the J11.0I word. Valid range is 0 to 11. (See J11 spec definition for use of the field)

::: note
::: title
Note
:::

Create one or more of these for each platform type used as a weapon.
:::

Default 0

::: command
use_time_of_target_measurement
:::

On receiving a J11.1-5 In-Flight Target Update (IFTU) Directive, the \"TIME OF TARGET MEASUREMENT\" in continuation word 3 will be used to extrapolate the IFTU data to the current simulation time of the receiving simulation. The IFTU time value is expected to be the simulation time of the sending simulation and assumes a time synchronization is occurring between the sending and receiving applications. (This does not adhere to the J11 spec. definition for use of the field)

Default off

::: command
wift_interval \<time-value\>
:::

Time interval for sending the J11.0-4 Weapon In-Flight Track (WIFT) Report.

::: command
print_messages
:::

Print the contents of the J11 messages when sending or receiving.



-   `WSF_LINK16_COMPUTER`{.interpreted-text role="model"}