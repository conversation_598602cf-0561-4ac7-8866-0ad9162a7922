# kinematic_mover.rst.txt.md
orphan

:   

::: demo
kinematic_mover
:::

| This demo has three platforms using the same route (just offset from each other) but have different
| limits using the kinematic mover. Flyer4 is the same as Flyer3 but uses the WSF_AIR_MOVER
| to illustrate the difference between the AIR MOVER vs the KINEMATIC MOVER.
| 
| Keep in mind that the KINEMATIC MOVER calculates acceleration in the
| vertical plane whereas the AIR MOVER does not (only in the horizontal plane).
| 
| After running, view the replay file with trace lines on to view the
| differences.
| 
| Note that the mover update time interval is 0.1 secs which make larger
| replay files sizes.