# wsfem_antenna.rst.txt.md
orphan

:   



::: WsfEM_Antenna
WsfEM_Antenna is the geometric representation of an antenna, providing the physical location and orientation.
:::



::: method
WsfArticulatedPart ArticulatedPart()

Returns the articulated part associated with the antenna.
:::

::: method
double Height()

Returns the height (m) of the antenna with respect to the host `WsfArticulatedPart`{.interpreted-text role="class"}.
:::

::: method
void SetHeight(double aHeight)

Sets the height (m) of the antenna with respect to the host `WsfArticulatedPart`{.interpreted-text role="class"}.
:::

::: method
double Pitch()

Returns the antenna pitch angle above/below the scan plane of the host `WsfArticulatedPart`{.interpreted-text role="class"}. (degrees)
:::

::: method
void SetPitch(double aPitch)

Sets the antenna pitch angle above/below the scan plane of the host `WsfArticulatedPart`{.interpreted-text role="class"}. (degrees)
:::

::: method
WsfFieldOfView FieldOfView()

Return the field of view of the antenna.
:::

::: method
void SetFieldOfView(WsfFieldOfView aFieldOfView)

Apply a field of view to the antenna.
:::

::: method
Array\<double\> AzimuthFieldOfView()

Returns the angular (degrees) azimuth field of view limits from a `rectangular field of view<field_of_view_commands.rectangular>`{.interpreted-text role="ref"}. The first value is the minimum azimuth field of view limit, while the second value is the maximum azimuth field of view limit.
:::

::: method
void SetAzimuthFieldOfView(double aMinAz, double aMaxAz)

Sets the angular (degrees) azimuth field of view limits to a `rectangular field of view<field_of_view_commands.rectangular>`{.interpreted-text role="ref"}. The first parameter is the minimum azimuth field of view limit, while the second value is the maximum azimuth field of view limit.
:::

::: method
Array\<double\> ElevationFieldOfView()

Returns the angular (degrees) elevation field of view limits from a `rectangular field of view<field_of_view_commands.rectangular>`{.interpreted-text role="ref"}. The first value is the minimum elevation field of view limit, while the second value is the maximum elevation field of view limit.
:::

::: method
void SetElevationFieldOfView(double aMinEl, double aMaxEl)

Sets the angular (degrees) elevation field of view limits to a `rectangular field of view<field_of_view_commands.rectangular>`{.interpreted-text role="ref"}. The first parameter is the minimum elevation field of view limit, while the second value is the maximum elevation field of view limit.
:::

::: method
double MaximumRange()

The maximum range (m) of interactions with this antenna.
:::

::: method
void SetMaximumRange(double aRange)

Sets the maximum range (m) of interactions with this antenna.
:::

::: method
double MinimumRange()

The minimum range (m) of interactions with this antenna.
:::

::: method
void SetMinimumRange(double aRange)

Sets the minimum range (m) of interactions with this antenna.
:::

::: method
double MaximumAltitude()

The maximum altitude (m) of interactions with this antenna.
:::

::: method
void SetMaximumAltitude(double aAltitude)

Sets the maximum altitude (m) of interactions with this antenna.
:::

::: method
double MinimumAltitude()

The minimum altitude (m) of interactions with this antenna.
:::

::: method
void SetMinimumAltitude(double aAltitude)

Sets the minimum altitude (m) of interactions with this antenna.
:::

::: method
string EBS_Mode()

Returns the electronic beam steering mode of this antenna. The following modes are available:

-   none
-   azimuth
-   elevation
-   both
:::

::: method
bool SetEBS_Mode(string aMode)

Sets the electronic beam steering mode of this antenna. The following modes are available:

-   none
-   azimuth
-   elevation
-   both
:::

::: method
double EBS_AzimuthLossExponent()

Returns the azimuth specific steering exponent for electronic beam steering azimuth dropoff.
:::

::: method
void SetEBS_AzimuthLossExponent(double aExponent)

Sets the azimuth specific steering exponent for electronic beam steering azimuth dropoff.
:::

::: method
double EBS_ElevationLossExponent()

Returns the elevation specific steering exponent for electronic beam steering elevation dropoff.
:::

::: method
void SetEBS_ElevationLossExponent(double aExponent)

Sets the elevation specific steering exponent for electronic beam steering elevation dropoff.
:::

::: method
double EBS_AzimuthCosineSteeringLimit()

Returns the cosine of the azimuth steering limit angle used in determining steering losses.
:::

::: method
void SetEBS_AzimuthCosineSteeringLimit(double aLimit)

Sets the cosine of the azimuth steering limit angle used in determining steering losses.
:::

::: method
double EBS_ElevationCosineSteeringLimit()

Returns the cosine of the elevation steering limit angle used in determining steering losses.
:::

::: method
void SetEBS_ElevationCosineSteeringLimit(double aLimit)

Sets the cosine of the elevation steering limit angle used in determining steering losses.
:::

::: method
string ScanMode()

Returns the scan mode for this antenna. The following modes are available:

-   fixed
-   azimuth
-   elevation
-   both (azimuth and elevation scanning)
:::

::: method
bool SetScanMode(string aMode)

Sets the scan mode for this antenna. Returns true if successful, false otherwise. The following modes are available:

-   fixed
-   azimuth
-   elevation
-   both (azimuth and elevation scanning)
:::

::: method
string ScanStabilization()

Returns the stabilization mode for this antenna. The following modes are available:

-   none
-   pitch
-   roll
-   pitch_and_roll (pitch and roll stabilization)
:::

::: method
bool SetScanStabilization(string aStabilization)

Sets the stabilization mode for this antenna. Returns true if successful, false otherwise. The following modes are available:

-   none
-   pitch
-   roll
-   pitch_and_roll (pitch and roll stabilization)
:::

::: method
double MaximumAzimuthScanLimit()

Returns the maximum azimuth angular limit for scanning with this antenna. (degrees)
:::

::: method
void SetMaximumAzimuthScanLimit(double aLimit)

Sets the maximum azimuth angular limit for scanning with this antenna. (degrees)
:::

::: method
double MinimumAzimuthScanLimit()

Returns the minimum azimuth angular limit for scanning with this antenna. (degrees)
:::

::: method
void SetMinimumAzimuthScanLimit(double aLimit)

Sets the minimum azimuth angular limit for scanning with this antenna. (degrees)
:::

::: method
double MaximumElevationScanLimit()

Returns the maximum elevation angular limit for scanning with this antenna. (degrees)
:::

::: method
void SetMaximumElevationScanLimit(double aLimit)

Sets the maximum elevation angular limit for scanning with this antenna. (degrees)
:::

::: method
double MinimumElevationScanLimit()

Returns the minimum elevation angular limit for scanning with this antenna. (degrees)
:::

::: method
void SetMinimumElevationScanLimit(double aLimit)

Sets the minimum elevation angular limit for scanning with this antenna. (degrees)
:::