# 简化版AFSIM Platform Agent

一个专注于AFSIM脚本解释、参数查询和脚本生成的智能助手。

## 🎯 核心功能

### 1. 脚本解释
输入AFSIM平台脚本，获得详细的中文解释：

**输入示例**:
```
platform BBB WSF_PLATFORM 
     side blue
     icon f-15
     add mover WSF_FORMATION_FLYER 
          position 34:31:34.58n 113:28:09.47e altitude 2000 m
          speed 100 m/s
          lead_aircraft leader
          offset_forward_from_lead 1 au
     end_mover  
     position 34:29:24.66n 113:26:46.51e altitude 2000 m
end_platform
```

**输出**: 详细解释每个参数的含义和作用

### 2. 飞机参数查询
查询军用飞机的详细技术参数：

- **F-18 Hornet**: 多用途战斗机
- **F-22 Raptor**: 隐形战斗机  
- **F-35 Lightning II**: 多用途隐形战斗机

**查询示例**: "F-18参数"、"F-22技术信息"

### 3. 脚本生成
基于真实飞机数据生成AFSIM平台脚本：

**生成示例**: "生成F-18脚本"、"生成F-22脚本"

### 4. 参数查询
查询AFSIM平台参数的含义和用法：

**查询示例**: "altitude参数是什么意思？"、"platform命令有哪些参数？"

## 🚀 快速开始

### 1. 环境准备

```bash
# 设置API密钥
export DEEPSEEK_API_KEY="your_deepseek_api_key"

# 安装依赖（如果需要）
pip install -r requirements.txt
```

### 2. 命令行模式

```bash
# 交互模式
python simple_main.py

# 演示模式
python simple_main.py --mode demo
```

### 3. GUI界面

```bash
python simple_gui.py
```

## 💡 使用示例

### 脚本解释示例

**用户输入**:
```
platform fighter_01 WSF_PLATFORM
    side BLUE
    altitude 5000 m
    speed 200 m/s
end_platform
```

**系统输出**:
```
## AFSIM平台脚本解释

**平台名称**: fighter_01
**平台类型**: WSF_PLATFORM

### 主要参数:
- **side**: BLUE
  指定平台所属阵营，通常为BLUE（蓝方）或RED（红方）

- **altitude**: 5000 m
  设置平台的飞行高度

- **speed**: 200 m/s
  设置平台的移动速度
```

### 飞机参数查询示例

**用户**: "F-18参数"

**系统**: 
```
## F/A-18 Hornet 技术参数

**基本信息**:
- 类型: 多用途战斗机
- 制造国: 美国
- 制造商: 麦克唐纳·道格拉斯/波音
- 首飞: 1978

**尺寸参数**:
- 长度: 17.1 米
- 翼展: 11.4 米
- 高度: 4.7 米

**重量参数**:
- 空重: 10455 公斤
- 最大起飞重量: 25401 公斤
- 燃料容量: 4900 公斤

**性能参数**:
- 最大速度: 1915 公里/小时
- 实用升限: 15240 米
- 作战半径: 722 公里
```

### 脚本生成示例

**用户**: "生成F-18脚本"

**系统**:
```
## F/A-18 Hornet AFSIM脚本

```
platform f_18 WSF_PLATFORM
    side BLUE
    spatial_domain air
    
    empty_mass 10455 kg
    fuel_mass 4900 kg
    
    length 17.1 m
    width 11.4 m
    height 4.7 m
    
    icon F18
    marking "F/A-18 Hornet"
end_platform
```

**脚本说明**: 基于F/A-18 Hornet真实技术参数生成的AFSIM平台脚本

**数据来源**: 基于 F/A-18 Hornet 公开技术参数生成
```

## 🎮 交互命令

### 命令行模式
- `help` - 显示帮助信息
- `status` - 显示Agent状态
- `clear` - 清空对话历史
- `aircraft` - 显示可用飞机列表
- `example` - 显示示例脚本
- `quit/exit/q` - 退出程序

### GUI模式
- 左侧功能面板选择不同功能
- 输入框输入问题或脚本
- 点击"示例脚本"加载测试脚本
- 查看状态和清空历史

## 📊 支持的飞机型号

| 型号 | 名称 | 类型 | 制造商 |
|------|------|------|--------|
| F-18 | F/A-18 Hornet | 多用途战斗机 | 麦克唐纳·道格拉斯/波音 |
| F-22 | F-22 Raptor | 隐形战斗机 | 洛克希德·马丁 |
| F-35 | F-35 Lightning II | 多用途隐形战斗机 | 洛克希德·马丁 |

## 🔧 技术架构

- **核心引擎**: 基于向量数据库的文档检索
- **脚本解析**: 正则表达式和结构化解析
- **数据来源**: 公开的军用飞机技术参数
- **AI模型**: DeepSeek API进行智能问答
- **界面**: Tkinter GUI + 命令行界面

## 📝 文件结构

```
├── core/
│   ├── enhanced_platform_agent.py  # 简化版Agent核心
│   ├── platform_agent.py          # 基础Agent
│   ├── vector_database.py         # 向量数据库
│   └── chinese_summarizer.py      # 中文总结器
├── simple_main.py                 # 命令行界面
├── simple_gui.py                  # GUI界面
├── requirements.txt               # 依赖包
└── docs/                          # AFSIM文档
```

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   export DEEPSEEK_API_KEY="your_correct_api_key"
   ```

2. **GUI无法启动**
   ```bash
   # 确保安装了tkinter
   sudo apt-get install python3-tk  # Ubuntu/Debian
   ```

3. **向量数据库未初始化**
   ```bash
   python main.py index docs/ --force
   ```

## 🎯 使用技巧

1. **脚本解释**: 直接粘贴完整的AFSIM脚本获得最佳解释效果
2. **参数查询**: 使用具体的参数名称获得准确信息
3. **脚本生成**: 生成的脚本可以作为模板进一步修改
4. **对话历史**: 系统会记住对话上下文，支持连续提问

## 📄 许可证

MIT License
