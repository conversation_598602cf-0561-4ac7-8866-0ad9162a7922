orphan

:   

# UCI_COMPUTER

::: model
uci_component COMPUTER
:::

::: parsed-literal
uci_component \<type\> COMPUTER

> `uci_component`{.interpreted-text role="command"} Commands \...

end_uci_component
:::

## Overview

COMPUTER is the UCI component that gives platforms the ability to send, receive, and process UCI messages via internal_links and the Abstract Service Bus (ASB). Every platform with uci_component on it must have a computer and each uci_component must have a connection to that computer in order to send messages.

In the example below, the platform EXAMPLE has an IRST and an ESM sensor that can send and receive UCI messages through their corresponding uci_components. Each of their components have an internal_link to the computer which will send the messages it receives to the ASB. The internal_link from the computer to the sensor uci_component in turn will send the messages it receives from the ASB to be processed.

Example:

::: parsed-literal
platform EXAMPLE WSF_PLATFORM

> uci_component computer COMPUTER
>
> :   internal_link esm_uci_component internal_link irst_uci_component
>
> end_uci_component
>
> add sensor irst WSF_IRST_SENSOR
>
> :   sensor irst internal_link computer
>
> end_sensor
>
> add sensor esm WSF_ESM_SENSOR
>
> :   sensor esm internal_link computer
>
> end_sensor

end_platform
:::
