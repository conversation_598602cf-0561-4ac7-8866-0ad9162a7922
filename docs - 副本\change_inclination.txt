# change_inclination.rst.txt.md
orphan

:   



Script Type: `WsfChangeInclination`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} change_inclination

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [inclination]() \...

end_maneuver
:::

A maneuver to change the satellite orbit\'s inclination angle. Except in the case where the initial inclination angle is zero, this maneuver must be performed at one of the nodes (ascending or descending node). Changing the inclination to a value greater than 90 degrees implies a retrograde orbital motion. This maneuver is often executed to bring an orbit into the equatorial plane (inclination = 0).

> ::: note
> ::: title
> Note
> :::
>
> If the initial orbit is not equatorial (inclination == 0), a `constraint<orbital_event.constraints>`{.interpreted-text role="ref"} of `ascending_node`{.interpreted-text role="command"} or `descending_node`{.interpreted-text role="command"} must be specified.
> :::

::: command
inclination \<angle-value\>

The final inclination of the satellite\'s orbit.
:::