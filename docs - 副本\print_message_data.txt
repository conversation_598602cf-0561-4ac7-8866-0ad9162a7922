# print_message_data.rst.txt.md
orphan

:   



This is documentation for the \<message-data\> field referenced in the `csv_event_output`{.interpreted-text role="command"} and `event_output`{.interpreted-text role="command"} documentation.

::: {.contents local="" depth="1"}
:::



::: note
::: title
Note
:::

The format varies depending on the message type. All of the \<track-data\> prints if `event_output.print_track_in_message`{.interpreted-text role="command"} is enabled; otherwise, a brief form of the \<track-data\> is printed.
:::

+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| Condition                                                                                                                                                               | Signature                                                                                                                                          |
+=========================================================================================================================================================================+====================================================================================================================================================+
| If the message is a `WsfTrackMessage`{.interpreted-text role="class"} AND the track exists OR the message is a `WsfTaskAssignMessage`{.interpreted-text role="class"} { | CSV:                                                                                                                                               |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | ::: parsed-literal                                                                                                                                 |
|                                                                                                                                                                         | \<track~id~\>,                                                                                                                                     |
|                                                                                                                                                                         | :::                                                                                                                                                |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | EVT:                                                                                                                                               |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | > TrackId: \<track~id~\>                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| If `event_output.print_track_in_message`{.interpreted-text role="command"}                                                                                              | ::: parsed-literal                                                                                                                                 |
|                                                                                                                                                                         | :::                                                                                                                                                |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| Else                                                                                                                                                                    | ::: parsed-literal                                                                                                                                 |
|                                                                                                                                                                         | :::                                                                                                                                                |
| }                                                                                                                                                                       |                                                                                                                                                    |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| If the message is a `WsfTrackDropMessage`{.interpreted-text role="class"}                                                                                               | CSV:                                                                                                                                               |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | > \<track~id~\>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,                                                                        |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | EVT:                                                                                                                                               |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | > TrackId: \<track~id~\>                                                                                                                           |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| If the message is a `WsfStatusMessage`{.interpreted-text role="class"}                                                                                                  | CSV:                                                                                                                                               |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | > \<status\>,\<request~id~\>,\<system~name~\>,\<platform~name~\>,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,                      |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | EVT:                                                                                                                                               |
|                                                                                                                                                                         |                                                                                                                                                    |
|                                                                                                                                                                         | > Status: \<status\> RequestId: \<request~id~\> System: \<system~name~\> Platform: \<platform~name~\>                                              |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+
| Else                                                                                                                                                                    | All other messages have their own way of printing. The appropriate printing function will execute if it exists; otherwise nothing will be printed. |
+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------------------------------------------------------------------------------------------------------------------------+

: \<message-data\> Possible Signatures



+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------+---------------------------------------------------------+
| Condition                                                                                                                                                                | Field                            | Description                                             |
+==========================================================================================================================================================================+==================================+=========================================================+
| For `WsfTrackMessage`{.interpreted-text role="class"}, `WsfTrackDropMessage`{.interpreted-text role="class"}, and `WsfTaskAssignMessage`{.interpreted-text role="class"} | \<track~id~\>                    | The ID of the track within the message                  |
|                                                                                                                                                                          |                                  |                                                         |
| For `WsfStatusMessage`{.interpreted-text role="class"} { If the message has a request If the message has a system                                                        | \<track-data\>                   | See \<track-data\> table below                      |
|                                                                                                                                                                          |                                  |                                                         |
|                                                                                                                                                                          | \<request~id~\> \<system~name~\> |                                                         |
+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------+---------------------------------------------------------+
| If the message has an associated platform                                                                                                                                | \<platform~name~\>               | ::: note                                                |
|                                                                                                                                                                          |                                  | ::: title                                               |
| }                                                                                                                                                                        | Not applicable                   | Note                                                    |
|                                                                                                                                                                          |                                  | :::                                                     |
| For all other messages                                                                                                                                                   |                                  |                                                         |
|                                                                                                                                                                          |                                  | This platform is not the platform who sent the message. |
|                                                                                                                                                                          |                                  | :::                                                     |
+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------------------+---------------------------------------------------------+

: \<message-data\> Signature Elements



::: admonition
Examples

from heatmap/heatmap_commander_demo extracted from MESSAGE_QUEUED message (for `WsfTrackMessage`{.interpreted-text role="class"})

-   csv_event_output Example:

        ,,,,ew_radar.1,2.700000e+02,2.700000e+02,0,5.000000e-01,air,M,,,red_1,STRIKER,red,-1.630000e+01,4.972858e+01,6.00000000e+00,-3.09694815e+06,5.28277976e+06,-1.77779023e+06,-1.624251e+01,5.076280e+01,1.06680000e+04,-3.19805944e+06,5.23628797e+06,-1.77463341e+06,L,3,,,,-1.624251e+01,5.076280e+01,1.06680000e+04,-3.19805944e+06,5.23628797e+06,-1.77463341e+06,0.000000e+00,2.572222e+02,4.709940e+00,2.572222e+02,4.709940e+00,1.113326e+05,1.515836e+00,8.723217e-02,1.113326e+05,1.515836e+00,8.723217e-02,,,,,,,,0,,,

-   event_output Example:

        TrackId: ew_radar.1
        Start_Time: 00:04:30.0 Update_Time: 00:04:30.0 Update_Count: 0 Quality: 0.5 Domain: air Type: M
        Target_Truth: Name: red_1 Type: STRIKER Side: red
        Originator: LLA: 16:18:00.00s 49:43:42.88e 6 m
        Track: LLA: 16:14:33.02s 50:45:46.08e 10668 m Flags: L3
        Truth: LLA: 16:14:33.02s 50:45:46.08e 10668 m  Difference: 0 m
        Track: Vel: 257.222 m/s Hdg: 269.86 deg  Truth: Vel: 257.222 m/s Hdg: 269.86 deg
        Track: Range: 111333 m Bearing: 86.851 deg Elevation: 4.99804 deg
        Truth: Range: 111333 m Bearing: 86.851 deg Elevation: 4.99804 deg
:::

::: admonition
Examples

from heatmap/heatmap_commander_demo extracted from MESSAGE_QUEUED message (for `WsfTrackDropMessage`{.interpreted-text role="class"})

-   csv_event_output Example:

        ,,,,ew_radar.1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,

-   event_output Example:

        TrackId: ew_radar.1
:::

::: admonition
Examples

from heatmap/heatmap_commander_demo extracted from MESSAGE_QUEUED message (for `WsfTrackDropMessage`{.interpreted-text role="class"})

-   csv_event_output Example:

        SYSTEM_TURNED_ON,,ew_radar,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,

-   event_output Example:

        Status: SYSTEM_TURNED_ON System: ew_radar
:::

::: admonition
Examples

from heatmap/heatmap_commander_demo extracted from MESSAGE_QUEUED message (for `WsfTaskAssignMessage`{.interpreted-text role="class"})

-   csv_event_output Example:

        ,,,,flight_lead.1,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,

-   event_output Example:

        TrackId: flight_lead.1
        Start_Time: 00:04:30.0 Update_Time: 00:04:30.0 Update_Count: 0 Quality: 0.5 Domain: air Type: P
        Target_Truth: Name: red_1 Type: STRIKER Side: red
        Originator: LLA: 16:18:00.00s 49:43:42.88e 6 m
        Track: LLA: 16:14:33.02s 50:45:46.08e 10668 m Flags: L3
        Truth: LLA: 16:14:33.22s 50:44:19.48e 10668 m  Difference: 2576.16 m
        Track: Vel: 257.222 m/s Hdg: 269.866 deg  Truth: Vel: 257.222 m/s Hdg: 269.866 deg
        Track: Range: 111333 m Bearing: 86.851 deg Elevation: 4.99804 deg
        Truth: Range: 108775 m Bearing: 86.7726 deg Elevation: 5.13919 deg
:::