# coverage_measure_lat_lon_stats.rst.txt.md
orphan

:   



::: parsed-literal

output lat_lon_stats

:   [latitude]() [longitude]() [bin_size]() \<angle-value\> [file]() \<file-name\> [width]() \<integer\> [precision]() \<integer\> [format]() \... [justify]() \...

end_output
:::

Lat/lon stats output will produce a file containing statistics of the measured value for each latitude, or longitude. The statistics are computed by projecting out either the latitude or longitude, binning the resulting points, and computing the minimum, maximum, and mean value of the measure for each bin. A line is written to the file only if the bin is not empty.



::: command
latitude

Produce summary statistics as a function of latitude.
:::

::: command
longitude

Produce summary statistics as a function of longitude.
:::

::: command
bin_size \<angle-value\>

Specify the size of the bins into which the grid points are projected.
:::