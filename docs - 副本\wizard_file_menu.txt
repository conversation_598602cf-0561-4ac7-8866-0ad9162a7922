# wizard_file_menu.rst.txt.md
orphan

:   





-   Open Project\... - Browse to open a project.
-   Open Recent - Open a recently used project.
-   New Project - Create a new project.
-   New File - Create a new file in a text editor.
-   Open File - Open a file in a text editor.
-   Close Project - Close the project.
-   Save Project\... - Save the project.
-   Export Project\... - Save the project in a new directory.
-   Save File\... - Save the file in the currently active editor window.
-   Save File As\... - Save the file in the currently active editor window under a different name.
-   Save All - Save all files in editor windows.
-   Print File\... - Print the file in the currently active editor window.
-   Save Configuration - Save the current `application settings<wizard_user_configurations>`{.interpreted-text role="doc"} to file.
-   Load Configuration - Load a saved `application settings<wizard_user_configurations>`{.interpreted-text role="doc"} file.
-   Import Configuration Options\... - Import some features from an `application settings<wizard_user_configurations>`{.interpreted-text role="doc"} file.
-   Recent Configurations - Choose from a recently used `application settings<wizard_user_configurations>`{.interpreted-text role="doc"} file.
-   Clear Platform Options - Clear out all of the `platform options<wizard_platform_options>`{.interpreted-text role="doc"}.
-   Exit - Exit the application.