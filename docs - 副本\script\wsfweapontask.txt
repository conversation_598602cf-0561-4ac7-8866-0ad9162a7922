# wsfweapontask.rst.txt.md
orphan

:   



::: {.WsfWeaponTask .inherits .WsfTask}
`WsfWeaponTask`{.interpreted-text role="class"} is a `WsfTask`{.interpreted-text role="class"} for firing a weapon.
:::



::: method
WsfWeaponTask Create(string aTaskType, string aWeaponName)

Returns a new `WsfWeaponTask`{.interpreted-text role="class"} with the given task type and weapon name.
:::

::: method
int Quantity()

Returns the number of weapons to be fired.
:::

::: method
void SetQuantity(int aQuantity)

Sets the number of weapons to be fired. To be used prior to `WsfTaskManager::AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}.
:::