# wk_application_launcher.rst.txt.md
orphan

:   





The `../warlock`{.interpreted-text role="doc"} Application Launcher provides a toolbar to open scenarios in other AFSIM applications.

By clicking on the associated icon on the toolbar, a user can open the following applications:

-   `../wizard`{.interpreted-text role="doc"} - An IDE used to edit the scenario. Please note that any changes to the scenario made in <PERSON> will not be reflected on Warlock\'s graphical interface unless the scenario is reloaded.
-   `../mystic`{.interpreted-text role="doc"} - A visualization tool used to view an affiliated AFSIM event recording file (.aer).