# wsfnormalmaneuver.rst.txt.md
orphan

:   



::: {.WsfNormalManeuver .inherits .WsfOrbitalManeuver}
Input type: `normal<../normal>`{.interpreted-text role="doc"}
:::

[WsfNormalManeuver](#wsfnormalmaneuver) can be used to change a delta V normal to a platforms orbit.

::: method
static WsfNormalManeuver Construct(WsfOrbitalEventCondition aCondition, double aDeltaV)

Static method to create a [WsfNormalManeuver](#wsfnormalmaneuver) using a specific condition and a delta-V (m/s) normal to the current vector of the platform.
:::