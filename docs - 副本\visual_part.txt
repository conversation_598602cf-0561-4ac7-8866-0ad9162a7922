# visual_part.rst.txt.md
orphan

:   

::: demo
visual_part
:::

| This demo directory demonstrates the use of WSF_VISUAL_PART, and the
| publishing of articulated parts to DIS. There are two tanks with visual
| parts. A script processor updates these to point at a aircraft using
| truth. The dis_interface block allows the visual parts (or any articulated
| parts) to publish over the DIS entity state PDUs.