# circularize.rst.txt.md
orphan

:   



Script Type: `WsfCircularize`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} circularize

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"}

end_maneuver
:::

Circularize the orbit (change eccentricity to zero) at the given radius as provided by the `ascending_radius`{.interpreted-text role="command"} or `descending_radius`{.interpreted-text role="command"} constraints.

> ::: note
> ::: title
> Note
> :::
>
> A `constraint<orbital_event.constraints>`{.interpreted-text role="ref"} of `ascending_radius`{.interpreted-text role="command"} or `descending_radius`{.interpreted-text role="command"} must be specified for this maneuver.
> :::
>
> ::: note
> ::: title
> Note
> :::
>
> The initial orbit must be elliptical, and the provided radius must be greater than or equal to the periapsis of the initial orbit and less than or equal to the apoapsis.
> :::