# cyber_effect.rst.txt.md
orphan

:   



::: {.command block=""}
cyber_effect
:::

::: parsed-literal
\# Defining a new type: cyber_effect *\<name\>* *\<type-name\>* \... end_cyber_effect

\# Adding instance inside a \'cyber_attack\' block: \... effect *\<name\>* \...
:::

-   \<name\> : Name of the Cyber effect type or instance to be created.
-   \<type-name\> : Name of an existing or predefined Cyber effect type whose definition will be used as the initial definition of the new type or instance.



An cyber_effect provides the ability to define the effect(s) associated with a specific `cyber_attack`{.interpreted-text role="command"}.