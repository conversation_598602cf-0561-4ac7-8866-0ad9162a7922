orphan

:   

# WsfPolygonalFieldOfView

::: {.WsfPolygonalFieldOfView .inherits .WsfFieldOfView cloneable=""}
`WsfPolygonalFieldOfView`{.interpreted-text role="class"} defines a `polygonal field of view<field_of_view_commands.polygonal>`{.interpreted-text role="ref"} that is used to dynamically change a sensor\'s field of view (originally defined using `antenna field of view commands<field_of_view>`{.interpreted-text role="command"}) using `WsfSensor.SetFOV`{.interpreted-text role="method"}.
:::

## Static Methods

::: method
static WsfPolygonalFieldOfView Construct(Array\<Array\<double\>\> aAzElPoints)

Returns a new `WsfPolygonalFieldOfView`{.interpreted-text role="class"} object defined with the specified azimuth and elevation points (in degrees).

Example:

    // Create a polygonal field of view consisting of three (az, el) pairs forming a triangle-shaped frustrum
    double az = 5.0; // deg
    double el = 5.0; // deg
    Array<Array<double>> points = {};
    Array<double> point1 = {};
    point1.PushBack(-az);
    point1.PushBack(el);
    points.PushBack(point1);
    Array<double> point2 = {};
    point2.PushBack(0.0);
    point2.PushBack(-el);
    points.PushBack(point2);
    Array<double> point3 = {};
    point3.PushBack(az);
    point3.PushBack(el);
    points.PushBack(point3);
    WsfPolygonalFieldOfView pfov = WsfPolygonalFieldOfView.Construct(points);

::: note
::: title
Note
:::

At least three (azimuth, elevation) points must be defined in a valid polygonal field of view.
:::
:::

## Methods

::: method
Array\<Array\<double\>\> AzElPoints()

Returns the array of (azimuth, elevation) pairs (in degrees) that define the points of the polygonal field of view.
:::
