orphan

:   

# WsfTaskAssignMessage

::: {.WsfTaskAssignMessage .inherits .WsfMessage}
`WsfTaskAssignMessage`{.interpreted-text role="class"} is sent by `WsfTaskManager::AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"} method when a task assignment is made. It should normally be processed by the receiving task manager but can be examined by a script method that handles messages.
:::

## Methods

::: method
WsfPlatform Assigner()

Returns the platform that assigned the task.
:::

::: method
string AssignerName()

Returns the name of the platform that assigned the task.
:::

::: method
WsfTrack Track()

Returns the track associated with the task.
:::

::: method
string TaskType()

Returns the task type.
:::

::: method
string ResourceName()

Returns the resource name specified to use in the task.
:::
