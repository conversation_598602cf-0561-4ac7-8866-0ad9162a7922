# wsfimplicitweapon.rst.txt.md
orphan

:   





::: method
bool Fire(string aTargetName)

Fire the weapon at a target. If a launch computer is associated with the `weapon <WSF_IMPLICIT_WEAPON>`{.interpreted-text role="model"} definition, the engagement will automatically terminate after the estimated engagement time from the launch computer. Otherwise, one needs to call `CeaseFire <WsfWeapon.CeaseFire>`{.interpreted-text role="method"} to terminate the engagement.
:::

::: method
bool Fire(string aTargetName, string aTargetOffset)

Fire the weapon at a target, specifying a targeted offset or sub-component of the platform. This form of `Fire <WsfImplicitWeapon.Fire>`{.interpreted-text role="method"} is appropriate when using directed energy weapons (e.g., `WSF_LASER_WEAPON`{.interpreted-text role="model"}).
:::