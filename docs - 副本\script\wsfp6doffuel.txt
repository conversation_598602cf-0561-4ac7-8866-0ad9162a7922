# wsfp6doffuel.rst.txt.md
orphan

:   



::: {.WsfP6DOF_Fuel .inherits .WsfFuel}
Like `WSF_FUEL`{.interpreted-text role="model"}, `WSF_P6DOF_FUEL`{.interpreted-text role="model"} encapsulates the ability to make queries regarding fuel status. In addition to the methods provided in `WsfP6DOF_Mover`{.interpreted-text role="class"}, this provides common fuel-related methods derived from the `WSF_FUEL`{.interpreted-text role="model"} class.
:::



::: method
bool Refuel() bool Refuel(double aAmount)

Refuels the platform to its maximum fuel capacity. Returns \'true\' if successful.
:::

::: method
double QuantityRemaining()

Returns the quantity of fuel in kg remaining on the platform.
:::

::: method
double QuantityRequired(double aDistance) double QuantityRequired(double aDistance, double aAltitude, double aSpeed)

Returns the quantity of fuel in kg required to travel the given distance. If no altitude and speed are provided, the platform\'s current altitude and speed are used.
:::

::: method
double QuantityRequired(WsfRoute aRoute)

Returns the quantity of fuel in kg required to travel the given route. The altitude and speed of each waypoint in the route are used in the calculation.
:::

::: method
void SetQuantityRemaining(double aQuantityRemaining)

Sets the remaining fuel quantity to the specified value (kg).
:::

::: method
double ConsumptionRate() double ConsumptionRate(double aAltitude, double aSpeed)

Returns the platform\'s fuel consumption rate (kg/sec). If no altitude and speed are provided, the platform\'s current altitude and speed are used.
:::

::: method
void SetConsumptionRate(double aConsumptionRate)

Sets the fuel consumption rate to the specified value (kg/sec).
:::

::: warning
::: title
Warning
:::

This method should only be used with the fixed consumption rate fuel model (i.e., not with P6DOF movers).
:::

::: method
double MaximumQuantity()

Returns the maximum fuel quantity (kg).
:::

::: method
double InitialQuantity()

Returns the initial fuel quantity (kg).
:::

::: method
void SetInitialQuantity(double aInitialQuantity)

Sets the initial fuel quantity to the specified value (kg).
:::

::: method
double BingoQuantity()

Returns the bingo fuel quantity (kg).
:::

::: method
void SetBingoQuantity(double aBingoQuantity)

Sets the bingo fuel quantity to the specified value (kg).
:::

::: method
double ReserveQuantity()

Returns the reserve fuel quantity (kg).
:::

::: method
void SetReserveQuantity(double aReserveQuantity)

Sets the reserve fuel quantity to the specified value (kg).
:::

::: method
double TimeAtBingo() double TimeAtBingo(double aAltitude, double aSpeed)

Returns the time in seconds that the platform can travel after BINGO. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double TimeAtReserve() double TimeAtReserve(double aAltitude, double aSpeed)

Returns the time in seconds that the platform can travel after reserve limit. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double TimeToBingo() double TimeToBingo(double aAltitude, double aSpeed)

Returns the time in seconds until the platform will declare BINGO. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double TimeToEmpty() double TimeToEmpty(double aAltitude, double aSpeed)

Returns the time in seconds until the platform is completely out of fuel. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double TimeToReserve() double TimeToReserve(double aAltitude, double aSpeed)

Returns the time in seconds until the platform reaches its fuel reserve limit. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double DistanceAtBingo() double DistanceAtBingo(double aAltitude, double aSpeed)

Returns the distance in meters that the platform can travel after BINGO. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double DistanceAtReserve() double DistanceAtReserve(double aAltitude, double aSpeed)

Returns the distance in meters that the platform can travel after reserve limit. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double DistanceToBingo() double DistanceToBingo(double aAltitude, double aSpeed)

Returns the distance in meters until the platform will declare BINGO. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double DistanceToReserve() double DistanceToReserve(double aAltitude, double aSpeed)

Returns the distance in meters until the platform reaches its fuel reserve limit. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::

::: method
double DistanceToEmpty() double DistanceToEmpty(double aAltitude, double aSpeed)

Returns the distance in meters until the platform is completely out of fuel. If no altitude and speed are provided, the platform\'s current altitude and speed are used. P6DOF movers do not support the altitude and speed parameters and use the current altitude and speed for both methods.
:::