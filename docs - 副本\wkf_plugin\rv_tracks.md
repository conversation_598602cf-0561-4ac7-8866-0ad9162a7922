orphan

:   

# Tracks - Mystic

The Tracks feature is responsible for displaying tracks on the Map Display and for information related to tracks shown in the Platform Details display.

Track icons appear on the Map Display for local tracks in the scenario.

![image](../images/rv_TracksDisplay.png)

## Platform Details

When a platform is selected, its Master Track and Sensor Track lists are added to the Platform Details display. Clicking on one of the local tracks from the list will highlight the track in the Map Display.

![image](../images/rv_TracksDetails.png)

Right clicking on a track will provide options to trace or plot a track.

### Trace

Trace will show a history of the track.

![image](../images/rv_TrackTrace.png)

### Plot {#rv_tracks_plot}

Plotting a track metric will show the metric\'s history versus time. Truth may be shown on the plot as well, and for local tracks the contributing sensor tracks may be plotted.

![image](../images/rv_TrackPlot.png)

#### Preferences {#rv_tracks_preferences}

![image](../images/rv_TracksPreferences.png)

The Track Visibility Preferences page controls when track are shown to the user on the Map Display.

-   Show No Tracks - No tracks will be shown on the Map Display.
-   Show Local Tracks Only - Displays only the local tracks for selected platforms.
-   Show All Visible Teams\' Tracks - Displays all local track for all platforms that are on a team that is marked as visible.
-   Show Selected Team\'s Tracks - Shows all tracks for the team indicated by the combo box.
-   Show Tracks From Network Simulations - Displays tracks received from AFSIM applications that are connected via XIO.

There are also customization options for how the track will appear to the user.

-   Show Team Color - Tracks will be colored according to the reported team color.
-   Show Track ID Label - Tracks will be labeled with the track ID according to the reported team.
-   Show Track Orientation - Controls whether track icons are billboarded or are angled according to a reported orientation.
-   Label Font - Sets the font applied to track labels.
-   Track Scale - Scales the track icon.
-   Wedge Icon - Shows the default track icon on the Map Display.
-   Custom Track Icon - Shows a specified track icon on the Map Display. To select an icon, either drag/drop a supported image or use the browser to manually select one.
-   Rules-Based Icon - If the \"Use Target Platform Icon\" box is checked, shows the model of the tracked platform. Otherwise, uses a custom model based on the track\'s reported spatial domain. Unspecified icons use the Wedge icon.

Hovering over a track icon on the Map Display provides the following information: track id, side, type, spatial domain, and heading.
