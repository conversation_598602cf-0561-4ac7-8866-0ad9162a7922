# wsfsixdofformationcommandsequence.rst.txt.md
orphan

:   



::: {.WsfSixDOF_FormationCommandSequence .inherits .WsfSixDOF_FormationCommand}
A `WsfSixDOF_FormationCommandSequence`{.interpreted-text role="class"} is a command that executes a sequence of commands in turn.
:::



::: method
static WsfSixDOF_FormationCommandSequence Construct()

Create an empty command sequence.
:::

::: method
void AppendCommand(WsfSixDOF_FormationCommand aCommand)

Append the given command to the command sequence. This method will do nothing if the command sequence has already been assigned to a formation.
:::

::: method
void AppendCommand(WsfSixDOF_FormationCommand aCommand, WsfSixDOF_FormationCommandConstraint aConstraint)

Append the given command to the command sequence, and give that command the given constraint. This method will do nothing if the command sequence has already been assigned to a formation.
:::

::: method
int GetNumCommands()

Return the number of commands in this command sequence.
:::

::: method
WsfSixDOF_FormationCommand CurrentCommand()

Return the command in the sequence that is currently being executed.
:::