# wiz_type_browser.rst.txt.md
orphan

:   



The Type Browser is accessible from the View menu. The browser may be docked in the left or right docking areas of the main window or be free-floating.



The browser provides a tree-view listing of all of the types that exist in the currently loaded project. The display may be expanded to show the derived types.

Double-clicking on the platform or platform-component nodes in the tree will open the command documentation for the those types.

Double-clicking on a named type will open up a `Text Editor<../wizard_text_editor>`{.interpreted-text role="doc"} and navigate to the appropriate definition.

The browser contains a search field at the top. Typing in this field will filter to types with names containing the value.