orphan

:   

# UCI_POST_ActivityCommand

## Overview {#overview .UCI_POST_ActivityCommand}

This command type will modify existing POST activities.

## Static Methods

::: method
static UCI_POST_ActivityCommand Construct(UCI_ActivityId existingPOST_ActivityId)

This method will create an instance of a UCI_POST_ActivityCommand that, if approved, will modify the activity with the given activity ID. The CommandID will be randomly generated upon creation.
:::

## Methods

::: method
void SetPointing(UCI_Pointing pointing)

This method will set the indicated position or location that the sensor will be commanded to point to.
:::
