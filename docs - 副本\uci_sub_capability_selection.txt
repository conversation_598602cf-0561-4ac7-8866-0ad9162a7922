# uci_sub_capability_selection.rst.txt.md
orphan

:   





This type indicates the subcapability and its details being commanded.



::: method
static UCI_SubCapabilitySelection Construct(UCI_SubCapabilityDetails capabilityDetails)

Creates a [UCI_SubCapabilitySelection](#uci_subcapabilityselection) with the new details for the capability.
:::



::: method
void PushBack(UCI_SubCapabilityDetails capabilityDetails)

Adds capabilityDetails to the [UCI_SubCapabilitySelection](#uci_subcapabilityselection).
:::