# new_guidance.rst.txt.md
orphan

:   

::: demo
new_guidance
:::

| This demo directory illustrates the use of the WSF_NEW_GUIDED_MOVER as well as
| the WSF_NEW_GUIDANCE_COMPUTER. A weapon is given the new mover and processor
| to precisely control its fly-out. (The use of \"WSF_NEW\_\" has been deprecated.)
| 
| To run the demos, type \"run new_guidance\[1-3\].txt\"



| Similar to the ship_ad_demo, a weapon is launched from a ship given
| a track from a remote sensor. During missile fly-out, the remote
| sensor platform provides in-flight target location updates to the
| missile. At terminal, a radar sensor on the missile provides final guidance
| to the target.



| Has a jet aircraft use the weapon defined in new_guidance1.txt but
| fires at a pre-briefed track (no sensors).



| Demonstrates a weapon fly-out that has to go to a lat and lon enroute
| to the target. It does not use the weapon defined in the first two scenarios.