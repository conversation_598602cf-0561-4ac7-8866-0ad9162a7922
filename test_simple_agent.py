#!/usr/bin/env python3
"""
测试简化版AFSIM Platform Agent
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.enhanced_platform_agent import SimplePlatformAgent


def test_script_explanation():
    """测试脚本解释功能"""
    print("🧪 测试脚本解释功能")
    print("=" * 50)
    
    agent = SimplePlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 测试脚本
    test_script = """platform BBB WSF_PLATFORM 
     side blue
     icon f-15
     add mover WSF_FORMATION_FLYER 
          position 34:31:34.58n 113:28:09.47e altitude 2000 m
          speed 100 m/s
          lead_aircraft leader
          offset_forward_from_lead 1 au
     end_mover  
     position 34:29:24.66n 113:26:46.51e altitude 2000 m
end_platform"""
    
    print("输入脚本:")
    print(test_script)
    print("\n解释结果:")
    print("-" * 30)
    
    result = agent.chat(test_script)
    print(result)


def test_aircraft_info():
    """测试飞机信息查询"""
    print("\n🧪 测试飞机信息查询")
    print("=" * 50)
    
    agent = SimplePlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 测试查询
    queries = ["F-18参数", "F-22信息", "F-35技术参数"]
    
    for query in queries:
        print(f"\n查询: {query}")
        print("-" * 20)
        result = agent.chat(query)
        # 只显示前200字符
        print(result[:200] + "..." if len(result) > 200 else result)


def test_script_generation():
    """测试脚本生成功能"""
    print("\n🧪 测试脚本生成功能")
    print("=" * 50)
    
    agent = SimplePlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 测试生成
    queries = ["生成F-18脚本", "生成F-22脚本"]
    
    for query in queries:
        print(f"\n请求: {query}")
        print("-" * 20)
        result = agent.chat(query)
        # 只显示前300字符
        print(result[:300] + "..." if len(result) > 300 else result)


def test_parameter_query():
    """测试参数查询功能"""
    print("\n🧪 测试参数查询功能")
    print("=" * 50)
    
    agent = SimplePlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 测试查询
    queries = ["altitude参数是什么意思？", "platform命令有哪些参数？"]
    
    for query in queries:
        print(f"\n查询: {query}")
        print("-" * 20)
        result = agent.chat(query)
        # 只显示前200字符
        print(result[:200] + "..." if len(result) > 200 else result)


def test_aircraft_database():
    """测试飞机数据库"""
    print("\n🧪 测试飞机数据库")
    print("=" * 50)
    
    agent = SimplePlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 获取可用飞机
    aircraft_list = agent.get_available_aircraft()
    print(f"可用飞机数量: {len(aircraft_list)}")
    print(f"飞机列表: {', '.join(aircraft_list)}")
    
    # 测试每种飞机的信息
    for aircraft in aircraft_list:
        info = agent.get_aircraft_info(aircraft)
        print(f"\n{aircraft} 信息:")
        print("-" * 20)
        # 只显示前150字符
        print(info[:150] + "..." if len(info) > 150 else info)


def test_conversation_flow():
    """测试对话流程"""
    print("\n🧪 测试对话流程")
    print("=" * 50)
    
    agent = SimplePlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 模拟对话
    conversation = [
        "你好",
        "F-18有哪些参数？",
        "生成一个F-18的脚本",
        "altitude参数是什么意思？"
    ]
    
    for i, message in enumerate(conversation, 1):
        print(f"\n轮次 {i}")
        print(f"用户: {message}")
        response = agent.chat(message)
        print(f"助手: {response[:100]}..." if len(response) > 100 else f"助手: {response}")
    
    # 检查对话历史
    history = agent.get_conversation_history()
    print(f"\n对话历史长度: {len(history)}")


def main():
    """主测试函数"""
    print("🚀 简化版AFSIM Platform Agent 测试")
    print("=" * 60)
    
    # 检查环境变量（可选，因为我们主要测试本地功能）
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️  未设置DEEPSEEK_API_KEY，某些功能可能无法正常工作")
        print("   但基础功能仍可测试")
    
    try:
        # 测试飞机数据库（不需要API）
        test_aircraft_database()
        
        # 如果有API密钥，测试其他功能
        if os.getenv("DEEPSEEK_API_KEY"):
            test_script_explanation()
            test_aircraft_info()
            test_script_generation()
            test_parameter_query()
            test_conversation_flow()
        else:
            print("\n⚠️  跳过需要API的测试")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n✅ 测试完成")


if __name__ == '__main__':
    main()
