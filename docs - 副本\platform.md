orphan

:   

# platform

::: {.contents local=""}
:::

**Script Class:** `WsfPlatform`{.interpreted-text role="class"}

::: command
platform \... end_platform

::: parsed-literal

[platform](#platform) *\<platform-name\>* *\<platform-type\>*

:   [acoustic_signature]() \... [altitude]() \... [aux_data]() \... [category]() \... [clear_categories]() [commander]() \... [command_chain]() \... [concealment_factor]() \... [creation_time]() \... [empty_mass]() \... [fuel]() \... end_fuel [fuel_mass]() \... [geo_point]() \... [group_join]() \... [group_leave]() \... [heading]() \... [height]() \... [icon]() \... [indestructible]() \... [infrared_signature]() \... [inherent_contrast]() \... [initial_damage_factor]() \... [length]() \... [marking]() \... [mgrs_coordinate]() \... [add_mover]() \... end_mover [mover]() \... end_mover [navigation_errors]() \... end_navigation_errors [nutation_update_interval]() \... [on_broken]() \... [optical_reflectivity]() \... [optical_signature]() \... [payload_mass]() \... [position]() \... [precession_nutation_update_interval]() \... [radar_signature]() \... [route]() \... end_route [side]() \... [spatial_domain]() \... `\<component\><platform.component>`{.interpreted-text role="ref"} \... [end]()\<component\> [add]() \<component\> \... [end]()\<component\> [edit]() \<component\> \... [end]()\<component\> [delete]() \<component\> \... [track]() \... end_track [track_manager]() \... end_track_manager [use_zone]() \... [width]() \... [zone]() \... end_zone [zone_set]() \... end_zone_set

    \# [Script Interface](#script-interface)

    `on_initialize`{.interpreted-text role="command"} \... end_on_initialize `on_initialize2`{.interpreted-text role="command"} \... end_on_initialize2 `on_update`{.interpreted-text role="command"} \... end_on_update `_.script_commands.script_variables`{.interpreted-text role="command"} \... end_script_variables `_.script_commands.script`{.interpreted-text role="command"} \... end_script \... Other Script_Commands \...

    script void [on_damage_received]()() \... end_script script void [on_platform_deleted]()() \... end_script script void [on_commander_deleted]()(`WsfPlatform`{.interpreted-text role="class"} aCommander) \... end_script script void [on_peer_deleted]()(`WsfPlatform`{.interpreted-text role="class"} aPeer) \... end_script script void [on_subordinate_deleted]()(`WsfPlatform`{.interpreted-text role="class"} aSubordinate) \... end_script `platform.callback`{.interpreted-text role="command"} \... end_callback

end_platform
:::
:::

## Overview

The **platform_type** command is used to create a platform type object that can be used as the source for the platform command. The **platform** command creates an object instance in the simulation and usually uses a previously defined platform_type as its base. Both platform_type and platform use many of the same subcommands. However, it is recommended to use only subcommands that apply to static definitions when using the **platform_type** command. In addition, creating a detailed platform without using a predefined platform base does not allow for easy reuse of the object in the simulation.

### Creating a New Instance

::: command
platform \<platform-name\> \<platform-type-name\> \... end_platform
:::

::: parsed-literal

platform *\<platform-name\>* *\<platform-type-name\>*

:   \... *platform commands* \...

end_platform

Defines a platform instance in the simulation or can be used to edit a platform instance. The *\<platform-name\>* specifies the name and must be a unique name in the simulation. Convention is to make *\<platform-name\>* lowercase. The *\<platform-type\>* can either be a previously defined **platform_type** or **WSF_PLATFORM**. **WSF_PLATFORM** is the only predefined platform type in WSF.

A unique platform name can be generated by specifying **\<default\>** (with the brackets) as the *\<platform-name\>*. The generated name will be *\<platform-type-name\>:\<number\>*.
:::

### Creating a New Type

::: block
platform
:::

::: command
platform_type \<new-type-name\> \<source-type-name\> \... end_platform_type

::: parsed-literal

platform_type *\<new-type-name\>* *\<source-type-name\>*

:   \... *platform commands* \...

end_platform_type
:::

Defines a platform type that can be referenced by the platform command. The *\<new-type-name\>* must be unique to the simulation. Convention is to make *\<new-type-name\>* uppercase. The *\<source-type-name\>* can either be a previously defined platform type or be **WSF_PLATFORM**. **WSF_PLATFORM** is the only *\<source-type-name\>* predefined platform type in WSF.
:::

### Editing an Instance

    edit platform <platform-name> ... end_platform

    edit platform <platform-name>
      ... platform commands ...
    end_platform

This command is useful if a platform is defined in a common scenario file, but one wants to override a setting without modifying the common scenario file. For instance, if one wants to change the route of a platform that was defined in a common scenario file\...

> This might be part of the common scenario file \'common_scenario.txt\':
>
>     platform airliner-1 AIRLINER
>        route
>           ... the baseline route ...
>        end_route
>     end_platform
>
> This is how one would override the route for *airliner-1*:
>
>     include common_scenario.txt
>
>     edit platform airliner-1
>        route
>           ... a different route ...
>        end_route
>     end_platform

## Commands

::: block
platform
:::

::: command
side \<side-name\>

Specify the \'side\' (\'team\' or \'affiliation\') to which platforms of this type belong. The side subcommand refers to blue, red, country or team name. Refer to the specific visualization application or tool to see what sides are supported.

**Default** None.
:::

::: command
icon \<icon-name\>

Specify the name of the icon to be used when platforms of this type are displayed and if the platform type does not have an associated `dis_interface.entity_type`{.interpreted-text role="command"} subcommand in the `dis_interface`{.interpreted-text role="command"} block.

**Default** The value of \<platform-name\> or \<new-type-name\>.
:::

::: command
marking \<marking-name\>

Specifies a *marking* to be applied to the platform. This text field is associated with this platform in the `dis_interface`{.interpreted-text role="command"}.

**Default** No marking.
:::

::: command
indestructible (or destructible)

Indicates that the platform type is indestructible. Each hit will update the cumulative probability of survival according to the formula:

> Ps(new) = Ps(old) \* (1 - Pk)

**Default** destructible
:::

::: command
on_broken \[ remove \| disable \| disabled_but_movable \]

Indicates what should be done when the platform is broken.

If **remove** is specified, the platform will be removed from the simulation when it is broken.

If **disable** is specified, the platform will remain in the simulation when it is broken. The motion of the platform will be halted and all of the subsystems will be set to \'nonoperational\' so they cannot be turned back on. The script method `WsfPlatform.DamageFactor`{.interpreted-text role="method"} will return a value of 1.0.

If **disabled_but_movable** is specified, the platform will remain in the simulation when it is broken. The motion of the platform will continue. Some movers may allow motion to be affected by the broken condition, such as slowing down and losing control. All of the subsystems will be set to \'nonoperational\' so they cannot be turned back on. The script method `WsfPlatform.DamageFactor`{.interpreted-text role="method"} will return a value of 1.0.

**Default** **remove**

::: note
::: title
Note
:::

The [on_damage_received]() script will be called whenever damage is received, even if the platform is broken. The script may be used to change the signature state, appearance, etc. It should be remembered however, that all subsystems will be disabled, so the platform will probably not be able to perform any meaningful action after it is has been broken.
:::
:::

::: command
spatial_domain \[ land \| air \| subsurface \| surface \| space\]

Indicates the primary spatial domain in which the platform operates. This is sometimes used to classify if an object is a \'land\' object, \'space\' object, etc.

**Default** If the platform has a mover then the default will be inferred from the type of the mover (e.g., for `WSF_AIR_MOVER`{.interpreted-text role="model"} it will be \'air\'). If the platform does not have a mover then a default of \'land\' will be assumed. land, air, surface, subsurface, space
:::

::: command
acoustic_signature \<string-reference\>

Specifies the acoustic signature definition for this platform type. The signature type must have been defined with `acoustic_signature`{.interpreted-text role="command"}.

**Default** None.
:::

::: command
infrared_signature \<string-reference\>

Specifies the infrared signature definition for this platform type. The signature type must be previously defined with `infrared_signature`{.interpreted-text role="command"}.

**Default** None.

::: note
::: title
Note
:::

If **infrared_signature** is not specified and a sensing event occurs that requires it, a default of 1000 w/sr will be assumed.
:::
:::

::: command
inherent_contrast \<string-reference\>

**Default** None.
:::

::: command
optical_reflectivity \<string-reference\>

Specifies the optical reflectivity definition for this platform type. The signature type must have been defined with `optical_reflectivity`{.interpreted-text role="command"}.

**Default** None.

::: note
::: title
Note
:::

If **optical_reflectivity** is not specified and a sensing event occurs that requires it, a default of 1.0 will be assumed.
:::
:::

::: command
optical_signature \<string-reference\>

Specifies the optical signature definition for this platform type. The signature type must have been defined with `optical_signature`{.interpreted-text role="command"}.

**Default** None.

::: note
::: title
Note
:::

If **optical_signature** is not specified, but [length](), [width]() and [height]() are specified, those values will be used to compute the optical cross section when needed.
:::

::: note
::: title
Note
:::

If **optical_signature** is not specified and **length**, **width** and **height** are not specified, a default of 1000 m\^ will be assumed.
:::
:::

::: command
radar_signature \<string-reference\>

Specifies the radar signature definition for this platform type. The signature type must have been defined with `radar_signature`{.interpreted-text role="command"}.

**Default** None.

::: note
::: title
Note
:::

If **radar_signature** is not specified and a sensing event occurs that requires it, a default of 1000 m\^2 will be assumed.
:::
:::

::: command
geo_point \<geo-point-name\> \<latitude-value\> \<longitude-value\> \<length-value\>

Defines a named location with a latitude, longitude, altitude tuple. The location can be accessed using the `WsfPlatform.GeoPoint`{.interpreted-text role="method"} script method.
:::

::: command
position \<latitude-value\> \<longitude-value\>

Specifies the latitude and longitude of the platform. This command is only for static platforms without movers.

**Default** 0n 0e
:::

::: command
nutation_update_interval \<time-value\>

Specifies the time interval between updates to nutation calculations that are used in WCS-ECI coordinate conversions.

**Default** 1000 s
:::

::: command
precession_nutation_update_interval \<time-value\>

Specifies the time interval between updates to nutation calculations that are used in WCS-ECI coordinate conversions.

**Default** 1000 s
:::

::: deprecated
2.9 This command will be replaced by [nutation_update_interval]().
:::

::: command
mgrs_coordinate \<MGRS-value\>

Specifies the coordinates of the platform in the Military Grid Reference System. This command is only for static platforms without movers.
:::

::: command
altitude \<length-value\> \[ agl \| msl \]

Specifies the altitude of the platform. This command is only for static platforms without movers. It is ignored if a mover is defined in the platform.

**Default** 0 m msl
:::

::: command
creation_time \<random-time-reference\>

Specifies the simulation time at which the platform is to be added to the simulation. The platform exists in memory but is not yet a player in the simulation.

**Default** 0 sec (created at the start of the simulation).
:::

::: command
heading \<angle-value\>

Specifies the orientation of the platform. This command is only for static platforms without movers. It is ignored if a mover is defined in the platform.

**Default** 0 deg
:::

::: command
empty_mass \<mass-reference\>

Specifies the empty mass of the platform, which is usually a fixed quantity.

**Default** 0 kg

::: note
::: title
Note
:::

Some movers provide a different mechanism for providing this value.
:::
:::

::: command
fuel_mass \<mass-reference\>

Specifies the fuel mass of the platform. When specified by a user in an input file, the value is assumed to be a fixed quantity. However, if the platform contains a `fuel`{.interpreted-text role="command"} object, it is permitted and even expected to modify the fuel mass during run time, overwriting what was specified with this keyword.

**Default** 0 kg

::: note
::: title
Note
:::

Some movers provide a different mechanism for providing this value.
:::
:::

::: command
payload_mass \<mass-reference\>

Specifies the payload mass of the platform. When specified by a user in an input file, the value is assumed to be a fixed quantity. However, certain run-time events (e.g., dropping buoys) may change the payload mass.

**Default** 0 kg

::: note
::: title
Note
:::

Some movers provide a different mechanism for providing this value.
:::
:::

::: command
concealment_factor \<concealment-factor\>

Used to denote how well a platform is VISUALLY concealed. A value of 0.0 means there is no attempt to hide. A factor of 1.0 means that it is hidden in such a way as to be undetectable (such as in a building or in an underground bunker). Values between the endpoints represent increasing amounts of camouflage. Most sensors will not detect platforms with a concealment factor of 1.0, but any lesser value has no effect.

**Default** 0.0
:::

::: command
initial_damage_factor \<initial_damage_factor\>

The damage factor is a value in the range \[0 .. 1\] and is used to indicate the amount of damage to the platform. A value of 0 indicates no damage while a value of 1 indicates the platform is \'broken.\'

**Default** 0.0
:::

::: command
track_manager track_manager#Commands track-manager-commands \... end_track_manager

Commands within this block are processed by the platform\'s master track manager. See `track_manager`{.interpreted-text role="command"}.
:::

::: command
aux_data \<aux-data\> \... end_aux_data

Defines auxiliary data for a platform. See `_.aux_data`{.interpreted-text role="command"}.
:::

::: command
height \<length-reference\>
:::

::: command
length \<length-reference\>
:::

::: command
width \<length-reference\>

Specifies the dimensions of the platform.

**Default** 0 for all values

::: note
::: title
Note
:::

If all of these have non-zero values and [optical_signature]() was not specified, these values will be used to compute the optical cross section when needed.
:::
:::

::: command
category \<category-name\>

Specifies that the platform is a member of the category specified. Categories can be used to force sensors to `sensor.ignore`{.interpreted-text role="command"} the platform. Categories are also accessible through the `WsfPlatform.CategoryMemberOf`{.interpreted-text role="method"} script method.

**Default** The platform is not a member of any categories.
:::

::: command
clear_categories

Has the effect of canceling any previous [category]() command.
:::

::: command
group_join \<group-name\>
:::

::: command
group_leave \<group-name\>

Adds or removes the platform from the specified `group <WsfGroup>`{.interpreted-text role="class"}.
:::

::: command
commander \<commander-name\>
:::

::: command
command_chain \<command-chain-name\> \<commander-name\>

**commander** specifies the platform\'s immediate superior (commander) in the default chain of command.

**command_chain** specifies the platform\'s immediate superior (commander) in the given chain of command.

A chain of command is used by decision making routines to determine the recipients of orders and/or reports.

::: note
::: title
Note
:::

See `command_chains`{.interpreted-text role="doc"} for more information and examples.
:::

\<command-chain-name\>

:   The name of the command chain of to which this command applies. (The default command chain is implied for commander).

\<commander-name\> \| SELF

:   The name of the platform that is the immediate superior (commander) of this platform.

**Default** commander SELF
:::

::: command
route route-commands \... end_route

The `route`{.interpreted-text role="command"} subcommand provides a collection of waypoints that define a path, or route, for platform movers.
:::

::: command
add_mover \<mover-type\> mover-commands \... end_mover
:::

::: command
mover \<mover-type\> mover-commands \... end_mover

A `mover`{.interpreted-text role="command"} defines the domain in which a platform can move and how it moves within the domain.

-   The **add mover** command is only required inside of the **platform** command (i.e., not inside of the **platform_type** command).
:::

::: command
fuel \<fuel-type\> fuel-commands .. end_fuel

A `fuel object <fuel>`{.interpreted-text role="command"} can be attached to a platform to model the effects of fuel consumption.

**Default** No fuel object is defined (fuel consumption is not modeled).

::: note
::: title
Note
:::

Some movers model fuel consumption internally.
:::
:::

::: {#platform.component}
**\<component\> \<component-name\> \[\<component-type\>\] component-commands \... end\_\<component\>**
:::

::: command
add \<component\> \<component-name\> \<component-type\> component-commands \... [end]()\<component\>
:::

::: command
edit \<component\> \<component-name\> component-commands \... [end]()\<component\>
:::

::: command
delete \<component\> \<component-name\>

Adds a component to the platform, modifies or removes an existing component.

-   The **add \<component\>** command adds a new component to the platform/platform_type.
-   The **edit \<component\>** command modifies an existing component on the platform/platform_type.
-   The **delete \<component\>** command removes an existing component on the platform/platform_type.
-   The **\<component\>** command is an abbreviated version, which adds a component if used inside a **platform_type** and modifies an existing component when used inside a **platform**.
-   A valid **\<component\>** includes `comm`{.interpreted-text role="command"}, `fuel`{.interpreted-text role="command"}, `mover`{.interpreted-text role="command"}, `processor`{.interpreted-text role="command"}, `sensor`{.interpreted-text role="command"}, etc.

::: note
::: title
Note
:::

When using edit or delete commands with a fuel or mover, you do not specify the \<component-name\>.
:::

::: note
::: title
Note
:::

Zones can be removed with the delete command but can\'t be modified via the add or edit commands.
:::
:::

::: command
track \... end_track

Defines a pre-briefed `track`{.interpreted-text role="command"}.
:::

::: command
use_zone \<shared-zone-type\> as \<zone-name\>

Attach a copy of the specified zone to the platform under the name \<zone-name\>. If the specified zone does not define a pose, the copied zone uses the current pose of the platform. This command can be repeated as necessary.
:::

::: command
zone \... end_zone
:::

::: command
zone_set \... end_zone_set

The `zone`{.interpreted-text role="command"} and `zone_set`{.interpreted-text role="command"} subcommands define:

-   Relative zones which align with the platform pose.
-   Relative zones which maintain a static pose.
-   Absolute zones which maintain a static pose.

::: seealso
`zone`{.interpreted-text role="command"} and `zone_set`{.interpreted-text role="command"} for subcommands to affect simulated behavior.
:::
:::

::: command
navigation_errors \... end_navigation_errors

The `_.navigation_errors`{.interpreted-text role="command"} block provides a method to define error in where the platform thinks it is located versus where it is really located. Please see `_.navigation_errors`{.interpreted-text role="command"} for a description of the inputs.

**Default** No navigation errors.
:::

## Script Interface

`platform`{.interpreted-text role="command"} uses the capabilities of the `Common_Script_Interface`{.interpreted-text role="ref"} and implements the following additional commands:

::: command
on_damage_received

    script void on_damage_received() ... end_script

An optional script that can be defined which will receive control whenever there is damage to the platform.

This script is generally used with the [indestructible]() or [on_broken]() command. Normally, when a platform takes damage, the platform is removed from the simulation if it is determined that this damage caused the platform to break. If **indestructible**, **on_broken disable**, or **on_broken disabled_but_movable** are specified, the platform will not be deleted, but instead the damage factor (DamageFactor) will be incremented and this script will be called. The script can do things like stop movement, turn off sensors, change appearance, etc.

::: note
::: title
Note
:::

If **on_broken disable** was specified, the platform motion will be halted and all subsystems will be set to nonoperational without any additional intervention. If **on_broken disabled_but_movable** was specified, the platform motion will continue, but and all subsystems will be set to nonoperational without any additional intervention.
:::
:::

::: command
on_platform_deleted

    script void on_platform_deleted() ... end_script

An optional script that can be defined which will receive control just before the platform is removed from the simulation.
:::

::: command
on_commander_deleted
:::

::: command
on_peer_deleted
:::

::: command
on_subordinate_deleted

::: parsed-literal
script void on_commander_deleted(`WsfPlatform`{.interpreted-text role="class"} aCommander) \... end_script script void on_peer_deleted(`WsfPlatform`{.interpreted-text role="class"} aPeer) \... end_script script void on_subordinate_deleted(`WsfPlatform`{.interpreted-text role="class"} aSubordinate) \... end_script
:::

Optional scripts that can be defined which will be executed just before other platforms within the platform\'s [command_chain]() hierarchy are removed from the simulation. The single argument to each script is a reference to the platform pending removal.
:::

::: command
callback \<callback-name\> \<callback-type\> callback-commands end_callback

A `platform.callback`{.interpreted-text role="command"} is a named action that can be called via the `route.execute`{.interpreted-text role="command"} command of a `route`{.interpreted-text role="command"} when the platform reaches a particular waypoint within the route.

::: note
::: title
Note
:::

This is an obsolete form that has been replaced by scripts.
:::
:::
