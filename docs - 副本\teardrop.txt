# teardrop.rst.txt.md
orphan

:   



Script Type: `WsfTeardropManeuver`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} teardrop

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [target_platform]() \... [radial_offset_at_poca]() \... [period]() \... [time_to_poca]() \... [repetitions]() \... [delta_time]() \... [maximum_delta_time]() \... [maximum_delta_v]() \... [optimize_time]() \... [optimize_delta_v]() \... [optimize_cost]() \... [tolerance]() \...

end_maneuver
:::

Perform a maneuver that brings the executing platform onto an orbit that, relative to the [target_platform]() of the maneuver, appears to have a teardrop shape. The parameters that specify the relative motion are a distance at the point of closest approach (POCA), and the time it takes the executing platform to traverse the teardrop.

In addition to the final relative motion, the executing platform will perform maneuvers similar to a `rendezvous`{.interpreted-text role="doc"} to transfer from its initial orbit to the final teardrop orbit. Because of this transfer, this maneuver also has many options that overlap with `rendezvous`{.interpreted-text role="doc"}.

See `Teardrop Orbit <teardrop_details>`{.interpreted-text role="doc"} for details.

::: note
::: title
Note
:::

This following conditions cannot be used with this maneuver: `ascending_radius`{.interpreted-text role="command"}, `descending_radius`{.interpreted-text role="command"}, `northern_intersection`{.interpreted-text role="command"}, and `southern_intersection`{.interpreted-text role="command"}.
:::



::: command
target_platform \<string-value\>

Specify the platform relative to which the executing platform will perform a Teardrop maneuver.
:::

::: command
radial_offset_at_poca \<length-value\>

Specify the distance at the point of closest approach. This distance will be entirely in the radial direction, with a positive value indicating that the teardrop will be above the [target_platform](). The provided value must be non-zero.
:::

::: command
period \<time-value\>

Specify the time taken to execute the teardrop maneuver. This period must be no greater than about 40.6 percent of the orbital period of the [target_platform](). The [period]() covers from when the executing platform first passes directly under (over) the target, and when it passes under (over) the target for the last time. These times are at half the given [period]() before the point of closest approach and half the [period]() after the point of closest approach.
:::

::: command
time_to_poca \<time-value\>

Specify the duration after the executing platform is inserted onto the teardrop orbit at which it reaches the point of closest approach to the [target_platform](). This value must be greater than half of the specified [period]().
:::

::: command
repetitions \<integer-value\>

Specify the number of times the executing platform should traverse the teardrop. This provided value must be positive.

Default: 1
:::