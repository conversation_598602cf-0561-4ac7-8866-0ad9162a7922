orphan

:   

# Command Line Arguments - Wizard

Usage:

    wizard.exe  [ <file_name.txt> ]
                [ <project_file.afproj> ]
                { -console ]

\<file_name1.txt\> \<file_name2.txt\> \<\...\>

:   Specifies an input file(s) to load as a scenario. More than one file may be specified, each one is added to the scenario.

\<project_file.afproj\>

:   Specifies the path to an existing project file which will be opened.

[-console]{.title-ref}

:   Commands Wizard to show the console window when starting up. This window displays additional status and debugging information.

Example: Launches Wizard and loads both **strike.txt** and **dis_interface.txt**:

    On Windows:
       >> wizard.exe strike.txt dis_inteface.txt
    or
       >> wizard.exe c:\project1\scenario1.afproj
    On Linux:
       >> wizard strike.txt dis_interface.txt
    or
       >> wizard /home/<USER>/project1/scenario1.afproj

::: note
::: title
Note
:::

The \'\>\>\' represents a command line.
:::
