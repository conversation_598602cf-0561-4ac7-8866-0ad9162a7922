orphan

:   

# WsfPitchDeltaManeuver

## Overview {#overview .WsfPitchDeltaManeuver .inherits .WsfManeuver}

This maneuver sets a target change in pitch angle for the platform to which it is assigned. This maneuver is done executing as soon as the target is set, so if there is a need to wait for the achieved change in pitch angle, a `WsfManeuverConstraint`{.interpreted-text role="class"} must be used.

## Methods

::: method
WsfPitchDeltaManeuver Construct(double aPitchDeltaDeg)

Construct a maneuver that will set a target change in pitch angle on the platform to which it assigned.
:::

::: method
double GetPitchDelta()

Return this maneuver\'s target change in pitch angle in degrees.
:::
