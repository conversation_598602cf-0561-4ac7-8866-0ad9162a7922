<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AFSIM Platform Agent</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 1000px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4757;
            animation: pulse 2s infinite;
        }

        .status-indicator.ready {
            background: #2ed573;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .info-bar {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            text-align: right;
        }

        .message.assistant {
            text-align: left;
        }

        .message.system {
            text-align: center;
        }

        .message-content {
            display: inline-block;
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            white-space: pre-wrap;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .message.system .message-content {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-size: 13px;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .input-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-field:focus {
            border-color: #4facfe;
        }

        .send-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s;
        }

        .send-btn:hover {
            transform: translateY(-2px);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }

        .loading.show {
            display: block;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                height: 95vh;
                margin: 10px;
            }
            
            .message-content {
                max-width: 90%;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="status-indicator" id="statusIndicator"></div>
            <h1>🛩️ AFSIM Platform Agent</h1>
            <p>智能对话 · 脚本解释 · 参数查询 · 脚本生成</p>
        </div>
        
        <div class="info-bar" id="infoBar">
            正在初始化系统...
        </div>
        
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message system">
                    <div class="message-content">
                        🎯 欢迎使用AFSIM Platform Agent！<br>
                        💡 您可以：输入AFSIM脚本进行解释 | 查询飞机参数 | 生成飞机脚本 | 询问平台问题
                    </div>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                正在处理您的请求...
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-row">
                <input type="text" id="messageInput" class="input-field" 
                       placeholder="输入您的问题或AFSIM脚本..." 
                       disabled>
                <button id="sendBtn" class="send-btn" disabled>发送</button>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn" onclick="clearHistory()">清空对话</button>
                <button class="action-btn" onclick="showStatus()">系统状态</button>
                <button class="action-btn" onclick="loadExample()">示例脚本</button>
                <button class="action-btn" onclick="showHelp()">使用帮助</button>
            </div>
        </div>
    </div>

    <script>
        let isInitialized = false;
        let isProcessing = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            setupEventListeners();
            
            // 定期检查状态
            setInterval(checkStatus, 5000);
        });

        function setupEventListeners() {
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');

            // 回车发送
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 发送按钮
            sendBtn.addEventListener('click', sendMessage);
        }

        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                updateStatus(data);
            } catch (error) {
                console.error('检查状态失败:', error);
            }
        }

        function updateStatus(data) {
            const statusIndicator = document.getElementById('statusIndicator');
            const infoBar = document.getElementById('infoBar');
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');

            if (data.initialized) {
                isInitialized = true;
                statusIndicator.classList.add('ready');
                
                const llmStatus = data.llm_available ? '可用' : '不可用';
                infoBar.innerHTML = `
                    📊 系统就绪 | 飞机数据库: ${data.aircraft_count} 种 | 
                    大模型查询: ${llmStatus} | 对话轮数: ${data.conversation_turns}
                `;
                
                messageInput.disabled = false;
                sendBtn.disabled = false;
                messageInput.placeholder = '输入您的问题或AFSIM脚本...';
            } else {
                isInitialized = false;
                statusIndicator.classList.remove('ready');
                infoBar.textContent = data.message || '正在初始化系统...';
                
                messageInput.disabled = true;
                sendBtn.disabled = true;
                messageInput.placeholder = '系统初始化中，请稍候...';
            }
        }

        async function sendMessage() {
            if (!isInitialized || isProcessing) return;

            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) return;

            // 显示用户消息
            addMessage('user', message);
            messageInput.value = '';
            
            // 显示加载状态
            setProcessing(true);

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();
                
                if (data.success) {
                    addMessage('assistant', data.response);
                } else {
                    addMessage('system', '❌ ' + data.message);
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('system', '❌ 网络错误，请重试');
            } finally {
                setProcessing(false);
            }
        }

        function addMessage(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function setProcessing(processing) {
            isProcessing = processing;
            const loading = document.getElementById('loading');
            const sendBtn = document.getElementById('sendBtn');
            
            if (processing) {
                loading.classList.add('show');
                sendBtn.disabled = true;
                sendBtn.textContent = '处理中...';
            } else {
                loading.classList.remove('show');
                sendBtn.disabled = !isInitialized;
                sendBtn.textContent = '发送';
            }
        }

        async function clearHistory() {
            try {
                const response = await fetch('/api/clear');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('chatMessages').innerHTML = `
                        <div class="message system">
                            <div class="message-content">
                                🧹 对话历史已清空
                            </div>
                        </div>
                    `;
                } else {
                    addMessage('system', '❌ ' + data.message);
                }
            } catch (error) {
                addMessage('system', '❌ 清空失败');
            }
        }

        async function showStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.initialized) {
                    const statusMsg = `📊 系统状态详情：
                    
🛩️ 飞机数据库: ${data.aircraft_count} 种飞机
🤖 大模型查询: ${data.llm_available ? '✅ 可用' : '❌ 不可用'}
💬 对话轮数: ${data.conversation_turns}
⚙️ 支持功能: ${data.features ? data.features.join(', ') : '脚本解释, 参数查询, 飞机信息查询, 脚本生成'}

📋 可用飞机型号: ${data.aircraft_list.join(', ')}`;
                    
                    addMessage('system', statusMsg);
                } else {
                    addMessage('system', '❌ 系统尚未初始化完成');
                }
            } catch (error) {
                addMessage('system', '❌ 获取状态失败');
            }
        }

        async function loadExample() {
            try {
                const response = await fetch('/api/example');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('messageInput').value = data.script;
                    addMessage('system', '📝 示例脚本已加载到输入框，您可以直接发送进行解释');
                } else {
                    addMessage('system', '❌ 加载示例失败');
                }
            } catch (error) {
                addMessage('system', '❌ 加载示例失败');
            }
        }

        function showHelp() {
            const helpMsg = `📚 AFSIM Platform Agent 使用帮助

🎯 主要功能:
• 脚本解释 - 输入AFSIM脚本获得详细解释
• 飞机查询 - 查询军用飞机技术参数
• 脚本生成 - 基于飞机数据生成AFSIM脚本
• 参数查询 - 询问AFSIM平台相关问题

💡 使用示例:
• "F-16参数" - 查询F-16技术参数
• "生成F-16脚本" - 生成F-16的AFSIM脚本
• "altitude参数是什么意思？" - 查询参数含义
• 直接粘贴AFSIM脚本 - 获得脚本解释

🔧 数据库模式:
• 优先查询本地数据库
• 如无数据则使用大模型查询
• 新查询结果自动入库
• 支持中外军用飞机查询

⌨️ 快捷操作:
• 回车键发送消息
• 清空对话重置历史
• 系统状态查看数据库信息`;
            
            addMessage('system', helpMsg);
        }
    </script>
</body>
</html>
