# electronic_attack.rst.txt.md
orphan

:   



::: {.command block=""}
electronic_attack
:::

::: parsed-literal

electronic_attack *\[\<type-name\>\]*

:   \... `Electronic Warfare Commands <electronic_warfare.commands>`{.interpreted-text role="ref"} \... \... `Electronic Attack Commands <WSF_ELECTRONIC_ATTACK>`{.interpreted-text role="model"} \... \... `Electronic Attack Technique Commands <WSF_EA_TECHNIQUE>`{.interpreted-text role="model"} \...

end_electronic_attack
:::

\<type-name\>

:   Name of an existing Electronic Attack type or `WSF_ELECTRONIC_ATTACK`{.interpreted-text role="model"}, whose definition will be used as the initial definition of the new instance.



electronic_attack defines an electronic attack (countermeasure) capability for a transmitter. An `Electronic Attack (EA) <electronic_attack>`{.interpreted-text role="command"} - `electronic protect (EP) <electronic_protect>`{.interpreted-text role="command"} architecture provides the ability to define EA and EP techniques and assess the interaction of these techniques. Multiple techniques are allowed for both EA and EP capability blocks. The effect at the receiver is as defined in the EA technique block unless the EP technique block defines a mitigating technique with an associated effect for the particular EA technique being considered.



None currently defined