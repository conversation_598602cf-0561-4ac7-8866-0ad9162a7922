orphan

:   

::: demo
fires
:::

# Fires types library and demos

| These types definitions and demos are to be used to show the capabilities
| of the wsf_fires library. This library provides a simplified propagation
| of short range ballistic projectiles. Initially this capability is only for
| indirect fire, and the provided types are only for certain generic types
| of rocket, artillery, and mortar (RAM).

# time_on_target.txt

| Five launchers are all commanded to attack a target with \"time on target,\"
| set to 2 minutes sim time.
| 
| Note: There is an issue in the task manager that starts an immediate salvo.
| Look for the second salvo; this is the one with the proper behavior.
