# atmospheric_coefficients.rst.txt.md
orphan

:   



::: {.command block=""}
atmospheric_coefficients \... end_atmospheric_coefficients

::: parsed-literal

[atmospheric_coefficients](#atmospheric_coefficients)

:   [altitude]() \... end_altitude [attenuation]() \... end_attenuation [scattering]() \... end_scattering [wavelength]() \<length-value\> [haze_model]() \<integer-value\> [atmosphere_model]() \<integer-value\>

end_atmospheric_coefficients
:::
:::

Atmospheric coefficients are required when using a `fluence_model`{.interpreted-text role="command"} with a `WSF_LASER_WEAPON`{.interpreted-text role="model"} or `WSF_CUED_LASER_WEAPON`{.interpreted-text role="model"}. Typically, these are produced from ModTran for the given wavelength, haze model, and atmosphere model. There are currently data for the standard `laser types <fluence_model.laser_type>`{.interpreted-text role="command"} in the hel_demoatmosphere folder in the WSF standard release, version `wsf_1.7.5`{.interpreted-text role="ref"} and above; these are the following:

-   Carbon Dioxide 1000 nm
-   ND-YAG 1064 nm
-   COIL 1315 nm
-   Deuterium Fluoride 3800 nm

the following atmosphere models are supported from the ModTran, Models 2,3, and 6 are supported for WSF at wavelength 1064; otherwise only model 2 is supported at other wavelengths.

1.  Tropical Atmosphere
2.  Midlatitude Summer (default)
3.  Midlatitude Winter
4.  Subarctic Summer
5.  Subarctic Winter
6.  1976 U.S. Standard

The following haze values are supported; Currently all models are only available for wavelengths 1000 and 1064 (CO2 and Nd-YAG) otherwise only model 1 is available (see model extraction procedure, below. to access other models at other wavelengths).

1.  RURAL Extinction, VIS = 23 km (Clear) (default)
2.  RURAL Extinction, VIS = 5 km (Hazy)
3.  Navy maritime Extinction
4.  MARITIME Extinction, VIS = 23 km
5.  Urban Extinction, VIS = 5 km

::: command
altitude \... end_altitude

A list of altitudes for which corresponding attenuation and scattering coefficient blocks are provided.
:::

::: command
attenuation \... end_attenuation

A list of attenuation coefficients in units of 1/m. Each entry in this block corresponds with the entry in the [altitude]() block.
:::

::: command
scattering \... end_scattering

A list of scattering coefficients in units of 1/m. Each entry in this block corresponds with the entry in the [altitude]() block.
:::

::: command
wavelength \<length-value\>

The wavelength at which the table is valid.
:::

::: command
haze_model \<integer-value\>

The haze model used, corresponding with one of the values above (1-5). This should be the same value used as input to the ModTran run.
:::

::: command
atmosphere_model \<integer-value\>

The atmosphere model used, corresponding with one of the values above (1-6). This should be the same value used as input to the ModTran run.
:::