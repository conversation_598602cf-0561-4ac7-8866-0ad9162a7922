# wsfuplinkprocessor.rst.txt.md
orphan

:   





::: method
bool UplinkCapable()

Returns true if this uplink processor is capable of uplinking (max uplinks has not been met or no max limit is being imposed).
:::

::: method
bool IsUplinkingTo(WsfPlatform aPlatform)

Returns true if this platform is currently uplinking to the provided aPlatform.
:::

::: method
int UplinkCount()

Returns the number of currently active uplinks.
:::

::: method
WsfPlatform UplinkPlatformEntry(int index)

Returns the platform receiving the uplink at the provided (zero based) index.
:::

::: method
bool StartUplinking(WsfPlatform aWeapon)

Starts uplinking for the provided aWeapon. Returns true if successful.
:::

::: method
bool StartUplinking(WsfPlatform aWeapon, WsfTrack aTrack)

Starts uplinking for the provided aWeapon specifically for the provided track aTrack. Returns true if successful.
:::

::: method
bool StopUplinking(WsfPlatform aWeapon)

Stops uplinking for the provided aWeapon Returns true if successful.
:::

::: method
int MaxSupportedUplinks()

Returns the number of max supported uplinks for this platform.
:::