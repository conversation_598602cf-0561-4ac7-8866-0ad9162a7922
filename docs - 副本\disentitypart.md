orphan

:   

# DisEntityPart {#DisEntityPart}

::: DisEntityPart
::: parsed-literal
`DisEntityPart.TypeDesignator`{.interpreted-text role="method"} `DisEntityPart.ChangeIndicator`{.interpreted-text role="method"} `DisEntityPart.PartAttachedTo`{.interpreted-text role="method"} `DisEntityPart.GetParameterType`{.interpreted-text role="method"} `DisEntityPart.Float32Value`{.interpreted-text role="method"} `DisEntityPart.EntityTypeValue`{.interpreted-text role="method"}
:::
:::

## Overview

[DisEntityPart](#disentitypart) is an implementation of the DIS entity part. The entity part is used by DIS to communicate the states of articulated parts.

## Methods

::: method
int TypeDesignator()

Returns an identifier for the part.
:::

::: method
int ChangeIndicator()

Returns a flag indicating the parts parameters have changed since the last received entity state.
:::

::: method
int PartAttachedTo()

If the part is attached to another part, this method will return the ID of the parent part.
:::

::: method
int GetParameterType()

Returns the a parameter type.
:::

::: method
int Float32Value()

Returns the value for the articulation.
:::

::: method
int EntityTypeValue()

Returns the entity type value ID.
:::
