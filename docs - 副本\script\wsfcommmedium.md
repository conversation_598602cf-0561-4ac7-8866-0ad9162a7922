orphan

:   

# WsfCommMedium

## Description {#description .WsfCommMedium .inherits .WsfObject}

This script class provides access to `medium`{.interpreted-text role="command"} objects of all types in the script context.

## Methods

::: method
int ModeCount()

Provides the number of modes that are available for this medium.
:::

::: method
int CurrentModeIndex()

Provides the index for the mode that is currently defined as the default used for transmissions.
:::

::: method
string CurrentModeName()

Provides the string name for the mode that is currently defined as the default used for transmissions.
:::

::: method
WsfCommMediumMode GetCurrentMode()

Returns a reference to the mode that is currently defined as the default used for transmissions.
:::

::: method
WsfCommMediumMode GetMode(int aIndex)

Returns a reference to the mode at the index provided by the user.
:::

::: method
WsfCommMediumMode GetMode(string aModeName)

Returns a reference to the mode with the name provided by the user.
:::

::: method
int ChannelCount()

Returns the maximum number of channels available for this medium.
:::

::: method
int TransmissionCount()

Returns the number of currently active transmissions using this medium object.
:::

::: method
int MessageStatusCount()

Returns the number of messages currently being handled by this medium, regardless of state.
:::

::: method
WsfCommMediumMessageStatus GetMessageStatus(int aSerialNumber, WsfAddress aMessageSource, WsfAddress aMessageDestination)

Returns the message status object specified by the user provided message serial number, the address of the message originator, and the address of the ultimate message destination, if found.
:::

::: method
WsfCommMediumMessageStatus GetMessageStatusByIndex(int aIndex)

Returns the message status object specified by the user provided index.
:::

::: method
bool IsShared()

Returns true if this medium object is a sharable medium, and potentially has multiple objects using it.
:::

::: method
bool SetMode(int aIndex)

Sets the default mode for any subsequent message transmissions using this medium to the supplied mode index.

::: note
::: title
Note
:::

This method resolves immediately, where message transmissions are scheduled. If the user calls this method multiple times at the same time, the last call of this type will determine the mode for all messages indicated to be transmitted at the same time. Either delay message transmissions slightly between usage of this method call, or utilize the script method overload available with the `medium`{.interpreted-text role="command"} definition.
:::

Returns true if the mode was successfully set to the indicated index, false otherwise.
:::

::: method
bool SetMode(string aModeName)

Sets the default mode for any subsequent message transmissions using this medium to the supplied mode name.

::: note
::: title
Note
:::

This method resolves immediately, where message transmissions are scheduled. If the user calls this method multiple times at the same time, the last call of this type will determine the mode for all messages indicated to be transmitted at the same time. Either delay message transmissions slightly between usage of this method call, or utilize the script method overload available with the `medium`{.interpreted-text role="command"} definition.
:::

Returns true if the mode was successfully set to the indicated mode name, false otherwise.
:::
