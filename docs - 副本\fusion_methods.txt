# fusion_methods.rst.txt.md
orphan

:   



::: parsed-literal

fusion_method `<fusion-type-name> <fusion_methods.available_fusion_methods>`{.interpreted-text role="ref"}

:   \... commands \...

end_fusion_method

Specifies the fusion algorithms used by the track manager.

A fusion algorithm combines information about a single entity from two or more sources into a coherent information set, or `track`{.interpreted-text role="command"}.
:::

\<fusion-method\> can be:

Each type has its own unique input keywords.





::: parsed-literal
fusion_method replacement end_fusion_method
:::

::: block
fusion_method.replacement
:::

Correlated measurements and tracks are fused according to a standard set of algorithms. Local track positions are replaced by nonlocal track positions.

::: end
:::



::: parsed-literal
fusion_method weighted_average end_fusion_method
:::

::: block
fusion_method.replacement
:::

Correlated measurements and tracks are fused according to a standard set of algorithms. Local track positions are combined with nonlocal track positions using the covariance matrices of the local and nonlocal tracks.

::: note
::: title
Note
:::

If there is no covariance matrix associated with a nonlocal track, the track manager will attempt to use a measurement covariance matrix, generated from the track\'s measurement errors in range, elevation, and bearing.
:::

::: end
:::