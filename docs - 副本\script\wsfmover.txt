# wsfmover.rst.txt.md
orphan

:   





::: method
WsfMover Create(string aMoverType)

Returns a new mover with the specified type. The result mover can be used with `WsfPlatform.SwapMover`{.interpreted-text role="method"}.
:::

::: method
static bool IsA_TypeOf(string aDerivedType, string aBaseType)

Returns true if the mover type specified by aDerivedType inherits from the mover type specified by aBaseType (compare to `WsfObject.IsA_TypeOf(aBaseType) <WsfObject.IsA_TypeOf>`{.interpreted-text role="method"} which determines if a specific object inherits from aBaseType).
:::



::: method
double UpdateInterval()

Returns the mover\'s update interval.
:::

::: method
WsfRoute Route()

Returns a copy of the mover\'s route.
:::

::: method
WsfRoute DefaultRoute()

Returns a copy of the mover\'s default route.
:::

::: method
void SetMode(string aModeString)

Sets the \"Mode\" of the mover. Effect is Mover type implementation-defined. Initial functionality is for the `WSF_ROTORCRAFT_MOVER`{.interpreted-text role="model"}, so see its documentation for that implementation.
:::

::: method
bool SetTSPI_FileName(string aFileName)

Sets the name of the data file to be opened and parsed by a `WSF_TSPI_MOVER`{.interpreted-text role="model"} to define trajectory motion path to follow. The aFileName will have path variable names substituted before opening. Returns true if successful. Will not be successful if Mover is of type other than TSPI_Mover, or if the TSPI file is already open.
:::

::: method
bool IsExtrapolating()

Returns \'true\' if the mover is \'extrapolating\'. For route-movers, this is true when extrapolating at end of path or after script commands like TurnToHeading(). Other mover types may or may not set this flag.
:::

::: method
void BurnedOut(double aBurnoutTime)

Triggers extrapolation in the mover.
:::

::: method
bool TurnOff()

Turn the mover off and returns true if successful.
:::

::: method
bool TurnOn()

Turn the mover on and returns true if successful.
:::



::: method
Array\<int\> PropertyInt(string aPropertyName)

Returns the integer value of the given property name. The array will be empty if the property is unrecognized.
:::

::: method
Array\<double\> PropertyDouble(string aPropertyName)

Returns the double value of the given property name. The array will be empty if the property is unrecognized.
:::

::: method
Array\<string\> PropertyString(string aPropertyName)

Returns the string value of the given property name. The array will be empty if the property is unrecognized.
:::