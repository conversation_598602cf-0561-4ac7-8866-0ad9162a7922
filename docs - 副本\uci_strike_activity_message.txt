# uci_strike_activity_message.rst.txt.md
orphan

:   





This message displays the current action(s) of the corresponding weapon(s).



::: method
UCI_StrikeActivity Activity(int aIndex)

Returns the activity at the given index. If the index is less than 0 or greater than the activity\'s \_ActivitySize, this returns null.
:::

::: method
int Size()

Returns the number of activities included in the activity message.
:::