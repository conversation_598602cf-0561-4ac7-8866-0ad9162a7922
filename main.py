#!/usr/bin/env python3
"""
AFSIM Platform Agent - 主启动脚本
提供命令行接口和Web API接口
"""

import os
import sys
import argparse
import json
import logging
from pathlib import Path

# 添加项目路径到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from core.platform_agent import AFSIMPlatformAgent

logger = logging.getLogger(__name__)


def setup_logging(level: str = "INFO"):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, level.upper(), logging.INFO),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def init_agent(config_path: str = None) -> AFSIMPlatformAgent:
    """初始化Agent"""
    agent = AFSIMPlatformAgent(config_path)
    if not agent.initialize():
        print("❌ Agent初始化失败")
        sys.exit(1)
    print("✅ Agent初始化成功")
    return agent


def cmd_index_docs(agent: AFSIMPlatformAgent, docs_path: str, force: bool = False):
    """索引文档命令"""
    print(f"📚 开始索引文档: {docs_path}")
    
    result = agent.index_documents(docs_path, force_reindex=force)
    
    if 'error' in result:
        print(f"❌ 索引失败: {result['error']}")
        return
    
    print("✅ 索引完成:")
    print(f"   - 索引文件数: {result['indexed_files']}")
    print(f"   - 文档块数: {result['total_chunks']}")
    print(f"   - 提取参数数: {result['total_parameters']}")


def cmd_query_params(agent: AFSIMPlatformAgent, query: str, top_k: int = 5):
    """查询参数命令"""
    print(f"🔍 查询Platform参数: {query}")
    
    result = agent.query_platform_parameters(query, top_k)
    
    if 'error' in result:
        print(f"❌ 查询失败: {result['error']}")
        return
    
    print("✅ 查询结果:")
    print(f"   - 找到结果数: {result['total_results']}")
    print("\n📋 中文总结:")
    print(result['summary'])
    
    print("\n📄 详细结果:")
    for i, res in enumerate(result['results'][:3], 1):  # 只显示前3个结果
        print(f"\n{i}. 相似度: {res['similarity']:.3f}")
        print(f"   来源: {res['metadata'].get('file_name', 'Unknown')}")
        print(f"   内容: {res['content'][:200]}...")


def cmd_generate_script(agent: AFSIMPlatformAgent, config_file: str = None, **kwargs):
    """生成脚本命令"""
    print("🚀 生成Platform脚本")
    
    # 从文件或命令行参数获取配置
    if config_file and Path(config_file).exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        config = {k: v for k, v in kwargs.items() if v is not None}
    
    if not config.get('platform_name'):
        print("❌ 缺少platform_name参数")
        return
    
    result = agent.generate_platform_script(config)
    
    if 'error' in result:
        print(f"❌ 生成失败: {result['error']}")
        return
    
    print("✅ 脚本生成完成:")
    print("\n📜 生成的脚本:")
    print("=" * 50)
    print(result['script_content'])
    print("=" * 50)
    
    print("\n📝 中文说明:")
    print(result['explanation'])


def cmd_list_params(agent: AFSIMPlatformAgent):
    """列出所有参数命令"""
    print("📋 获取所有Platform参数")
    
    result = agent.get_all_parameters()
    
    if 'error' in result:
        print(f"❌ 获取失败: {result['error']}")
        return
    
    print("✅ 参数获取完成:")
    print(f"   - 总参数数: {result['total_parameters']}")
    print(f"   - 索引文件数: {result['indexed_files']}")
    
    print("\n📋 参数总结:")
    if 'summary' in result and 'summary' in result['summary']:
        print(result['summary']['summary'])
    
    print("\n📄 参数列表 (前10个):")
    for i, param in enumerate(result['parameters'][:10], 1):
        print(f"{i:2d}. {param['name']:20s} ({param['type']:10s}) - {param['description'][:50]}...")


def cmd_status(agent: AFSIMPlatformAgent):
    """显示状态命令"""
    print("📊 Agent状态")
    
    status = agent.get_status()
    
    print("✅ 状态信息:")
    print(f"   - 初始化状态: {'✅' if status['initialized'] else '❌'}")
    print(f"   - 索引文件数: {status['indexed_files']}")
    print(f"   - 配置加载: {'✅' if status['config_loaded'] else '❌'}")
    
    if 'vector_db_info' in status and status['vector_db_info']:
        db_info = status['vector_db_info']
        if 'count' in db_info:
            print(f"   - 向量数据库文档数: {db_info['count']}")


def interactive_mode(agent: AFSIMPlatformAgent):
    """交互模式"""
    print("\n🎯 进入交互模式 (输入 'help' 查看命令, 'quit' 退出)")
    
    while True:
        try:
            command = input("\n> ").strip()
            
            if command.lower() in ['quit', 'exit', 'q']:
                print("👋 再见!")
                break
            elif command.lower() in ['help', 'h']:
                print("""
可用命令:
  query <查询文本>        - 查询Platform参数
  generate <平台名称>     - 生成Platform脚本
  list                   - 列出所有参数
  status                 - 显示状态
  help                   - 显示帮助
  quit                   - 退出
""")
            elif command.startswith('query '):
                query_text = command[6:].strip()
                if query_text:
                    cmd_query_params(agent, query_text)
                else:
                    print("❌ 请提供查询文本")
            elif command.startswith('generate '):
                platform_name = command[9:].strip()
                if platform_name:
                    cmd_generate_script(agent, platform_name=platform_name)
                else:
                    print("❌ 请提供平台名称")
            elif command == 'list':
                cmd_list_params(agent)
            elif command == 'status':
                cmd_status(agent)
            elif command:
                print(f"❌ 未知命令: {command}")
                print("输入 'help' 查看可用命令")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 命令执行错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AFSIM Platform Agent")
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 索引文档命令
    index_parser = subparsers.add_parser('index', help='索引文档')
    index_parser.add_argument('docs_path', help='文档目录路径')
    index_parser.add_argument('--force', action='store_true', help='强制重新索引')
    
    # 查询参数命令
    query_parser = subparsers.add_parser('query', help='查询Platform参数')
    query_parser.add_argument('query_text', help='查询文本')
    query_parser.add_argument('--top-k', type=int, default=5, help='返回结果数量')
    
    # 生成脚本命令
    generate_parser = subparsers.add_parser('generate', help='生成Platform脚本')
    generate_parser.add_argument('--config-file', help='配置文件路径')
    generate_parser.add_argument('--platform-name', help='平台名称')
    generate_parser.add_argument('--platform-type', default='WSF_PLATFORM', help='平台类型')
    generate_parser.add_argument('--side', help='阵营')
    generate_parser.add_argument('--position', help='位置 (lat,lon)')
    generate_parser.add_argument('--altitude', type=float, help='高度')
    generate_parser.add_argument('--heading', type=float, help='航向')
    
    # 列出参数命令
    subparsers.add_parser('list', help='列出所有Platform参数')
    
    # 状态命令
    subparsers.add_parser('status', help='显示Agent状态')
    
    # 交互模式命令
    subparsers.add_parser('interactive', help='进入交互模式')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 初始化Agent
    agent = init_agent(args.config)
    
    # 执行命令
    if args.command == 'index':
        cmd_index_docs(agent, args.docs_path, args.force)
    elif args.command == 'query':
        cmd_query_params(agent, args.query_text, args.top_k)
    elif args.command == 'generate':
        # 处理位置参数
        kwargs = {
            'platform_name': args.platform_name,
            'platform_type': args.platform_type,
            'side': args.side,
            'altitude': args.altitude,
            'heading': args.heading
        }
        if args.position:
            try:
                lat, lon = map(float, args.position.split(','))
                kwargs['position'] = {'lat': lat, 'lon': lon}
            except ValueError:
                print("❌ 位置格式错误，应为: lat,lon")
                return
        
        cmd_generate_script(agent, args.config_file, **kwargs)
    elif args.command == 'list':
        cmd_list_params(agent)
    elif args.command == 'status':
        cmd_status(agent)
    elif args.command == 'interactive':
        interactive_mode(agent)
    else:
        print("🎯 AFSIM Platform Agent")
        print("使用 --help 查看可用命令")
        print("或使用 'interactive' 命令进入交互模式")


if __name__ == '__main__':
    main()
