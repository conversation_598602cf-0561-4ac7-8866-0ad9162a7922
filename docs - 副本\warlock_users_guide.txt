# warlock_users_guide.rst.txt.md
orphan

:   



Warlock is an Operator-in-the-loop (OITL) application that is integrated with AFSIM.

::: {.contents local=""}
:::



The Warlock application is similar to the `mission <mission>`{.interpreted-text role="doc"} application, and it supports the same AFSIM scenarios, command line arguments, and plugins.

The following is a list of differences between Warlock and the mission application.

-   Warlock is intended to be run in real-time. It can be run constructively however some interfaces may not perform as intended such as the ability to control entities. See `realtime`{.interpreted-text role="command"}.
-   Warlock supports starting it with no command line arguments. If no command line arguments are used then <PERSON><PERSON> will open a `Start Dialog <warlock_start_dialog>`{.interpreted-text role="doc"} where the user can select what scenario file they want to run.
-   Warlock supports not just AFSIM plugins, but Warlock plugins. Warlock plugins can have graphical displays and interfaces.
-   Warlock is a multi-threaded application, in which AFSIM runs on its own thread and the graphical portion runs on a different thread.



Warlock can be registered with <PERSON> so that Warlock can be executed when the Run button is pressed within Wizard. For more information read `Run from Wizard <Wizard_Users_Guide.run_from_wizard>`{.interpreted-text role="ref"} documentation.



Many parts of Warlock will store the applications state and store it, so that the next time Warlock is started the state is the same.

This information is stored in a user settings file referred to as the `configuration file<warlock_user_configurations>`{.interpreted-text role="doc"}.

The default configuration file on Windows is stored in the C:\\Users\\\<USER>\>\\AppData directory.