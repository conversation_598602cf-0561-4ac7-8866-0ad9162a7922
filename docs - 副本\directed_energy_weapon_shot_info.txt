# directed_energy_weapon_shot_info.rst.txt.md
orphan

:   



The data contained in DIRECTED_ENERGY_WEAPON_BEGIN_SHOT, DIRECTED_ENERGY_WEAPON_END_SHOT, and DRECTED_ENERGY_WEAPON_UPDATE_SHOT are identical.



    <time> <event> <firing platform> <target platform> <target platform offset>
    Weapon <weapon name> ( <weapon type> ) Target_Type <target type>
    Target_Relative_Loc(NED): <N> <E> <D>
    Target_Range_Bearing_Elevation: <range> <bearing> <elevation> 
    Power <power> Peak_Irradiance <peak irradiance> Edge_Irradiance: <edge irradiance> Damage_Radius <damage radius>
    Energy Density <energy density> Energy <energy> Power <power> 
    Beam_Semi_Major_Axis <beam semi-major axis> Beam_Semi_Minor_Axis <beam semi-minor axis> Incidence_Angle <incidence angle>



  Tag                                   Description
  ------------------------------------- -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  \<time\>                              current simulation time
  \<event\>                             simulation event recorded in this message
  \<firing platform\>                   name of firing platform
  \<target platform\>                   name of target platform. If the target has already been killed or removed from the simulation, the message \"(Target Not in Simulation)\" will appear instead.
  \<target offset\>                     name of target offset (if any)
  \<weapon name\>                       name of the directed energy weapon that is firing
  \<weapon type\>                       type of directed energy weapon that is firing
  \<target type\>                       target platform\'s type
  \<N\> \<E\> \<D\>                     The location of the target with respect to the firing platform in local North-East-Down coordinates. These data will only be present if the target has not been killed / removed from the simulation.
  \<range\> \<bearing\> \<elevation\>   The location of the target with respect to the firing platform in range, bearing, and elevation. These data will only be present if the target has not been killed / removed from the simulation.
  \<power\>                             power on target
  \<peak irradiance\>                   irradiance at the center of the beam
  \<edge irradiance\>                   irradiance at the damage radius
  \<damage radius\>                     the radius within which the beam is effective. This is the bucket size for power in the bucket calculations
  \<energy density\>                    the energy density (fluence) on target for this shot
  \<energy\>                            the energy on target for this shot
  \<semi-major axis\>                   the longitudinal beam size on target, measured from the peak to the point at which irradiance is 1/e. This will be the beam spot size divided by the cosine of the incidence angle.
  \<semi-minor axis\>                   the transverse beam size on target, measured from the peak to the point at which irradiance is 1/e.



    31.00000 DIRECTED_ENERGY_WEAPON_BEGIN_SHOT redeye su-37 (IRST) \ 
     Weapon: hel (HEL) Target_Type: SU-37 \ 
     Target_Relative_Loc(NED): 18905.1 -389.152 1028.21 \ 
     Target_Range_Bearing_Elevation: 18937.1 -1.17924 -3.11247 \ 
     Power: 0.175134 kW Peak_Irradiance: 0.0144336 kW/cm^2 Edge_Iradiance: 0.0134337 kW/cm^2 Damage_Radius: 2 cm \ 
     Energy_Density: 3.21477e-009 kJ/cm^2 Energy: 1.75134e-007 kJ \ 
     Beam_Semi_Major_Axis: 32.3593 cm Beam_Semi_Minor_Axis: 7.4643 cm Incidence_Angle: 76.6635 deg

    32.00000 DIRECTED_ENERGY_WEAPON_UPDATE_SHOT redeye su-37 (IRST) \
    Weapon: hel (HEL) Target_Type: SU-37 \
    Target_Relative_Loc(NED): 18765.5 -466.06 1027.8 \
    Target_Range_Bearing_Elevation: 18799.4 -1.4227 -3.13403 \
    Power: 0.45285 kW Peak_Irradiance: 0.0373152 kW/cm^2 Edge_Iradiance: 0.0347428 kW/cm^2 Damage_Radius: 2 cm \
    Energy_Density: 0.0170642 kJ/cm^2 Energy: 0.358749 kJ \
    Beam_Semi_Major_Axis: 12.5195 cm Beam_Semi_Minor_Axis: 7.48324 cm Incidence_Angle: 53.2926 deg

    35.00000 DIRECTED_ENERGY_WEAPON_END_SHOT redeye su-37 (IRST) \
    Weapon: hel (HEL) (Target Not in Simulation) \
    Power: 0.718465 kW Peak_Irradiance: 0.0593554 kW/cm^2 Edge_Iradiance: 0.0549737 kW/cm^2 Damage_Radius: 2 cm \
    Energy_Density: 0.147195 kJ/cm^2 Energy: 2.10834 kJ \
    Beam_Semi_Major_Axis: 8.23206 cm Beam_Semi_Minor_Axis: 7.22219 cm Incidence_Angle: 28.6788 deg



::: parsed-literal

`event_output`{.interpreted-text role="command"}

:   file replay.evt \# write event messages to file \"replay.evt\" enable DIRECTED_ENERGY_WEAPON_BEGIN_SHOT enable DIRECTED_ENERGY_WEAPON_UPDATE_SHOT enable DIRECTED_ENERGY_WEAPON_END_SHOT end_event_output
:::