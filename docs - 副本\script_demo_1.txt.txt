# script_demo_1.txt.rst.txt.md
orphan

:   



::: parsed-literal
\# 1 2 3 4 5 6 7 #234567890123456789012345678901234567890123456789012345678901234567890123456789 \################################################################################ \# Basic `Script <_.script_commands.script>`{.interpreted-text role="command"} Demo \# \# This is a basic `_.script_commands.script`{.interpreted-text role="command"} example that demonstrates some the features \# of the scripting language and how to use scripting in a WSF component. \# \# In this scenario we have: \# \# a) an air `platform`{.interpreted-text role="command"}. \# \# The air `platform`{.interpreted-text role="command"} will fly a `route`{.interpreted-text role="command"} and print its configuration using a `_.script_commands.script`{.interpreted-text role="command"}. \# \################################################################################

`_.script_commands.script_interface`{.interpreted-text role="command"}

:   debug

end_script_interface

\################################################################################ // Define the `platform`{.interpreted-text role="command"} type for the notional 737.

`platform.platform_type`{.interpreted-text role="command"} 737 `WSF_PLATFORM`{.interpreted-text role="model"}

> `mover`{.interpreted-text role="command"} `WSF_AIR_MOVER`{.interpreted-text role="model"} end_mover
>
> // This is an example single shot `processor`{.interpreted-text role="command"} that runs only once. // It dumps out a bunch of information about the `platform`{.interpreted-text role="command"}. In its // current configuration it doesn\'t contain any sensors, `comm`{.interpreted-text role="command"} devices, etc. `processor`{.interpreted-text role="command"} show-config-proc `WSF_SCRIPT_PROCESSOR`{.interpreted-text role="model"} update_interval 1.0 sec
>
> > // An example of how to create instance variables. These can be used // in any scripts defined within the current `processor`{.interpreted-text role="command"}. `ScriptVariables`{.interpreted-text role="ref"} int mMyInt = 999; double mMyDouble = 123.456; `WsfPlatform`{.interpreted-text role="class"} mMyPlatform = PLATFORM;
> >
> > > // Create an `Array\<T\>`{.interpreted-text role="class"}. Maps are also available (see the User\'s guide). `Array\<double\><Array\<T\>>`{.interpreted-text role="class"} mMyArray = `Array\<double\><Array\<T\>>`{.interpreted-text role="class"} ();
> >
> > end_script_variables
> >
> > // This is an example of how to create a `_.script_commands.script`{.interpreted-text role="command"} that is available // on the current `processor`{.interpreted-text role="command"}. `_.script_commands.script`{.interpreted-text role="command"} void PrintPlatformName(`WsfPlatform`{.interpreted-text role="class"} aPlatform) // Print name using `_.script_commands.script`{.interpreted-text role="command"} argument. print(\"The `platform`{.interpreted-text role="command"}\'s name is \", aPlatform.Name());
> >
> > > // Print name using the `_.script_commands.script`{.interpreted-text role="command"} variable (declared in `ScriptVariables`{.interpreted-text role="ref"}). print(\"The `platform`{.interpreted-text role="command"}\'s name is \", mMyPlatform.Name());
> >
> > end_script
> >
> > // on_initialize is a \'common\' `_.script_commands.script`{.interpreted-text role="command"} provided by several WSF components // (see User\'s guide for a complete list). Notice the syntax is different // from how regular scripts are declared. on_initialize print(\"on_initialize\");
> >
> > > // Add some data to the `Array<Array\<T\>>`{.interpreted-text role="class"}. mMyArray.PushBack(1.2); mMyArray.PushBack(2.3); mMyArray.PushBack(3.4);
> >
> > end_on_initialize
> >
> > // on_update is a \'common\' `_.script_commands.script`{.interpreted-text role="command"} provided by several WSF components // (see User\'s guide for a complete list). Notice the syntax is different // from how regular scripts are declared.. on_update print(\"on_update\");
> >
> > > // Calls to external `_.script_commands.script`{.interpreted-text role="command"} must be externed. extern void PrintPlatformName(`WsfPlatform`{.interpreted-text role="class"}); PrintPlatformName(PLATFORM);
> > >
> > > print(\"\"); print(\"Print my member variables\"); print(\"\-\-- mMyPlatform name = \", mMyPlatform.Name()); print(\"\-\-- mMyInt = \", mMyInt); print(\"\-\-- mMyDouble = \", mMyDouble); print(\"\-\-- mMyArray = \");
> > >
> > > // For each loop. The key loop variable is optional. print(\"`Array<Array\<T\>>`{.interpreted-text role="class"} elements using a foreach with key and data\"); foreach (int key : double data in mMyArray) { print(\"\-\-- key, data \", key, \", \", data); } print(\"\");
> > >
> > > // For each loop without the key. print(\"`Array<Array\<T\>>`{.interpreted-text role="class"} elements using a foreach with data\"); foreach (double data in mMyArray) { print(\"\-\-- data \", data); } print(\"\");
> > >
> > > // You can use an `iterator`{.interpreted-text role="class"}. print(\"`Array<Array\<T\>>`{.interpreted-text role="class"} elements using an `iterator`{.interpreted-text role="class"}\"); `ArrayIterator`{.interpreted-text role="class"} arrayIter = mMyArray.GetIterator(); while (arrayIter.HasNext()) { double data = (double)arrayIter.Next(); print(\"\-\-- key, data \", arrayIter.Key(), \", \", data); } print(\"\");
> > >
> > > print(\"Information for \", PLATFORM.Name(), \".\", PLATFORM.Type());
> > >
> > > print(\" Command Chains\"); for (int i = 0; i \< PLATFORM.CommandChainCount(); i = i + 1) { `WsfCommandChain`{.interpreted-text role="class"} chain = PLATFORM.CommandChainEntry(i); print(\" \", chain.Name()); if (chain.Commander().IsValid()) { print(\" Commander: \", chain.Commander().Name()); } print(\" Peers\"); foreach (`WsfPlatform`{.interpreted-text role="class"} peer in chain.Peers()) { print(\" \", peer.Name()); } print(\" Subordinates\"); foreach (`WsfPlatform`{.interpreted-text role="class"} subordinate in chain.Subordinates()) { print(\" \", subordinate.Name()); } } print(\" `comm`{.interpreted-text role="command"} Systems\"); for (int i = 0; i \< PLATFORM.CommCount(); i = i + 1) { `WsfComm`{.interpreted-text role="class"} `comm`{.interpreted-text role="command"} = PLATFORM.CommEntry(i); print(\" \", `comm`{.interpreted-text role="command"}.Name(), \"; Type=\", `comm`{.interpreted-text role="command"}.Type(), \" On=\", `comm`{.interpreted-text role="command"}.IsTurnedOn()); } print(\" `sensor`{.interpreted-text role="command"} Systems\"); for (int i = 0; i \< PLATFORM.SensorCount(); i = i + 1) { `WsfSensor`{.interpreted-text role="class"} `sensor`{.interpreted-text role="command"} = PLATFORM.SensorEntry(i); print(\" \", `sensor`{.interpreted-text role="command"}.Name(), \"; Type=\", `sensor`{.interpreted-text role="command"}.Type(), \" On=\", `sensor`{.interpreted-text role="command"}.IsTurnedOn()); } print(\" Processors\"); for (int i = 0; i \< PLATFORM.ProcessorCount(); i = i + 1) { `WsfProcessor`{.interpreted-text role="class"} `processor`{.interpreted-text role="command"} = PLATFORM.ProcessorEntry(i); print(\" \", `processor`{.interpreted-text role="command"}.Name(), \"; Type=\", `processor`{.interpreted-text role="command"}.Type(), \" On=\", `processor`{.interpreted-text role="command"}.IsTurnedOn(), \" UpdateInterval=\", `processor`{.interpreted-text role="command"}.UpdateInterval()); }
> > >
> > > // Disable future calls. PROCESSOR.TurnOff();
> >
> > end_on_update
>
> end_processor

end_platform_type

\# 1 2 3 4 5 6 7 #234567890123456789012345678901234567890123456789012345678901234567890123456789 \################################################################################

`platform`{.interpreted-text role="command"} 737-1 737

:   side blue

    command_chain ATC SELF

    `route`{.interpreted-text role="command"}

\# Take off

:   position 38:44:52.3n 90:21:36.4w altitude 6 ft agl speed 0 kts position 38:45:07.6n 90:22:09.4w altitude 6 ft agl speed 120 kts \# climb_rate 1000 fpm

    position 38:49:00n 90:29:00w altitude 15000 ft speed 400 kts position 39:29:00n 91:30:00w altitude 35000 ft position 38:45:00n 90:06:08w position 38:38:24n 90:07:46w altitude 10000 ft speed 250 kts

\# Landing

:   position 38:44:52.3n 90:21:36.4w altitude 6 ft agl speed 120 kts position 38:45:07.6n 90:22:09.4w altitude 6 ft agl speed 0 kts end_route

end_platform

`end_time`{.interpreted-text role="command"} 1200 sec
:::