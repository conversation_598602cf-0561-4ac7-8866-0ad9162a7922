orphan

:   

# WsfOrbitalEventCondition

::: WsfOrbitalEventCondition
Specify a condition upon which to execute an `orbital event<WsfOrbitalEvent>`{.interpreted-text role="class"} (including all `WsfOrbitalManeuver`{.interpreted-text role="class"}s). These objects are passed to the *Construct* methods of specific `script orbital events<available_orbital_events>`{.interpreted-text role="ref"}. These conditions are identical with those specified in the input `orbital event conditions<orbital_event.constraints>`{.interpreted-text role="ref"}.
:::

## Static Methods

::: method
WsfOrbitalEventCondition NONE()

There is no explicit constraint on the event: it will execute as soon as possible.
:::

::: method
WsfOrbitalEventCondition AT_RELATIVE_TIME(double aTime)

Execute the event at a relative time in the future.
:::

::: method
WsfOrbitalEventCondition AT_PERIAPSIS()

Execute the event once the satellite next reaches its periapsis.
:::

::: method
WsfOrbitalEventCondition AT_APOAPSIS()

Execute the event once the satellite next reaches its apoapsis.
:::

::: method
WsfOrbitalEventCondition AT_ASCENDING_NODE()

Execute the event once the satellite reaches the ascending node.
:::

::: method
WsfOrbitalEventCondition AT_DESCENDING_NODE()

Execute the event once the satellite reaches the descending node.
:::

::: method
WsfOrbitalEventCondition AT_ASCENDING_RADIUS(double aRadius)

Execute the event once the satellite reaches the specified radius and after reaching the ascending node.
:::

::: method
WsfOrbitalEventCondition AT_DESCENDING_RADIUS(double aRadius)

Execute the event once the satellite reaches the specified radius and after reaching the descending node.
:::

::: method
WsfOrbitalEventCondition AT_NORTHERN_INTERSECTION()

Execute the event (typically a `WsfChangeRAAN`{.interpreted-text role="class"} or `WsfChangeRAAN_Inclination`{.interpreted-text role="class"} maneuver) once the satellite reaches a specific northern latitude intersection.
:::

::: method
WsfOrbitalEventCondition AT_SOUTHERN_INTERSECTION()

Execute the event (typically a `WsfChangeRAAN`{.interpreted-text role="class"} or `WsfChangeRAAN_Inclination`{.interpreted-text role="class"} maneuver) once the satellite reaches a specific southern latitude intersection.
:::

::: method
WsfOrbitalEventCondition AT_ECLIPSE_ENTRY()

Execute the event when the satellite enters earth\'s shadow.
:::

::: method
WsfOrbitalEventCondition AT_ECLIPSE_EXIT()

Execute the event when the satellite exits earth\'s shadow.
:::

## Methods

::: method
string ConditionType()

Returns the string identifier of the condition required for the event to execute.
:::

::: method
void SetOrbitNumber(int aOrbitNumber)

Optionally specify a number of orbits to propagate before the event is executed (providing an additional delay).
:::
