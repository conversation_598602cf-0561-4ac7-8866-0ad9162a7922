# p6dof_integrators_support.rst.txt.md
orphan

:   



P6DOF was designed with the ability to use different numerical integration approaches. These different integrators are \"loaded\" through the use of the [p6dof_integrators]() command.

At the current time, only a single (default) integrator is provided, but software developers can derive from the base class (P6DofIntegrator) to introduce other integrators.

::: command
p6dof_integrators \<filename\>

This will load one or more P6DOF integrators by reading/loading the specified file.

See `P6DOF_Integrators_File_Definition`{.interpreted-text role="ref"} for the definition of the integrators file format.
:::

The [p6dof_integrators]() line can be defined independently, but is often grouped into a another file (typically p6dof_environment.txt) along with the `P6DOF_Environment_Support`{.interpreted-text role="ref"} items.

Return to `p6dof_object_types`{.interpreted-text role="doc"}