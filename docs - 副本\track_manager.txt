# track_manager.rst.txt.md
orphan

:   



::: {.command block=""}
track_manager
:::

::: parsed-literal

track_manager

:   [debug]() [correlation_method]() \... [fusion_method]() \... [tracker_type]() \... [uncorrelated_track_drops]() \... [retain_raw_tracks]() [retain_track_history]() [filter]() \... end_filter [track]() \... end_track [aux_data]() \... end_aux_data [aux_data_fusion_rules]() \... end_aux_data_fusion_rules [type_specific_tracker_inputs]()

end_track_manager
:::



The [track_manager](#track_manager) block is a subcommand of a `platform`{.interpreted-text role="command"} that defines subcommands for the platform\'s master track manager. A track manager maintains the platform\'s master track list. It can also be used to maintain alternate track lists with the standard WSF processor, `WSF_TRACK_PROCESSOR`{.interpreted-text role="model"}.



::: command
debug

Enable the writing of debugging information to standard output.
:::

::: command
correlation_method

::: parsed-literal

correlation_method `<correlation-method> <correlation_methods.available_correlation_methods>`{.interpreted-text role="ref"}

:   `Correlation commands... <correlation_methods>`{.interpreted-text role="ref"}

end_correlation_method
:::

Specifies the correlation algorithm used by the track manager.

A correlation (association) algorithm determines whether new information in the form of track and measurement updates is matched with existing tracks. If so, the new information is fused with the existing tracks using the [fusion_method](); otherwise, new tracks are created.

\<correlation-method\> can be:
:::

::: command
fusion_method

::: parsed-literal

fusion_method \<fusion-method\>

:   Fusion Commands\...

end_fusion_method
:::

Specifies the fusion algorithms used by the track manager.

A fusion algorithm combines information about a single entity from two or more sources into a coherent information set, or `track`{.interpreted-text role="command"}.

\<fusion-method\> can be:
:::

::: command
tracker_type

::: parsed-literal

tracker_type \<type-name\>

:   `Correlation commands... <correlation_methods>`{.interpreted-text role="ref"} `Fusion commands...      <fusion_methods>`{.interpreted-text role="ref"} Other tracker commands\...

end_tracker_type
:::

Specifies a standard tracker type to use. Use of this input means that one does not have to specify [correlation_method]() or [fusion_method]().

::: note
::: title
Note
:::

Currently the only valid [tracker_type]() is \"mtt\" (Multi-Target Tracker; see MTT documentation for specific tracker inputs).
:::
:::

::: command
uncorrelated_track_drops \[ on; off \]

Specifies whether uncorrelated local tracks (tracks that no longer have any associated raw tracks) are to be dropped. Automatically set to off if tracks are purged in an associated `WSF_TRACK_PROCESSOR`{.interpreted-text role="model"}.

Default on
:::

::: command
retain_raw_tracks

Specifies that the track manager is to retain all raw track information. If set, it is the responsibility of the user to manage raw track information.

Default No raw track information is retained.
:::

::: command
retain_track_history

Specifies that the track manager is to retain track history information. If set, it is the responsibility of the user to manage the track history.

Default No track history information is retained
:::

::: command
filter \<type-name\> end_filter

Associates a `filter`{.interpreted-text role="command"} type with the track manager. All incoming tracks of type \"unfiltered sensor\" will be filtered with this filter type.
:::

::: command
track \... end_track

Defines a pre-briefed `track`{.interpreted-text role="command"}.
:::

::: command
aux_data \... end_aux_data

Specifies additional data that will be added to any `"local" track <WsfLocalTrack>`{.interpreted-text role="class"} created and maintained by the track manager. These will be in addition to any `_.aux_data`{.interpreted-text role="command"} present in the associated / correlated `"raw" tracks <WsfTrack>`{.interpreted-text role="class"}, as they are naturally merged into the local tracks. These data are useful as additional aids to tracking and resource allocation.
:::

::: command
aux_data_fusion_rules \... end_aux_data_fusion_rules

Defines the rules to be applied when \'fusing\' `_.aux_data`{.interpreted-text role="command"} variables from a raw track into a local track. Normally a variable in a raw track will overwrite one in the local track with the same name.

variable \<name\> private

> A variable marked \"private\' is one that will never be overwritten by when fusion occurs. The variable can only be manipulated by various local script/task processors.

variable \<name\> only_local

> A variable marked \"only_local\' can only be overwritten by incoming raw tracks that originate from the this platform.

variable \<name\> prefer_local

> A variable marked \"prefer_local\' can be overwritten by an incoming track from the local platform OR from an incoming track from another platform IF the local track does not have any other contributing track from this platform that contains the same variable.
:::

::: command
type_specific_tracker_inputs

Various inputs will be valid, based on the [tracker_type]() selected. Specifically, see the ` mtt <MTT Configuration>`{.interpreted-text role="ref"} inputs for the MTT tracker.
:::