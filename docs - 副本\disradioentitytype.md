orphan

:   

# DisRadioEntityType {#DisRadioEntityType}

::: DisRadioEntityType
::: parsed-literal
`DisRadioEntityType.EntityKind`{.interpreted-text role="method"} `DisRadioEntityType.Domain`{.interpreted-text role="method"} `DisRadioEntityType.Country`{.interpreted-text role="method"} `DisRadioEntityType.Category`{.interpreted-text role="method"} `DisRadioEntityType.NomenclatureVersion`{.interpreted-text role="method"} `DisRadioEntityType.Nomenclature`{.interpreted-text role="method"}
:::
:::

## Overview

[DisRadioEntityType](#disradioentitytype) is an implementation of the DIS radio entity type. The radio entity type is used by DIS to identify types of radios.

## Methods

::: method
int EntityKind()

Returns the entity kind ID.
:::

::: method
int Domain()

Returns the domain ID.
:::

::: method
int Country()

Returns the country ID.
:::

::: method
int Category()

Returns the category ID.
:::

::: method
int NomenclatureVersion()

Returns the nomenclature version ID.
:::

::: method
int Nomenclature()

Returns the nomenclature ID.
:::
