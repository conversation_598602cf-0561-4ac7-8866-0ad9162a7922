# group.rst.txt.md
orphan

:   



Script Class: `WsfGroup`{.interpreted-text role="class"}

::: {.command block=""}
group
:::

::: parsed-literal

group \<group-name\> \<base-group-type\>

:   [aux_data]() \...

end_group
:::



A `group`{.interpreted-text role="command"} object represents an aggregation of `platform`{.interpreted-text role="command"} and platform part (`sensor`{.interpreted-text role="command"}, `processor`{.interpreted-text role="command"}, `comm`{.interpreted-text role="command"}) objects. In order to join a group the object must either provide the *group_join* input for `platforms <platform.group_join>`{.interpreted-text role="command"} or `platform parts <_.platform_part.group_join>`{.interpreted-text role="command"}, or one may use the script method `WsfGroup.AddMember(...) <WsfGroup.AddMember>`{.interpreted-text role="method"}.

The base type `WsfGroup`{.interpreted-text role="class"} has only auxiliary data.



::: command
aux_data \<aux-data\> \... end_aux_data

Defines auxiliary data for a platform. See `_.aux_data`{.interpreted-text role="command"}.
:::

Referenced by: `WsfGroup`{.interpreted-text role="class"}, `platform`{.interpreted-text role="command"}, `WsfPlatform`{.interpreted-text role="class"} and `WsfSimulation`{.interpreted-text role="class"} for group methods.