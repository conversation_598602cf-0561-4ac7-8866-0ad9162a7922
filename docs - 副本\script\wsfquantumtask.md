orphan

:   

# WsfQuantumTask

## Overview {#overview .WsfQuantumTask .inherits .WsfTask}

`WsfQuantumTask`{.interpreted-text role="class"} is used by `WSF_QUANTUM_TASKER_PROCESSOR`{.interpreted-text role="model"} to represent a task with a priority and accessible type. Quantum Tasks automatically generate a unique id for themselves if they were constructed with a task type and a track. If the user exercises either of the two simpler constructors listed above and a track is not given to the quantum task, then the user should call SetUniqueId() on the created task.

## Methods

::: method
static WsfQuantumTask Construct(double aPriority) static WsfQuantumTask Construct(double aPriority, string aType) static WsfQuantumTask Construct(double aPriority, string aType, WsfTrack aTargetTrack)

Static methods to create a quantum task. \'aType\' is the task type, also equivalent with resource type. (weapon, sensor, jammer, or none)
:::

::: method
double Priority()

Returns the priority of the quantum task.
:::

::: method
void Priority(double aPriority)

Sets the priority of the quantum task.
:::

::: method
void UniqueId(int aUniqueId)

Sets the id of the quantum task (hopefully the user makes it unique). This is necessary if the task does not have a track associated with it.
:::

::: method
string ResourceType()

Returns the resource type (equivalent with task type) as a string. Expected values: \"weapon\", \"sensor\", \"jammer\", or \"unknown\".
:::
