# gun_engagement.rst.txt.md
orphan

:   

::: demo
gun_engagement
:::



1.  Populate the variables into the file \"input_variables.txt\" (They are already input for you using bullet info). The input_variables.txt file has the basic parameters required to create a bullet.
2.  Then do \"mission run_me_first.txt\". The run_me_first.txt script will read the input_variables.txt file and create the bullet weapon and launch generator files. The files bullet.txt (weapon) and bullet_buildertool.txt (launch computer builder) are created in the weapons/guns subdirectory.
3.  Next do \"mission run_me_second.txt\". This will create a launch computer file in the weapons/guns/subdirectory using the bullet_buildertool.txt file. The file name in this case is bullet_launch_computer.txt. The file bullet_buildertool.txt can be deleted.
4.  The last is to run the scenario that puts the launcher shooting at a target. Prior to running \"run_me_third.txt\", modify the platforms/gun_battery.txt file and put the proper weapon (bullet) and launch_computer references in (in this case, they are done for you with the bullet). In the actual bullet weapon file (bullet.txt) you MUST uncomment out the launch computer include file reference (it was commented out to create the launch computer).

::: note
::: title
Note
:::

\* In the launch computer file, you can uncomment out the draw methods to see where the launch computer is aiming for.

-   If you want to see the bullets fly through the target, comment out the fuse in the weapon.
-   The geometric sensor on the launcher is set to have a 0.1 mil error.
:::



| <http://fas.org/man/dod-101/sys/ac/equip/gau-8.htm>
| <https://en.wikipedia.org/wiki/GAU-8_Avenger>
| <http://www.gd-ots.com/armament_systems/ags_A-10.html>
| <https://en.wikipedia.org/wiki/Goalkeeper_CIWS>
| <http://everything.explained.today/Goalkeeper_CIWS/>
| <http://www.worldlibrary.org/Article.aspx?Title=goalkeeper_ciws>
| <http://www.navweaps.com/Weapons/WNNeth_30mm_Goalkeeper.htm>
|  UNCLASSIFIED TECHNICAL REPORT ARSCD-TR464022,30-MM TUBULAR PROJECTILE, OCTOBER 1984
|        US ARMY ARMAMENT RESEARCH AND DEVELOPMENT CENTER
|  Mass:
|    PGU-13/B HE-I: 1.48 lbs. (0.671 kg)
| 
| Muzzle Velocity
|    HE-I: 3,350 fps (1,021 mps)
| Elevation FOV limits
|   -10 to 85 degrees
| Elevation_rate
|   85 degs per sec
| AZ FOV limits
|   360 degs
| AZ RATE
|   100 degs per sec
| Accuracy
|   5mil, 80 percent - 80% of rounds fired at 4,000ft hit within a 20ft radius
| Min/Max Range - various
|   500 meters min
|   1500 to 2000 meters max (flat trajectory) (sources vary) - Im using 2000 meters.
| Magazine Size - 1200 rounds
| Firing rate - 70 rounds per second