orphan

:   

# WsfMoonMonopoleTerm

## Overview {#overview .WsfMoonMonopoleTerm .inherits .WsfOrbitalDynamicsTerm}

The `WsfMoonMonopoleTerm`{.interpreted-text role="class"} represents the point mass gravitational field of the Moon. See also `Moon Monopole <orbital_dynamics_terms.moon_monopole>`{.interpreted-text role="ref"}.

In typical uses of this term by `WsfIntegratingSpaceMover`{.interpreted-text role="class"}, the Moon\'s position is queried repeatedly, which impacts the performance of this term. To alleviate this computational load, this term uses interpolation. The interpolation used is 3rd order with an interpolant formed from the position of the Moon at four equally spaced moments in time. The interval between these moments is user-specifiable, and if interpolation is not desired, this behavior can be turned off.

## Static Methods

::: method
WsfMoonMonopoleTerm Construct() WsfMoonMonopoleTerm Construct(double aGravitationalParameter) WsfMoonMonopoleTerm Construct(double aGravitationalParameter, double aInterpolationInterval)

Create a term representing the Moon\'s point mass gravitational field with the given gravitational parameter in SI units, and with the given interpolation interval. If no gravitational parameter is specified, a default value equal to the return value from `Moon.GRAVITATIONAL_PARAMETER`{.interpreted-text role="method"} will be used. If no interpolation interval is specified, the default value of 600 s will be used.

To disable interpolation of lunar positions for this term, use an interval of 0.

::: warning
::: title
Warning
:::

Disabling interpolation will impose a runtime penalty.
:::
:::

## Methods

::: method
double GravitationalParameter()

Return the gravitational parameter of the Moon in SI units used by this term.
:::

::: method
double InterpolationInterval()

Return the interval in seconds used for the interpolation of the Moon\'s position used by this term. If interpolation is disabled, this will return 0.
:::

::: method
Vec3 MoonPositionECI(Calendar aTime)

Return the moon position in the ECI frame used by this term at the given time.
:::
