orphan

:   

# P6Dof Data - Mystic

::: note
::: title
Note
:::

P6DOF Mover and its associated plugins are deprecated in favor of the new SixDOF Mover and its plugins. P6DOF Data has been deprecated in favor of SixDOF Data, so is disabled by default and must be enabled through the `Plugin Manager <../mystic_plugin_manager>`{.interpreted-text role="doc"}.
:::

![image](../images/rv_p6dof_data.png)

The P6Dof Data plugin is responsible for populating the Platform Details with data specific to the P6Dof mover.

For more information on enabling P6DOF data, see `event_pipe<p6dof_event_pipe>`{.interpreted-text role="ref"}.
