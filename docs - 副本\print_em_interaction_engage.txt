# print_em_interaction_engage.rst.txt.md
orphan

:   



Print EM Interaction prints receiver, target, and transmitter interaction information common to a number of event records.





::: parsed-literal

PrintEM_AbsXmtrRcvrData

:   

    Xmtr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<heading\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

    Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<heading\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelXmtrRcvrData

:   

    Xmtr-\>Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

    Rcvr-\>Xmtr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_BeamData

:   XmtrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"} RcvrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"}
:::



::: parsed-literal

PrintEM_AbsXmtrRcvrData

:   

    Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_AbsTargetData

:   

    Tgt: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<heading\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelXmtrRcvrData

:   Rcvr-\>Tgt Brg: \<bearing\> deg El: \<elevation\> deg Apparent: Brg: \<bearing\> deg El: \<elevation\> deg

PrintEM_RelTargetData

:   Tgt-\>Rcvr Brg: \<bearing\> deg El: \<elevation\> deg Apparent: Brg: \<bearing\> deg El: \<elevation\> deg

PrintEM_BeamData

:   RcvrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"}
:::



    PrintEM_AbsXmtrRcvrData
     Xmtr: Type: <type> LLA: <lat> <lon> <alt> m Heading: <hdg> deg Pitch: <pitch> deg Roll: <roll> deg
      Speed: <speed> m/s
     Rcvr: Type: <type> LLA: <lat> <lon> <alt> m Heading: <hdg> deg Pitch: <pitch> deg Roll: <roll> deg
      Speed: <speed> m/s

    PrintEM_AbsTargetData
     Tgt: Type: <type> LLA: <lat> <lon> <alt> m Heading: <hdg> deg Pitch: <pitch> deg Roll: <roll> deg
      Speed: <speed> m/s

    PrintEM_RelXmtrRcvrData
     Xmtr->Tgt: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg

    PrintEM_RelTargetData
     Tgt->Xmtr: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg

    PrintEM_RelXmtrRcvrData
     Rcvr->Tgt: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg

    PrintEM_RelTargetData
     Tgt->Rcvr: Brg: <bearing> deg El: <elev> deg Apparent: Brg: <bearing> deg El: <elev> deg



::: parsed-literal

PrintEM_AbsXmtrRcvrData

:   

    Xmtr/Rcvr: Type: \<type\> LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_AbsTargetData

:   

    Tgt: LLA: Type: \<type\> \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelXmtrRcvrData

:   

    Xmtr/Rcvr-\>Tgt: LLA: \<lat\> \<lon\> \<alt\> m Heading: \<hdg\> deg Pitch: \<pitch\> deg Roll: \<roll\> deg

    :   Speed: \<speed\> m/s

PrintEM_RelTargetData

:   Tgt-\>Xmtr/Rcvr: Brg: \<bearing\> deg El: \<elev\> deg Apparent: Brg: \<bearing\> deg El: \<elev\> deg

PrintEM_BeamData

:   XmtrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"} RcvrBeam: Brg: \<angle\> deg El: \<angle\> deg TgtRel Az: \<angle\> deg El: \<angle\> deg `gain data <Beam_Gain_Data>`{.interpreted-text role="ref"}
:::



    Radar_Sig: <RadarSignal> dBsm (<RadarSignal (linear)> m^2) Az: <azimuth> deg El: <elev> deg



    Optical_Sig: <RadarSignal> dBsm (<RadarSignal (linear)> m^2) Az: <azimuth> deg El: <elev> deg



    Infrared_Sig: <RadarSignal> dBsm (<RadarSignal (linear)> m^2) Az: <azimuth> deg El: <elev> deg

    If Signal to Noise > 0.0
     Background_radiant_intensity: <BackgroundRadiantIntensity> w/sr
     Contrast_radiant_intensity: <ContrastRadiantIntensity> w/sr
     Transmittance: <AbsorptionFactor>



    Absorption_factor: <AbsorptionFactor> dB (<AbsorptionFactor (linear)>)



    Propagation_factor_F^4: <PropagationFactor> dB (<PropagationFactor (linear)>) F: <PropagationFactor ^0.25>



    Xmtd_Power: <TransmittedPower (linear)> dBw



    Rcvd_Power: <ReceivedPower (linear)> dBw



    Rcvr_Noise: <ReceiverNoisePower (linear)> dBw



    Clutter_Power: <ClutterPower (linear)> dBw



    Noise_Jammer_Power: <NoiseJammerPower (linear)> dBw



    Coherent_Jammer_Power: <CoherentJammerPower (linear)> dBw



    Pixel_Count: <PixelCount>



    S/I: <SignalToNoise> dB
    If Detection Threshold > 0.0
     Threshold: <DetectionThreshold> dB
    If Received Power > 0.0
     S/N: <SignalPower/noisePower> dB S/(N+C): <SignalPower/(noisePower+clutterPower)> dB
      S/(N+C+J): <SignalPower/(noisePower+clutterPower+jammerPower)> dB



    EW_Effects:
    If Dropped Track
     Drop_Track
    If Maintain Track
     Maintain_Track
    If Track Error
     Track_Error
    If Drop Message
     Drop_Message
    If Maintain Message
     Maintain_Message
    If Message_Error
     Message_Error
    If Jammer Power Effect
     Jammer_Power_Effect
    If False Target Effect
     False_Target



    Xmtr_Range_Limits_Exceeded
    Rcvr_Range_Limits_Exceeded
    Xmtr_Altitude_Limits_Exceeded
    Rcvr_Altitude_Limits_Exceeded
    Xmtr_Angle_Limits_Exceeded
    Rcvr_Angle_Limits_Exceeded
    Xmtr_Masked_By_Horizon
    Rcvr_Masked_By_Horizon
    Xmtr_Masked_By_Terrain
    Rcvr_Masked_By_Terrain
    Insufficient_Signal



If beam gain is greater than 0.0 and Antenna EBS Mode is available:

    EBS Az: <angle> deg El: <angle> deg Omega: <angle> deg
    Gain: <gain> dB