# wsfcommrouter.rst.txt.md
orphan

:   





::: method
static bool IsA_TypeOf(string aDerivedType, string aBaseType)

Returns true if the router type specified by aDerivedType inherits from the router type specified by aBaseType (compare to `WsfObject.IsA_TypeOf(aBaseType) <WsfObject.IsA_TypeOf>`{.interpreted-text role="method"} which determines if a specific object inherits from aBaseType).
:::



::: method
bool TurnOff()

Turns the router device off and returns true if successful.
:::

::: method
bool TurnOn()

Turns the router device on and returns true if successful.
:::

::: method
bool HasGateway()

Returns true if a valid gateway interface has been set for this router.
:::

::: method
WsfAddress GetGateway()

Returns the address of the comm interface acting as the gateway for this router. If a gateway has not been set for the router, the address returned will be null.
:::

::: method
void SetGateway(WsfAddress aAddress)

Sets the gateway interface for this router to be the comm interface designated by the provided address. If the specified address does not belong to a comm interface assigned to this router, the gateway will not be set.
:::

::: method
int GetHopLimit()

Returns the number of hops (time to live, TTL) currently being used for messages transmitted from an interface assigned to this router.
:::

::: method
void SetHopLimit(int aNumHops)

Sets the number of hops (time to live, TTL) that will be used for all subsequent messages transmitted from an interface assigned to this router.
:::

::: method
bool HasInterface(WsfAddress aAddress)

Returns true if the router has a comm interface assigned to it with the provided address.
:::

::: method
void AddInterface(WsfAddress aAddress)

Attempts to assign the comm interface specified by the provided address to this router.

A comm being assigned to a router must belong to the same platform instance. In addition, any attempt to add an interface to a router, when the comm is already assigned as an interface to another router, will cause the immediate removal of the interface from the other router (an interface may only be assigned to a single router at any given time).
:::

::: method
void RemoveInterface(WsfAddress aAddress)

Removes the comm interface with the provided address from the current router.
:::

::: method
Array\<WsfAddress\> GetInterfaces()

Returns an array of addresses corresponding to all the comm interfaces currently assigned to this router.
:::

::: method
WsfCommMedium GetMedium()

Gets the medium object associated with this router.
:::