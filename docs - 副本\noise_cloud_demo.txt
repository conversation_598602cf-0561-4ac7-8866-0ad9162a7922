# noise_cloud_demo.rst.txt.md
orphan

:   

::: demo
noise_cloud
:::

| This demonstrates the use of WSF_NOISE_CLOUD. It defines a noise
| cloud layer and uses it to affect detections of an air target from
| a radar. This demo uses sensorplot.
| 
| noise_cloud.txt
| We generate a horizontal map of probability of detection around a radar.
| The cloud layer is defined, and is given a sensor_modifier. The sensor
| uses this modifier with the modifier_category command in its definition.
| Adjust the values in the noise_cloud to see how they affect the structure
| of the cloud layer.
| 
| outputnoise_cloud.tiff
| This is the output that will be generated after executing with sensor_plot.
| It may be viewed in wizard by double-clicking on it in the project browser.