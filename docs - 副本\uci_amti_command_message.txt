# uci_amti_command_message.rst.txt.md
orphan

:   





This message allows the user to command a radar via UCI messaging.



::: method
static UCI_AMTI_CommandMessage Construct(UCI_AMTI_Command AMTICommand)

This method constructs an instance of an UCI_AMTI_CommandMessage.
:::



::: method
string CommandUUID(int commandIndex)

This method returns the UUID of the command at the given index.
:::

::: method
void PushBack(UCI_AMTI_Command AMTICommand)

This method adds the given UCI_AMTI_Command to the list of commands in the message.
:::