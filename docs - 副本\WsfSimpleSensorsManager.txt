# WsfSimpleSensorsManager.rst.txt.md
orphan

:   



::: WsfSimpleSensorsManager
Navigation: `script_types`{.interpreted-text role="doc"}
:::

Derives From: `WsfBMSensorsManager`{.interpreted-text role="class"}, `WsfProcessor`{.interpreted-text role="class"}, `WsfPlatformPart`{.interpreted-text role="class"}, `WsfObject`{.interpreted-text role="class"}



WsfSimpleSensorsManager is the script interface for invoking methods on the HELIOS Simple Sensors Manager processor. This class provides the main implementation for the AFSIM port for this model.



This class does not provide any additional interfaces over what the base class, `WsfBMSensorsManager`{.interpreted-text role="class"}, provides.