# mc_flight_test.rst.txt.md
orphan

:   



Mover Creator along with <PERSON><PERSON> give users the opportunity to test the vehicle they have designed. This is done through the *Flight Test* page. There are two sections to the *Flight Test* page:

-   [Initial Settings](#initial-settings)
-   3D rendering of the vehicle





In this section the user sets the desired starting location, heading and speed of the vehicle. Specifically, the user can change the following starting parameters:

-   Latitude
-   Longitude
-   Altitude
-   Heading
-   Speed

Once the user has the initial conditions set, they can launch *Warlock* by clicking the *Perform Flight Test* button. *Warlock* then loads up a simple scenario containing the vehicle designed by the user.



The user should then press the \'play\' button at the top of the *Warlock* application to observe the vehicle in flight. Refer to the `Warlock documentation <warlock>`{.interpreted-text role="doc"} for more details about options available in *Warlock*.



From this page the user can navigate to one of the following pages via the tabs or by clicking the *Previous* button.

-   `Start/Setup<mc_users_guide>`{.interpreted-text role="doc"}
-   `Geometry<mc_geometry>`{.interpreted-text role="doc"}
-   `Aerodynamics<mc_aerodynamics>`{.interpreted-text role="doc"}
-   `Performance<mc_performance>`{.interpreted-text role="doc"}
-   `Pilots/Controls<mc_pilots_controls>`{.interpreted-text role="doc"}
-   `Autopilot<mc_autopilot>`{.interpreted-text role="doc"}