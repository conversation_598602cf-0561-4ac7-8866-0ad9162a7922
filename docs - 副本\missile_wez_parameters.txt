# missile_wez_parameters.rst.txt.md
orphan

:   





The `missile_wez_parameters`{.interpreted-text role="command"} block provides a way for the user to circumvent the need for a more complex launch computer. If a launch computer is defined on the processor\'s owning platform, the `missile_wez_parameters`{.interpreted-text role="command"} commands have no effect.

The missile_wez_parameters block specifies the parameters used in WEZ calculations for the Air Combat WEZ Lines in the `Air Combat Visualization<wkf_plugin/wk_air_combat_visualization>`{.interpreted-text role="doc"} Plugin. These parameters are also used when invoking WEZ script methods such as ExpectedWezRangeAgainstTarget and ExpectedWezRangeByTarget

Required subcommands within the `missile_wez_parameters`{.interpreted-text role="command"} block are explained in [Required Commands](#required-commands). Any other commands specified by the user are optional and simply override default values. These commands are explained in [Optional Commands](#optional-commands)

::: {.command block=""}
missile_wez_parameters \... end_missile_wez_parameters

::: parsed-literal

missile_wez_parameters

:   // [Required Commands](#required-commands) [side]() \... [type]() \... // [Optional Commands](#optional-commands) [avg_speed]() \... [max_time_of_flight]() \... [max_off_boresight_angle]() \... [min_time_of_flight]() \... [nominal_pk]() \... [head_on_pk_reduction]() \... [broadside_pk_reduction]() \...

end_missile_wez_parameters
:::
:::



::: command
side \[red ; blue\]

Specifies the side for which the WEZ parameters apply. The available options are \'red\' and \'blue\'.
:::

::: command
type \[mrm ; srm\]

Specifies the missile type for which the WEZ parameters apply. The available options are \'mrm\' (medium-range missile) and \'srm\' (short-range missile).

The combination of the [side]() and [type]() commands determines the default WEZ parameters. These can be overwritten with the [Optional Commands](#optional-commands).

For example:

    # A medium-range missile of side blue will have the default WEZ parameters, except for the average speed. 
    missile_wez_parameters
       side        blue
       type        mrm
       avg_speed   600 m/s
    end_missile_wez_parameters
:::



Any additional parameters that are explicitly set will override the respective parameters from the predefined set.



::: command
avg_speed \<speed-value\>

The average missile speed that will be used in the WEZ calculations.

Default: 550 m/s
:::

::: command
max_time_of_flight \<time-value\>

The missile\'s maximum time of flight that will be used in the WEZ calculations.

Defaults: dependent on [side]() and [type](). See the table below. See also [Missile WEZ Defaults](#missile-wez-defaults).

        blue          red
  ----- ------------- -------------
  mrm   120 seconds   100 seconds
  srm   20 seconds    18 seconds
:::

::: command
max_off_boresight_angle \<angle-value\>

The maximum angle off the nose from the launching aircraft to the target.

Defaults: dependent on [side]() and [type](). See the table below. See also [Missile WEZ Defaults](#missile-wez-defaults).

        blue          red
  ----- ------------- -------------
  mrm   80 degrees    100 degrees
  srm   120 degrees   132 degrees
:::

::: command
min_time_of_flight \<time-value\>

The missile\'s minimum time of flight that will be used in the WEZ calculations.

Defaults: dependent on [side]() and [type](). See the table below. See also [Missile WEZ Defaults](#missile-wez-defaults).

        blue          red
  ----- ------------- -------------
  mrm   3.5 seconds   4.5 seconds
  srm   1.5 seconds   1.5 seconds
:::



A combination of [nominal_pk](), [head_on_pk_reduction](), and [broadside_pk_reduction]() determines the overall probability of kill. See the equation below:

[$$Pk = Pk_n (1.0 - f_h \frac{\theta}{\pi}) (1.0 - f_b \sin{\theta})$$]{label="pk"}

where:

> -   $Pk$ is the overall probability of kill,
> -   $Pk_n$ is defined by [nominal_pk]()
> -   $f_h$ is defined by [head_on_pk_reduction]()
> -   $f_b$ is defined by [broadside_pk_reduction]()
> -   $\theta$ is the angle off boresight

::: command
nominal_pk \<real\>

The nominal probability of kill used in equation `pk`{.interpreted-text role="eq"} above.

Defaults: dependent on [side]() and [type](). See the table below. See also [Missile WEZ Defaults](#missile-wez-defaults).

        blue   red
  ----- ------ ------
  mrm   0.80   0.75
  srm   0.95   0.90
:::

::: command
head_on_pk_reduction \<real\>

A reduction factor that accounts for head-on effects. This number must be between 0.0 and 1.0. See equation `pk`{.interpreted-text role="eq"} above for usage.

Defaults: dependent on [side]() and [type](). See the table below. See also [Missile WEZ Defaults](#missile-wez-defaults).

        blue   red
  ----- ------ -----
  mrm   0.0    0.3
  srm   0.0    0.3
:::

::: command
broadside_pk_reduction \<real\>

A reduction factor that accounts for broadside effects. This number must be between 0.0 and 1.0. See equation `pk`{.interpreted-text role="eq"} above for usage.

Defaults: dependent on [side]() and [type](). See the table below. See also [Missile WEZ Defaults](#missile-wez-defaults).

        blue   red
  ----- ------ -----
  mrm   0.2    0.3
  srm   0.2    0.2
:::



The default WEZ parameters for each combination of [side]() and [type]() are shown in tables below. If only [side]() and [type]() are specified, all default values will be used. Any other command that is explicitly specified will override the default value for that particular side/type.



  Parameter Name                Default Value
  ----------------------------- ---------------
  [avg_speed]()                 550 m/s
  [max_time_of_flight]()        120 seconds
  [max_off_boresight_angle]()   80 degrees
  [min_time_of_flight]()        3.5 seconds
  [nominal_pk]()                0.80
  [head_on_pk_reduction]()      0.0
  [broadside_pk_reduction]()    0.2



  Parameter Name                Default Value
  ----------------------------- ---------------
  [avg_speed]()                 550 m/s
  [max_time_of_flight]()        20 seconds
  [max_off_boresight_angle]()   120 degrees
  [min_time_of_flight]()        1.5 seconds
  [nominal_pk]()                0.95
  [head_on_pk_reduction]()      0.0
  [broadside_pk_reduction]()    0.2



  Parameter Name                Default Value
  ----------------------------- ---------------
  [avg_speed]()                 550 m/s
  [max_time_of_flight]()        100 seconds
  [max_off_boresight_angle]()   100 degrees
  [min_time_of_flight]()        4.5 seconds
  [nominal_pk]()                0.75
  [head_on_pk_reduction]()      0.3
  [broadside_pk_reduction]()    0.3



  Parameter Name                Default Value
  ----------------------------- ---------------
  [avg_speed]()                 550 m/s
  [max_time_of_flight]()        18 seconds
  [max_off_boresight_angle]()   132 degrees
  [min_time_of_flight]()        1.5 seconds
  [nominal_pk]()                0.90
  [head_on_pk_reduction]()      0.3
  [broadside_pk_reduction]()    0.2