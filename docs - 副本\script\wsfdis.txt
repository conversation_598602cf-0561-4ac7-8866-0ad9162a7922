# wsfdis.rst.txt.md
orphan

:   



::: WsfDIS
WsfDIS provides static access to some of the `dis_interface`{.interpreted-text role="command"} functionality.
:::



::: method
EntityType EntityType(WsfObject aObject) EntityType EntityType(string aTypeName)

Returns the `EntityType`{.interpreted-text role="class"} for the given `WsfObject`{.interpreted-text role="class"} type or type name if it exists in the `dis_interface.entity_type`{.interpreted-text role="command"} mapping command.

Example:

    ...

    EntityType et = WsfDIS.EntityType("AIRCRAFT");
    writeln(" Checking Entity Type by DIS, platform type: " + et.String());

    et = WsfDIS.EntityType(pltfrm);
    writeln(" Checking Entity Type by DIS, object  type:  " + et.String());

    ...
:::