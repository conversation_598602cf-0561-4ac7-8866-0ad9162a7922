# wsfsixdofpitchanglemaneuver.rst.txt.md
orphan

:   





This maneuver sets the target pitch angle of the platform to which it is assigned. This maneuver is done executing as soon as the target is set, so if there is a need to wait for the achieved pitch angle to reach the target value, a `WsfSixDOF_ManeuverConstraint`{.interpreted-text role="class"} must be used.



::: method
static WsfSixDOF_PitchAngleManeuver Construct(double aPitchAngleDeg)

Construct a maneuver that will set a target pitch angle in degrees.
:::

::: method
double GetPitchAngle()

Return this maneuver\'s target pitch angle in degrees.
:::