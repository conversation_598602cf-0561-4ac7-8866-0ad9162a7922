# uci_pointing.rst.txt.md
orphan

:   





This type indicates the position or location for the sensor to point to.



::: method
static UCI_Pointing Construct(UCI_LOS_Reference referenceType, double azimuth, double elevation)

This method creates an instance of an [UCI_Pointing](#uci_pointing) type with the LOS option selected. The azimuth and elevation are the desired values for the sensor to point to.
:::

::: note
::: title
Note
:::

The azimuth and elevation are expected to be in radians.
:::



::: method
void SetLOSAzimuth(double desiredAzimuth)

This method sets the azimuth of the [UCI_Pointing](#uci_pointing) object.
:::

::: note
::: title
Note
:::

The azimuth is expected to be in radians.
:::

::: method
void SetLOSElevation(double desiredElevation)

This method sets the elevation of the [UCI_Pointing](#uci_pointing) object.
:::

::: note
::: title
Note
:::

The elevation is expected to be in radians.
:::