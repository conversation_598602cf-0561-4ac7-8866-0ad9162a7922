orphan

:   

# WsfSpeedMachManeuver

## Overview {#overview .WsfSpeedMachManeuver .inherits .WsfManeuver}

This maneuver sets a target speed on the platform to which the maneuver is assigned. This maneuver uses mach number as its measure of speed. This maneuver is done executing as soon as the target speed is set, so if there is a need to wait for the achieved speed, a `WsfManeuverConstraint`{.interpreted-text role="class"} must be used.

## Methods

::: method
WsfSpeedMachManeuver Construct(double aMachNumber)

Construct a maneuver that will set a target mach number for the platform to which the maneuver is assigned.
:::

::: method
double GetMach()

Return this maneuver\'s target mach number.
:::
