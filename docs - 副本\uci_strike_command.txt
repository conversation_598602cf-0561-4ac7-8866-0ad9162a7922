# uci_strike_command.rst.txt.md
orphan

:   





This type holds the information given by a strike command.



::: method
static UCI_StrikeCommand Construct(UCI_StrikeCapabilityCommand aCapabilityCommand)

Returns a \_UCI_StrikeCommand with the given capability command.
:::

::: method
static UCI_StrikeCommand Construct(UCI_StrikeActivityCommand aActivityCommand)

Returns a \_UCI_StrikeCommand with the given activity command.
:::