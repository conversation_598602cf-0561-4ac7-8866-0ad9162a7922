# wsfpiecewiseexponentialatmosphere.rst.txt.md
orphan

:   





The `WsfPiecewiseExponentialAtmosphere`{.interpreted-text role="class"} models the atmospheric density using a series of exponential profiles for different altitude ranges. In each range the profile takes the form:

$\rho(h) = \rho_0 \exp{\left[-\frac{h - h_0}{H}\right]}$,

where $H$ is a scale height, $h_0$ is the altitude at which the segment begins, and $\rho_0$ is the density of the model at that altitude. The values of these constants can be found in Vallado\'s *Fundamentals of Astrodynamics and Application*, Fourth Edition, in Table 8-4 on page 567. The values are chosen so that the resulting density is continuous at the segment boundaries.



::: method
WsfPiecewiseExponentialAtmosphere Construct()

Construct an atmosphere model with a piecewise exponential density profile.
:::