orphan

:   

# Type Objects and Instance Objects

In order to facilitate rapid scenario development it is useful to create type objects. A type object is a simulation object (e.g., platform, sensor, mover) that has been populated with the desired attributes and registered as a type object in the simulation framework. The type object can then be used as a source to create instance objects that can then participate directly in the simulation. Note that type objects do not participate in the simulation. They only serve as a \"template\' from which to create concrete objects. The concept of type objects is very powerful. Repositories of types can be created and stored in files which can be included as needed (using the include or include_once commands). Scenarios development can be reduced to simply creating instances of predefined types and defining their initial state.
