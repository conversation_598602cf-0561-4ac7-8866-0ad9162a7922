# wsfsa_perceiveditem.rst.txt.md
orphan

:   





The `WsfSA_PerceivedItem`{.interpreted-text role="class"} provides a representation of a perceived item, which may be an entity perception or an unfocused group. Perceived items are key to the situation awareness component of the cognitive model, provided by the `WsfSA_Processor`{.interpreted-text role="class"}.

Perceived items are used within the \"perception\" and \"assessment\" modules of the `WsfSA_Processor`{.interpreted-text role="class"} (SAP). The SAP uses these perceived items to form various lists.

The first is an unsorted list of all perceived threat items. This list is not prioritized, although they may be limited due to range and/or scoring parameters when limited perception is enabled.

The next group of lists provide an assessment of the prioritized threats and targets, which are composed of bandits and bogies. These lists are prioritized, from highest threat to lowest threat and from the best target to the least attractive target. Threats are perceived items that present an adverse condition (a threat) to the platform, while targets are perceived items that the platform may wish to attack.

See `WsfSA_EntityPerception`{.interpreted-text role="class"} and `WsfSA_Group`{.interpreted-text role="class"} for lists of entities or groups in particular.



::: method
int ItemType()

Returns the type of the perceived item. The item can be explicitly casted to its respective type.
:::

  Value   Identification
  ------- ----------------------------------------------------------
  0       `WsfSA_EntityPerception`{.interpreted-text role="class"}
  1       `WsfSA_Group`{.interpreted-text role="class"}

::: method
double Lat()

Returns the latitude (deg) of the perceived item.
:::

::: method
double Lon()

Returns the longitude (deg) of the perceived item.
:::

::: method
double Altitude()

Returns the altitude (meters) of the perceived item.
:::

::: method
double Bearing()

Returns the bearing (deg) of the perceived item.
:::

::: method
double Speed()

Returns the speed (meters/sec) of the perceived item.
:::

::: method
double Heading()

Returns the heading (deg) of the perceived item.
:::

::: method
double ThreatLevel()

Returns the threat level of the perceived item. This is a normalized value between 0.0 and 1.0.
:::

::: method
double TargetValue()

Returns the target value of the perceived item. This is a normalized value between 0.0 and 1.0.
:::

::: method
string PerceivedName()

Returns the name of the perceived item. For entities, this is the perceived platform name. For groups, this is the group name.
:::

::: method
int Identification()

Returns the identification of the perceived item.
:::

  Value   Identification
  ------- ----------------
  0       <USER>
  <GROUP>       Friendly
  2       Neutral
  3       Bogie
  4       Bandit

::: method
bool AngleOnly()

Returns true if the perceived item is defined by an angle-only track. This will always be false for groups.
:::

::: method
bool AltitudeValid()

Returns true if the perceived item altitude is valid.
:::

::: method
bool SpeedValid()

Returns true if the perceived item speed is valid.
:::

::: method
bool HeadingValid()

Returns true if the perceived item heading is valid.
:::

::: method
double SelectionScore()

Returns the current selection score of the perceived item.
:::

::: method
double EstimatedTimeToIntercept(WsfPlatform aTarget)

Returns the estimated time for this perceived item to intercept the specified target platform. If no intercept is possible, -1.0 is returned. If the perceived item is angle-only, or if it lacks speed data, no intercept will be calculated and -1.0 will be returned.
:::