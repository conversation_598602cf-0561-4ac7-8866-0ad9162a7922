# sensor_plot_vertical_map.rst.txt.md
orphan

:   



Navigation: `sensor_plot`{.interpreted-text role="command"} \> vertical\_\_map

::: {.command block=""}
vertical_map \... end_vertical_map

::: parsed-literal
[vertical_map]()

> `sensor_plot.Stub_Definition_Commands`{.interpreted-text role="ref"}
>
> \# [Sensor Control](#sensor-control)
>
> [mode_name]() \...
>
> \# [Jammer Control](#jammer-control)
>
> [jammer_to_signal_reference]() \... [jamming_platform_name]() \...
>
> [target_platform_type]() [target_speed]() \| [target_mach]() [target_heading]() [target_yaw]() [target_pitch]() [target_roll]()
>
> \# [Target Region](#target-region)
>
> [ground_range_limits]() \... [ground_range_step]() \... [altitude_limits]() \... [altitude_step]() \...
>
> \# [Output Selection](#output-selection)
>
> [variable]() \... [script_variable]() \... [detection_reference]() \...
>
> [pd_map_file]() \... [header_line_1]() \... [header_line_2]() \... [header_line_3]() \... [output_column_limit]() \...
>
> [gnuplot_file]() \... [gnuplot_player_file]() \...

end_vertical_map
:::
:::



The vertical_map command is used to generate a plot file of target detectability at the intersection points of a vertical rectangular mesh. Two types of plot files can be produced:

-   A \'pd map\' file for the program \'plotview\' to show a typical ground range/altitude plot.
-   A file suitable for plotting with the program \'gnuplot\'.

To create a file, the following process should be followed:

-   Define a platform type of the type specified by the command [target_platform_type]() (Default: TARGET_PLATFORM_TYPE) with the desired `radar <radar_signature>`{.interpreted-text role="command"}, `infrared <infrared_signature>`{.interpreted-text role="command"}, `optical <optical_signature>`{.interpreted-text role="command"} or `acoustic <acoustic_signature>`{.interpreted-text role="command"} signature depending on the type(s) of sensors being tested.
-   Define a platform type of SENSOR_PLATFORM_TYPE, which contains the sensor to be used to detect the target.
-   Define the vertical_map input block with:
    -   [ground_range_limits]() and [altitude_limits]() (and optionally [ground_range_step]() and [altitude_step]() commands to define the mesh of sample points.)
    -   An optional [target_speed]() command to specify the target speed.
    -   Output selection commands.



::: command
mode_name \<mode_name\>

Specifies the name of the mode to be used if the sensor is a multi-mode sensor.

Default The default mode of the sensor. This will be the value of the `sensor.initial_mode`{.interpreted-text role="command"} command of the sensor (if defined) or the first mode (if `sensor.initial_mode`{.interpreted-text role="command"} was not defined).
:::



::: command
jammer_to_signal_reference \<db-ratio-value\>

Specifies the jammer-to-signal (J/S) reference to be used when plotting the \'required_jamming_power\' variable.

Default 0.0 db
:::

::: command
jamming_platform_name \<platform-name\>

Specifies the platform that will be used to calculate the required jamming power. This input is to be used when plotting the \'required_jamming_power\' variable for the location of the jammer system.

Default TARGET_PLATFORM_TYPE
:::



::: command
ground_range_limits \<min-length-value\> \<max-length-value\>

Specifies the ground range limits of the sample mesh.

Default none. This must be provided.
:::

::: command
ground_range_step \<step-length-value\>

Specifies the increment between sample points in the ground range direction.

Default 1.0 nm
:::

::: command
altitude_limits \<min-length-value\> max-length-value\>

Specifies the altitude limits of the sample mesh.

Default none. This must be provided.
:::

::: command
altitude_step \<step-length-value\>

Specifies the increment between sample points in the altitude direction.

Default 0.25 nm
:::



::: command
detection_reference \<db-ratio-value\>

The reference signal-to-noise ratio used to determine the required_rcs or rcs_required [variable]().

Default 12.8 dB
:::

::: command
pd_map_file \<file-name\>

Specifies the name of the file to which \'pd map\' output will be written. The name does not imply that only pd can be written but rather denotes a common file format.

Default \'pd map\' output will not be produced.
:::

::: command
header_line_1 \<text\>
:::

::: command
header_line_2 \<text\>
:::

::: command
header_line_3 \<text\>

Specifies the text to be contained in the first three lines of the output file when [pd_map_file]() is specified.

Default all header lines are blank.
:::

::: command
output_column_limit \<integer\>

Specifies the maximum number of columns per physical line in the output file when [pd_map_file]() is specified.

Default 100

::: note
::: title
Note
:::

If the file is to be imported into a spreadsheet such as Microsoft Excel, this value should be set so that the rows do not have to be split into multiple physical lines.
:::
:::

::: command
gnuplot_file \<file-name\>

Specifies the name of the file to which \'gnuplot\' output will be written.

Default \'gnuplot\' output will not be produced.
:::

::: command
gnuplot_player_file \<file-name\>

Specifies the name of the file to which player\'s Down range/Cross range or Latitude/Longitude will be written. Will not output target locations.

Default \'gnuplot\' player location output will not be produced.
:::