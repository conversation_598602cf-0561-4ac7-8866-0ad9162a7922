orphan

:   

The Wizard Platform Browser context menu also has options for displaying the type info for the platform. Selecting this option will show the file and line number where the platform is defined, as well as its types and the locations of their definitions in the file. Platform components may be added and edited with the \'Manage Platform Parts\...\' option in the platform context menu. This will open the `Platform Part Manager <wiz_platform_part_manager>`{.interpreted-text role="doc"}. Each platform item in the Platform Browser may by expanded to show its components. Double-clicking on a platform or platform-component will open the `Text Editor<../wizard_text_editor>`{.interpreted-text role="doc"} and navigate to the appropriate definition.

The number of platforms in the scenario is shown in parentheses in the platform browser title bar. The browser contains a search field at the top. Typing in this field will filter to show platforms or platform components with names containing the entered text.
