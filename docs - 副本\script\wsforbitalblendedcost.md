orphan

:   

# WsfOrbitalBlendedCost

::: {.WsfOrbitalBlendedCost .inherits .WsfOrbitalOptimizationCost}
`WsfOrbitalBlendedCost`{.interpreted-text role="class"} represents an orbital targeting optimization cost function that is a polynomial that blends together the elapsed time, $\Delta t$, and the total change in speed, $\Delta V$. The value of the cost function is set by three constants, $A$, $B$, and $C$ as follows:
:::

$g(\Delta t, \Delta V) = A \Delta t + B \Delta V + C \Delta t \Delta V$.

This cost function is more general than either least time, or least delta-V optimization, but it contains both of those as special cases.

## Static Methods

::: method
static WsfOrbitalBlendedCost Construct(double aA_Value, double aB_Value, double aC_Value)

Construct a new `WsfOrbitalBlendedCost`{.interpreted-text role="class"} instance with the function\'s coefficients set to **aA_Value**, **aB_Value**, and **aC_Value**.
:::

::: method
static WsfOrbitalBlendedCost ConstructLeastTime()

Construct a new `WsfOrbitalBlendedCost`{.interpreted-text role="class"} instance with the coefficients set to reproduce least-time optimization ($A = 1, B = C = 0$).
:::

::: method
static WsfOrbitalBlendedCost ConstructLeastDeltaV()

Construct a new `WsfOrbitalBlendedCost`{.interpreted-text role="class"} instance with the coefficients set to reproduce least-delta V optimization ($B = 1, A = C = 0$).
:::

## Methods

::: method
double A_Value()

Return the value of the $A$ coefficient for this instance.
:::

::: method
double B_Value()

Return the value of the $B$ coefficient for this instance.
:::

::: method
double C_Value()

Return the value of the $C$ coefficient for this instance.
:::

::: method
void SetA_Value(double aA_Value)

Set the $A$ coefficient for this instance to the provided **aA_Value**.
:::

::: method
void SetB_Value(double aB_Value)

Set the $B$ coefficient for this instance to the provided **aB_Value**.
:::

::: method
void SetC_Value(double aC_Value)

Set the $C$ coefficient for this instance to the provided **aC_Value**.
:::
