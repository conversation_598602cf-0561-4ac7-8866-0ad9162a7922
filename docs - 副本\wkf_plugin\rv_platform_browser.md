orphan

:   

The Platform Browser title bar shows in parentheses the number of platforms that currently exists in the simulation followed by the total number of platforms that the scenario contains. The browser, by default, will only show active platforms. From the preferences, this may be changed to also show inactive (dead, removed, or not yet initialized) platforms.

The filter at the top of the browser can be used to only show platforms of a specific name, type, or team. Items in the browser are color-coded by team.

# Preferences

![image](../images/rv_platform_browser_prefs.png)

-   Show Inactive Entities - If checked, inactive entities (those not yet created, or those destroyed) will show faded in the browser, otherwise they will be hidden.
