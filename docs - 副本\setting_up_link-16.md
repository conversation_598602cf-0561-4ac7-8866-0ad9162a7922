orphan

:   

# Setting up Link-16 {#Setting_up_Link-16}

The following components are required for setting up Link-16 over DIS:

-   `link16_interface`{.interpreted-text role="command"} - Configures the Link-16 interface. The absence of this command will disable the Link 16 interface.
-   `WSF_LINK16_COMPUTER`{.interpreted-text role="model"} - A processor that handles sending and receiving Link16 - Tadil-J messages. Tadil-J messages can be sent to another platform in the simulation, or to another application over DIS. By default, this processor only receives messages. To send messages, message processors must be added to the WSF_LINK16_COMPUTER. To be functional, the Link16 Computer must be used in conjunction with a WSF_JTIDS_TERMINAL or other comm device operating as part of a network.
-   `WSF_JTIDS_TERMINAL`{.interpreted-text role="model"} - The WSF implementation of a JTIDS terminal. This implementation models the transport of data over a JTIDS network. It does not model either the message content or the pulse-level characteristics. Note: Any WSF comm device may be used to model the transport of the data.
