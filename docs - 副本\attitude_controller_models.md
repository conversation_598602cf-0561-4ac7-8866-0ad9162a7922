orphan

:   

::: {#attitude_controller_models}
:::

# Attitude Controller Types

## Instant Attitude Controller {#attitude_controller_models.instant}

::: parsed-literal

[attitude_controller]() instant

:   [orientation]() \<orientation_type\> \...

end_attitude_controller
:::

Specify an instant attitude controller. This will reorient the mover immediately upon having the orientation set.

::: note
::: title
Note
:::

The instant attitude controller is used by default.
:::

## Rate Limited Attitude Controller {#attitude_controller_models.rate_limited}

::: parsed-literal

[attitude_controller]() rate_limited

:   [maximum_yaw_rate]() \... [maximum_pitch_rate]() \... [maximum_roll_rate]() \... [orientation]() \<orientation_type\> \...

end_attitude_controller
:::

Specify a rate limited attitude controller. This will reorient the mover to the target orientation at a maximum angular rate as specified by [maximum_yaw_rate](), [maximum_pitch_rate]() and [maximum_roll_rate]().

::: command
maximum_yaw_rate \<angle-rate-value\>

Specify the maximum time rate of change for reorientations in the platform\'s yaw.

**Default** 1 degree/s
:::

::: command
maximum_pitch_rate \<angle-rate-value\>

Specify the maximum time rate of change for reorientations in the platform\'s pitch.

**Default** 1 degree/s
:::

::: command
maximum_roll_rate \<angle-rate-value\>

Specify the maximum time rate of change for reorientations in the platform\'s roll.

**Default** 1 degree/s
:::

# Orientation Types
