# wsftracknotifymessage.rst.txt.md
orphan

:   



::: {.WsfTrackNotifyMessage .inherits .WsfMessage}
WsfTrackNotifyMessage is communicated from a `WsfTrackProcessor`{.interpreted-text role="class"} to `_.platform_part.internal_link`{.interpreted-text role="command"} on the same platform, informing them of tracking events of interest (see `WsfTrackNotifyMessage.ChangedReason`{.interpreted-text role="method"}, below). The message contains enough information to retrieve the track and take action based on the new information in it.
:::



::: method
int ChangedReason()

Return the reason for the track status change. The integer corresponds to the track status indication as defined below:

-   0 = No change or invalid code
-   1 = Local track created
-   2 = Local track updated
-   3 = Local track dropped
-   4 = Candidate local track promoted to full track
-   5 = Raw track dropped
-   6 = Unprocessed raw track received
-   7 = Current target has changed
:::

::: method
WsfTrackId TrackId()

Return the track Id associated with the message.
:::

::: method
string TrackProcessorName()

Return the name of the `track processor <WsfTrackProcessor>`{.interpreted-text role="class"} that sent the message.
:::