orphan

:   

# WsfWeaponFuse

## Methods {#methods .WsfWeaponFuse .inherits .WsfProcessor}

::: method
void Detonate()

Command the weapon fuse to detonate.
:::

::: method
double MaximumTimeOfFlight()

Returns the maximum time of flight as defined by `WSF_WEAPON_FUSE.maximum_time_of_flight`{.interpreted-text role="command"}
:::

::: method
double GetDetonateOnMachDecreasingTo()

Returns the mach number that triggers detonation as defined by `WSF_WEAPON_FUSE.detonate_on_mach_decreasing_to`{.interpreted-text role="command"}.

::: note
::: title
Note
:::

If `WSF_WEAPON_FUSE.detonate_on_mach_decreasing_to`{.interpreted-text role="command"} isn\'t defined, this method will return 0.
:::
:::

::: method
double GetDetonateBelowMach()

Returns the mach number that triggers detonation as defined by `WSF_WEAPON_FUSE.detonate_below_mach`{.interpreted-text role="command"}.

::: note
::: title
Note
:::

If `WSF_WEAPON_FUSE.detonate_below_mach`{.interpreted-text role="command"} isn\'t defined, this method will return 0.
:::
:::

::: method
double GetDetonateBelowSpeed()

Returns the speed (in m/s) that triggers detonation as defined by `WSF_WEAPON_FUSE.detonate_below_speed`{.interpreted-text role="command"}

::: note
::: title
Note
:::

If `WSF_WEAPON_FUSE.detonate_below_speed`{.interpreted-text role="command"} isn\'t defined, this method will return 0.
:::
:::

::: method
double GetDetonateBelowHeightAGL()

Returns the minimum height that triggers detonation as defined by `WSF_WEAPON_FUSE.detonate_below_height_agl`{.interpreted-text role="command"}

::: note
::: title
Note
:::

If `WSF_WEAPON_FUSE.detonate_below_height_agl`{.interpreted-text role="command"} isn\'t defined, this method will return 0.
:::
:::

::: method
double GetDetonateBelowHeightMSL()

Returns the minimum height that triggers detonation as defined by `WSF_WEAPON_FUSE.detonate_below_height_msl`{.interpreted-text role="command"}

::: note
::: title
Note
:::

If `WSF_WEAPON_FUSE.detonate_below_height_msl`{.interpreted-text role="command"} isn\'t defined, this method will return 0.
:::
:::

::: method
double GetDetonateAboveHeightAGL()

Returns the maximum height that triggers detonation as defined by `WSF_WEAPON_FUSE.detonate_above_height_agl`{.interpreted-text role="command"}

::: note
::: title
Note
:::

If `WSF_WEAPON_FUSE.detonate_above_height_agl`{.interpreted-text role="command"} isn\'t defined, this method will return 0.
:::
:::

::: method
double GetDetonateAboveHeightMSL()

Returns the maximum height that triggers detonation as defined by `WSF_WEAPON_FUSE.detonate_above_height_msl`{.interpreted-text role="command"}

::: note
::: title
Note
:::

If `WSF_WEAPON_FUSE.detonate_above_height_msl`{.interpreted-text role="command"} isn\'t defined, this method will return 0.
:::
:::

::: method
double TimeOfFlightToArm()

Returns the time of flight necessary to allow for detonation to occur as defined by `WSF_WEAPON_FUSE.time_of_flight_to_arm`{.interpreted-text role="command"}
:::
