# wsfxio_connection.rst.txt.md
orphan

:   



::: WsfXIO_Connection
WsfXIO_Connection provides methods that operate on an XIO connection. WsfXIO_Connection objects cannot be constructed but are accessed using the `WsfXIO`{.interpreted-text role="class"} script class.
:::



::: method
void Execute(string aFunctionName, Array\<Object\> aParams)

Executes a function local to the connected application. aFunctionName should be the name of a global script function. The connected application should execute the function with the given arguments. User must take care to supply the correct number and type of arguments for the method. Arguments are only supported for a limited set of types including string, int, double, `Array\<T\>`{.interpreted-text role="class"}, `WsfTrackId`{.interpreted-text role="class"}, `WsfTrack`{.interpreted-text role="class"}, `WsfLocalTrack`{.interpreted-text role="class"}, and `WsfGeoPoint`{.interpreted-text role="class"}.
:::

::: method
string Name()

Returns the name of the connected application. This is the value specified with the `xio_interface.application`{.interpreted-text role="command"} command.
:::

::: method
int Index()

Returns the index of the connection.
:::