orphan

:   

# csv_event_output

::: {.contents local=""}
:::

::: {.command block=""}
csv_event_output \... end_csv_event_output
:::

::: parsed-literal

csv_event_output

:   [file]() \[ \| STDOUT \| NULL\] [flush_output]() [insert_data_tags]() [disable]() \[ \<event\> \| all \] [enable]() \[ \<event\> \| all \]

end_csv_event_output
:::

Since csv_event_output is comma-separated values, it is useful for importing into tables or spreadsheets, as well as use in post-processing tools for data analysis.

## General Format

+--------------------+-----------------------------------------------------------------------+
| One-party event:   | ::: parsed-literal                                                    |
|                    | \<time\>,\<event\>,\<party-1\>,\<extra-data\>                         |
|                    | :::                                                                   |
+--------------------+-----------------------------------------------------------------------+
| Two-party event:   | ::: parsed-literal                                                    |
|                    | \<time\>,\<event\>,\<party-1\>,\<party-2\>,\<extra-data\>             |
|                    | :::                                                                   |
+--------------------+-----------------------------------------------------------------------+
| Three-party event: | ::: parsed-literal                                                    |
|                    | \<time\>,\<event\>,\<party-1\>,\<party-2\>,\<party-3\>,\<extra-data\> |
|                    | :::                                                                   |
+--------------------+-----------------------------------------------------------------------+

## Event Index

::: {.toctree maxdepth="1" glob=""}
event/\*
:::

::: command
insert_data_tags \<boolean\>

Specifies if the CSV header is written. This describes the format of each event in the log.

::: note
::: title
Note
:::

This is used by the post-processing tools to parse the event log.
:::

**Default** true
:::

    0,PLATFORM_ADDED,SimpleStriker,blue,BLUE_STRIKER,,0,0,0,2.23167444e+06,5.97496947e+06,-7.95753188e+02,1.571,0.000,0.000,0.000,0.000,0.000,0.000,465.101,-0.937,0.350,0.000,0.000,0.000,0.000,0.000,0.034,-0.350,-0.937,0.000
    0.000000e+00,SIMULATION_STARTING,2003,6,1,12,0,0.000000e+00
    6.010010e+02,SIMULATION_COMPLETE,2003,6,1,12,10,1.000000e+00
    6.010010e+02,PLATFORM_DELETED,SimpleStriker,blue,BLUE_STRIKER,1.000000e+00,1.050603e+00,2.661749e+00,9.14400000e+03,1.68605423e+06,6.15963498e+06,1.15696178e+05,1.571,0.000,0.000,298.378,0.000,1.000,-0.000,764.068,-0.965,0.264,0.000,0.014,0.000,0.000,1.000,0.091,-0.264,-0.965,-0.003
