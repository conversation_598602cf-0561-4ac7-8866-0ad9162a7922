# wsfscripteddynamicsterm.rst.txt.md
orphan

:   





The `WsfScriptedDynamicsTerm`{.interpreted-text role="class"} represents a user-scriptable dynamics term to implement dynamics that are not build into AFSIM. See also `Scripted Term <orbital_dynamics_terms.scripted>`{.interpreted-text role="ref"}.



::: method
WsfScriptedDynamicsTerm Construct(string aScriptName)

Construct a scripted dynamics term that uses the script with the given name to compute the acceleration. The named script must return a `Vec3`{.interpreted-text role="class"} and accept the following arguments in order: a `WsfIntegratingSpaceMover`{.interpreted-text role="class"} giving the mover that this term is affecting, a double giving the mass of the platform at the time of the script execution, a `Calendar`{.interpreted-text role="class"} giving the time at which the dynamical term is being computed, a `Vec3`{.interpreted-text role="class"} giving the position of the platform, and a `Vec3`{.interpreted-text role="class"} giving the velocity of the platform.
:::



::: method
string ScriptName()

Return the name of the script that computes the acceleration for this term.
:::

::: method
Vec3 ComputeAcceleration(double aMass, Calendar aTime, Vec3 aPosition, Vec3 aVelocity)

For the `WsfScriptedDynamicsTerm`{.interpreted-text role="class"} this method will return the zero vector; the acceleration provided by this term can only be computed when the dynamics are fully initialized and are in use by a `WsfIntegratingSpaceMover`{.interpreted-text role="class"}.
:::