# electronic_protect.rst.txt.md
orphan

:   



::: {.command block=""}
electronic_protect
:::

::: parsed-literal

electronic_protect

:   \... `Electronic Warfare Commands <electronic_warfare.commands>`{.interpreted-text role="ref"} \... \... `Electronic Protect Commands <WSF_ELECTRONIC_PROTECT>`{.interpreted-text role="model"} \... \... `Electronic Protect Technique Commands <WSF_EP_TECHNIQUE>`{.interpreted-text role="model"} \...

end_electronic_protect
:::

\<type-name\>

:   Name of an existing Electronic Protect type or `WSF_ELECTRONIC_PROTECT`{.interpreted-text role="model"}, whose definition will be used as the initial definition of the new instance.



electronic_protect defines an electronic protect (counter-countermeasure) capability for a receiver. An `electronic attack (EA) <electronic_attack>`{.interpreted-text role="command"} - `electronic protect (EP) <electronic_protect>`{.interpreted-text role="command"} architecture provides the ability to define EA and EP techniques and assess the interaction of these techniques. Multiple techniques and effects are allowed for both EA and EP capability blocks. EP techniques defined in the receiver block mitigate EA techniques defined in transmitter blocks. Mitigating behavior for false targets is also accomplished with the `false_target_screener`{.interpreted-text role="command"}.



None currently defined