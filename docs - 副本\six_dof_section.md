orphan

:   

# Section Formation

::: command
six_dof_section
:::

::: parsed-literal

six_dof_section \<name\>

:   debug detached offset \... \[lead\] six_dof_unit \... end_six_dof_unit

end_six_dof_section
:::

## Overview

A section is a formation that is formed from two unit sub-formations. Sections cannot be defined at the root level in input files. Sections can also be defined in script, see `WsfSixDOF_Formation`{.interpreted-text role="class"} and `WsfSixDOF_FormationManager`{.interpreted-text role="class"}. Sections can be given commands, see `WsfSixDOF_FormationCommand`{.interpreted-text role="class"}.

::: {#Section_Formation.SixDOF_Formation_Commands}
:::

## Sub-formation Commands

::: command
six_dof_unit \... end_six_dof_unit

Define a unit sub-formation. See `six_dof_unit`{.interpreted-text role="doc"}.
:::

::: command
lead

Specify that the following unit sub-formation is to be the lead sub-formation of this section. Sections defined in input files must have one of their sub-formations designated as the lead sub-formation.
:::
