orphan

:   

# Mystic

# Overview

Mystic (renamed from Results Visualization as of release 2.7.0) is a tool that is used to visualize the recorded results of an AFSIM simulation `event_pipe`{.interpreted-text role="command"}. It reads in AFSIM event recordings (AER) files generated by the simulation and displays them in a geographic context. It can be launched from Wizard by double clicking on a generated AER file, or as a standalone application.

-   `User's Guide <mystic_users_guide>`{.interpreted-text role="doc"}
-   `Reference Guide <mystic_reference_guide>`{.interpreted-text role="doc"}
-   `Change Log <mystic_change_log>`{.interpreted-text role="doc"}
