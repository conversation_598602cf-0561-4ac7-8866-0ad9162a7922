orphan

:   

# WsfNetworkStar

::: {.WsfNetworkStar .inherits .WsfNetwork}
::: warning
::: title
Warning
:::

The comm framework does not initialize until all platforms have initialized in the simulation. Most comm script methods are invalid during `on_initialize`{.interpreted-text role="command"} and `on_initialize2`{.interpreted-text role="command"}.
:::
:::

## Methods

::: method
bool AddHub(WsfAddress aMemberAddress) bool AddHub(string aPlatformName, string aCommName)

Adds the specified comm as a member of the star network. Additionally, specifies this member is the hub of the network and creates all linkage between the hub and other comm members.

Returns true if adding the hub succeeded, false otherwise.
:::

::: method
bool RemoveHub(WsfAddress aMemberAddress) bool RemoveHub(string aPlatformName, string aCommName)

Removes the specified hub member from the star network, and removes all existing linkage in the star network.

Returns true if removing the hub succeeded, false otherwise.
:::
