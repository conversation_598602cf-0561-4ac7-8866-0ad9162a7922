# wsforbitalmaneuver.rst.txt.md
orphan

:   



::: {.WsfOrbitalManeuver .inherits .WsfOrbitalEvent}
`WsfOrbitalManeuvers<WsfOrbitalManeuver>`{.interpreted-text role="class"} are specific `orbital events<WsfOrbitalEvent>`{.interpreted-text role="class"}, utilized by a `WsfSpaceMover`{.interpreted-text role="class"} to change the orbit of a platform, either directly, using `WsfSpaceMover.ExecuteEvent`{.interpreted-text role="method"}, or as part of a `mission sequence<WsfOrbitalMissionSequence>`{.interpreted-text role="class"} , using `WsfSpaceMover.ExecuteMissionSequence`{.interpreted-text role="method"}. Each `script orbital maneuver<available_orbital_maneuvers>`{.interpreted-text role="ref"} type is meant to be configured with a `WsfOrbitalEventCondition`{.interpreted-text role="class"} object, passed in the object\'s *Construct* method.
:::

