# event_pipe.rst.txt.md
orphan

:   



::: {.contents local="" depth="1"}
:::

::: {.command block=""}
event_pipe \... end_event_pipe

::: parsed-literal

[event_pipe](#event_pipe)

:   [file]() \<file-name\> [use_preset]() \[ default \| low \| high \| full \] [disable]() \<group-name\> [enable]() \<group-name\> [maximum_mover_update_interval]() \<time-value\> [entity_state_angle_threshold]() \<angle-value\> [entity_state_position_threshold]() \<length-value\> [disable_entity_state_thresholds]() [entity_state_maximum_interval]() \<time-value\> [visual_part_update_interval]() \<time-value\>

end_event_pipe
:::
:::





::: command
file \<file-name\>

Specifies the name of the file to which the event recording is written. If the file already exists then it will be overwritten.

::: note
::: title
Note
:::

To insert the run number in the file name use \"*%d*\".
:::

For example:

    file events_%d.aer
:::

::: command
use_preset \[ default \| low \| high \| full \]

Configures the messages that will be output. By default, the default preset is used. The messages output presets are as follows:

default & low:

-   BASE_DATA
-   ENTITY_STATE
-   DRAW
-   DETECTION_CHANGE
-   COMMENT
-   TRACK
-   XIO
-   AUX_DATA
-   ROUTE_CHANGED
-   ZONES

high:

-   BASE_DATA
-   ENTITY_STATE
-   DRAW
-   DETECTION_CHANGE
-   COMMENT
-   TRACK
-   XIO
-   AUX_DATA
-   ROUTE_CHANGED
-   TRACK_UPDATE
-   COMM_INFO
-   MESSAGE_RECEIVED
-   MESSAGE_TRANSMITTED
-   MESSAGE_HOP
-   BEHAVIOR_TREE

full:

-   BASE_DATA
-   ENTITY_STATE
-   DRAW
-   DETECTION_CHANGE
-   COMMENT
-   TRACK
-   XIO
-   AUX_DATA
-   ROUTE_CHANGED
-   TRACK_UPDATE
-   COMM_INFO
-   MESSAGE_RECEIVED
-   MESSAGE_TRANSMITTED
-   MESSAGE_HOP
-   BEHAVIOR_TREE
-   DETECTION_ATTEMPT
:::

::: command
disable \<group-name\>
:::

::: command
enable \<group-name\>

Specifies the names of the event groups to be included or excluded in the file. These commands are typically specified multiple times to select the events of interest. The commands are processed in order of appearance with each successive command selecting or deselecting events as appropriate.

The groups are listed below with the events associated with them.

BASE_DATA

-   [MsgPlatformInfo](#msgplatforminfo)
-   [MsgPlatformStatus](#msgplatformstatus)
-   [MsgPartStatus](#msgpartstatus)
-   [MsgSensorModeChange](#msgsensormodechange)
-   [MsgSensorModeDefinition](#msgsensormodedefinition)
-   [MsgSetDate](#msgsetdate)
-   [MsgVisualPartDefinition](#msgvisualpartdefinition)
-   [MsgExecData](#msgexecdata)
-   [MsgScriptData](#msgscriptdata)
-   [MsgResource](#msgresource)

ENTITY_STATE

-   [MsgEntityState](#msgentitystate)
-   [MsgPartArticulationStatus](#msgpartarticulationstatus)

DETECTION_CHANGE

-   [MsgSensorDetectionChange](#msgsensordetectionchange)
-   [MsgPlatformAppearanceChanged](#msgplatformappearancechanged)

TRACK

-   [MsgLocalTrackCreated](#msglocaltrackcreated)
-   [MsgLocalTrackDrop](#msglocaltrackdrop)
-   [MsgLocalTrackCorrelation](#msglocaltrackcorrelation)
-   [MsgLocalTrackDecorrelation](#msglocaltrackdecorrelation)
-   [MsgSensorTrackCreated](#msgsensortrackcreated)
-   [MsgSensorTrackDrop](#msgsensortrackdrop)

TRACK_UPDATE

-   [MsgLocalTrackUpdate](#msglocaltrackupdate)
-   [MsgSensorTrackUpdate](#msgsensortrackupdate)

DETECTION_ATTEMPT

-   [MsgDetectAttempt](#msgdetectattempt)

DRAW

-   [MsgDrawCommand](#msgdrawcommand)

MESSAGE_RECEIVED

-   [MsgMessageReceived](#msgmessagereceived)

MESSAGE_TRANSMITTED

-   [MsgMessageTransmitted](#msgmessagetransmitted)

MESSAGE_HOP

-   [MsgMessageHop](#msgmessagehop)

COMM_INFO

-   [MsgCommInfo](#msgcomminfo)
-   [MsgRouterInfo](#msgrouterinfo)
-   [MsgNetworkInfo](#msgnetworkinfo)

COMMENT

-   [MsgComment](#msgcomment)

XIO

-   [MsgXioHeartbeat](#msgxioheartbeat)

AUX_DATA

-   [MsgAuxData](#msgauxdata)

ROUTE_CHANGED

-   [MsgRouteChanged](#msgroutechanged)

ZONES

-   [MsgCircularZone](#msgcircularzone)
-   [MsgEllipticalZone](#msgellipticalzone)
-   [MsgPolygonalZone](#msgpolygonalzone)
-   [MsgZoneSet](#msgzoneset)

BEHAVIOR_TREE

-   [MsgBehaviorTree](#msgbehaviortree)
-   [MsgBehaviorTreeState](#msgbehaviortreestate)
:::

::: command
maximum_mover_update_interval \<time-value\>

If this value is less than a mover\'s `mover.update_interval`{.interpreted-text role="command"}, the mover will use this value instead. If this value is set to 0, the event_pipe will take no active role in mover update timing, which may cause odd behavior in movers with no update-interval.

Default: 5 seconds
:::

::: command
entity_state_angle_threshold \<angle-value\>

Specifies an angle threshold to control entity-state publish rates. Every time a mover is updated, the previous orientation will be compared to the current orientation, and if the difference does not exceed the threshold, a new entity state will be ignored.

Default: 3 degrees
:::

::: command
entity_state_position_threshold \<length-value\>

Specifies a length threshold to control entity-state publish rates. Every time a mover is updated, the extrapolated previous location will be compared to the current location, and if the length between them does not exceed the threshold, a new entity state will be ignored.

Default: 1 meter
:::

::: command
disable_entity_state_thresholds

Disables the entity-state thresholds, causing entity-state to be published at the movers\' update intervals. This may be overwritten by further calls to [entity_state_position_threshold]() or [entity_state_angle_threshold]().
:::

::: command
entity_state_maximum_interval \<time-value\>

Specifies a maximum interval of time between entity-state updates. If a platform does not receive an update to its location within this interval, a new state, extrapolated from the last state, will be published.

Default: 10 seconds
:::

::: command
visual_part_update_interval \<time-value\>

Specifies the update period at which visual part articulations will be published. Time values should be greater than zero. If unset, visual parts\' articulations will not be published.

Default: 0 seconds
:::



The following would show all events except those dealing with track updates

::: parsed-literal

[event_pipe](#event_pipe)

:   file events.aer use_preset full disable TRACK_UPDATED

end_event_pipe
:::





-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   double damageFactor
-   Vec3d locationWCS
-   bool velocityWCSvalid
-   Vec3f velocityWCS
-   bool accelerationWCSvalid
-   Vec3f accelerationWCS
-   bool orientationWCSvalid
-   Vec3f orientationWCS
-   bool fuelCurrentValid
-   double fuelCurrent
-   bool machNumberValid
-   double machNumber



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   bool nameValid
-   string name
-   bool typesValid
-   list\<string\> types
-   bool sideValid
-   string side
-   bool iconValid
-   string icon
-   bool markingValid
-   string marking
-   bool spatialDomainValid
-   [SpatialDomain](#spatialdomain) spatialDomain
-   bool commandChainsValid
-   list\<[CommandChain](#commandchain)\> commandChains
-   bool categoriesValid
-   list\<string\> categories
-   bool disIdValid
-   [DisEntityId](#disentityid) disId
-   bool fuelCapacityValid
-   double fuelCapacity



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track](#track) track



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) localTrackId
-   [Track_Id](#track_id) addedTrackId



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) localTrackId
-   [Track_Id](#track_id) removedTrackId



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string sensorName
-   string modeName
-   bool activate



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   bool broken
-   bool removed



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   unsigned int appearance



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string partName
-   [PartType](#parttype) partType
-   bool on
-   bool broken
-   bool disabled



-   double simTime
-   uint8 simIndex
-   string text
-   bool platformIndexValid
-   unsigned int platformIndex



-   double simTime
-   uint8 simIndex
-   bool sensorPlatformIndexValid
-   unsigned int sensorPlatformIndex
-   bool sensorNameValid
-   string sensorName
-   bool targetPlatformIndexValid
-   unsigned int targetPlatformIndex
-   bool beamValid
-   int beam
-   bool rangeErrorValid
-   float rangeError
-   bool azimuthErrorValid
-   float azimuthError
-   bool elevationErrorValid
-   float elevationError
-   bool rangeRateErrorValid
-   float rangeRateError
-   bool pdValid
-   float pd
-   bool requiredPdValid
-   float requiredPd
-   bool reportedSideValid
-   string reportedSide
-   bool reportedTypeValid
-   string reportedType
-   [EM_Interaction](#em_interaction) interaction



-   double simTime
-   uint8 simIndex
-   list\<[MsgDrawCommand_Item](#msgdrawcommand_item)\> items
-   bool layerValid
-   string layer



-   double simTime
-   uint8 simIndex
-   bool xmtrPlatformIndexValid
-   unsigned int xmtrPlatformIndex
-   bool xmtrCommNameValid
-   string xmtrCommName
-   bool rcvrPlatformIndexValid
-   unsigned int rcvrPlatformIndex
-   bool rcvrCommNameValid
-   string rcvrCommName
-   bool messageTypeValid
-   string messageType
-   bool messageSubTypeValid
-   string messageSubType
-   bool messageSizeValid
-   int messageSize
-   bool serialNumberValid
-   unsigned int serialNumber



-   double simTime
-   uint8 simIndex
-   bool xmtrPlatformIndexValid
-   unsigned int xmtrPlatformIndex
-   bool xmtrCommNameValid
-   string xmtrCommName
-   bool rcvrPlatformIndexValid
-   unsigned int rcvrPlatformIndex
-   bool messageTypeValid
-   string messageType
-   bool messageSubTypeValid
-   string messageSubType
-   bool messageSizeValid
-   int messageSize
-   bool serialNumberValid
-   unsigned int serialNumber



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   string sensorName
-   unsigned int targetIndex
-   bool detected



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex
-   string sensorName



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track](#track) track



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   [Track_Id](#track_id) trackId
-   bool targetIndexValid
-   unsigned int targetIndex



-   double simTime
-   uint8 simIndex
-   unsigned int ownerIndex
-   string sensorName
-   string modeName
-   list\<[BeamDefinition](#beamdefinition)\> beamList



-   double simTime
-   uint8 simIndex
-   unsigned int ownerId
-   string partName
-   [PartType](#parttype) partType
-   Vec3f locationECS
-   Vec3f orientationECS



-   double simTime
-   uint8 simIndex
-   int year
-   int month
-   int day
-   float time



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string partName
-   [PartType](#parttype) partType
-   string partIcon



-   double simTime
-   uint8 simIndex
-   string execPath
-   string cmdLine
-   string version
-   string features
-   string extensions
-   string timeStamp



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   string key
-   [MsgScriptData_Value](#msgscriptdata_value) value



-   double simTime
-   uint8 simIndex
-   [ResourceType](#resourcetype) resourceType
-   string resourceId
-   list\<string\> resourceLocation



-   double simTime
-   uint8 simIndex
-   unsigned int xmtrPlatormIndex
-   string xmtrCommName
-   unsigned int rcvrPlatformIndex
-   string rcvrCommName
-   string messageType
-   string messageSubType
-   int messageSize
-   unsigned int serialNumber



-   double simTime
-   uint8 simIndex
-   [ApplicationUniqueId](#applicationuniqueid) applicationId
-   float timeStamp



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   list\<[AuxDataValue](#auxdatavalue)\> auxData



-   double simTime
-   uint8 simIndex
-   double time
-   string msgType
-   string msgText



-   double simTime
-   uint8 simIndex
-   unsigned int platformIndex
-   vec3 location
-   float heading
-   list\<[Waypoint](#waypoint)\> route



-   [CircularZoneInfo](#circularzoneinfo) zoneInfo



-   [EllipticalZoneInfo](#ellipticalzoneinfo) zoneInfo



-   [PolygonalZoneInfo](#polygonalzoneinfo) zoneInfo



-   string zoneSetName
-   string parentPlatform
-   bool isGlobal
-   ColorF lineColor
-   ColorF fillColor
-   list\<[CircularZoneInfo](#circularzoneinfo)\> circularZones
-   list\<[EllipticalZoneInfo](#ellipticalzoneinfo)\> ellipticalZones
-   list\<[PolygonalZoneInfo](#polygonalzoneinfo)\> polygonalZones



-   string commName
-   string parentPlatform
-   string commAddress
-   string commType
-   string networkName
-   string networkAddress
-   string routerName
-   string gatewayAddress
-   string gatewayCommName
-   string gatewayPlatformName
-   list\<[link](#link)\> links
-   StringList linkAddresses



-   string routerName
-   string commName
-   string parentPlatform
-   string commAddress
-   string routerType
-   string gatewayAddress
-   string gatewayCommName
-   int hopLimit
-   bool useDefaultProtocol
-   bool useMulticastProtocol



-   string networkName
-   string networkType
-   string networkAddress





-   unsigned int a
-   unsigned int b
-   unsigned int c



-   string name
-   string commander



-   unsigned int site
-   unsigned int application
-   unsigned int entity



-   int localTrackNumber
-   string ownerId



-   [Track_Id](#track_id) trackId
-   bool startTimeValid
-   double startTime
-   bool updateTimeValid
-   double updateTime
-   bool originatorIndexValid
-   unsigned int originatorIndex
-   bool sensorNameValid
-   string sensorName
-   bool sensorModeValid
-   string sensorMode
-   bool updateCountValid
-   int updateCount
-   bool originatorWCSValid
-   Vec3d originatorWCS
-   bool locationWCSValid
-   Vec3d locationWCS
-   bool rangeValid
-   float range
-   bool rangeErrorValid
-   float rangeError
-   bool bearingValid
-   float bearing
-   bool bearingErrorValid
-   float bearingError
-   bool elevationValid
-   float elevation
-   bool elevationErrorValid
-   float elevationError
-   bool rangeRateValid
-   float rangeRate
-   bool rangeRateErrorValid
-   float rangeRateError
-   bool trackQualityValid
-   float trackQuality
-   bool signalToNoiseValid
-   float signalToNoise
-   bool pixelCountValid
-   float pixelCount
-   bool spatialDomainValid
-   [SpatialDomain](#spatialdomain) spatialDomain
-   bool stateCovarianceValid
-   [Covariance](#covariance) stateCovariance
-   bool targetIndexValid
-   unsigned int targetIndex



-   unsigned char rowCount
-   unsigned char colCount
-   list\<float\> values



-   [EM_Interaction_Status](#em_interaction_status) status
-   bool xmtrWCSValid
-   Vec3d xmtrWCS
-   bool rcvrWCSValid
-   Vec3d rcvrWCS
-   bool targetWCSValid
-   Vec3d targetWCS
-   bool rcvrToTargetValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) rcvrToTarget
-   bool targetToRcvrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) targetToRcvr
-   bool xmtrToTargetValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) xmtrToTarget
-   bool targetToXmtrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) targetToXmtr
-   bool xmtrToRcvrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) xmtrToRcvr
-   bool rcvrToXmtrValid
-   [EM_Interaction_RelativeData](#em_interaction_relativedata) rcvrToXmtr
-   bool rcvrBeamValid
-   [EM_Interaction_BeamData](#em_interaction_beamdata) rcvrBeam
-   bool xmtrBeamValid
-   [EM_Interaction_BeamData](#em_interaction_beamdata) xmtrBeam
-   bool maskingFactorValid
-   float maskingFactor
-   bool infraredSignatureValid
-   float infraredSignature
-   bool infraredSignatureAzimuthValid
-   float infraredSignatureAzimuth
-   bool infraredSignatureElevationValid
-   float infraredSignatureElevation
-   bool radarSignatureValid
-   float radarSignature
-   bool radarSignatureAzimuthValid
-   float radarSignatureAzimuth
-   bool radarSignatureElevationValid
-   float radarSignatureElevation
-   bool opticalSignatureValid
-   float opticalSignature
-   bool opticalSignatureAzimuthValid
-   float opticalSignatureAzimuth
-   bool opticalSignatureElevationValid
-   float opticalSignatureElevation
-   bool transmittedPowerValid
-   float transmittedPower
-   bool powerDensityAtTargetValid
-   float powerDensityAtTarget
-   bool receivedPowerValid
-   float receivedPower
-   bool rcvrNoisePowerValid
-   float rcvrNoisePower
-   bool clutterPowerValid
-   float clutterPower
-   bool interferencePowerValid
-   float interferencePower
-   bool signalToNoiseValid
-   float signalToNoise
-   bool propagationFactorValid
-   float propagationFactor
-   bool absorbtionFactorValid
-   float absorbtionFactor
-   bool detectionThresholdValid
-   float detectionThreshold
-   bool pixelCountValid
-   float pixelCount
-   bool zoomAttenuationFactorValid
-   float zoomAttenuationFactor



-   bool rcvrRangeLimitsValid
-   bool rcvrRangeLimits
-   bool rcvrAltitudeLimitsValid
-   bool rcvrAltitudeLimits
-   bool rcvrAngleLimitsValid
-   bool rcvrAngleLimits
-   bool rcvrHorizonMaskingValid
-   bool rcvrHorizonMasking
-   bool rcvrTerrainMaskingValid
-   bool rcvrTerrainMasking
-   bool xmtrRangeLimitsValid
-   bool xmtrRangeLimits
-   bool xmtrAltitudeLimitsValid
-   bool xmtrAltitudeLimits
-   bool xmtrAngleLimitsValid
-   bool xmtrAngleLimits
-   bool xmtrHorizonMaskingValid
-   bool xmtrHorizonMasking
-   bool xmtrTerrainMaskingValid
-   bool xmtrTerrainMasking
-   bool signalLevelValid
-   bool signalLevel



-   float range
-   float trueAzimuth
-   float trueElevation
-   float apparentAzimuth
-   float apparentElevation



-   float azimuth
-   float elevation
-   float gain



-   [DrawType](#drawtype) drawType
-   bool idValid
-   unsigned int id
-   bool colorValid
-   unsigned int color
-   bool durationValid
-   float duration
-   bool drawSizeValid
-   unsigned char drawSize
-   bool drawStyleValid
-   unsigned char drawStyle
-   bool drawStyle2Valid
-   unsigned char drawStyle2
-   bool orientationValid
-   Vec3f orientation
-   bool axisValid
-   Vec3f axis
-   bool textValid
-   string text
-   bool vertex1Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex1
-   bool vertex2Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex2
-   bool vertex3Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex3
-   bool vertex4Valid
-   [MsgDrawCommand_Vertex](#msgdrawcommand_vertex) vertex4



-   [MsgDrawCommand_VertexType](#msgdrawcommand_vertextype) type
-   bool relativePlatformIndexValid
-   unsigned int relativePlatformIndex
-   bool xyzValid
-   Vec3f xyz



-   int beamId
-   float r0_range
-   int r0_rangeType
-   float minRange
-   float maxRange
-   int stabilized
-   float tilt
-   [FieldOfView](#fieldofview) fov



-   int shape
-   list\<Vec2f\> points



-   float threshold
-   float draw



-   string name
-   [AuxDataType](#auxdatatype) type
-   bool boolean
-   int integer
-   double real
-   string text



-   [WaypointLocationType](#waypointlocationtype) locationType
-   string label
-   double locationX
-   double locationY
-   bool altitudeValid
-   double altitude
-   bool headingValid
-   double heading
-   bool gotoIdValid
-   string gotoId





-   double floating
-   int integer
-   bool boolean
-   string text





-   unknown
-   land
-   air
-   surface
-   subsurface
-   space



-   sensor
-   mover
-   comm
-   processor
-   visual



-   line
-   point
-   icon
-   ellipse
-   erase
-   ellipsoid
-   quadrilateral
-   text
-   timer



-   unset_vertex
-   absolute_wcs
-   relative_zero
-   relative_ecs
-   relative_ned
-   relative_ss



-   in_progress
-   dud
-   target_impact
-   far_away_in_air
-   far_away_above_ground
-   far_away_ground_impact
-   target_proximity_air_burst
-   target_proximity_above_ground
-   target_proximity_ground_impact
-   part_disabled_or_destroyed



-   other
-   dted
-   raw_dted
-   geotiff_dem



-   Unknown
-   Boolean
-   Integer
-   Real
-   Text



-   LatitudeAndLongitude
-   RelativeOffset



-   string zoneName
-   string parentPlatform
-   string referencePlatform
-   bool isGlobal
-   ColorF lineColor
-   ColorF fillColor
-   double maxAltitude
-   double minAltitude
-   double referenceLat
-   double referenceLon
-   double heading
-   bool locationDefined



-   [CommonZoneData]{.title-ref} commonInfo
-   double radius
-   double minRadius
-   double startAngle
-   double stopAngle



-   [CommonZoneData]{.title-ref} commonInfo
-   double latAxis
-   double lonAxis
-   double minRadius
-   double startAngle
-   double stopAngle



-   [CommonZoneData]{.title-ref} commonInfo
-   list\<Vec2d\> points
-   bool useLatLon



-   string platformName
-   string commName





-   string filePath
-   int64 fileModTime
-   uint nodeId
-   uint treeId
-   string nodeType
-   string nodeName
-   uint numChildren
-   list\<int\> childrenIds
-   bool isRootNode



-   uint nodeId
-   string nodeName
-   [NodeExecState](#nodeexecstate) execState
-   string tooltipText



-   string key
-   bool value



-   string key
-   int value



-   string key
-   double value



-   string key
-   string value



-   string key
-   [Track](#track) value



-   string name
-   StringList types
-   string side



-   string key
-   [BehaviorTreePlatformInfo](#behaviortreeplatforminfo) value



-   uint nodeId
-   string sharedBlackboard
-   list\<[BoolDictEntry](#booldictentry)\> boolDict
-   list\<[IntDictEntry](#intdictentry)\> intDict
-   list\<[DoubleDictEntry](#doubledictentry)\> doubleDict
-   list\<[StringDictEntry](#stringdictentry)\> stringDict
-   list\<[TrackDictEntry](#trackdictentry)\> trackDict
-   list\<[PlatformDictEntry](#platformdictentry)\> platformDict





-   index platformIndex
-   uint id
-   string treeName
-   string treeDesc
-   list\<[BehaviorTreeNode](#behaviortreenode)\> nodes



-   index platformIndex
-   uint treeId
-   list\<[BehaviorTreeNodeExec](#behaviortreenodeexec)\> execList
-   list\<[BehaviorTreeBlackboardData](#behaviortreeblackboarddata)\> blackboardList





-   running
-   success
-   failure
-   idle
-   halted
-   disabled