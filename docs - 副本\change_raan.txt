# change_raan.rst.txt.md
orphan

:   



Script Type: `WsfChangeRAAN`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} change_raan

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [raan]() \...

end_maneuver
:::

Change the right ascension of the ascending node (raan) of the orbit to the given value.

> ::: note
> ::: title
> Note
> :::
>
> A `constraint<orbital_event.constraints>`{.interpreted-text role="ref"} of `northern_intersection`{.interpreted-text role="command"} or `southern_intersection`{.interpreted-text role="command"} must be specified for this maneuver.
> :::
>
> ::: note
> ::: title
> Note
> :::
>
> The initial orbit must be non-equatorial for this maneuver to execute (raan is undefined for equatorial orbits).
> :::

::: command
raan \<angle-value\>

The right ascension of the ascending node (raan) of the final orbit.
:::