orphan

:   

# six_dof_object_types

::: {.command block=""}
six_dof_object_types \... end_six_dof_object_types
:::

::: parsed-literal
six_dof_object_types

> // `Rigid_Body_Vehicle_Type_Label`{.interpreted-text role="ref"} `rigid_body_vehicle_type`{.interpreted-text role="command"} \... end_rigid_body_vehicle_type
>
> // `Point_Mass_Vehicle_Type_Label`{.interpreted-text role="ref"} `point_mass_vehicle_type`{.interpreted-text role="command"} \... end_point_mass_vehicle_type
>
> // `SixDOF_Thrust_Producer_Types`{.interpreted-text role="ref"} `rigid_body_engine_type`{.interpreted-text role="command"} \... end_rigid_body_engine_type `point_mass_engine_type`{.interpreted-text role="command"} \... end_point_mass_engine_type
>
> // `SixDOF_Platform_Mappings`{.interpreted-text role="ref"} `map_vehicle_to_platform`{.interpreted-text role="command"} \... end_map_vehicle_to_platform
>
> // [SixDOF Integrators Support](#sixdof-integrators-support) [integrators]() \...
>
> // [SixDOF Environment Support](#sixdof-environment-support) [terrain]() \...

end_six_dof_object_types
:::

## Overview

A six_dof_object_types block is used to specify various **types** of SixDOF objects, subobjects, components (such as engines), and environmental infrastructure (such as terrain). Once defined, a `rigid_body_vehicle_type`{.interpreted-text role="command"} or `point_mass_vehicle_type`{.interpreted-text role="command"} can be used when defining a `WSF_RIGID_BODY_SIX_DOF_MOVER`{.interpreted-text role="model"} or `WSF_POINT_MASS_SIX_DOF_MOVER`{.interpreted-text role="model"}.

Multiple [six_dof_object_types](#six_dof_object_types) blocks may be utilized. Types defined in the [six_dof_object_types](#six_dof_object_types) blocks must be uniquely named. In addition, a type must be defined before it is referenced. As a result, the order in which types are defined is important. Types should be defined in the order in which they are referenced. Thus, if a \"F2H_Banshee\" rigid-body aircraft uses \"Westinghouse_J34\" jet engines, the engine type (`rigid_body_engine_type`{.interpreted-text role="command"}) should be defined first, followed by the aircraft (`rigid_body_vehicle_type`{.interpreted-text role="command"}).

## SixDOF Integrators Support

SixDOF was designed with the ability to use different numerical integration approaches. These different integrators are \"loaded\" through the use of the [integrators]() command.

At the current time, only a single (default) integrator is provided, but software developers can derive from the base class (WsfSixDOF_Integrator) to introduce other integrators.

::: command
integrators \<file-name\>

This will load one or more SixDOF integrators by reading/loading the specified file.

See `SixDOF_Integrators_File_Definition`{.interpreted-text role="ref"} for the definition of the integrators file format.
:::

The [integrators]() line can be defined independently, but is often grouped into a another file (typically six_dof_environment.txt) along with the [SixDOF Environment Support](#sixdof-environment-support) items.

## SixDOF Environment Support

SixDOF movers lean on the AFSIM scenario-local information for most environmental components. These include atmosphere, wind, terrain, and gravity. AFSIM terrain can be augmented to specify areas like airfields, where noise in other terrain data may have an adverse affect on landing, takeoff, or taxi operations.

::: command
terrain \<file-name\>

Create a SixDOF terrain object using the specified file. Only a single terrain object should be defined for a given scenario.

See `SixDOF_Terrain_File_Definition`{.interpreted-text role="ref"} for the definition of the terrain file format.
:::

A [integrators]() command is also typically included in the file for convenience.

It may also be convenient to define a `atmosphere_table`{.interpreted-text role="command"} block here. If one is not defined, the default `atmosphere`{.interpreted-text role="doc"} is used. This will lead to inaccuracies above 61 Km MSL, where the default atmosphere ends.
