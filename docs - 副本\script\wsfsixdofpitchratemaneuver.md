orphan

:   

# WsfSixDOF_PitchRateManeuver

## Overview {#overview .WsfSixDOF_PitchRateManeuver .inherits .WsfSixDOF_Maneuver}

This maneuver sets the target pitch rate of the platform to which it is assigned. This maneuver is done executing as soon as the target is set.

## Methods

::: method
static WsfSixDOF_PitchRateManeuver Construct(double aPitchRateDegPerSec)

Construct a maneuver that will set the target pitch rate of the platform to which the maneuver is assigned.
:::

::: method
double GetPitchRate()

Return this maneuver\'s target pitch rate in degrees per second.
:::
