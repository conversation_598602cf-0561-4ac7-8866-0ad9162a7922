# wsfcyberengagement.rst.txt.md
orphan

:   



Navigation: `cyber_overview`{.interpreted-text role="ref"}



[WsfCyberEngagement](#wsfcyberengagement) objects provide information about an active cyber engagement. Objects of this type are provided as an argument to various script methods within the cyber framework.

::: warning
::: title
Warning
:::

[WsfCyberEngagement](#wsfcyberengagement) objects contain data about the most recent attack and scan against the victim by an attacker using a particular named attack. Subsequent attacks or scans will invalidate any data regarding previous attacks or scans with the exception of developed immunity of the victim platform. In addition, [WsfCyberEngagement](#wsfcyberengagement) objects are destroyed if the victim platform is removed from the simulation.
:::



::: method
string Attacker()

Returns the name of the attacking platform.
:::

::: method
WsfPlatform AttackerPlatform()

Returns a reference to the attacking platform.
:::

::: method
string AttackType()

Returns the name of the `cyber_attack`{.interpreted-text role="command"} type employed in the attack.
:::

::: method
string Victim()

Returns the name of the victim platform.
:::

::: method
WsfPlatform VictimPlatform()

Returns a reference to the victim platform.
:::



To cancel a pending scan/attack see `WsfSimulation.CyberScanCancel`{.interpreted-text role="method"}.



These methods return detailed information about the engagement.

::: method
double TimeAttackInitiated()

Returns the time when the attack was initiated, or a large positive value (\>1.0E+30) if an attack has not been initiated (e.g.: only a scan has been initiated.)
:::

::: method
bool AttackStatus()

Returns true if the attack initiation was successful or false if not.
:::

::: method
int ReportedAttackStatus()

TODO_CYBER
:::

::: method
double AttackSuccessThreshold()

Returns the probability threshold for determining if an attack is successful.
:::

::: method
double AttackSuccessDraw()

The random draw used for determining if an attack is successful.
:::

These methods return detailed information about the engagement.

::: method
double StatusReportThreshold()

Returns the probability threshold for determining if a status report is successful.
:::

::: method
double StatusReportDraw()

The random draw used for determining if a status report is successful.
:::

::: method
double TimeAttackDiscovered()

The simulation time when the attack will be detected by the victim, or a very large value (\> 1.0E+30) if the attack will not be detected.
:::

::: method
double AttackDetectionThreshold()

Returns the probability threshold for determining if the victim can detect the attack.
:::

::: method
double AttackDetectionDraw()

The random draw used for determining if the victim can detect the attack.
:::

::: method
double TimeAttackRecovery()

The simulation time when the victim will recover from the attack, or a very large value (\> 1.0E+30) if the victim will not recover from the attack.
:::

::: method
double AttackRecoveryThreshold()

Returns the probability threshold for determining if the victim can recover from the attack.
:::

::: method
double AttackRecoveryReportDraw()

The random draw used for determining if the victim can recover from the attack.
:::

::: method
bool Recovery()

Returns if the victim platform will attempt to recover from an attack.
:::

::: method
double AttackDeliveryDelayTime()

Returns the time required to simulate the delivery of a payload to the victim.
:::

::: method
double AttackDetectionDelayTime()

Returns the time required for the victim to determine that is has been attacked.
:::

::: method
double AttackRecoveryDelayTime()

Returns the time required for the victim to recover from an attack.
:::

::: method
bool AttackSuccess()

Returns the success of the current attack against the victim. Will return false if an attack has not been initiated or is not completed.
:::

::: method
bool AttackInProgress()

Returns true if an attack is currently in progress.
:::

::: method
bool AttackInProgress()

Returns true if an attack is currently in progress.
:::

::: method
int AttackFailureReason()

Returns an integer that indicates the reason for attack failure. These values are:

-   0 for a random draw
-   1 for victim immunity
-   2 for an attack blocked by the victim detecting a previous scan
-   3 for failing the user defined vulnerability script
-   4 for no failure due never being attacked, or an attack still in progress
:::



::: method
double TimeScanInitiated()

The simulation time when the CyberScan() method was attempted, or a very large number (\> 1.0E+30) if a scan was never attempted.
:::

::: method
double ScanDetectionThreshold()

Returns the probability threshold for determining if the victim can detect the scan.
:::

::: method
double ScanDetectionDraw()

Returns the probability threshold for determining if the victim can detect the scan.
:::

::: method
double ScanDelayTime()

Returns the time delay required to simulate the scan of a victim.
:::

::: method
bool ScanSuccess()

Returns the success of the current scan against the victim. Will return false if a scan has not been initiated or is not completed.
:::

::: method
bool ScanInProgress()

Returns true if a scan is currently in progress.
:::

::: method
int ScanFailureReason()

Returns an integer that indicates the reason for scan failure. These values are:

-   0 for victim immunity
-   1 for the scan being detected by the victim
-   2 for failing the user defined vulnerability script
-   3 for no failure due never being scanned, or a scan still in progress
:::