orphan

:   

::: demo
iads_c2_demos
:::

| UNCLASSIFIED
| 
| The iads_c2_demos folder contains a number of scenarios that demonstrate the core capabilities of the IADS C2 processors in AFSIM. This folder contains six top-level scenario files:
| 
|   - **air_interceptor.txt** - Demonstrates the ability to for a C2 network to engage threats with ground-controlled interceptors via the WSF_WEAPONS_MANAGER_AI.
| 
|   - **assignment_priority.txt** - Demonstrates how the unclassified battle manager prioritizes weapons for assignment based on command chain structure and the presence or absence of attached zones on weapon platforms. Examples of defended asset (DA) and weapon engagement zones (WEZ) are demonstrated.
| 
|   - **basic_iads.txt** - A scenario which shows the basic concepts of the IADS C2 module. Basic assignment, asset management, and dissemination are handled with few extra features utilized.
| 
|   - **delegate_battle_manager.txt** - Demonstrates how delegate battle managers (WSF_UNCLASS_BM) can be utilized to specify unique resource allocation schemes for subsets of a command chain. In this scenario, the IADS commander (10_iads_cmdr) assigns a weapon in a subordinate battalion managed by a delegate battle manager, which in turn selects and assigns individual SAM launchers according to its configuration.
| 
|   - **sensors_manager_fov.txt** - This scenario highlights the ability of the WSF_SENSORS_MANAGER_FOV to manage the slew and cue of a target tracking radar (TTR).
| 
|   - **weapon_engagement_zone.txt** - A scenario in which a weapon engagement zone (WEZ) is attached to a SAM launcher. The presence of the WEZ will effect the assignment prioritization of the SAM launcher and limit the threats assigned to the launcher to those which will be engaged within the attached WEZ.
| 
