orphan

:   

# User Configurations - Wizard

Wizard will store the state of the application, windows and preferences in a configuration file for the user whenever the `Preferences <wizard_preferences>`{.interpreted-text role="doc"} are changed or the application is closed. The configuration of Wizard can be saved using the Save Configuration option in the File menu. Configurations can also be loaded using the Load Configuration option in the File menu.

Configurations can also be imported, which allows for the user to pick and choose which parts of the configuration file to import. This is different from Load Configuration because Load Configuration will load all the configuration options in the file.
