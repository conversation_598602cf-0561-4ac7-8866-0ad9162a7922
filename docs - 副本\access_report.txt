# access_report.rst.txt.md
orphan

:   



::: note
::: title
Note
:::

This feature has been re-factored to the Post Processor. See Detection Report in the Post Processor documentation.
:::

::: {.command block=""}
access_report \... end_access_report

::: parsed-literal

access_report

:   [default_sensor]() \... [file_prefix]() \... [file_format]() \... [report]() \...

end_access_report
:::
:::

[Access Report](#access-report)s provide start time, end time, and the duration of platforms\' visibility to each other. Input options for detecting platform(s), detected platform(s), and detecting sensor(s) are available.

::: note
::: title
Note
:::

Access reports are computed with `sensor`{.interpreted-text role="ref"}s; therefore, access is defined to be a function of sensor detection, not simply geometry. For example, a `WSF_RADAR_SENSOR`{.interpreted-text role="model"} will usually not detect all platforms within line-of-sight due to signal-to-noise limitations provided in the sensor definition. The accuracy of the report is also limited by the sensor\'s `frame_time`{.interpreted-text role="command"}.
:::



::: command
default_sensor \<sensor-type\>

Specify the name of a default `sensor`{.interpreted-text role="ref"} type definition that will be used to generate the access reports (only in the case where no \<sensor-name\> is provided in [report]() command specifications). If this command is provided, the sensor type will be cloned and added to the specified \<detector-platform\>.
:::

::: command
file_prefix

Specify the output file prefix. The file extension is specified by the [file_format]() command.
:::

::: command
file_format { csv \| tsv }

Specify the output file format, which must be either of *csv* (comma-separated variables) or *tsv* (tab-separated variables).

Default csv
:::

::: note
::: title
Note
:::

The csv [file_format]() can be viewed in Microsoft Excel, but the formats for start and end times must be changed by selecting cells in the spreadsheet and using the *Format Cells\...* option to select a compatible format (i.e., *m/d/yyyy h:mm.000* ).
:::

::: command
report { \<detector-platform\> \| all } to { \<detectee-platform\> \| all } \[ using { \<sensor-name\> \| all } \]

Specify which access events are to be reported in the output file. Selections for each detector-detectee pair should be provided. optionally, a sensor name on the detector can also be specified, in which case access is only computed for that particular sensor. If no sensor is specified, all sensors on the detecting platform will be used to compute access separately. For each selection, the keyword *all* can be inserted, meaning that access will be computed for all detectors, detectees, and / or sensors.
:::





> ::: parsed-literal
>
> access_report
>
> :   file_prefix access_report_test report haystack to [RADIO_ROSTO]()(RS-15)(23439) using radar // A sensor named \'radar\' // must be defined on platform \'haystack\'.
>
> end_access_report
> :::



> ::: parsed-literal
>
> access_report
>
> :   file_prefix access_report_test file_format tsv // specify tab-separated values format report haystack to all using radar
>
> end_access_report
> :::



> ::: parsed-literal
>
> access_report
>
> :   file_prefix access_report_test report all to [RADIO_ROSTO]()(RS-15)(23439) default_sensor TELESCOPE // Instance of sensor type TELESCOPE will be placed on platforms without // other sensor definitions, and it will be used on those platforms to compute access // (sensor type TELESCOPE must be defined elsewhere in the input).
>
> end_access_report
> :::