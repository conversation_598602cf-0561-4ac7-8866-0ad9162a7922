# wsfcircularize.rst.txt.md
orphan

:   



::: {.WsfCircularize .inherits .WsfOrbitalManeuver}
Input type: `circularize<../circularize>`{.interpreted-text role="doc"}
:::

[WsfCircularize](#wsfcircularize) can be used to circularize an elliptical orbit of a platform.

::: method
static WsfCircularize Construct(WsfOrbitalEventCondition aCondition)

Static method to create a [WsfCircularize](#wsfcircularize) maneuver that will circularize an elliptical orbit.
:::

::: note
::: title
Note
:::

This maneuver will have no effect when the current eccentricity is 0. The supplied condition should be `WsfOrbitalEventCondition.AT_ASCENDING_RADIUS`{.interpreted-text role="method"} or `WsfOrbitalEventCondition.AT_DESCENDING_RADIUS`{.interpreted-text role="method"}.
:::