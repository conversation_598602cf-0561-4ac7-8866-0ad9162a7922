orphan

:   

# WsfTask

::: {.WsfTask cloneable=""}
`WsfTask`{.interpreted-text role="class"} is used by `WsfTaskManager`{.interpreted-text role="class"} to represent a task. It is included in various task messages and has script methods. WsfTask is further specialized to provide access to weapon, jamming, and uplinking parameters.
:::

## Methods

::: method
int TaskId()

Returns the serial number of the task.
:::

::: method
WsfTrackId TrackId()

Returns the track ID as known by the assigner.
:::

::: method
WsfTrackId LocalTrackId()

Returns the track ID as known by the assignee.
:::

::: method
string TaskType()

Returns the \'task type\' from the `WsfTaskManager::AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"} method.
:::

::: method
void SetTaskType(string aTaskType)

Sets the \'task type\'. To be used prior to `WsfTaskManager::AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}
:::

::: method
WsfPlatform Assigner()

Returns the platform that made the assignment.

::: note
::: title
Note
:::

This returned value may be a null reference if the platform has been deleted. Use `IsValid() <WsfObject.IsValid>`{.interpreted-text role="method"} to ensure it is valid.
:::
:::

::: method
string AssignerName()

Returns the name of the platform that made the assignment.
:::

::: method
WsfPlatform Assignee()

Returns the platform to which the task was assigned.

::: note
::: title
Note
:::

This returned value may be a null reference if the platform has been deleted. Use `IsValid() <WsfObject.IsValid>`{.interpreted-text role="method"} to ensure it is valid.
:::
:::

::: method
string AssigneeName()

Returns the name of the platform to which the task was assigned.
:::

::: method
void SetAssignee(WsfPlatform aAssignee)

Sets the assignee of the task. To be used prior to `WsfTaskManager::AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}.
:::

::: method
void SetAssignee(string aAssignee)

Sets the platform name of the task assignee. To be used prior to `WsfTaskManager::AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}.
:::

::: method
string Status()

Returns the task\'s status. Possible values are: \"ACCEPTED\", \"REJECTED\", \"COMPLETED\", \"IN_PROGRESS\", \"ACKNOWLEDGE_CANCEL\", \"ACKNOWLEDGE_COMPLETE\", \"WEAPON_RELEASED\", \"SUCCESSFUL\", or \"UNSUCCESSFUL\".
:::

::: method
string SubStatus()

Returns the task\'s sub-status. This is a user-defined status string set by methods like `WsfTaskManager.TaskComplete`{.interpreted-text role="method"}.
:::

::: method
double TimeAssigned()

Returns the simulation time when the task was assigned.
:::

::: method
double TimeUpdated()

Returns the simulation time when the task was last updated.
:::

::: method
double TimeSinceAssigned()

Returns the number of seconds that have elapsed since the task was assigned.
:::

::: method
double TimeSinceUpdated()

Returns the number of seconds that have elapsed since the task was last updated.
:::

::: method
void SetCommDevice(string aCommDevice)

Sets the name of the comm device to be used when transmitting the task assignment. To be used prior to `WsfTaskManager::AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}
:::

## Resource Methods

The following methods apply only if the task is a resource (i.e., sensor, weapon, or processor) control task.

::: method
string ResourceName()

Returns the name of the sensor, weapon or processor resource. This will be an empty string if the task is not a resource control task.
:::

::: method
bool ResourceIsSensor()
:::

::: method
bool ResourceIsWeapon()
:::

::: method
bool ResourceIsJammer()
:::

::: method
bool ResourceIsProcessor()
:::

::: method
bool ResourceIsUplink()

Returns true if the indicated resource is a sensor, weapon, jammer, processor, or uplink, respectively.
:::

::: method
string ResourceMode()

Returns the \'mode\' for a sensor or jammer task.
:::

::: method
int ResourceCount()

Returns the number of resources (rounds) to be used.
:::

::: method
double ResourceFrequency()

Returns the center frequency in Hertz for a jamming task.
:::

::: method
double ResourceBandwidth()

Returns the jamming bandwidth in Hertz for a jamming task.
:::

## Auxiliary Data Methods {#WsfTask.Auxiliary_Data_Methods}
