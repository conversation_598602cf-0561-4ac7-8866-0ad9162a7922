#!/usr/bin/env python3
"""
AFSIM Platform Agent Web界面
基于Flask的HTML对话界面
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import sys
import os
from pathlib import Path
import logging
import threading
import time

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.enhanced_platform_agent import SimplePlatformAgent

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'afsim_platform_agent_2024'

# 全局Agent实例
agent = None
agent_initialized = False


def init_agent():
    """初始化Agent"""
    global agent, agent_initialized
    try:
        logger.info("开始初始化AFSIM Platform Agent...")
        agent = SimplePlatformAgent()
        if agent.initialize():
            agent_initialized = True
            logger.info("Agent初始化成功")
        else:
            logger.error("Agent初始化失败")
    except Exception as e:
        logger.error(f"Agent初始化异常: {e}")


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/chat', methods=['POST'])
def chat():
    """处理对话请求"""
    global agent, agent_initialized
    
    if not agent_initialized:
        return jsonify({
            'success': False,
            'message': 'Agent尚未初始化完成，请稍候重试'
        })
    
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({
                'success': False,
                'message': '请输入有效的消息'
            })
        
        # 处理消息
        response = agent.chat(user_message)
        
        return jsonify({
            'success': True,
            'response': response,
            'message': '处理成功'
        })
        
    except Exception as e:
        logger.error(f"处理对话请求失败: {e}")
        return jsonify({
            'success': False,
            'message': f'处理失败: {str(e)}'
        })


@app.route('/api/status')
def status():
    """获取系统状态"""
    global agent, agent_initialized
    
    if not agent_initialized:
        return jsonify({
            'initialized': False,
            'message': 'Agent正在初始化中...'
        })
    
    try:
        agent_status = agent.get_status()
        aircraft_list = agent.get_available_aircraft()
        
        return jsonify({
            'initialized': True,
            'aircraft_count': len(aircraft_list),
            'aircraft_list': aircraft_list,
            'llm_available': agent.summarizer is not None,
            'conversation_turns': agent_status.get('conversation_turns', 0),
            'features': agent_status.get('features', [])
        })
        
    except Exception as e:
        logger.error(f"获取状态失败: {e}")
        return jsonify({
            'initialized': False,
            'message': f'获取状态失败: {str(e)}'
        })


@app.route('/api/clear')
def clear_history():
    """清空对话历史"""
    global agent, agent_initialized
    
    if not agent_initialized:
        return jsonify({
            'success': False,
            'message': 'Agent尚未初始化'
        })
    
    try:
        agent.clear_history()
        return jsonify({
            'success': True,
            'message': '对话历史已清空'
        })
        
    except Exception as e:
        logger.error(f"清空历史失败: {e}")
        return jsonify({
            'success': False,
            'message': f'清空失败: {str(e)}'
        })


@app.route('/api/example')
def get_example():
    """获取示例脚本"""
    example_script = """platform BBB WSF_PLATFORM 
     side blue
     icon f-15
     add mover WSF_FORMATION_FLYER 
          position 34:31:34.58n 113:28:09.47e altitude 2000 m
          speed 100 m/s
          lead_aircraft leader
          offset_forward_from_lead 1 au
     end_mover  
     position 34:29:24.66n 113:26:46.51e altitude 2000 m
end_platform"""
    
    return jsonify({
        'success': True,
        'script': example_script,
        'description': '这是一个包含编队飞行器的AFSIM平台脚本示例'
    })


def main():
    """主函数"""
    # 检查环境变量
    has_api_key = bool(os.getenv("DEEPSEEK_API_KEY"))
    if not has_api_key:
        print("⚠️  未设置DEEPSEEK_API_KEY环境变量")
        print("   大模型查询功能将不可用，但本地数据库查询仍可正常使用")
    
    # 在后台线程中初始化Agent
    init_thread = threading.Thread(target=init_agent, daemon=True)
    init_thread.start()
    
    print("🚀 AFSIM Platform Agent Web界面启动中...")
    print("📱 请在浏览器中访问: http://localhost:5000")
    print("⏹️  按 Ctrl+C 停止服务")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5000, debug=False)


if __name__ == '__main__':
    main()
