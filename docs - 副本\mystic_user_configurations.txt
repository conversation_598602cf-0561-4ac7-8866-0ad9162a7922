# mystic_user_configurations.rst.txt.md
orphan

:   



<PERSON><PERSON> will store the state of the application, windows and preferences in a configuration file for the user whenever the `Preferences <mystic_preferences>`{.interpreted-text role="doc"} are changed or the application is closed. The configuration of <PERSON><PERSON> can be saved using the Save Configuration option in the `File menu<mystic_file_menu>`{.interpreted-text role="doc"}. Configurations can also be loaded using the Load Configuration option in the `File menu<mystic_file_menu>`{.interpreted-text role="doc"}.

Configurations can also be imported, which allows for the user to pick and choose which parts of the configuration file to import. This is different from Load Configuration because Load Configuration will load all the configuration options in the file.

The benefit of saving multiple configurations is to support easily transitioning between different scenarios for Mystic. Saving and loading configurations allows the user to set up the configuration once and the switch between the configurations easily.