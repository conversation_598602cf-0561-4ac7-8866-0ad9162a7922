# outer_air_battle.rst.txt.md
orphan

:   

::: demo
outer_air_battle
:::



This model demonstrates several features of WSF, listed below:

1.  Executing scripts at route waypoints (callback)
2.  Flying orbits with time limits
3.  Defining and using global routes
4.  Creation and deletion of platforms by another platform
5.  Maintaining related data in a \"script_struct\" object type



OAB was developed by the US Navy in response to the threat to the CVBG of long range bombers carrying anti-ship missiles (ASM\'s). Fighter and Airborne Early Warning (AEW) aircraft were flown to defend the CVBG. The fighters would fly CAP (combat air patrol) stations some distance from the CVBG. The AEW aircraft would detect the incoming bombers and vector the fighters to intercept and destroy the bombers. Ideally, the fighters would destroy the bombers before they could fire their ASM\'s at the carrier (CV). (This is called shooting the archer.) If that failed, then the fighters would attempt to destroy the individual ASM\'s.

The model supports different approaches to OAB. Two example approaches are provided here:

1.  The first approach establishes medium range CAP (MRC) and Extended Range CAP (ERC) routes. The MRC places aircraft in orbits about 200 NM from the CV. The ERC, despite its name, is actually a long range \"out and back\" mission.
2.  The second approach establishes MRC and a Long Range CAP (LRC) stations. The LRC stations are 100-200 NM farther out than the MRC stations.