orphan

:   

# Routes - Wizard

![image](../images/wiz_route.png)

![image](../images/wiz_orbit.png)

Routes and orbits may be displayed on platforms via the `platform options<../wizard_platform_options>`{.interpreted-text role="doc"}.

## Route Modification

Routes can be modified in the `text editor<../wizard_text_editor>`{.interpreted-text role="doc"} , on the `map display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"}, or the `route browser<../wkf_plugin/wiz_route_browser>`{.interpreted-text role="doc"}.

![image](../images/wizard_waypoint_context_menu.png)

Right-clicking on a waypoint from the `map display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"} will give you the options to:

-   Delete the waypoint
-   Add a waypoint after the selected waypoint
-   Edit the waypoint

![image](../images/wizard_waypoint_context_menu2.png)

Selecting a waypoint and right clicking on the map will give you an option to add a waypoint at the cursor location after the selected waypoint.

## Orbit Preferences

![image](../images/wiz_orbit_prefs.png)

The orbit preferences control how orbits will be displayed.

### Line width

Sets the line width of the orbit in pixels.

### Periods

When viewing an orbit on a flat-map or without the ECI camera (see `map display<../wkf_plugin/wiz_map_display>`{.interpreted-text role="doc"} preferences) the periods value will determine how many periods of the orbit will be displayed.
