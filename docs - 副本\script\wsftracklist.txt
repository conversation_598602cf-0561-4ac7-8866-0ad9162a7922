# wsftracklist.rst.txt.md
orphan

:   



::: {.WsfTrackList container=""}
WsfTrackList is a container of references to `WsfTrack`{.interpreted-text role="class"} objects. A list of local tracks is typically retrieved as follows:
:::

::: parsed-literal
`WsfTrackList`{.interpreted-text role="class"} tl = `PLATFORM.MasterRawTrackList() <WsfPlatform.MasterRawTrackList>`{.interpreted-text role="method"};
:::

In addition to the methods described below, the container may be processed using the foreach script language statement as follows:

::: parsed-literal
`WsfTrackList`{.interpreted-text role="class"} trackList = \<a method that returns a `WsfTrackList`{.interpreted-text role="class"}\>; foreach (`WsfTrack`{.interpreted-text role="class"} t in trackList) { \# \...Code to process the `WsfTrack`{.interpreted-text role="class"} object referenced through the variable \'t\'\... }
:::



::: method
int Count() int Size()

Return the number of entries in the list.
:::

::: method
bool Empty()

Returns true if the list is empty.

::: note
::: title
Note
:::

This is faster than checking for Count() != 0.
:::
:::

::: method
WsfTrack Entry(int aIndex) WsfTrack Get(int aIndex)

Return the entry at the given index. The index must be in the range \[ 0 .. Count()-1 \]

::: note
::: title
Note
:::

The Get method allows the entries to be accessed via array indexing: e.g:

    WsfTrack t = trackList[i];
:::
:::

::: method
WsfTrack Find(WsfTrackId aTrackId)

Find the entry with specified track ID. Using the `IsValid() <WsfObject.IsValid>`{.interpreted-text role="method"} method on the return value will return false if the requested entry could not be found.
:::

::: method
Iterator WsfTrackListIterator GetIterator()

Return an iterator that points to the beginning of the list. This is used by the script language to support the foreach command but may also be used directly.
:::