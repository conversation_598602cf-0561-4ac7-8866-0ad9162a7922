# script_right_arrow_operator.rst.txt.md
orphan

:   





The right arrow operator (-\>) operator is used in script to access user-defined variables (defined with `ScriptVariables`{.interpreted-text role="ref"}) or call user-defined scripts. Any script object which allows addition of scripts like `WsfPlatform`{.interpreted-text role="class"} and `struct`{.interpreted-text role="class"} may use the right arrow operator.



For additional examples using the -\> operator, see `script_struct`{.interpreted-text role="class"}

    platform myplatform1 WSF_PLATFORM
       script_variables
          double x = 2.2;
          WsfGeoPoint p = WsfGeoPoint.Construct("0n 0e");
       end_script_variables
       script void a_method(int param1)
          writeln("Called a_method ", param1);
       end_script
    end_platform

    platform myplatform2 WSF_PLATFORM
       execute at_time 1 s absolute

       # Access existing variables on myplatform1

       WsfPlatform myplatform1 = WsfSimulation.FindPlatform("myplatform1");
       writeln("X = ", myplatform1->x);
       writeln("p = ", myplatform1->p);

       # Add new variables
       myplatform1->y = 3.3;
       PLATFORM->z = 4.4;

       # Call a method
       myplatform1->a_method(5);

       end_execute
    end_platform



Attribute access using \'-\>\' is not verified at initialization like using \'.\'. A runtime error will display when trying to access a variable or script that doesn\'t exist. See has_attr and has_script in `BUILTIN <__BUILTIN__>`{.interpreted-text role="class"}.

Only user-defined scripts may be called, use the \'.\' operator to call methods provided by built-in types.