# wsfearthmonopoleterm.rst.txt.md
orphan

:   





The `WsfEarthMonopoleTerm`{.interpreted-text role="class"} represents the point mass gravitational field of the Earth. See also `Earth Monopole <orbital_dynamics_terms.earth_monopole>`{.interpreted-text role="ref"}.



::: method
WsfEarthMonopoleTerm Construct() WsfEarthMonopoleTerm Construct(double aGravitationalParameter) WsfEarthMonopoleTerm Construct(string aModel)

Create a term representing Earth\'s point mass gravitational field with the given gravitational parameter in SI units. If no gravitational parameter is specified, this term will use the EGM-96 value.

Alternatively, users may select the Earth\'s gravitational parameter using the name of a gravity model. Allowed values are \'wgs84\' and \'egm96\', which select the WGS-84 and EGM-96 values, respectively.
:::



::: method
double GravitationalParameter()

Return the gravitational parameter for this dynamical term.
:::