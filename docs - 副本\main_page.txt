# main_page.rst.txt.md
orphan

:   





The Advanced Framework for Simulation, Integration and Modeling (AFSIM) is an object-oriented C++ library that is used to create simulations that model platform interactions in a geographic context. Top-level objects in the simulation are called platforms (a.k.a. bodies, entities or players). Think of a platform as a simple body to which systems and attributes are attached. Platforms represent such things as vehicles (ground, air, space, surface, subsurface), buildings, or living beings. Interactions include sensor detections, collisions, and communications to name a few.



Applications that use the AFSIM library can use the AFSIM input system to load system attributes, populate simulation entities and exercise control over the simulation. AFSIM core applications include:



AFSIM suite applications aid in utilization of the AFSIM framework and its core applications for developing, executing, analyzing and processing core application and simulation data. AFSIM suite applications include:



Users may visit the [AFSIM Helpdesk on DI2E (Access Controlled)](https://confluence.di2e.net/x/cQx8G) to report a problem or get support from a community of expert users and developers.