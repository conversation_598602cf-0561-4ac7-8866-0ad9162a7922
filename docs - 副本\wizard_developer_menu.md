orphan

:   

# Developer Menu - Wizard

![image](images/wizard_developer_menu.png)

The developer menu is enabled from the `preferences<wizard_preferences>`{.interpreted-text role="doc"}.

::: note
::: title
Note
:::

Use of the developer menu is not recommended unless you are a developer. Stability is not guaranteed.
:::

-   Document Memory Usage - Report the amount of memory in use by documents.
-   Object Mode - Switch to object edit mode, this will lock text editing until the user returns to text-edit mode.
-   Parse Tree Dump - Dump the contents of the parse tree to a dialog.
-   Proxy Dump - Dump the contents of the proxy (object) tree to a dialog.
-   Watch Proxy - Setup a watch on a proxy value.
