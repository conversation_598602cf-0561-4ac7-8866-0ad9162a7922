"""
AFSIM Platform Agent - 参数提取服务
按需提取文档中的参数信息，支持LLM和正则表达式两种方法
"""

import re
import json
import logging
import os
from typing import List, Dict, Any
import yaml
from pathlib import Path
from openai import OpenAI

logger = logging.getLogger(__name__)


class ParameterExtractor:
    """参数提取器 - 按需提取文档中的参数信息"""
    
    def __init__(self, config_path: str = None):
        """
        初始化参数提取器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.llm_client = None
        self._initialize_llm()
        
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = Path(__file__).parent.parent / "config" / "config.yaml"
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'llm': {
                'provider': 'deepseek',
                'deepseek': {
                    'api_key': '',
                    'base_url': 'https://api.deepseek.com/v1',
                    'model': 'deepseek-chat',
                    'temperature': 0.1,
                    'max_tokens': 4000
                }
            },
            'parameter_extraction': {
                'method': 'auto',
                'llm_extraction': {
                    'max_content_length': 8000,
                    'retry_on_failure': True,
                    'extract_default_values': True,
                    'extract_examples': True,
                    'extract_requirements': True
                }
            }
        }
    
    def _initialize_llm(self):
        """初始化LLM客户端"""
        try:
            llm_config = self.config.get('llm', {})
            provider = llm_config.get('provider', 'deepseek')
            
            if provider == 'deepseek':
                config = llm_config.get('deepseek', {})
                api_key = config.get('api_key') or os.getenv('DEEPSEEK_API_KEY')
                
                if not api_key:
                    logger.warning("未设置DeepSeek API Key，将使用传统正则表达式方法提取参数")
                    return
                
                self.llm_client = OpenAI(
                    api_key=api_key,
                    base_url=config.get('base_url', 'https://api.deepseek.com/v1')
                )
                self.model = config.get('model', 'deepseek-chat')
                self.temperature = config.get('temperature', 0.1)
                self.max_tokens = config.get('max_tokens', 4000)
                
                logger.info("参数提取器LLM客户端初始化成功")
                
            else:
                logger.warning(f"不支持的LLM提供商: {provider}，将使用传统正则表达式方法")
                
        except Exception as e:
            logger.error(f"LLM客户端初始化失败: {e}，将使用传统正则表达式方法")
            self.llm_client = None
    
    def extract_parameters(self, content: str, parameter_type: str = "platform") -> List[Dict[str, Any]]:
        """
        从文档内容中提取参数
        
        Args:
            content: 文档内容
            parameter_type: 参数类型 (platform, sensor, weapon等)
            
        Returns:
            List[Dict]: 参数列表
        """
        extraction_method = self.config.get('parameter_extraction', {}).get('method', 'auto')
        
        if extraction_method == 'llm' and self.llm_client:
            return self._extract_with_llm(content, parameter_type)
        elif extraction_method == 'regex':
            return self._extract_with_regex(content, parameter_type)
        else:  # auto
            if self.llm_client:
                return self._extract_with_llm(content, parameter_type)
            else:
                return self._extract_with_regex(content, parameter_type)
    
    def _extract_with_llm(self, content: str, parameter_type: str) -> List[Dict[str, Any]]:
        """
        使用大模型提取参数
        
        Args:
            content: 文档内容
            parameter_type: 参数类型
            
        Returns:
            List[Dict]: 参数列表
        """
        try:
            # 限制内容长度
            max_length = self.config.get('parameter_extraction', {}).get('llm_extraction', {}).get('max_content_length', 8000)
            if len(content) > max_length:
                content = self._extract_parameter_sections(content, max_length)
            
            # 构建提示词
            prompt = self._build_extraction_prompt(content, parameter_type)
            
            # 调用LLM
            response = self.llm_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": f"你是一个专业的AFSIM技术文档分析师，擅长从技术文档中提取{parameter_type}参数信息。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # 解析响应
            response_text = response.choices[0].message.content
            parameters = self._parse_llm_response(response_text)
            
            logger.info(f"使用LLM方法提取到 {len(parameters)} 个{parameter_type}参数")
            return parameters
            
        except Exception as e:
            logger.error(f"LLM参数提取失败: {e}")
            
            # 如果配置了自动回退，使用正则表达式方法
            if self.config.get('parameter_extraction', {}).get('llm_extraction', {}).get('retry_on_failure', True):
                logger.info("回退到正则表达式方法")
                return self._extract_with_regex(content, parameter_type)
            else:
                return []
    
    def _extract_with_regex(self, content: str, parameter_type: str) -> List[Dict[str, Any]]:
        """
        使用正则表达式提取参数
        
        Args:
            content: 文档内容
            parameter_type: 参数类型
            
        Returns:
            List[Dict]: 参数列表
        """
        parameters = []
        
        # 根据参数类型选择不同的模式
        patterns = self._get_regex_patterns(parameter_type)
        
        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
            
            for match in matches:
                param_name = match.group(1)
                
                # 获取上下文
                context_window = self.config.get('parameter_extraction', {}).get('regex_extraction', {}).get('context_window', 200)
                start_pos = max(0, match.start() - context_window)
                end_pos = min(len(content), match.end() + context_window)
                context = content[start_pos:end_pos].strip()
                
                # 提取描述
                description = self._extract_description_from_context(content, match.start(), param_name)
                
                param_info = {
                    'name': param_name,
                    'type': self._infer_parameter_type(param_name, context),
                    'description': description,
                    'context': context,
                    'position': match.start(),
                    'default_value': None,
                    'required': None,
                    'example': None,
                    'extraction_method': 'regex',
                    'pattern_used': pattern_info['description']
                }
                
                # 避免重复
                if not any(p['name'] == param_name for p in parameters):
                    parameters.append(param_info)
        
        # 按位置排序
        parameters.sort(key=lambda x: x['position'])
        
        logger.info(f"使用正则表达式方法提取到 {len(parameters)} 个{parameter_type}参数")
        return parameters
    
    def _get_regex_patterns(self, parameter_type: str) -> List[Dict[str, str]]:
        """
        根据参数类型获取正则表达式模式
        
        Args:
            parameter_type: 参数类型
            
        Returns:
            List[Dict]: 模式列表
        """
        # 基础模式（适用于所有类型）
        base_patterns = [
            {
                'pattern': r'\[([a-zA-Z_][a-zA-Z0-9_]*)\]\(\)\s*\.{3}',
                'description': '基本参数模式: [parameter_name]() ...'
            },
            {
                'pattern': r'([a-zA-Z_][a-zA-Z0-9_]*)\s+\.{3}\s+end_\1',
                'description': '命令模式: parameter_name ... end_parameter'
            },
            {
                'pattern': r'`([a-zA-Z_][a-zA-Z0-9_]*)`',
                'description': '代码块参数'
            }
        ]
        
        # 根据参数类型添加特定模式
        if parameter_type.lower() == 'platform':
            base_patterns.extend([
                {
                    'pattern': r'`(Wsf[A-Z][a-zA-Z]*)`',
                    'description': 'WSF平台类'
                },
                {
                    'pattern': r'platform\.([a-zA-Z_][a-zA-Z0-9_]*)',
                    'description': '平台属性'
                }
            ])
        elif parameter_type.lower() == 'sensor':
            base_patterns.extend([
                {
                    'pattern': r'sensor\.([a-zA-Z_][a-zA-Z0-9_]*)',
                    'description': '传感器属性'
                },
                {
                    'pattern': r'`(WsfSensor[A-Z][a-zA-Z]*)`',
                    'description': 'WSF传感器类'
                }
            ])
        elif parameter_type.lower() == 'weapon':
            base_patterns.extend([
                {
                    'pattern': r'weapon\.([a-zA-Z_][a-zA-Z0-9_]*)',
                    'description': '武器属性'
                },
                {
                    'pattern': r'`(WsfWeapon[A-Z][a-zA-Z]*)`',
                    'description': 'WSF武器类'
                }
            ])
        
        return base_patterns

    def _extract_parameter_sections(self, content: str, max_length: int) -> str:
        """
        从长文档中提取包含参数信息的重要部分

        Args:
            content: 原始文档内容
            max_length: 最大长度

        Returns:
            str: 提取的重要部分
        """
        # 查找包含参数关键词的段落
        parameter_keywords = [
            'parameter', 'param', 'argument', 'option', 'setting', 'config',
            'platform', 'sensor', 'weapon', 'wsf', 'command', 'property', 'attribute',
            'default', 'value', 'type', 'description', 'example'
        ]

        lines = content.split('\n')
        important_lines = []

        for i, line in enumerate(lines):
            line_lower = line.lower()
            # 如果行包含参数关键词，包含该行及其上下文
            if any(keyword in line_lower for keyword in parameter_keywords):
                # 添加上下文（前后各2行）
                start_idx = max(0, i - 2)
                end_idx = min(len(lines), i + 3)
                for j in range(start_idx, end_idx):
                    if lines[j] not in important_lines:
                        important_lines.append(lines[j])

        # 如果提取的内容仍然太长，截取前面部分
        extracted_content = '\n'.join(important_lines)
        if len(extracted_content) > max_length:
            extracted_content = extracted_content[:max_length] + "..."

        # 如果提取的内容太少，使用原文档的前面部分
        if len(extracted_content) < max_length // 2:
            extracted_content = content[:max_length] + "..."

        return extracted_content

    def _build_extraction_prompt(self, content: str, parameter_type: str) -> str:
        """
        构建参数提取的提示词

        Args:
            content: 文档内容
            parameter_type: 参数类型

        Returns:
            str: 提示词
        """
        return f"""
请从以下AFSIM {parameter_type}技术文档中提取所有的参数信息。

文档内容：
{content}

请提取以下信息并以JSON格式返回：
1. 参数名称 (name): 实际在代码中使用的参数名
2. 参数类型 (type): 如 string, number, boolean, coordinate, angle, distance, time, array, object 等
3. 参数描述 (description): 参数的作用和用途说明
4. 默认值 (default_value): 如果文档中提到了默认值
5. 是否必需 (required): true/false，如果文档中明确说明
6. 示例值 (example): 如果文档中有使用示例
7. 取值范围 (range): 如果有最小值、最大值等限制
8. 单位 (unit): 如果参数有单位（如米、度、秒等）

返回格式：
```json
[
  {{
    "name": "参数名称",
    "type": "参数类型",
    "description": "参数描述",
    "default_value": "默认值或null",
    "required": true/false/null,
    "example": "示例值或null",
    "range": "取值范围或null",
    "unit": "单位或null"
  }}
]
```

注意事项：
1. 只提取明确的{parameter_type}参数，忽略一般性的文档说明
2. 参数名称应该是实际在代码中使用的名称，去掉括号等符号
3. 描述要简洁明了，重点说明参数的作用
4. 如果无法确定某个字段的值，请设置为null
5. 确保返回的是有效的JSON格式
6. 优先提取有明确定义和说明的参数
"""

    def _parse_llm_response(self, response_text: str) -> List[Dict[str, Any]]:
        """
        解析LLM返回的参数信息

        Args:
            response_text: LLM响应文本

        Returns:
            List[Dict]: 解析后的参数列表
        """
        try:
            # 尝试提取JSON部分
            json_start = response_text.find('[')
            json_end = response_text.rfind(']') + 1

            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                parameters = json.loads(json_text)

                # 标准化参数格式
                standardized_params = []
                for param in parameters:
                    if isinstance(param, dict) and 'name' in param:
                        standardized_param = {
                            'name': param.get('name', ''),
                            'type': param.get('type', 'unknown'),
                            'description': param.get('description', ''),
                            'default_value': param.get('default_value'),
                            'required': param.get('required'),
                            'example': param.get('example'),
                            'range': param.get('range'),
                            'unit': param.get('unit'),
                            'extraction_method': 'llm',
                            'position': len(standardized_params)
                        }
                        standardized_params.append(standardized_param)

                return standardized_params
            else:
                logger.warning("LLM响应中未找到有效的JSON格式")
                return []

        except json.JSONDecodeError as e:
            logger.error(f"解析LLM响应JSON失败: {e}")
            logger.debug(f"响应内容: {response_text}")
            return []
        except Exception as e:
            logger.error(f"解析LLM响应失败: {e}")
            return []

    def _extract_description_from_context(self, content: str, position: int, param_name: str) -> str:
        """
        从上下文中提取参数描述

        Args:
            content: 文档内容
            position: 参数位置
            param_name: 参数名称

        Returns:
            str: 参数描述
        """
        lines = content.split('\n')
        param_line = None

        # 找到参数所在行
        for i, line in enumerate(lines):
            if param_name in line and abs(sum(len(l) for l in lines[:i]) - position) < 100:
                param_line = i
                break

        if param_line is None:
            return ""

        # 查找描述（通常在参数后面的几行）
        description_lines = []
        for i in range(param_line + 1, min(param_line + 5, len(lines))):
            line = lines[i].strip()
            if line and not line.startswith('[') and not line.startswith('#'):
                description_lines.append(line)
            elif description_lines:  # 遇到空行或新参数，停止
                break

        return ' '.join(description_lines)

    def _infer_parameter_type(self, param_name: str, context: str) -> str:
        """
        推断参数类型

        Args:
            param_name: 参数名称
            context: 参数上下文

        Returns:
            str: 参数类型
        """
        param_lower = param_name.lower()
        context_lower = context.lower()

        # 基于参数名称推断类型
        if any(keyword in param_lower for keyword in ['position', 'coordinate', 'point', 'location']):
            return 'coordinate'
        elif any(keyword in param_lower for keyword in ['heading', 'angle', 'rotation', 'bearing']):
            return 'angle'
        elif any(keyword in param_lower for keyword in ['altitude', 'height', 'depth', 'distance', 'range']):
            return 'distance'
        elif any(keyword in param_lower for keyword in ['mass', 'weight']):
            return 'mass'
        elif any(keyword in param_lower for keyword in ['time', 'duration', 'delay']):
            return 'time'
        elif any(keyword in param_lower for keyword in ['name', 'id', 'identifier', 'label']):
            return 'string'
        elif any(keyword in param_lower for keyword in ['factor', 'ratio', 'percentage', 'rate']):
            return 'number'
        elif any(keyword in param_lower for keyword in ['enable', 'disable', 'flag', 'active']):
            return 'boolean'
        elif any(keyword in param_lower for keyword in ['speed', 'velocity']):
            return 'speed'
        elif any(keyword in param_lower for keyword in ['frequency', 'freq']):
            return 'frequency'
        elif any(keyword in param_lower for keyword in ['list', 'array', 'collection']):
            return 'array'

        # 基于上下文推断
        if any(keyword in context_lower for keyword in ['true', 'false', 'boolean']):
            return 'boolean'
        elif any(keyword in context_lower for keyword in ['string', 'text', 'name']):
            return 'string'
        elif any(keyword in context_lower for keyword in ['number', 'integer', 'float', 'double']):
            return 'number'
        elif any(keyword in context_lower for keyword in ['coordinate', 'latitude', 'longitude']):
            return 'coordinate'
        elif any(keyword in context_lower for keyword in ['degree', 'radian', 'angle']):
            return 'angle'

        return 'unknown'
