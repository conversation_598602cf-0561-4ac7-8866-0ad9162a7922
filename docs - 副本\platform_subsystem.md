orphan

:   

# Platform_Subsystem {#Platform_Subsystem}

## Overview

Platform subsystems are entities that can be attached to platforms. Subsystem types include comm, fuel, mover, processor, and sensor. Each subsystem type can be added to the platform in a similar manner. All commands for subsystems are used within a platform *block*.

    platform_type  TEST_PLATFORM WSF_PLATFORM #Creates the TEST_PLATFORM type definition
      ...platform subsystem commands...
    end_platform_type

    platform test_platform1 TEST_PLATFORM     #Instantiates test_platform1 from TEST_PLATFORM into a simulation
      ...platform subsystem commands...
    end_platform

    edit platform test_platform1              #Edits the instantiated test_platform1
      ...platform subsystem commands...
    end_platform

## Adding a Subsystem

    platform_type ...
      subsystem <subsystem-name> <subsystem-type>

      end_subsystem
    end_platform_type

    platform_type ...
      add subsystem <subsystem-name> <subsystem-type>

      end_subsystem
    end_platform_type

## Removing a Subsystem

    platform_type ...
      delete subsystem <subsystem-name>
    end_platform_type

    platform ...
      delete subsystem <subsystem-name>
    end_platform_type

## Editing a Subsystem

    platform_type ...
      edit subsystem <subsystem-name>
      end_subsystem
    end_platform_type

    edit platform ...
      subsystem <subsystem-name>
      end_subsystem
    end_platform_type
