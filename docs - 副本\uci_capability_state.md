orphan

:   

# UCI_CapabilityState

## Overview {#overview .UCI_CapabilityState}

This class defines an a capability (mode) state to be used in a `SettingsCommandMessage<UCI_POST_SettingsCommandMessage>`{.interpreted-text role="class"}.

## Static Methods

::: method
UCI_CapabilityState ENABLE()

Creates a capability state set to UCI_ENABLE.
:::

::: method
UCI_CapabilityState DISABLE()

Creates a capability state set to UCI_DISABLE.
:::
