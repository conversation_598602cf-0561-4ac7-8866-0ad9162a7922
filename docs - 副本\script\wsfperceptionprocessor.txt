# wsfperceptionprocessor.rst.txt.md
orphan

:   





::: method
Array\<WsfAssetPerception\> PerceivedAssets()

Returns an array of copied asset perception objects (similar to tracks, but for friendlies).
:::

::: method
Array\<WsfTrack\> PerceivedThreats()

Returns an array of copied threat perception objects (tracks).
:::

::: method
WsfAssetPerception NearestAsset()

Returns the asset perception object that is closest to the platform\'s position. Use `IsValid() <Object>`{.interpreted-text role="class"} to verify value returned.
:::

::: method
WsfTrack NearestThreat()

Returns the threat perception object (track) that is closest to the platform\'s position. To be considered a track must have a valid 3D location. Use `IsValid() <Object>`{.interpreted-text role="class"} to verify value returned.
:::

::: method
void AddExtraCognitiveLoading(double timeDelaySeconds)

Add a time delay to perception updating or any other cognitive task. This is provided to represent a condition where a platform or agent is busy doing some task and his regular perception updating should be delayed. Be careful using this, if it is called from a regularly updating script and a delay is added each time that is equal to or greater than the update interval than no perception updating will ever occur. Pay attention to the time values you delay with.
:::

::: method
void SetAssetImportant(WsfPlatform asset)
:::

::: method
void SetAssetImportant(WsfAssetPerception asset)
:::

::: method
void SetAssetTypeImportant(string asset)

Marks the given asset (or given type) as important so that the asset perception will always include that asset. The defined \"max_asset_load\" will still bound the number of assets you can perceive, even if you\'ve marked more than that as important.
:::

::: method
void SetAssetUnimportant(WsfPlatform asset)
:::

::: method
void SetAssetUnimportant(WsfAssetPerception asset)
:::

::: method
void SetAssetTypeUnimportant(string asset)

Marks the given asset (or given type) as unimportant so that the asset perception will NEVER include that asset. If you have status for five friendly platforms, but have marked all five as unimportant than your asset perception will be empty.
:::

::: method
void SetThreatImportant(WsfLocalTrack threat)
:::

::: method
void SetThreatTypeImportant(string threat)

Marks the given threat (or given threat type) as important so that the threat perception will always include that threat. The defined \"max_threat_load\" will still bound the number of threats you can perceive, even if you\'ve marked more than that as important.
:::

::: method
void SetThreatUnimportant(WsfLocalTrack threat)
:::

::: method
void SetThreatTypeUnimportant(string threat)

Marks the given threat (or given type) as unimportant so that the threat perception will NEVER include that threat.
:::

::: method
bool GetNearestHeat(WsfGeoPoint aLocation, WsfGeoPoint aHeatLocation, double aHeatValue)
:::

::: method
bool GetNearestMaxHeat(WsfGeoPoint aLocation, WsfGeoPoint aHeatLocation)

Sets aHeatLocation to the location of the grid cell containing \"heat\" that is closest to aLocation. Returns true if a cell with heat is found in the heat map. GetNearestHest will return the nearest heat that is greater than or equal to aHeatValue. GetNearestMaxHeat returns the nearest heat that is of maximum value (1.0).
:::

::: method
double GetHeat(WsfGeoPoint aLocation)

Returns the value of the heat cell at the requested location.
:::

::: method
int GetHeatSourcesCount()

Returns the number of active heat sources (lost tracks) in the heat map. This value can be used to determine if there will be heat in the map and is faster then multiple queries to GetNearestHeat().
:::