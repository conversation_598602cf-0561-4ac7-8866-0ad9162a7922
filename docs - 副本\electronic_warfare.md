orphan

:   

# electronic_warfare

::: {.command block=""}
electronic_warfare
:::

::: parsed-literal

electronic_warfare *\<name\>* *\<type-name\>*

:   [debug]() technique *\<technique-name\>* *\<technique-type\>* \... `Electronic Warfare Technique Commands <electronic_warfare_technique.commands>`{.interpreted-text role="ref"} \... end_technique

end_electronic_warfare
:::

-   \<name\> : Name of the new Electronic Warfare type to be created.
-   \<type-name\> : Name of an existing Electronic Warfare type or `WSF_ELECTRONIC_ATTACK`{.interpreted-text role="model"} or `WSF_ELECTRONIC_PROTECT`{.interpreted-text role="model"}, whose definition will be used as the initial definition of the new type.

## Overview

An electronic_warfare provides the ability to define Electronic Warfare types for `electronic_attack`{.interpreted-text role="command"} and `electronic_protect`{.interpreted-text role="command"} in transmitters and receivers, respectively. The `Electronic Attack (EA) <electronic_attack>`{.interpreted-text role="command"} -`electronic protect (EP) <electronic_protect>`{.interpreted-text role="command"} architecture provides the ability to define EA and EP techniques and assess the interaction of these techniques. Multiple techniques are allowed for both EA and EP capability blocks. The effect at the receiver is as defined in the EA technique block unless the EP technique block defines a mitigating technique with an associated effect for the particular EA technique being considered.

## Commands {#electronic_warfare.commands}

::: command
debug

Specifies to use a debug mode to output debug data to the standard output.

**Default** false or off
:::

::: command
technique \<technique-name\> \[\<technique-type-name\>\] \... end_technique
:::

Defines the uniquely named technique and its derived type, if required. Multiple technique blocks can be entered.

-   \<technique-name\>\* : A string input of the technique\'s unique name.
-   \<technique-type-name\>\* : A string input of the technique\'s type. If editing an instance name already defined this input is not required.
