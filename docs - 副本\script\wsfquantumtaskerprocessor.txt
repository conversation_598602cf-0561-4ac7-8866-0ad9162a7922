# wsfquantumtaskerprocessor.rst.txt.md
orphan

:   



::: {.WsfQuantumTaskerProcessor .inherits .WsfProcessor arrow=""}
+-------------+------------------------------------------------------------------------------------------------------------------------------------+
|             | WsfQuantumTaskerProcessor                                                                                                          |
+=============+====================================================================================================================================+
| Control | -   `CancelTask <WsfQuantumTaskerProcessor.CancelTask>`{.interpreted-text role="method"}                                           |
|             |                                                                                                                                    |
| By assigner |                                                                                                                                    |
+-------------+------------------------------------------------------------------------------------------------------------------------------------+
| Status  | -   `SetTaskProgress <WsfQuantumTaskerProcessor.SetTaskProgress>`{.interpreted-text role="method"}                                 |
|             | -   `SetTaskComplete <WsfQuantumTaskerProcessor.SetTaskComplete>`{.interpreted-text role="method"}                                 |
| By assignee | -   `RejectTask <WsfQuantumTaskerProcessor.RejectTask>`{.interpreted-text role="method"}                                           |
+-------------+------------------------------------------------------------------------------------------------------------------------------------+
| Query   | -   `AssignedTaskList <WsfQuantumTaskerProcessor.AssignedTaskList>`{.interpreted-text role="method"}                               |
|             | -   `AssignedTaskListOfType <WsfQuantumTaskerProcessor.AssignedTaskListOfType>`{.interpreted-text role="method"}                   |
| By assigner | -   `AssignedTaskListForTarget <WsfQuantumTaskerProcessor.AssignedTaskListForTarget>`{.interpreted-text role="method"}             |
|             | -   `AssignedTaskListForResourceName <WsfQuantumTaskerProcessor.AssignedTaskListForResourceName>`{.interpreted-text role="method"} |
|             | -   `AssignedTaskListForResourceType <WsfQuantumTaskerProcessor.AssignedTaskListForResourceType>`{.interpreted-text role="method"} |
+-------------+------------------------------------------------------------------------------------------------------------------------------------+
| Query   | -   `ReceivedTaskList <WsfQuantumTaskerProcessor.ReceivedTaskList>`{.interpreted-text role="method"}                               |
|             | -   `ReceivedTaskListOfType <WsfQuantumTaskerProcessor.ReceivedTaskListOfType>`{.interpreted-text role="method"}                   |
| By assignee | -   `ReceivedTaskListOfTypeForTarget <WsfQuantumTaskerProcessor.ReceivedTaskListOfTypeForTarget>`{.interpreted-text role="method"} |
|             | -   `ReceivedTaskListForTarget <WsfQuantumTaskerProcessor.ReceivedTaskListForTarget>`{.interpreted-text role="method"}             |
|             | -   `ReceivedTaskListForResourceName <WsfQuantumTaskerProcessor.ReceivedTaskListForResourceName>`{.interpreted-text role="method"} |
|             | -   `ReceivedTaskListForResourceType <WsfQuantumTaskerProcessor.ReceivedTaskListForResourceType>`{.interpreted-text role="method"} |
+-------------+------------------------------------------------------------------------------------------------------------------------------------+
| Debug   | -   `TasksConsidered <WsfQuantumTaskerProcessor.TasksConsidered>`{.interpreted-text role="method"}                                 |
|             | -   `AssetsConsidered <WsfQuantumTaskerProcessor.AssetsConsidered>`{.interpreted-text role="method"}                               |
|             | -   `ValuesConsidered <WsfQuantumTaskerProcessor.ValuesConsidered>`{.interpreted-text role="method"}                               |
|             | -   `AssetAssigneesFor <WsfQuantumTaskerProcessor.AssetAssigneesFor>`{.interpreted-text role="method"}                             |
+-------------+------------------------------------------------------------------------------------------------------------------------------------+
:::



::: method
bool CancelTask(WsfTrackId aTrackId)

Called by the assigner to cancel any tasks this procoessor has assigned that match the given track ID. If the given track ID is null, then all tasks assigned by this processor are canceled.
:::



::: method
void SetTaskProgress(WsfTask aTask, string aSubStatus)

Called by the assignee to update the task assigner with a status message about progress. The user provides sub-status as an input parameter; the status will be the default \"IN_PROGRESS\".
:::

::: method
void SetTaskComplete(WsfTask aTask)
:::

::: method
void SetTaskComplete(WsfTask aTask, string aSubStatus)

Called by the assignee to update the task assigner with a task complete message. The status of the task complete message will be the default \"COMPLETE\". The default value for sub-status is \"SUCCESSFUL\". User can provide a sub-status; usual values: \"SUCCESSFUL\" or \"UNSUCCESSFUL\"
:::

::: method
void RejectTask(WsfTask aTask)

Called by the assignee to reject the assigned task, sends a task reject/cancel message to the assigner.
:::



::: method
WsfTaskList AssignedTaskList()

Returns a list of all tasks this processor has assigned.
:::

::: method
WsfTaskList AssignedTaskListOfType(string aType)

Returns a list of tasks this processor has assigned for the given task type.
:::

::: note
::: title
Note
:::

Task type is no longer equivalent with resource type.
:::

::: method
WsfTaskList AssignedTaskListForTarget(WsfTrackId aTargetTrackId)

Returns a list of tasks this processor has assigned for the given target.
:::

::: method
WsfTaskList AssignedTaskListForResourceName(string aResourceName)
:::

::: method
WsfTaskList AssignedTaskListForResourceType(string aResourceType)

Returns a list of tasks this processor has for the given resource with the specific name or type. Type can be \"weapon\", \"sensor\", \"jammer\", or any other string (equivalent to passing in \"none\").
:::



::: method
WsfTaskList ReceivedTaskList()

Returns a list of all tasks that have been received.
:::

::: method
WsfTaskList ReceivedTaskListOfType(string aType)

Returns a list of tasks that have been received for the given task type.
:::

::: note
::: title
Note
:::

Task type is no longer equivalent with resource type.
:::

::: method
WsfTaskList ReceivedTaskListOfTypeForTarget(string aType, WsfTrackId aTargetTrackId)

Returns a list of tasks that have been received for the given task type and given target.
:::

::: method
WsfTaskList ReceivedTaskListForTarget(WsfTrackId aTargetTrackId)

Returns a list of tasks that have been received for the given target.
:::

::: method
WsfTaskList ReceivedTaskListForResourceName(string aResourceName)
:::

::: method
WsfTaskList ReceivedTaskListForResourceType(string aResourceType)

Returns a list of tasks that have been received for the given resource with the specific name or type. Type can be \"weapon\", \"sensor\", \"jammer\", or any other string (equivalent to passing in \"none\").
:::



::: method
Array\<WsfQuantumTask\> TasksConsidered()
:::

::: method
Array\<WsfAssetPerception\> AssetsConsidered()

Data returned by these methods are copies of information currently resident in the quantum matrix. This information is updated during the generation step of the quantum tasker processor.
:::

::: method
Array\<Array\<double\>\> ValuesConsidered()

Data returned by this method is a copy of information currently resident in the quantum matrix. This information is updated during the evaluation step of the quantum tasker processor.
:::

::: method
Array\<WsfAssetPerception\> AssetAssigneesFor(WsfQuantumTask aTask)

Returns copies of the asset perceptions allocated for the given task. This information is updated during the allocation step of the quantum tasker processor.
:::