# wsftrackid.rst.txt.md
orphan

:   



::: {.WsfTrackId cloneable=""}
WsfTrackId objects are used to identify tracks as defined by `WsfTrack`{.interpreted-text role="class"} and `WsfLocalTrack`{.interpreted-text role="class"}
:::



::: method
string Name()

Returns the name of the owning platform as a string.
:::

::: method
int Number()

Returns the track number of the track on the owning platform.
:::

::: method
bool IsNull()

Returns \'true\' if the owning platform name has not been assigned. Calling WsfTrackId() returns a null track ID.
:::

::: method
string ToString()

Returns a string representation of the track ID in the form *\<name\>:\<number\>*.
:::

::: method
static WsfTrackId Construct(string aName, int aNumber)

Returns a track ID with the supplied owning platform name and track number.

::: note
::: title
Note
:::

Use with great caution as track Id\'s are typically generated internally.
:::
:::

::: method
void Null()

Clears the name and number in the track ID. This is equivalent to calling [SetName]()(\"\") and [SetNumber]()(0).
:::

::: method
void SetName(string aName)

Sets the name of the owning platform.

::: note
::: title
Note
:::

Use with great caution as track Id\'s are typically generated internally.
:::
:::

::: method
void SetNumber(int aNumber)

Sets the track number of the track on the owning platform.

::: note
::: title
Note
:::

Use with great caution as track Id\'s are typically generated internally.
:::
:::