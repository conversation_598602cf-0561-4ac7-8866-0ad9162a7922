orphan

:   

# WsfFormationFlyer

## Methods {#methods .WsfFormationFlyer .inherits .WsfMover}

::: method
void SetLeadAircraft(WsfPlatform aPlatform)
:::

::: method
WsfPlatform GetLeadAircraft()

Set/Get the lead aircraft for the formation flyer to follow.
:::

::: method
void SetFormationRollsWithLead(bool aRollsWithLead)
:::

::: method
bool GetFormationRollsWithLead()

Set/Get whether the formation flyer should attempt to roll with its lead.
:::

::: method
void SetOffsetFromLeadECS(double aForward, double aRight, double aDown)
:::

::: method
Vec3 GetOffsetFromLeadECS()

Set/Get the offset distance in `ECS`{.interpreted-text role="ref"} of the lead aircraft.

Units: Meters
:::
