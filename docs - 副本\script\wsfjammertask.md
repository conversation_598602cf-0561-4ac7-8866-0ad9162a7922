orphan

:   

# WsfJammerTask

::: WsfJammerTask
WsfJammerTask is a `WsfTask`{.interpreted-text role="class"} used to begin jamming.
:::

## Methods

::: method
int BeamNumber()

Returns the beam number for the jamming task.
:::

::: method
void BeamNumber(int aBeamNumber)

Sets the beam number for the jamming task. To be used prior to `AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}.
:::

::: method
double Bandwidth()

Returns the bandwidth for the jamming task.
:::

::: method
void Bandwidth(double aBandwidth)

Sets the bandwidth for the jamming task. To be used prior to `AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}.
:::

::: method
double Frequency()

Returns the frequency for the jamming task.
:::

::: method
void Frequency(double aFrequency)

Sets the frequency for the jamming task. To be used prior to `AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}.
:::

::: method
string Technique()

Returns the technique for the jamming task.
:::

::: method
void Technique(string aJammingTechnique)

Sets the technique for the jamming task. To be used prior to `AssignTask <WsfTaskManager.AssignTask>`{.interpreted-text role="method"}.
:::
