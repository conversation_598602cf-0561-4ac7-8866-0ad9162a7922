# wsfsunmonopoleterm.rst.txt.md
orphan

:   





The `WsfSunMonopoleTerm`{.interpreted-text role="class"} represents the point mass gravitational field of the Sun. See also `Sun Monopole <orbital_dynamics_terms.sun_monopole>`{.interpreted-text role="ref"}.



::: method
WsfSunMonopoleTerm Construct() WsfSunMonopoleTerm Construct(double aGravitationalParameter)

Create a term representing Sun\'s point mass gravitational field with the given gravitational parameter in SI units. If no gravitational parameter is specified, a default value return value from `Sun.GRAVITATIONAL_PARAMETER`{.interpreted-text role="method"} will be used.
:::



::: method
double GravitationalParameter()

Return the gravitational parameter of the Sun in SI units used by this term.
:::

::: method
Vec3 SunPositionECI(Calendar aTime)

Return the position of the Sun in the ECI frame used by this term at the given time.
:::