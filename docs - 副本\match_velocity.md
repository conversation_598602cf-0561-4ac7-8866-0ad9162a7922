orphan

:   

# Match Velocity

**Script Type:** `WsfMatchVelocity`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} match_velocity

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [platform]() \...

end_maneuver
:::

::: block
match_velocity_maneuver
:::

Perform a maneuver that matches the velocity of the specified target platform.

::: command
platform \<string-value\>

Specify the name of the platform whose velocity should be matched.

::: note
::: title
Note
:::

This maneuver is automatically performed as the final maneuver in the `rendezvous`{.interpreted-text role="doc"} sequence.
:::

::: note
::: title
Note
:::

If the location of the maneuvering platform is different from the target platform, the velocity vector will be rotated into the frame of the maneuvering platform to compensate for differences in the local coordinate system.
:::
:::
