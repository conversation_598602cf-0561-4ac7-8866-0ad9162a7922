orphan

:   

# clutter_model

::: {.command block=""}
clutter_model \... end_clutter_model
:::

::: parsed-literal

[clutter_model](#clutter_model) *\<derived-name\>* *\<base-name\>*

:   \... Input for the clutter model \...

end_clutter_model
:::

## Overview

clutter_model can be used to create configured *clutter models* that can be referenced in the definition of a `WSF_RADAR_SENSOR`{.interpreted-text role="model"}. *\<derived-name\>* is the name by which the user wishes to refer to the configured clutter model. *\<base-name\>* is one of the [Available Clutter Models](#available-clutter-models):

According to <PERSON><PERSON><PERSON><PERSON>, Introduction to Radar (3rd Ed.), \"Clutter strength is defined to be $\sigma^0F^4$, where $\sigma^0$ is the clutter cross-section per unit area, and *F* is the *propagation factor* that sometimes appears in the radar equation to account for propagation effects such as multipath reflections, diffraction, and attenuation.\" The default clutter strength tables, as found in <PERSON><PERSON><PERSON>'s book Low Altitude Land Clutter, are functions of both `global_environment.land_cover`{.interpreted-text role="command"} and `global_environment.land_formation`{.interpreted-text role="command"} , as well as frequency and polarization.

## Effective Use Of Clutter Models

A clutter model definition may be embedded directly in the definition of a radar. For example, assume you have a file called \'ex_radar.txt\':

    sensor EX_RADAR WSF_RADAR_SENSOR
       transmitter
          ... transmitter commands ...
       end_transmitter
       receiver
          ... receiver commands ...
       end_receiver
       clutter_model surface_clutter
          ... surface_clutter commands ...
       end_clutter_model
    end_sensor

The problem with this method is that one must modify the radar definition to change or eliminate the clutter model. In many production uses this is undesirable or infeasible. What would be more desirable is to provide a \'default\' clutter model definition that can be overridden.

The new \'ex_radar.txt\' would now contain:

    # Define the 'default' clutter model
    clutter_model EX_RADAR_CLUTTER surface_clutter
       ... surface_clutter commands ...
    end_clutter_model

    sensor EX_RADAR WSF_RADAR_SENSOR
       transmitter
          ... transmitter commands ...
       end_transmitter
       receiver
          ... receiver commands ...
       end_receiver
       clutter_model EX_RADAR_CLUTTER    # References the clutter model symbolically
    end_sensor

Then to override the clutter model:

    #include ex_radar.txt

    # Provide a new definition that overrides the existing definition.
    # This example disables clutter calculations.

    clutter_model EX_RADAR_CLUTTER none
    end_clutter_model

The radar model will use the **last** definition of EX_RADAR_CLUTTER when it finally creates instances of the radar in the simulation.

## Available Clutter Models

### none {#clutter_model.none}

A \'dummy\' clutter model that is the equivalent of no clutter.

    clutter_model <derived-name> none
    end_clutter_model

### surface_clutter_table {#clutter_model.surface_clutter_table}

**surface_clutter_table** represents clutter by a table, which is typically generated by `sensor_plot`{.interpreted-text role="command"}. The table contains clutter data as a function of target altitude and target range. Additionally, if the table is site-specific it will also contain data as a function of target bearing.

::: note
::: title
Note
:::

The number of ranges in each altitude block are independent. A minimum of 1 range is required for each altitude block.
:::

A site-independent clutter table has the following form:

    clutter_model <derived-name> WSF_SURFACE_CLUTTER_TABLE
       clutters
          altitude <length-value>
            range <length-value>  clutter <power-value>
            ...
            range <length-value>  clutter <power-value>
          altitude <length-value>
            range <length-value>  clutter <power-value>
            ...
            range <length-value>  clutter <power-value>
            ...
          altitude <length-value>
            range <length-value>  clutter <power-value>
            ...
            range <length-value>  clutter <power-value>
          ...
       end_clutters
    end_clutter_model

A site-specific table is similar in form, except that bearing data are included for the set of ranges, as follows:

    clutter_model <derived-name> WSF_SURFACE_CLUTTER_TABLE
       clutters
          altitude <length-value>
             bearing <angle-value>
               range <length-value>  clutter <power-value>
               ...
               range <length-value>  clutter <power-value>
               ...
             bearing <angle-value>
               // Full set of ranges:
               range <length-value>  clutter <power-value>
               ...
               range <length-value>  clutter <power-value>
               ...
             ...
          altitude <length-value>
             bearing <angle-value>
               range <length-value>  clutter <power-value>
               ...
               range <length-value>  clutter <power-value>
               ...
             ...
          ...
       end_clutters
    end_clutter_model
