# AFSIM Platform Agent 配置文件

# 向量数据库配置
vector_db:
  provider: "chroma"  # 向量数据库提供商
  path: "data/chroma_db"  # 数据库存储路径
  collection_name: "afsim_platform_docs"  # 集合名称
  embedding_model: "sentence-transformers/paraphrase-multilingual-mpnet-base-v2"  # 嵌入模型
  embedding_model_path: "models/paraphrase-multilingual-mpnet-base-v2"  # 本地模型路径
  chunk_size: 500  # 文档分块大小
  chunk_overlap: 50  # 分块重叠大小
  similarity_threshold: 0.7  # 相似度阈值

# 文档处理配置
document_processing:
  docs_path: "docs"  # MD文档存储路径
  supported_formats: ["md", "markdown"]  # 支持的文档格式
  max_file_size: 10485760  # 最大文件大小 (10MB)
  encoding: "utf-8"  # 文件编码
  
  # 内容过滤配置
  filters:
    min_content_length: 50  # 最小内容长度
    remove_code_blocks: false  # 是否移除代码块
    remove_tables: false  # 是否移除表格
    remove_links: true  # 是否移除链接

# LLM配置
llm:
  provider: "deepseek"  # LLM提供商: deepseek, openai, ollama
  
  # DeepSeek配置
  deepseek:
    api_key: "***********************************"  # 请在环境变量DEEPSEEK_API_KEY中设置
    base_url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    temperature: 0.1
    max_tokens: 4000
    
  # OpenAI配置 (备用)
  openai:
    api_key: ""  # 请在环境变量OPENAI_API_KEY中设置
    base_url: "https://api.openai.com/v1"
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 4000
    
  # Ollama配置 (本地部署)
  ollama:
    base_url: "http://localhost:11434"
    model: "llama3"
    temperature: 0.1

# 翻译和总结配置
translation:
  enable_translation: true  # 是否启用翻译功能
  target_language: "zh"  # 目标语言
  source_language: "en"  # 源语言
  
  # 总结配置
  summary:
    max_length: 500  # 总结最大长度
    min_length: 100  # 总结最小长度
    enable_bullet_points: true  # 是否使用要点形式

# Platform脚本生成配置
platform_generation:
  template_path: "templates"  # 模板路径
  output_path: "output"  # 输出路径
  
  # 默认参数
  default_params:
    platform_type: "WSF_PLATFORM"
    side: "BLUE"
    spatial_domain: "LAND"
    
  # 参数验证
  validation:
    required_params: ["platform_name", "platform_type"]
    optional_params: ["side", "position", "heading", "altitude"]

# 日志配置
logging:
  level: "INFO"  # 日志级别: DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/afsim_agent.log"  # 日志文件路径
  max_file_size: 10485760  # 最大日志文件大小 (10MB)
  backup_count: 5  # 备份文件数量

# 缓存配置
cache:
  enable: true  # 是否启用缓存
  ttl: 3600  # 缓存过期时间 (秒)
  max_size: 1000  # 最大缓存条目数

# 性能配置
performance:
  max_concurrent_requests: 10  # 最大并发请求数
  request_timeout: 30  # 请求超时时间 (秒)
  batch_size: 50  # 批处理大小
