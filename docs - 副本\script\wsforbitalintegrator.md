orphan

:   

# WsfOrbitalIntegrator

## Overview {#overview .WsfOrbitalIntegrator}

The `WsfOrbitalIntegrator`{.interpreted-text role="class"} represents a numerical integrator used by the `WsfIntegratingSpaceMover`{.interpreted-text role="class"}. See also `Orbital Integrator <orbital_integrator_models>`{.interpreted-text role="ref"}.

## Methods

::: method
string IntegratorType()

Return a string identifying the type of orbital integrator.
:::

## Available Orbital Integrators

The following orbital integrators are available for use in AFSIM:

-   `WsfPrinceDormand45Integrator`{.interpreted-text role="class"}
-   `WsfPrinceDormand78Integrator`{.interpreted-text role="class"}
