# wsfsar_sensor.rst.txt.md
orphan

:   



::: {.WsfSAR_Sensor .inherits .WsfSensor}
The script class [WsfSAR_Sensor](#wsfsar_sensor) allows an analyst to interact with a `WSF_SAR_SENSOR`{.interpreted-text role="model"} during run-time. The following methods are available:
:::



::: method
double GetDwellTime() double GetDwellTime(string aMode)

Returns the `WSF_SAR_SENSOR.dwell_time`{.interpreted-text role="command"} (seconds) of the specified *aMode* or the currently selected mode if none is given.
:::

::: method
void SetDwellTime(double aDwellTime) void SetDwellTime(double aDwellTime, string aMode)

Sets the `WSF_SAR_SENSOR.dwell_time`{.interpreted-text role="command"} (seconds) of the specified *aMode* or the currently selected mode if none is given.
:::

::: method
double GetResolution() double GetResolution(string aMode)

Returns the `WSF_SAR_SENSOR.resolution`{.interpreted-text role="command"} (meters) of the specified *aMode* or the currently selected mode if none is given.
:::

::: method
void SetResolution(double aResolution) void SetResolution(double aResolution, string aMode)

Sets the `WSF_SAR_SENSOR.resolution`{.interpreted-text role="command"} (meters) of the specified *aMode* or the currently selected mode if none is given.
:::