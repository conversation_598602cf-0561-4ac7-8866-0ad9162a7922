orphan

:   

::: demo
terrain_following
:::

| This demo is an example of a script to control a WSF_AIR_MOVER altitude over terrain.
| 
| The key points:
|    It creates a geopoint ahead of you based on the speed of your body and gets
|    future height above terrain.
| 
|    It chooses two vertical rates to choose based on the severity of the
|    altitude delta coming up.
| 
|    It has a upper and lower (hysteresis) boundary so commanded altitude isn\'t
|    constantly changing with every on_update.
| 
|    It keeps the route it is on and only changes altitude.
| 
|    It does not check for negative altitudes so it could possibly fly under
|    ground if an altitude delta was too large.
| 
|    It does not do terrain avoidance.
