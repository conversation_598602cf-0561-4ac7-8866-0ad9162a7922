orphan

:   

# UCI_Control

## Overview {#overview .UCI_Control}

This type is used to define the type of control being requested.

## Static Methods

::: method
static UCI_Control MISSION()

Returns the MISSION control type.
:::

::: method
static UCI_Control CAPABILITY_PRIMARY()

Returns the CAPABILITY_PRIMARY control type.
:::

::: method
static UCI_Control CAPABILITY_SECONDARY()

Returns the CAPABILITY_SECONDARY control type.
:::

::: method
static UCI_Control CAPABILITY_MANAGER()

Returns the CAPABILITY_MANAGER control type.
:::
