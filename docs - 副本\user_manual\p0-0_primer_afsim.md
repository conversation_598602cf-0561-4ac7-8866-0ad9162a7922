orphan

:   

# AFSIM

**Security and legal related items to AFSIM and its included projects:**

::: topic
****Property of the United States Air Force (USAF)****

This software is provided \'As Is\', with no warranties, either express or implied, and the USAF will not be liable for any damages arising in any way from the use of this software.
:::

::: topic
****DISTRIBUTION C****

Distribution authorized to U.S. Government Agencies and their contractors.
:::

## Overview

The **Advanced Framework for Simulation, Integration and Modeling (AFSIM)** is an object-oriented C++ library that is used to create simulations that model platform interactions in a geographic context. Top-level objects in the simulation are called platforms (a.k.a. bodies, entities or players). Think of a platform as a simple body to which systems and attributes are attached. Platforms represent such things as vehicles (ground, air, space, surface, subsurface), buildings, or living beings. Interactions include sensor detections, collisions, and communications to name a few.
