orphan

:   

# SixDOF Terrain File Definition {#SixDOF_Terrain_File_Definition}

A SixDOF terrain file defines one or more regions, with each region containing one or more locations. The purpose of the file is to define the altitude of terrain measured in feet above sea level. Its primary use is to define airport regions to support SixDOF ground operations.

The file format is as follows:

    terrain

      region

        location
          lat       21.3178275
          lon       -157.92026310
          alt_ft    13.0
          minLat    21.27
          maxLat    21.37
          minLon    -157.97
          maxLon    -157.87
        end_location

        ...

        location
          lat       21.4814475
          lon       -158.0378379
          alt_ft    843.0
        end_location

      end_region

    end_terrain

There are two configurations for location blocks (as shown above).

The first location shows the fully defined approach, which defines the lat/lon (in degrees) and altitude (in feet) along with minimum/maximum lat lon values. This defines a location that is bounded by the max/min lat/lon. It is assumed that all terrain within that location is at the altitude specified.

The second location shows the partially defined approach, which defines only the lat/lon (in degrees) and altitude (in feet). In this case, the minimum/maximum lat lon values will be calculated, by adjusting values to be approximately 3 nautical miles (3 arc minutes) on either side of the specified lat/lon. It is assumed that all terrain within that location is at the altitude specified.

This allows various airports to be defined within a region and allows several regions to be defined as well. Grouping airports into several regions (rather than simply using a single region) can improve runtime performance.

Return to `six_dof_object_types`{.interpreted-text role="command"} or `docs/six_dof_object_types:SixDOF Environment Support`{.interpreted-text role="ref"}
