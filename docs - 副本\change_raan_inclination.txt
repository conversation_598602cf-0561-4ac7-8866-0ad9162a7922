# change_raan_inclination.rst.txt.md
orphan

:   



Script Type: `WsfChangeRAAN_Inclination`{.interpreted-text role="class"}

::: parsed-literal

`maneuver<orbital_event.maneuver>`{.interpreted-text role="ref"} change_raan_inclination

:   `Common Maneuver Commands ...<orbital_event.common_commands>`{.interpreted-text role="ref"} [raan]() \... [inclination]() \...

end_maneuver
:::

Change the right ascension of the ascending node (raan) and the inclination of the orbit to the given values.

> ::: note
> ::: title
> Note
> :::
>
> A `constraint<orbital_event.constraints>`{.interpreted-text role="ref"} of `northern_intersection`{.interpreted-text role="command"} or `southern_intersection`{.interpreted-text role="command"} must be specified for this maneuver.
> :::
>
> ::: note
> ::: title
> Note
> :::
>
> The initial orbit must be circular for this maneuver to execute.
> :::
>
> ::: note
> ::: title
> Note
> :::
>
> The final orbit must be non-equatorial for this maneuver to execute (raan is undefined for equatorial orbits).
> :::

::: command
raan \<angle-value\>

The right ascension of the ascending node (raan) of the final orbit.
:::

::: command
inclination \<angle-value\>

The inclination of the final orbit.
:::