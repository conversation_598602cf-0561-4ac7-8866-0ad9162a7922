# wsfspeedktas_maneuver.rst.txt.md
orphan

:   





This maneuver sets a target speed on the platform to which the maneuver is assigned. This maneuver uses knots true air speed as its measure of speed. This maneuver is done executing as soon as the target speed is set, so if there is a need to wait for the achieved speed, a `WsfManeuverConstraint`{.interpreted-text role="class"} must be used.



::: method
WsfSpeedKTAS_Maneuver Construct(double aSpeedKTAS)

Construct a maneuver that will set a target speed for the platform to which the maneuver is assigned in knots true air speed.
:::

::: method
double GetSpeed()

Return this maneuver\'s target speed in knots true air speed.
:::