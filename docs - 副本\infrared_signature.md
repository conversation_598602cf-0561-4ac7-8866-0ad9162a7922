orphan

:   

# infrared_signature

::: {.command block=""}
infrared_signature \... end_infrared_signature
:::

::: parsed-literal

infrared_signature *\<signature-name\>* *(model-name)*

:   \... *model specific commands* \...

end_infrared_signature
:::

## Overview

`infrared_signature`{.interpreted-text role="command"} defines the infrared signature for a platform type. The infrared signature is used when an infrared sensor attempts to detect a platform.

**\<signature-name\>** is the name to be given to the signature. If the name specifies the name of an existing definition then the new definition will replace the current one (i.e. the last occurrence will be used for the simulation.)

**(model-name)** is an optional parameter that specifies the implementation model to be used for defining the signature. Available models are:
