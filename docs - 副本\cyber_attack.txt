# cyber_attack.rst.txt.md
orphan

:   



Navigation: `cyber_overview`{.interpreted-text role="ref"}

::: {.command block=""}
cyber_attack \<type\> \<base_type\> \... end_cyber_attack

::: parsed-literal
cyber_attack \<type\> \<base_type\>

> \# Define the effect(s) of the attack on the victim
>
> [effect]() \... [duration]() \... [scan_delay_time]() \... [delivery_delay_time]() \...
>
> \# Define default probability thresholds.
>
> [probability_of_scan_detection]() \... [probability_of_scan_attribution]() \... [probability_of_attack_success]() \... [probability_of_status_report]() \... [probability_of_attack_detection]() \... [probability_of_attack_attribution]() \... [probability_of_future_immunity]() \...
>
> \# Random draw frequencies.
>
> [scan_detection_draw_frequency]() \... [scan_attribution_draw_frequency]() \... [attack_success_draw_frequency]() \... [status_report_draw_frequency]() \... [attack_detection_draw_frequency]() \... [attack_attribution_draw_frequency]() \... [future_immunity_draw_frequency]() \...

end_cyber_attack
:::
:::

\<type\>

:   The name of the cyber_attack type being defined.

\<base_type\>

:   The name of an existing cyber_attack type or WSF_CYBER_EFFECT, whose definition will be used as the initial definition of the new type.



A [cyber_attack](#cyber_attack) object defines the types of effects that affect the victim of the attack and defines default responses on the victim should they not have a defined response in their `cyber_protect`{.interpreted-text role="command"} object.



::: command
effect \<effect_type\>

A `cyber_effect <predefined_cyber_effect_types>`{.interpreted-text role="doc"} type that models the effect of the attack on the victim. This command may be repeated to specify multiple effects. If multiple effects are associated with this attack, the effects will resolve in the order in which they are listed.

Default None. At least one effect must be provided.

::: warning
::: title
Warning
:::

Specific effect types may require user input at the time an attack is initiated, via the `CyberAttack <wsfplatform.cyber_methods>`{.interpreted-text role="ref"} method call, as noted in each of effects documentation available `here <Predefined_Cyber_Effect_Types>`{.interpreted-text role="ref"}. The user is restricted to a single variable input via these calls. Multiple effects with this requirement will share the same variable. Multiple effects with different variable types on a single attack are currently not supported at this time, and can be placed on a separate attack type definition as a current workaround.
:::
:::

::: command
duration \<time-value\>

A duration will specify how long the `cyber_attack.effect`{.interpreted-text role="command"} lasts. When duration is not set but there exists a `cyber_protect`{.interpreted-text role="command"} block associated with the cyber_attack effect then duration will be the summation of the attack_detection_delay_time and attack_recovery_delay_time.

Default 0 seconds

::: note
::: title
Note
:::

If `cyber_attack.duration`{.interpreted-text role="command"} \< `cyber_protect.attack_detection_delay_time`{.interpreted-text role="command"} then the victim will not be able to draw for cyber immunity to the attack.
:::
:::

::: command
scan_delay_time \<random-time-value\>

This specifies the amount of time it takes to perform a scan. This is the amount of time between the call to `WsfPlatform.CyberScan`{.interpreted-text role="method"} and when `WsfPlatform.CyberScanStatus`{.interpreted-text role="method"} returns a non-negative value. Any call to `WsfPlatform.CyberScanStatus`{.interpreted-text role="method"} that occurs before this time elapses will return a negative value, indicating the scan is in progress.

Default 0 secs
:::

::: command
delivery_delay_time \<random-time-value\>

This specifies the amount of time it would take to deliver an exploit. This is the amount of time between the call to `WsfPlatform.CyberAttack`{.interpreted-text role="method"} and when `WsfPlatform.CyberAttackStatus`{.interpreted-text role="method"} returns a non-negative value. Any call to `WsfPlatform.CyberAttackStatus`{.interpreted-text role="method"} that occurs before this time elapses will return a negative value, indicating the delivery is in progress.

Default 0 secs
:::



These commands specify the default probability threshold to be used if the corresponding value is not provided in the `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

::: command
probability_of_scan_detection \[ 0 .. 1 \]

This specifies the default value for `cyber_protect.probability_of_scan_detection`{.interpreted-text role="command"} of a corresponding `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

Default 0
:::

::: command
probability_of_scan_attribution \[ 0 .. 1 \]

This specifies the default value for `cyber_protect.probability_of_scan_attribution`{.interpreted-text role="command"} of a corresponding `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

Default 0
:::

::: command
probability_of_attack_success \[ 0 .. 1 \]

This specifies the default value for `cyber_protect.probability_of_attack_success`{.interpreted-text role="command"} of a corresponding `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

Default 1
:::

::: command
probability_of_status_report \[ 0 .. 1 \]

This specifies the default value for `cyber_protect.probability_of_status_report`{.interpreted-text role="command"} of a corresponding `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

Default 1
:::

::: command
probability_of_attack_detection \[ 0 .. 1 \]

This specifies the default value for `cyber_protect.probability_of_attack_detection`{.interpreted-text role="command"} of a corresponding `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

Default 0
:::

::: command
probability_of_attack_attribution \[ 0 .. 1 \]

This specifies the default value for `cyber_protect.probability_of_attack_attribution`{.interpreted-text role="command"} of a corresponding `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

Default 0
:::

::: command
probability_of_future_immunity \[ 0 .. 1 \]

This specifies the default value for `cyber_protect.probability_of_future_immunity`{.interpreted-text role="command"} of a corresponding `cyber_protect.attack_response`{.interpreted-text role="command"} in `cyber_protect`{.interpreted-text role="command"}.

Default 0
:::



The following commands define how often uniform random draws are performed. Each categorical use of random numbers within an attack type is controlled by a separate command.

::: {#draw_frequency.}
In each of the following commands *\<draw_frequency\>* can have the following values:
:::

-   always - A new random value is drawn for each evaluation.
-   once_per_simulation - A random value is drawn for the first evaluation in the simulation and used for all subsequent evaluations.
-   once_per_target - A random value is drawn for the first evaluation involving a specific target and is used for all subsequent evaluations involving the same target.
-   interval_per_simulation \<random-time-value\> - A random value is drawn if the simulation time since the last draw exceeds the threshold.
-   interval_per_target \<random-time-value\> A random value is draw if the simulation time since the last draw INVOLVING THE SAME TARGET exceeds the threshold.

::: command
scan_detection_draw_frequency \<[draw_frequency]()\>

The frequency of random draws for determining if a scan is detected.

Default always
:::

::: command
scan_attribution_draw_frequency \<[draw_frequency]()\>

The frequency of random draws for determining if a scan is attributed.

Default always
:::

::: command
attack_success_draw_frequency \<[draw_frequency]()\>

The frequency of random draws for determining if an attack is successful.

Default always
:::

::: command
status_report_draw_frequency \<[draw_frequency]()\>

The frequency of random draws for determining if a status report is to be made.

Default always
:::

::: command
attack_detection_draw_frequency \<[draw_frequency]()\>

The frequency of random draws for determining if an attack is detectable by the victim.

Default always
:::

::: command
attack_attribution_draw_frequency \<[draw_frequency]()\>

The frequency of random draws for determining if an attack is attributed by the victim.

Default always
:::

::: command
future_immunity_draw_frequency \<[draw_frequency]()\>

The frequency of random draws for determining if the victim will be immune to future attacks of the same type.

Default always
:::