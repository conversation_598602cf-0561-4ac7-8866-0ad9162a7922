# wsfsixdofguidancecomputer.rst.txt.md
orphan

:   





WsfSixDOF_GuidanceComputer is derived from `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"} and provides a similar set of controls for platforms that use `WSF_SIX_DOF_MOVER`{.interpreted-text role="model"} movers. While not all `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"} commands are supported, many are. This article describes differences between [WsfSixDOF_GuidanceComputer](#wsfsixdof_guidancecomputer) and `WsfGuidanceComputer`{.interpreted-text role="class"}.

::: warning
::: title
Warning
:::

Be sure to use `WSF_SIX_DOF_GUIDANCE_COMPUTER`{.interpreted-text role="model"} for platforms that include a `WSF_SIX_DOF_MOVER`{.interpreted-text role="model"}. `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"} operates only on `WSF_GUIDED_MOVER`{.interpreted-text role="model"} and will not have an effect on a `WSF_SIX_DOF_MOVER`{.interpreted-text role="model"}.
:::



::: method
void StopEngines()

This immediately terminates thrust.
:::



The following methods (inherited from `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"}) are not supported at this time.

::: method
void EjectStage() void EjectStage(double aPreSeparationCoastTime, double aPreIgnitionCoastTime)
:::

::: method
bool ClearCommandedThrottle() bool ClearCommandedThrottle(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_throttle`{.interpreted-text role="command"} (resumes the default throttle control in the mover).
:::

::: method
bool SetCommandedAltitude(double aValue) bool SetCommandedAltitude(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_altitude`{.interpreted-text role="command"} (meters above mean sea level).
:::

::: method
bool SetMaximumAscentRate(double aValue) bool SetMaximumAscentRate(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_ascent_rate`{.interpreted-text role="command"} (meters/second).
:::

::: method
bool SetMaximumDescentRate(double aValue) bool SetMaximumDescentRate(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_descent_rate`{.interpreted-text role="command"} (meters/second).
:::

::: method
bool SetMaximumPitchAngle(double aValue) bool SetMaximumPitchAngle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_pitch_angle`{.interpreted-text role="command"} (degrees).
:::

::: method
bool SetPitchChangeGain(double aValue) bool SetPitchChangeGain(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.pitch_change_gain`{.interpreted-text role="command"} (unitless).
:::

::: method
bool SetCommandedThrottle(double aValue) bool SetCommandedThrottle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_throttle`{.interpreted-text role="command"} in the range \[0..1\].
:::