orphan

:   

# default fluence_model {#Default_Fluence_Model}

::: block
fluence_model
:::

::: {.command id="default" block=""}
fluence_model default \... end_fluence_model

::: parsed-literal

fluence_model default

:   `base fluence model commands <fluence_model.commands>`{.interpreted-text role="ref"}

    [atmosphere_model]() \... [haze_model]() \...

end_fluence_model
:::

The default fluence model provides a representation of beam jitter, diffraction, atmospheric turbulence, and atmospheric extinction. It is based on the paper \"High Power Laser Propagation\" (<PERSON><PERSON>, Applied Optics, vol.15(6), 1479-1493). This model requires valid `atmospheric_coefficients`{.interpreted-text role="command"} for the given `fluence_model.wavelength`{.interpreted-text role="command"} or `fluence_model.laser_type`{.interpreted-text role="command"}, [atmosphere_model](), and [haze_model](). Currently these can be found in any WSF release (1.7.5 or later), in the directory hel_demo/atmosphere.
:::

## Commands

::: command
base fluence model commands

see `base fluence model commands <fluence_model.commands>`{.interpreted-text role="ref"}
:::

::: command
atmosphere_model \<integer-value\>

Specify the atmosphere model to use. Valid values are 1-6 and correspond with the following:

1.  Tropical Atmosphere
2.  Midlatitude Summer (default)
3.  Midlatitude Winter
4.  Subarctic Summer
5.  Subarctic Winter
6.  1976 U.S. Standard

::: note
::: title
Note
:::

Currently, all models are only supported for the 1064 nanometer wavelength; otherwise, only model 2 (Midlatitude Summer) is supported.
:::
:::

::: command
haze_model \<integer-value\>

Specify the haze model to use. Valid values are 1-5 and correspond with the following:

1.  RURAL Extinction, VIS = 23 km (Clear) (default)
2.  RURAL Extinction, VIS = 5 km (Hazy)
3.  Navy maritime Extinction
4.  MARITIME Extinction, VIS = 23 km
5.  Urban Extinction, VIS = 5 km

::: note
::: title
Note
:::

Currently, all models are only supported for the 1064 nanometer wavelength (laser_type nd_yag); otherwise, only model 1 (clear) is supported.
:::
:::
