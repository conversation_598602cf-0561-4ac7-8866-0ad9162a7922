orphan

:   

# WsfEM_XmtrRcvr

::: WsfEM_XmtrRcvr
WsfEM_XmtrRcvr is the base script class for `WsfEM_Xmtr`{.interpreted-text role="class"} and `WsfEM_Rcvr`{.interpreted-text role="class"}.
:::

## Methods

::: method
WsfEM_Antenna Antenna()

Returns the antenna part associated with the transmitter or receiver.
:::

::: method
WsfArticulatedPart ArticulatedPart()

Returns the articulated part associated with the transmitter or receiver.
:::

::: method
WsfEM_Attenuation AttenuationModel()

Returns the attenuation model associated with the transmitter or receiver, if applicable.
:::

::: method
WsfEM_Propagation PropagationModel()

Returns the propagation model associated with the transmitter or receiver, if applicable.
:::

::: method
double Bandwidth()

Returns the EM operating bandwidth (Hz).
:::

::: method
void SetBandwidth(double aBandwidth)

Sets the EM operating bandwidth (Hz).
:::

::: method
double Frequency()

Returns the current EM operating frequency (Hz).
:::

::: method
void SetFrequency(double aFrequency)

Sets the EM operating frequency (Hz).
:::

::: method
double Wavelength()

Returns the current EM operating wavelength (m).
:::

::: method
Array\<string\> PolarizationTypes()

Returns an array containing the string names of polarization types supported by this transmitter or receiver. The potential polarization types available in AFSIM are the following:

-   horizontal
-   vertical
-   slant_45
-   slant_135
-   left_cirular
-   right_circular
-   default
:::

::: method
string Polarization()

Returns the current EM operating polarization type.
:::

::: method
void SetPolarization(string aPolarizationType)

Sets the current EM operating polarization type.
:::

::: method
WsfAntennaPattern AntennaPattern(double aFrequency) WsfAntennaPattern AntennaPattern(double aFrequency, string aPolarizationType)

Returns the antenna pattern object associated with the provided frequency for this transmitter or receiver. If not specified, the polarization type is assumed to be the **default** type.
:::

::: method
double BeamTilt()

Returns the beam tilt, the elevation angle of the beam center above the horizontal plane. (degrees)
:::

::: method
void SetBeamTilt(double aTilt)

Sets the beam tilt, the elevation angle of the beam center above the horizontal plane. (degrees)
:::

::: method
double EarthRadiusMultiplier()

Returns the multiplicative factor associated with the earth\'s radius used by this transmitter or receiver.
:::

::: method
void SetEarthRadiusMultiplier(double aMultiplier)

Sets the multiplicative factor associated with the earth\'s radius used by this transmitter or receiver.
:::

::: method
bool CheckMasking()

Returns true if terrain and horizon line of sight masking is checked, false otherwise.
:::

::: method
void SetCheckMasking(bool aCheck)

When set to true, indicates that the receiver or transmitter will use terrain and horizon line of sight masking checks. When set to false, these checks will not be performed for the transmitter or receiver.
:::

::: method
string MaskingMode()

Returns the current terrain masking type.
:::

::: method
void SetMaskingMode(string aTerrainMaskingModeType)

Sets the mode or type of masking checks to perform. Valid terrain masking types are the following: (all other values will be ignored)

-   terrain_and_horizon
-   terrain_only
-   horizon_only
:::

::: method
double InternalLoss()

Returns the internal loss factor (dB) associated with various losses between the antenna and the receiver or transmitter.
:::

::: method
void SetInternalLoss(double aRatio)

Sets the internal loss factor (dB) associated with various losses between the antenna and the receiver or transmitter.
:::

::: method
int Index()

Returns the identifying index of this transmitter or receiver.
:::
