# wsftasklist.rst.txt.md
orphan

:   



::: WsfTaskList
`WsfTaskList`{.interpreted-text role="class"} is a container of references to `WsfTask`{.interpreted-text role="class"} objects. Task lists are returned by `WsfTaskManager::AssignedTaskList <WsfTaskManager.AssignedTaskList>`{.interpreted-text role="method"} and `WsfTaskManager::ReceivedTaskList <WsfTaskManager.ReceivedTaskList>`{.interpreted-text role="method"}.
:::



::: method
int Count()

Return the number of entries list.
:::

::: method
WsfTask Entry(int aIndex)

Return the entry at the given index, which must be in the range \[ 0 .. Count()-1 \].
:::

::: method
Iterator WsfTaskListIterator GetIterator()

Return an iterator that points to the beginning of the list. This is used by the script language to support the foreach command but may also be used directly.
:::