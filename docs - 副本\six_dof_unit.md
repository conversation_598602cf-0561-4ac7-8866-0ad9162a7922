orphan

:   

# Unit Formation

::: command
six_dof_unit
:::

::: parsed-literal

six_dof_unit \<name\>

:   debug detached offset \... member_platform \<platform-name\>

end_six_dof_unit
:::

## Overview

A unit formation can have no sub-formations, but are where the member platforms of a formation hierarchy are specified. Unit formations cannot be defined at the root level in input files, and cannot have subformations. Units can also be defined in script, see `WsfSixDOF_Formation`{.interpreted-text role="class"} and `WsfSixDOF_FormationManager`{.interpreted-text role="class"}. Units can be given commands, see `WsfSixDOF_FormationCommand`{.interpreted-text role="class"}.

::: {#Unit_Formation.SixDOF_Formation_Commands}
:::

## Member Platform

::: command
member_platform \<platform_name\>

Set the member platform of the unit formation to be the platform with the given name.
:::
