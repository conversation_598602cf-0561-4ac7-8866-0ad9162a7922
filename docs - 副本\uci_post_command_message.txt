# uci_post_command_message.rst.txt.md
orphan

:   





This message allows the user to command a `WSF_IRST_SENSOR`{.interpreted-text role="model"}.



::: method
string CommandUUID(int commandIndex)

This method returns the UUID of the command at the given index.
:::

::: method
static UCI_POST_CommandMessage Construct(UCI_POST_Command postCommand)

This method constructs an instance of an [UCI_POST_CommandMessage](#uci_post_commandmessage).
:::

::: method
UCI_LOS LOS(int commandIndex)

Returns the line of sight (LOS) information of the indexed command.
:::

::: method
void PushBack(UCI_POST_Command postCommand)

This method adds the given `UCI_POST_Command`{.interpreted-text role="class"} to the list of commands in the message.
:::