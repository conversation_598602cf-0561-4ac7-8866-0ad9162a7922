orphan

:   

# WsfTaskStatusMessage

::: {.WsfTaskStatusMessage .inherits .WsfMessage}
`WsfTaskStatusMessage`{.interpreted-text role="class"} is sent by `WsfTaskManager::TaskComplete <WsfTaskManager.TaskComplete>`{.interpreted-text role="method"} method when an assignee wants to report completion of an assigned task. It should normally be processed by the receiving task manager but can be examined by a script method that handles messages.
:::

## Methods

::: method
WsfPlatform Assigner()

Returns the platform that assigned the task.
:::

::: method
string AssignerName()

Returns the name of the platform that assigned the task.
:::

::: method
WsfPlatform Assignee()

Returns the platform that was assigned the task.
:::

::: method
string AssigneeName()

Returns the name of the platform that was assigned the task.
:::

::: method
string Status()

Returns the task\'s status.
:::

::: method
string SubStatus()

Returns the task\'s sub-status.
:::

::: method
WsfTrackId TrackId()

Returns the track Id associated with the task.
:::

::: method
string TaskType()

Returns the task type.
:::

::: method
string ResourceName()

Returns the resource name that was specified to be used for the task.
:::
