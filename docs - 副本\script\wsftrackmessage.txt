# wsftrackmessage.rst.txt.md
orphan

:   



::: {.WsfTrackMessage .inherits .WsfMessage cloneable="" constructible=""}
A `WsfTrackMessage`{.interpreted-text role="class"} is used to convey tracks between objects in the system. They are most often produced by sensors and track processors.
:::



::: method
WsfTrack Track()

Return the track contained in the message.
:::

::: method
void SetTrack(WsfTrack aTrack)

Set the track to the given track.
:::