# optical_signature.rst.txt.md
orphan

:   



::: {.command block=""}
optical_signature
:::

::: parsed-literal

optical_signature *\<signature-name\>* *(model-name)*

:   \... *model specific commands* \...

end_optical_signature
:::



`optical_signature`{.interpreted-text role="command"} defines the optical signature for a platform type. The optical signature is used when a sensor attempts to detect a platform and needs the projected area of the platform in the direction of the sensor.

\<signature-name\> is the name to be given to the signature. If the name specifies the name of an existing definition then the new definition will replace the current one (i.e. the last occurrence will be used for the simulation.)

(model-name) is an optional parameter that specifies the implementation model to be used for defining the signature. Available models are: