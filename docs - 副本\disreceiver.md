orphan

:   

# DisReceiver {#DisReceiver}

::: {.DisReceiver .inherits .DisPdu}
::: parsed-literal
`DisReceiver.EntityId`{.interpreted-text role="method"} `DisReceiver.RadioId`{.interpreted-text role="method"} `DisReceiver.ReceiverState`{.interpreted-text role="method"} `DisReceiver.ReceivedPower`{.interpreted-text role="method"} `DisReceiver.TransmitterEntityId`{.interpreted-text role="method"} `DisReceiver.TransmitterRadioId`{.interpreted-text role="method"}
:::
:::

## Overview

[DisReceiver](#disreceiver) is an implementation of the DIS receiver PDU. Receiver PDUs are used to communicate information about comm receive events.

## Methods

::: method
DisEntityId EntityId()

Returns the ID of the subject platform.
:::

::: method
int RadioId()

Returns an identifier for the radio on the receiving entity.
:::

::: method
int ReceiverState()

Returns the receiver\'s state index.
:::

::: method
double ReceivedPower()

Returns the power of the signal received.
:::

::: method
DisEntityId TransmitterEntityId()

Returns the ID of the transmitting platform.
:::

::: method
int TransmitterRadioId()

Returns an identifier for the radio on the transmitting entity.
:::
