orphan

:   

# Overview

In the WsfDraw Browser draw layers may be shown/hidden from the Map Display by checking/unchecking the individual layer or by checking \"Show All\". Draws can also be received from other connected simulations by checking the top checkbox.

![image](../images/wk_wsf_draw.png)

The browser can be put in \'extended-mode\' through the \'Extended Draw Browser\' option in the `Developer Menu<../warlock_developer_menu>`{.interpreted-text role="doc"}. When in extended-mode the contents of a given layer may be viewed.

::: note
::: title
Note
:::

Toggling the contents of a draw layer is for debugging purposes only. The state of a draw command will change under normal WsfDraw operation.
:::
