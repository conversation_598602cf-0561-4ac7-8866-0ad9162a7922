# six_dof_theory.rst.txt.md
orphan

:   





::: note
::: title
Note
:::

Theory behind the rigid-body 6DOF model (`WSF_RIGID_BODY_SIX_DOF_MOVER`{.interpreted-text role="model"}, `WsfRigidBodySixDOF_Mover`{.interpreted-text role="class"}) is identical to that of the antecedent P6DOF, and can be studied via the `p6dof_theory`{.interpreted-text role="doc"} article. When `WSF_P6DOF_MOVER`{.interpreted-text role="model"} is removed, that document will be folded into this one.
:::

The `WSF_POINT_MASS_SIX_DOF_MOVER`{.interpreted-text role="model"} is intended to bridge the gap between 3DOF models such as `WSF_GUIDED_MOVER`{.interpreted-text role="model"} and `WSF_AIR_MOVER`{.interpreted-text role="model"} and full 6DOF models such as `WSF_P6DOF_MOVER`{.interpreted-text role="model"} and `WSF_RIGID_BODY_SIX_DOF_MOVER`{.interpreted-text role="model"}.

Where 3DOF models are required to assume elements of their orientation, a full 6DOF model will propagate orientation according to conservation of angular momentum ($\Sigma M = I \dot{\omega}$). This approach is kinematically correct, but imposes data requirements which may not be practical. Specifically, the net moment requires accurate and timely knowledge of the distribution of force about the body, while the moment of inertia tensor requires knowledge of the of the distribution of mass throughout the body.

The PointMass SixDOF mover uses a effects-based system to estimate $\dot{\omega}$, composed as much as possible by rotation data that can be directly measured or assumed. Like similar models, the aerodynamic force coefficients for a PM6DOF model should represent a trim (zero net moment) state for a given airspeed and angle of attack. Rotation rates are either commanded by the active pilot (human-in-the-loop or otherwise) or are generated to mimic the effects of aerodynamic stability or instability.

Thrust magnitude calculations are identical to that used for the RigidBody model, but the nature of the kinematic model requires that thrust vectoring must also be effects-based. Additionally, thrust reversal is not currently considered for PointMass vehicles.



  --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  Symbol                                 Definition                                                                              SixDOF Command                                                                                                                                                                    SixDOF Script Method
  -------------------------------------- --------------------------------------------------------------------------------------- --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- ---------------------------------------------------------------------------------------
  $\alpha$                               angle of attack                                                                                                                                                                                                                                                           `GetAlpha<WsfSixDOF_Mover.GetAlpha>`{.interpreted-text role="method"}

  $\beta$                                angle of side slip                                                                                                                                                                                                                                                        `GetBeta<WsfSixDOF_Mover.GetBeta>`{.interpreted-text role="method"}

  $\alpha_{T}$                           total angle of attack                                                                                                                                                                                                                                                     `GetAlpha<WsfSixDOF_Mover.GetAlpha>`{.interpreted-text role="method"}

  $M$                                    Mach number                                                                                                                                                                                                                                                               `GetMach<WsfSixDOF_Mover.GetMach>`{.interpreted-text role="method"}

  $t$                                    time                                                                                                                                                                                                                                                                      

  $p$                                    body roll rate                                                                                                                                                                                                                                                            `GetRollRate<WsfSixDOF_Mover.GetRollRate>`{.interpreted-text role="method"}

  $q$                                    body pitch rate                                                                                                                                                                                                                                                           `GetPitchRate<WsfSixDOF_Mover.GetPitchRate>`{.interpreted-text role="method"}

  $r$                                    body yaw rate                                                                                                                                                                                                                                                             `GetYawRate<WsfSixDOF_Mover.GetYawRate>`{.interpreted-text role="method"}

  $\dot{p}$                              body roll acceleration                                                                                                                                                                                                                                                    

  $\dot{q}$                              body pitch acceleration                                                                                                                                                                                                                                                   

  $\dot{r}$                              body yaw acceleration                                                                                                                                                                                                                                                     

  $\vec{\omega}$                         rotational velocity vector                                                                                                                                                                                                                                                

  $\vec{\dot{\omega}}$                   rotational acceleration vector                                                                                                                                                                                                                                            

  $\vec{\dot{\omega}}_{max}$             total limit of angular acceleration from controls                                                                                                                                                                                                                         

  $\vec{\dot{\omega}}_{max, aero}$       limit of angular acceleration from aerodynamic controls                                                                                                                                                                                                                   

  $\vec{\dot{\omega}}_{max, aero_{0}}$   value of $\vec{\omega}_{max, aero}$ at dry weight under standard sea-level conditions                                                                                                                                                                                     

  $\vec{\dot{\omega}}_{max, prop}$       limit of angular acceleration from propulsive controls                                                                                                                                                                                                                    

  $\vec{\dot{\omega}}_{max, prop_{0}}$   value of $\vec{\omega}_{max, prop}$ at dry weight                                                                                                                                                                                                                         

  $\vec{\omega}_{n}$                     natural frequency of the stabilizing aerodynamic response                                                                                                                                                                                                                 

  $\vec{\omega}_{n_{0}}$                 value of $\vec{\omega}_{n}$ at dry weight under standard sea-level conditions                                                                                                                                                                                             

  $m$                                    current mass                                                                                                                                                                                                                                                              `GetCurrentWeight<WsfSixDOF_Mover.GetCurrentWeight>`{.interpreted-text role="method"}

  $m_{0}$                                empty mass                                                                              `mass`{.interpreted-text role="command"}                                                                                                                                          `GetEmptyWeight<WsfSixDOF_Mover.GetEmptyWeight>`{.interpreted-text role="method"}

  $\bold{I}$                             moment of inertia matrix                                                                `moment_of_inertia_ixx`{.interpreted-text role="command"}, `moment_of_inertia_iyy`{.interpreted-text role="command"}, `moment_of_inertia_izz`{.interpreted-text role="command"}   

  $\vec{M}_T$                            total moment                                                                                                                                                                                                                                                              

  $T$                                    engine thrust, dependent on the engine type                                                                                                                                                                                                                               

  $rho$                                  density of the atmosphere at the vehicle\'s current altitude                                                                                                                                                                                                              

  $rho_{0}$                              standard density of the atmosphere at the ellipsoid surface                                                                                                                                                                                                               
  --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------



As with the rigid-body models, linear motion is a second-order integral of acceleration, calculated according to conservation of momentum (`f_equals_ma`{.interpreted-text role="eq"}).

Orientation is similarly a second-order integral of angular acceleration, but unlike the rigid-body models, there is no real consideration for angular momentum in the PointMass mover - angular acceleration $\vec{\dot{\omega}}$ is simply built up by control capabilities and (de-)stabilizing effects.

The `p6dof_theory`{.interpreted-text role="doc"} document contains an in-depth discussion of how these differential equations are integrated (see `docs/p6dof_theory:Equations of Motion`{.interpreted-text role="ref"}), and we will not duplicate that here.



While the forces on a PM6DOF are summed in the same way as for an RB6DOF, the angular acceleration is built up in a way that abstracts away the actual torques on the vehicle.

Control response data does not need to be supplied for unguided munitions (bombs, rockets, decoys), but is required for a vehicle to be guided or piloted. Baseline angular acceleration limits are provided in user-defined tables with respect to Mach number, then modified to account for air density and angle-of-attack effects. The angular acceleration tables should be informed by reference data for the vehicle or class of vehicle, but can also be estimated kinematically.

As an example, consider a description of a reasonable fighter which specifies an ability at Mach 0.8 to accelerate from wings-level to a roll rate of 180 deg/s within one second. In this case, the peak acceleration should be specified at least 180 deg/s/s at Mach 0.8 - possibly higher to account for altitude and any stabilizing effects (see `SixDOF_Stabilizing_Effects`{.interpreted-text role="ref"} below).

::: note
::: title
Note
:::

How this changes with respect to Mach will vary per vehicle type, but a reasonable first-pass strategy is to scale the acceleration limit with the square of Mach, remembering that aerodynamic torques scale with the square of speed. Shifts in center of pressure or controllers which back down control authority into the transonic and supersonic regimes can be approximated by reducing maximum acceleration down from this initial estimate at those Mach numbers.
:::

[]{label="omega_dot_aero_limits

\\vec{\\dot{\\omega}}_{max, aero} = \\left( \\frac{\\rho}{\\rho_{0}} \\right) \\left( \\frac{m_{0}}{m} \\right) \\vec{\\dot{\\omega}}_{max, aero_{0}} \\cos{\\alpha_{T}}"}

These corrections account for reduced control efficacy at higher altitudes and at higher slip angles, and for changes in inertia as mass is added or removed.

A separate, second term is added to account for the effects of thrust vectoring given some amount of thrust.

[]{label="omega_dot_prop_limits

\\vec{\\dot{\\omega}}_{max, prop} &= \\left( \\frac{m_{0}}{m} \\right) \\left( \\frac{\\Delta \\vec{\\dot{\\omega}}_{prop}}{\\Delta T} \\right) T"}

Thrust itself is modeled as a function of altitude, so additional altitude effects are not considered. To account for increased agility as fuel is burned away, a mass factor is introduced.

[]{label="omega_dot_final_limits

\\vec{\\dot{\\omega}}_{max} &= \\vec{\\dot{\\omega}}_{max, aero} + \\vec{\\dot{\\omega}}_{max, prop}"}

The stick-and-rudder flight controls on a PM6DOF are mapped directly to rotational rate commands. This map is a 1D table defined by the user in script, which allows for linear or nonlinear mappings. The rotational rate commands are achieved via a linear ramp, with a slope limited by the angular acceleration limit.

[]{label="omega_dot_controls

\\vec{\\dot{\\omega}}_{desired} &= \\frac{\\vec{\\omega}_{cmd} - \\vec{\\omega}}{\\Delta t}"}



The impact of the stabilizing effect is most obvious on unguided projectiles. By default, a PM6DOF vehicle has no tendency to change its rotation rate absent some action by a pilot. This includes pointing into the wind, or \"weathervaning.\"

This effect can be introduced by adding tables that specify a baseline stabilizing frequency as a function of Mach number. These frequencies specify the critically damped system response of an uncontrolled vehicle as it returns to equilibrium. Currently, equilibrium states are 0 degrees angle-of-attack for the pitch axis, 0 degrees sideslip for the yaw axis, and 0 deg/s roll rate for the roll axis. Settings may be introduced in future releases to allow for modifications to the equilibrium state such as a nonzero angle of attack for aircraft, or a specified spin rate for projectiles.

As with the control acceleration data, the input parameters are modified to account for changes in loadout and operating air density.

[]{label="stabilizing_freq

{\\omega}_{n} = \\frac{m_{0}}{m} \\frac{\\rho}{\\rho_{0}} {\\omega}_{n_{0}}"}

The response frequency is then translated into a rotational acceleration for the pitch and yaw axes:

[]{label="stabilizing_accel_pitch_yaw

\\dot{q} = (0 - \\alpha) {\\omega}^{2}_{n_{pitch}} - 2 {\\omega}_{n_{pitch}} \\dot{\\alpha}

\\dot{r} = (0 - \\beta) {\\omega}^{2}_{n_{yaw}} - 2 {\\omega}_{n_{yaw}} \\dot{\\beta}"}

For the roll axis, we are affecting rotation *rate* rather than rotation, so a first-order lag system is employed:

[]{label="stabilizing_accel_roll

f &= \\frac{\\omega_{roll} \\Delta t}{1 + \\omega_{n_{roll}} \\Delta t}

p_{t+\\Delta t} &= (1 - f) p_{t}

\\dot{p} &= \\frac{p_{t+\\Delta t} - p_{t}}{\\Delta t}"}

To avoid overshoot and numerical instability, these accelerations are limited based on kinematic extrapolations:

[]{label="stabilizing_accel_limits

\\dot{p}_{max} &= \\left| \\frac{-p}{\\Delta t} \\right|

\\dot{q}_{max} &= \\left| \\frac{2}{\\Delta t^{2}} (-\\alpha - \\dot{\\alpha} \\Delta t) \\right|

\\dot{r}_{max} &= \\left| \\frac{2}{\\Delta t^{2}} (-\\beta - \\dot{\\beta} \\Delta t) \\right|"}

The final rotational acceleration for a PM6DOF is the sum of the control acceleration and stabilizing accelerations.