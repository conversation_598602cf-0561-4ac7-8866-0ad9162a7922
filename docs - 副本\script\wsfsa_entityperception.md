orphan

:   

# WsfSA_EntityPerception

## Overview {#overview .WsfSA_EntityPerception constructible="" cloneable=""}

The `WsfSA_EntityPerception`{.interpreted-text role="class"} provides a representation of a perceived entity, which may be a platform (asset) or track (bogie/bandit). Entity perceptions are a key component of the situation awareness component of the cognitive model, provided by the `WsfSA_Processor`{.interpreted-text role="class"}.

Entity perceptions are used within the \"perception\" and \"assessment\" modules of the `WsfSA_Processor`{.interpreted-text role="class"} (SAP). The SAP uses these entity perceptions to form various lists.

The first group of lists involve assets (friendly), bogies (unknown), and bandits (enemy). These lists are not prioritized, although they may be limited due to range and/or scoring parameters when limited perception is enabled.

The next group of lists provide an assessment of the prioritized threats and targets, which are composed of bandits and bogies. These lists are prioritized, from highest threat to lowest threat and from the best target to the least attractive target. Threats are perceived entities that present an adverse condition (a threat) to the platform, while targets are perceived entities that the platform may wish to attack.

In addition, the SAP provides lists of perceived groups (`WsfSA_Group`{.interpreted-text role="class"}), and each group may contain a list of `WsfSA_EntityPerception`{.interpreted-text role="class"} that make up the \"elements\" of the group.

Entity perceptions may be perceived as a missile. The default assumption, however, is that an entity is an aircraft. Thus, it is quite possible that an entity may actually be a missile, but is not yet perceived as such.

## Methods

::: method
int EntityPlatformIndex()

Returns the index of the entity platform. This may be zero (0) if the true platform is unknown.
:::

::: method
WsfTrack Track()

Returns the WsfTrack of the group. Use `IsValid() <Object>`{.interpreted-text role="class"} to verify value returned.
:::

::: method
double Lat()

Returns the latitude (deg) of the entity.
:::

::: method
double Lon()

Returns the longitude (deg) of the entity.
:::

::: method
double Altitude()

Returns the altitude (meters) of the entity.
:::

::: method
double Bearing()

Returns the bearing (deg) of the entity.
:::

::: method
double Speed()

Returns the speed (meters/sec) of the entity.
:::

::: method
double Heading()

Returns the heading (deg) of the entity.
:::

::: method
double ThreatLevel()

Returns the threat level of the entity. This is a normalized value between 0.0 and 1.0.
:::

::: method
double TargetValue()

Returns the target value of the entity. This is a normalized value between 0.0 and 1.0.
:::

::: method
double Risk()

Returns the risk posed by the entity. This is a normalized value between 0.0 and 1.0.
:::

::: method
double Defensiveness()

Returns the defensiveness induced by the entity. This is a normalized value between 0.0 and 1.0.
:::

::: method
double Urgency()

Returns the sense of urgency imposed by the entity. This is a normalized value between 0.0 and 1.0.
:::

::: method
string IdString()

Returns the id string of the entity.
:::

::: method
int PerceivedIndex()

Returns the perceived platform index of the entity.
:::

::: method
string PerceivedName()

Returns the perceived platform name of the entity.
:::

::: method
string PerceivedType()

Returns the perceived platform type of the entity.
:::

::: method
int FlightId()

Returns the flight id of the entity.
:::

::: method
string IdFlag()

Returns the id flag of the entity.
:::

::: method
int Identification()

Returns the identification of the entity.
:::

  Value   Identification
  ------- ----------------
  0       <USER>
  <GROUP>       Friendly
  2       Neutral
  3       Bogie
  4       Bandit

::: method
bool FriendlyAsset()

Returns true if the entity is a friendly asset.
:::

::: method
bool AngleOnly()

Returns true if the entity is defined by an angle-only track.
:::

::: method
bool IdIffFriend()

Returns true if the entity has been identified as friendly via IFF.
:::

::: method
bool IdIffFoe()

Returns true if the entity has been identified as foe (enemy) via IFF.
:::

::: method
bool IdIffNeutral()

Returns true if the entity has been identified as neutral via IFF.
:::

::: method
bool IdIffUnknown()

Returns true if the entity has not been identified via IFF.
:::

::: method
bool IdAuxFriend()

Returns true if the entity has been identified as friendly via aux data.
:::

::: method
bool IdAuxFoe()

Returns true if the entity has been identified as foe (enemy) via aux data.
:::

::: method
bool IdAuxNeutral()

Returns true if the entity has been identified as neutral via aux data.
:::

::: method
bool IdAuxUnknown()

Returns true if the entity has not been identified via aux data.
:::

::: method
bool IdSideFriend()

Returns true if the entity has been identified as friendly via side data.
:::

::: method
bool IdSideFoe()

Returns true if the entity has been identified as foe (enemy) via side data.
:::

::: method
bool IdSideNeutral()

Returns true if the entity has been identified as neutral via side data.
:::

::: method
bool IdSideUnknown()

Returns true if the entity has not been identified via side data.
:::

::: method
bool IdTypeFriend()

Returns true if the entity has been identified as friendly via type data.
:::

::: method
bool IdTypeFoe()

Returns true if the entity has been identified as foe (enemy) via type data.
:::

::: method
bool IdTypeNeutral()

Returns true if the entity has been identified as neutral via type data.
:::

::: method
bool IdTypeUnknown()

Returns true if the entity has not been identified via type data.
:::

::: method
bool IsHostile()

Returns true if the entity has been identified as hostile.
:::

::: method
bool IsMissile()

Returns true if the entity is perceived as a missile.
:::

::: method
bool AltitudeValid()

Returns true if the entity altitude is valid.
:::

::: method
bool SpeedValid()

Returns true if the entity speed is valid.
:::

::: method
bool HeadingValid()

Returns true if the entity heading is valid.
:::

::: method
double SelectionScore()

Returns the current selection score of the entity.
:::

::: method
double EstimatedTimeToIntercept(WsfPlatform aTarget)

Returns the estimated time for this entity to intercept the specified target platform. If no intercept is possible, -1.0 is returned. If the entity is angle-only, or if it lacks speed data, no intercept will be calculated and -1.0 will be returned.
:::
