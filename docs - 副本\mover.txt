# mover.rst.txt.md
orphan

:   



Script Class: `WsfMover`{.interpreted-text role="class"}

::: {.command block=""}
mover \... end_mover

::: parsed-literal

mover [Mover Types](#mover-types) \...

:   \... `Platform_Part_Commands`{.interpreted-text role="ref"} \... [update_interval]() \<time-reference\> [update_time_tolerance]() \<time-reference\>

end_mover
:::
:::

A mover is a `Platform_Subsystem`{.interpreted-text role="ref"} which defines the domain in which a platform can move and how it moves within the domain. In other words, each mover type definition defines how a platform behaves in movement during simulation runtime. Several types of predefined movers exist in WSF that can be placed on a platform. A platform may only have one type of mover defined. A base mover definition may be defined external to a platform in order to be used by several platform_types.



See `Predefined_Mover_Types`{.interpreted-text role="ref"} for more information on each class of [mover](#mover).



::: command
update_interval \<time-reference\>

If non-zero, specifies a periodic time interval at which the simulation will call the mover. If zero then the mover will be called only when it is necessary to determine the position of the containing platform.

Default: 0 seconds unless overridden by the specific mover implementation.
:::

::: command
update_time_tolerance \<time-reference\>

When a position update is requested by the simulation, if the time since the previous update is less than or equal to this value then the mover will ignore the update.

Default: Most mover implementations define this as the time it takes to travel 1 meter at some nominal velocity that is appropriate for the implementation.

::: note
::: title
Note
:::

A mover implementation may choose to ignore this command.
:::
:::