# wsfcybermitmmessage.rst.txt.md
orphan

:   



::: {.WsfCyberMitmMessage constructible=""}
WsfCyberMitmMessage is used to represent a message that has been manipulated by a Man-in-the-Middle attack.
:::

::: method
static WsfCyberMitmMessage Construct(WsfMessage aMessage, double aDelay, bool aDrop)

Static method to create a WsfCyberMitmMessage using a specified WsfMessage, a delay time, and whether the message should be dropped.
:::



::: method
WsfMessage GetMessage()

Returns the message to be sent. If a message was not set at construction or by SetMessage(WsfMessage aMessage) the returned value is not valid.
:::

::: method
double GetDelay()

Returns the amount of simulation time to delay the message.
:::

::: method
bool GetDrop()

Returns true if the message will be dropped, otherwise false.
:::

::: method
void SetMessage(WsfMessage aMessage)

Sets the message to be sent.
:::

::: method
void SetDelay(double aDelay)

Sets the amount of time to delay the message.
:::

::: method
void SetDrop(double aDrop)

Sets whether the message should be dropped.
:::