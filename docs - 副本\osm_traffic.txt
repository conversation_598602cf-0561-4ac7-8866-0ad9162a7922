# osm_traffic.rst.txt.md
orphan

:   



::: {.command block=""}
osm_traffic \... end_osm_traffic
:::

Script Class: `WsfOSM_Traffic`{.interpreted-text role="class"}

::: parsed-literal

[osm_traffic](#osm_traffic)

:   [debug]() \<boolean-value\> [network]() \<route-network-name\> [vehicle_count]() \<positive-integer\> [maximum_speed]() \<speed-value\>

    > [vehicle]() \<platform-type\>
    >
    > :   [fraction]() \<real-value\> [mean_speed]() \<speed-value\> [sigma_speed]() \<speed-value\> [ignore_speed_limits]()
    >
    > end_vehicle
    >
    > [route_aux_data_mapping]()
    >
    > :   [minimum_speed]() \<string\> [maximum_speed]() \<string\> [lanes]() \<string\> [one_way]() \<string\> [density]() \<string\> [surface]() \<string\>
    >
    > end_route_aux_data_mapping
    >
    > [waypoint_aux_data_mapping]()
    >
    > :   [stop]() \<string\> [traffic_signals]() \<string\> [highway]() \<string\>
    >
    > end_waypoint_aux_data_mapping

    end_network

end_osm_traffic
:::



The osm_traffic command generates background road traffic via ground moving platforms during the simulation on a predefined `route_network`{.interpreted-text role="command"}. This capability is similar to the `road_traffic`{.interpreted-text role="command"} command; the primary difference between the two is that [osm_traffic](#osm_traffic) is designed to be used with `route_network`{.interpreted-text role="command"}s generated by *Wizard\'s OSM Converter Tool*. This converter allows for additional `route`{.interpreted-text role="command"} commands to be utilized in the generation of the traffic via the `route.aux_data`{.interpreted-text role="command"} as further described below.



Generated background traffic vehicles will adhere to the general rules of the road as specified in the `route.aux_data`{.interpreted-text role="command"}, and are responsible for the following behaviors.

-   Stopping accordingly at traffic lights and stop signs
-   Lining up at intersections when traffic becomes congested
-   Observing minimum and maximum speed limits ([minimum_speed]() and [maximum_speed]())
-   Attempting to avoid collisions by changing lanes and speeds as necessary

Road data such as speed limits and traffic lights should be added through the route and waypoint aux_data sections, then mapped to the osm_traffic command via the [route_aux_data_mapping]() and [waypoint_aux_data_mapping]() blocks.

::: note
::: title
Note
:::

[osm_traffic](#osm_traffic) modifies the routes and their associated names to provide a more realistic representation of the routes as roads. The rules, naming conventions and an example route name are provided below. The described suffixes are computer generated, and appear in the order they are listed. `Script methods <WsfOSM_Traffic>`{.interpreted-text role="class"} are available for routes to simplify access lookups.

-   Intersections are modeled visually as small blank spaces without any routes running through them. When a single route passes through multiple intersections, it is split into several parts, designated by double underscores followed by a numeric \"segment\" suffix. The segments are numbered in the order of traversal in the original route input.
-   Most roads are bidirectional, but only a single route input is provided. As a result, when necessary, [osm_traffic](#osm_traffic) internally creates new routes going in the opposite direction. These are designated by a double underscore followed by either a \"normal\" or \"opposite\" suffix, with \"normal\" referring to the input route\'s original direction.
-   Routes with X lanes are split into X different routes (up to ten lanes). Even when there is only one lane, every route name has a numeric suffix that designates which lane it represents.
-   Example: \"Lackland_Road\_\_1\_\_normal2\", which means Lackland_Road, segment 1, 2nd lane going in the normal direction.
:::

[osm_traffic](#osm_traffic) is also compatible with scripted vehicles. For scripted vehicles, it is the user\'s decision to follow (or ignore) the rules of the road. However, as long as the associated commands are used, the background traffic will be aware of these added vehicles, and will attempt to avoid rear collisions with them when possible. More information regarding the [osm_traffic](#osm_traffic) script methods can be found at `WsfOSM_Traffic`{.interpreted-text role="class"}.

::: note
::: title
Note
:::

Currently route_networks will not be drawn on the map display. A simple example is provided below that demonstrates how to visualize the route network.
:::



::: command
debug \<boolean-value\>

Enables or disables debugging output to command line (standard out). Provides more verbose information for debugging when true.

Default false
:::

::: {.command block=""}
network \<route-network-name\> \... end_network

Defines the attributes and vehicles that are generated on the defined `route_network`{.interpreted-text role="command"} as specified by the \<route-network-name\>.

::: command
vehicle_count \<positive-integer\>

Specifies the number of vehicles that will be generated on the route network.
:::

::: command
maximum_speed \<speed-value\>

The maximum speed any vehicle on the network can travel even if a faster speed is specified in the vehicle block. Generally, it will be more effective to define speed limits for individual routes or to define a maximum speed on the ground mover itself.
:::

::: {.command block=""}
vehicle \<platform-type\> end_vehicle

Defines the attributes of a specific vehicle type within the context of this network. To define multiple vehicle types, create multiple vehicle blocks. The \<platform-type\> must be a previously defined platform_type.

::: command
fraction \<real-value\>

If multiple vehicle blocks are defined, the fraction is the percentage (as a decimal value) of this specific vehicle type that would populate the road network. The sum of all the vehicle fractions must add up to 1.
:::

::: command
mean_speed \<speed-value\>

The average speed of all the vehicles defined by this vehicle block. This value will only be used when no speed limits ([maximum_speed]() or [minimum_speed]()) are defined on a particular route or if the [ignore_speed_limits]() flag is set.
:::

::: command
sigma_speed \<speed-value\>

The speed magnitude of one standard deviation. This command follows same conditions as [mean_speed]().
:::

::: command
ignore_speed_limits

Indicates that vehicles of the specified type do not need to follow the speed limits defined by [maximum_speed]() or [minimum_speed]()
:::
:::

::: end
:::

::: {.command block=""}
route_aux_data_mapping \... end_route_aux_data_mapping

This block applies to all routes in the `route_network`{.interpreted-text role="command"}, and maps [osm_traffic](#osm_traffic) route aux_data variable names to the rules of the road. The parameter for each command is simply a string representation of the variables\' name as defined in the route aux_data input blocks.

::: note
::: title
Note
:::

Ensure that the naming conventions of desired features is consistent across all of the routes in the route_network (i.e. the aux_data variable name for [maximum_speed]() should be the same across all routes).
:::

::: command
minimum_speed \<aux-data-variable-name\>

Maps an aux_data numeric variable to the route\'s minimum speed.

::: note
::: title
Note
:::

If a [minimum_speed]() string is not found on the route, then minimum speed will be half the [maximum_speed]() if it is found instead.
:::

Default minspeed
:::

::: command
maximum_speed \<aux-data-variable-name\>

Maps an aux_data numeric variable to the route\'s maximum speed.

::: note
::: title
Note
:::

If a [maximum_speed]() string is not found on the route, then maximum speed will be double the [minimum_speed]() if it is found instead.
:::

Default maxspeed
:::

::: command
lanes \<aux-data-variable-name\>

Maps an aux_data integer variable to the number of lanes in the route. This defines the number of lanes traveling in both directions, unless otherwise specified. By default, on a two-way road, there will be two lanes (one for each direction).

Default lanes
:::

::: command
one_way \<aux-data-variable-name\>

Maps an aux_data boolean variable to whether a road is one way or not. This will also affect how lanes are interpreted.

Default oneway
:::

::: command
density \<aux-data-variable-name\>

Maps an aux_data integer variable to a road\'s average vehicle density.

::: note
::: title
Note
:::

This input will only apply to background vehicles; any additional scripted vehicles will not be considered when populating and traveling through roads.
:::

Default density
:::

::: command
surface \<aux-data-variable-name\>

Maps an aux_data string variable to a road\'s surface type.

::: note
::: title
Note
:::

This aux_data field is currently not utilized by the [osm_traffic](#osm_traffic) generated traffic.
:::

Default surface
:::
:::

::: end
:::

::: {.command block=""}
waypoint_aux_data_mapping \... end_waypoint_aux_data_mapping

This block applies to all waypoints in the `route_network`{.interpreted-text role="command"}, and maps the osm_traffic waypoint aux_data variables names to the rules of the road.

::: note
::: title
Note
:::

The parameter usage and naming convention rules are the same as in the [route_aux_data_mapping]() block.
:::

::: warning
::: title
Warning
:::

The aux_data will be ignored unless the waypoint has a `route.node_id`{.interpreted-text role="command"}; stop signs and traffic lights are inconsequential when there are no connected roads.
:::

::: command
stop \<aux-data-variable-name\>

Maps an aux_data boolean variable to whether a waypoint is a stop sign.

::: note
::: title
Note
:::

By default, the waypoint will not be treated as a stop sign.
:::

Default stop
:::

::: command
traffic_signals \<aux-data-variable-name\>

Maps an aux_data boolean variable to whether a waypoint has a traffic light.

::: note
::: title
Note
:::

By default, there will be no traffic light.
:::

Default traffic_signals
:::

::: command
highway \<aux-data-variable-name\>

Maps an aux_data boolean variable to highway description / type.

::: note
::: title
Note
:::

By default, there will be no highway.
:::

Default highway
:::
:::

::: end
:::
:::



::: parsed-literal

[osm_traffic](#osm_traffic)

:   

    [network]() osm_netowrk

    :   [vehicle_count]() 500 [maximum_speed]() 75 mi/h [vehicle]() Car [fraction]() 0.80 [mean_speed]() 60 mi/h [sigma_speed]() 5 mi/h end_vehicle [vehicle]() Pickup_Truck [fraction]() 0.15 [mean_speed]() 60 mi/h [sigma_speed]() 10 mi/h end_vehicle [vehicle]() School_Bus [fraction]() 0.05 [mean_speed]() 50 mi/h [sigma_speed]() 10 mi/h end_vehicle

    end_network

end_osm_traffic
:::



::: parsed-literal
\# Simple script to visualize the road network \# the string literals may change based on what the aux_data variable and network names actually are script void DrawRoads() WsfDraw draw = WsfDraw(); WsfRouteNetwork network = WsfRouteNetwork.FindRouteNetwork(\"network_name\"); WsfOSMTraffic traffic = WsfOSMTraffic.GetTraffic(); Array\<WsfRoute\> trafficRoutes = traffic.GetTrafficRoutes(\"network_name\");

> foreach (WsfRoute rte in trafficRoutes) { WsfRoute route = rte.Copy(); route.SetAltitude(10); draw.SetColor(.93,.51,.93); draw.SetLayer(route.Name()); draw.SetId(route.Name()); draw.SetLineSize(2); draw.BeginPolyline(); draw.VertexRoute(route); draw.End(); draw.SetId(route.Name()); draw.SetTextSize(6); draw.SetColor(0,0,0); \# If aux_data should be listed on the map, uncomment and add to this section \# if (route.AuxDataExists(\"maxspeed\") \|\| route.AuxDataExists(\"name\")) \# { \# string name = (string)route.AuxDataInt(\"name\"); \# string speedLimit = (string)route.AuxDataDouble(\"maxspeed\") + \" mph\"; \# draw.BeginText(name); \# } draw.BeginText(route.Name()); WsfGeoPoint textPos = route.Waypoint(0).Location(); textPos.SetAltitudeAGL(30); draw.Vertex(textPos); #draw.Vertex(route.Waypoint(0).Location()); draw.End(); draw.SetColor(0,0,0); draw.SetLineSize(1); draw.BeginLines(); draw.Vertex(route.Waypoint(0).Location()); draw.Vertex(textPos); draw.End(); }

end_script

\# Simple script to draw colored shapes where stop signs and traffic lights are located script void DrawStops() WsfDraw draw = WsfDraw(); WsfRouteNetwork network = WsfRouteNetwork.FindRouteNetwork(\"network_name\"); foreach (WsfRoute rte in network.Routes()) { WsfRoute route = rte.Copy(); foreach (WsfWaypoint node in route) { if (node.AuxDataExists(\"stop_sign\") && node.AuxDataBool(\"stop_sign\")) { draw.SetColor(1,0,0); draw.SetEllipseMode(\"fill\"); draw.BeginCircle(0, 100); draw.VertexLLA(node.Latitude(), node.Longitude(), 0); draw.End(); }

> if (node.AuxDataExists(\"traffic_signals\") &&
>
> :   node.AuxDataBool(\"traffic_signals\"))
>
> {
>
> :   draw.SetColor(1,1,0); draw.SetEllipseMode(\"fill\"); draw.BeginCircle(0, 100); draw.VertexLLA(node.Latitude(), node.Longitude(), 0); draw.End();
>
> ### }

end_script
:::