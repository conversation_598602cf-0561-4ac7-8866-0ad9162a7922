orphan

:   

# Predefined Network Types {#Predefined_Network_Types}

-   `WSF_COMM_NETWORK_AD_HOC`{.interpreted-text role="model"}
-   `WSF_COMM_NETWORK_DIRECTED_RING`{.interpreted-text role="model"}
-   `WSF_COMM_NETWORK_GENERIC`{.interpreted-text role="model"}
-   `WSF_COMM_NETWORK_MESH`{.interpreted-text role="model"}
-   `WSF_COMM_NETWORK_MESH_LEGACY`{.interpreted-text role="model"}
-   `WSF_COMM_NETWORK_P2P`{.interpreted-text role="model"}
-   `WSF_COMM_NETWORK_RING`{.interpreted-text role="model"}
-   `WSF_COMM_NETWORK_STAR`{.interpreted-text role="model"}
