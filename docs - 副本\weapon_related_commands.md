orphan

:   

# Weapon-Related Commands

## Defining A Weapon

-   `weapon`{.interpreted-text role="command"} (`Predefined_Weapon_Types`{.interpreted-text role="ref"})
-   `weapon_effects`{.interpreted-text role="command"} (`Predefined_Weapon_Effect_Types`{.interpreted-text role="ref"})
-   `aero`{.interpreted-text role="command"}
-   `launch_computer`{.interpreted-text role="command"} (`Predefined_Launch_Computer_Types`{.interpreted-text role="ref"})
