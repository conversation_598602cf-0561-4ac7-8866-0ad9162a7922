# wsfp6dof_guidancecomputer.rst.txt.md
orphan

:   





WsfP6DOF_GuidanceComputer is derived from `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"} and provides a similar set of controls for platforms that use `WSF_P6DOF_MOVER`{.interpreted-text role="model"} movers.

::: warning
::: title
Warning
:::

Be sure to use `WSF_P6DOF_GUIDANCE_COMPUTER`{.interpreted-text role="model"} for platforms that include a `WSF_P6DOF_MOVER`{.interpreted-text role="model"}. Use of a `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"} with a `WSF_P6DOF_MOVER`{.interpreted-text role="model"} will not result in proper guidance and control.
:::



::: method
void SelectPhase(string aPhaseName)

Immediately terminates the current phase and initiates the specified phase. The `WSF_P6DOF_GUIDANCE_COMPUTER.on_exit`{.interpreted-text role="command"} script block in the current phase is executed, then the `WSF_P6DOF_GUIDANCE_COMPUTER.on_entry`{.interpreted-text role="command"} script block is executed in the selected phase.
:::

::: method
bool SetGuidanceDelay(double aValue) bool SetGuidanceDelay(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.guidance_delay`{.interpreted-text role="command"} (in seconds).
:::

::: method
bool SetMaximumCommandedGees(double aValue) bool SetMaximumCommandedGees(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.maximum_commanded_g`{.interpreted-text role="command"} (G\'s: i.e.: a value of 1.0 is 1 g).
:::

::: method
void StopEngines()

This immediately terminates thrust.
:::



::: method
bool SetProportionalNavigationGain(double aValue) bool SetProportionalNavigationGain(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.proportional_navigation_gain`{.interpreted-text role="command"} (unitless).
:::

::: method
bool SetProportionalNavigationLimitAngle(double aValue) bool SetProportionalNavigationLimitAngle(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.proportional_navigation_limit_angle`{.interpreted-text role="command"} (degrees).
:::

::: method
bool SetProportionalNavigationMethod(double aValue) bool SetProportionalNavigationMethod(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.proportional_navigation_method`{.interpreted-text role="command"} (\"pure\" or \"augmented\").
:::



::: method
bool SetVelocityPursuitGain(double aValue) bool SetVelocityPursuitGain(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.velocity_pursuit_gain`{.interpreted-text role="command"} (unitless).
:::



::: method
bool SetGeeBias(double aValue) bool SetGeeBias(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.g_bias`{.interpreted-text role="command"} (unitless).
:::

::: method
bool SetLateralGeeBias(double aValue) bool SetLateralGeeBias(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.lateral_g_bias`{.interpreted-text role="command"} (unitless).
:::

::: method
bool SetCommandedAzimuthOffset(double aValue) bool SetCommandedAzimuthOffset(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.commanded_azimuth_offset`{.interpreted-text role="command"} (degrees).
:::



::: method
bool SetGuidanceTarget(string aValue) bool SetGuidanceTarget(string aPhaseName, string aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.guidance_target`{.interpreted-text role="command"} (\"truth\", \"perception\", \"predicted_intercept\", \"default\").
:::

::: method
bool SetAimpointAltitudeOffset(double aValue) bool SetAimpointAltitudeOffset(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.aimpoint_altitude_offset`{.interpreted-text role="command"} (meters).
:::

::: method
bool SetAimpointAzimuthOffset(double aValue, string aDirection) bool SetAimpointAzimuthOffset(string aPhaseName, double aValue, string aDirection)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.aimpoint_azimuth_offset`{.interpreted-text role="command"} (degrees). aDirection must be \"left\", \"right\" or \"either\".
:::

::: method
bool SetAimpointRangeOffset(double aValue) bool SetAimpointRangeOffset(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.aimpoint_range_offset`{.interpreted-text role="command"} (meters).
:::

::: method
bool SetAimpointEvaluationInterval(double aValue) bool SetAimpointEvaluationInterval(string aPhaseName, double aValue)

Sets the value of `WSF_P6DOF_GUIDANCE_COMPUTER.aimpoint_evaluation_interval`{.interpreted-text role="command"} (seconds).
:::



The following methods (inherited from `WSF_GUIDANCE_COMPUTER`{.interpreted-text role="model"}) are not supported at this time.

::: method
void EjectStage() void EjectStage(double aPreSeparationCoastTime, double aPreIgnitionCoastTime)
:::

::: method
bool SetAllowRouteFollowing(bool aValue) bool SetAllowRouteFollowing(string aPhaseName, bool aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.allow_route_following`{.interpreted-text role="command"} (true or false).
:::

::: method
bool ClearCommandedAltitude() bool ClearCommandedAltitude(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_altitude`{.interpreted-text role="command"}.
:::

::: method
bool ClearCommandedSpeed() bool ClearCommandedSpeed(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_speed`{.interpreted-text role="command"} or `WSF_GUIDANCE_COMPUTER.commanded_mach`{.interpreted-text role="command"}.
:::

::: method
bool ClearCommandedFlightPathAngle() bool ClearCommandedFlightPathAngle(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_flight_path_angle`{.interpreted-text role="command"}.
:::

::: method
bool ClearCommandedThrottle() bool ClearCommandedThrottle(string aPhaseName)

Clears the `WSF_GUIDANCE_COMPUTER.commanded_throttle`{.interpreted-text role="command"} (resumes the default throttle control in the mover).
:::

::: method
bool SetCommandedAltitude(double aValue) bool SetCommandedAltitude(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_altitude`{.interpreted-text role="command"} (meters above mean sea level).
:::

::: method
bool SetCommandedAltitudeAGL(double aValue) bool SetCommandedAltitudeAGL(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_altitude`{.interpreted-text role="command"} (meters above ground level).
:::

::: method
bool SetMaximumAscentRate(double aValue) bool SetMaximumAscentRate(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_ascent_rate`{.interpreted-text role="command"} (meters/second).
:::

::: method
bool SetMaximumDescentRate(double aValue) bool SetMaximumDescentRate(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_descent_rate`{.interpreted-text role="command"} (meters/second).
:::

::: method
bool SetMaximumPitchAngle(double aValue) bool SetMaximumPitchAngle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.maximum_pitch_angle`{.interpreted-text role="command"} (degrees).
:::

::: method
bool SetPitchChangeGain(double aValue) bool SetPitchChangeGain(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.pitch_change_gain`{.interpreted-text role="command"} (unitless).
:::

::: method
bool SetCommandedFlightPathAngle(double aValue) bool SetCommandedFlightPathAngle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_flight_path_angle`{.interpreted-text role="command"} (degrees).
:::

::: method
bool SetCommandedMach(double aValue) bool SetCommandedMach(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_mach`{.interpreted-text role="command"} (unitless Mach number).
:::

::: method
bool SetCommandedSpeed(double aValue) bool SetCommandedSpeed(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_speed`{.interpreted-text role="command"} (meters/second).
:::

::: method
bool SetCommandedThrottle(double aValue) bool SetCommandedThrottle(string aPhaseName, double aValue)

Sets the value of `WSF_GUIDANCE_COMPUTER.commanded_throttle`{.interpreted-text role="command"} in the range \[0..1\].
:::