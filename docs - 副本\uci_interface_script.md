orphan

:   

# UCI_Interface

## Overview {#overview .UCI_Interface}

This is a helper class to retrieve interface data.

## Static Methods

::: method
static string GenerateRandomUUID()

Returns a randomly generated UUID from the interface.
:::

::: method
static WsfSensor GetSensorFromCapabilityID(string capabilityID)

Returns the sensor with the given capability. Returns null if capability is unknown on the interface.
:::
