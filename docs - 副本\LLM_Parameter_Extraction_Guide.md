# LLM参数提取功能使用指南

## 概述

AFSIM Platform Agent现在支持使用大语言模型（LLM）来智能提取Platform参数信息。相比传统的正则表达式方法，LLM方法能够：

- 🧠 **智能理解文档内容**：理解参数的语义和上下文
- 📝 **提取详细信息**：包括参数描述、默认值、示例等
- 🔍 **更准确的识别**：减少误识别和遗漏
- 🌐 **多语言支持**：处理中英文混合文档
- 🎯 **结构化输出**：提供标准化的参数信息格式

## 功能特性

### 1. 双模式支持

- **LLM模式**：使用大模型智能提取参数
- **正则表达式模式**：传统的基于规则的提取
- **自动回退**：LLM失败时自动使用正则表达式

### 2. 丰富的参数信息

LLM模式可以提取以下信息：

```json
{
  "name": "参数名称",
  "type": "参数类型",
  "description": "详细描述",
  "default_value": "默认值",
  "required": true/false,
  "example": "使用示例",
  "extraction_method": "llm"
}
```

### 3. 支持的LLM提供商

- **DeepSeek**（推荐）：性价比高，中文支持好
- **OpenAI**：GPT-3.5/GPT-4系列
- **Ollama**：本地部署方案

## 配置方法

### 1. 环境变量配置

```bash
# DeepSeek API Key
export DEEPSEEK_API_KEY="your_deepseek_api_key"

# 或者 OpenAI API Key
export OPENAI_API_KEY="your_openai_api_key"
```

### 2. 配置文件设置

编辑 `config/config.yaml`：

```yaml
llm:
  provider: "deepseek"  # 选择提供商
  
  deepseek:
    api_key: ""  # 留空使用环境变量
    base_url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    temperature: 0.1
    max_tokens: 4000

parameter_extraction:
  method: "auto"  # auto, llm, regex
  llm_extraction:
    max_content_length: 8000
    retry_on_failure: true
    extract_default_values: true
    extract_examples: true
```

## 使用方法

### 1. 基本使用

```python
from core.document_processor import PlatformDocumentProcessor

# 初始化处理器
processor = PlatformDocumentProcessor()

# 处理文档
result = processor.process_md_file("docs/platform.md")

# 查看提取的参数
parameters = result['platform_params']
for param in parameters:
    print(f"参数: {param['name']}")
    print(f"类型: {param['type']}")
    print(f"描述: {param['description']}")
    if param.get('default_value'):
        print(f"默认值: {param['default_value']}")
```

### 2. 强制使用特定方法

```python
# 强制使用LLM方法
llm_params = processor._extract_parameters_with_llm(content)

# 强制使用正则表达式方法
regex_params = processor._extract_parameters_with_regex(content)
```

### 3. 批量处理

```python
# 处理整个文档目录
results = processor.process_directory("docs/")

print(f"总共提取到 {results['total_parameters']} 个参数")
```

## 测试和验证

### 1. 运行测试脚本

```bash
python test_llm_parameter_extraction.py
```

### 2. 测试输出示例

```
=== 测试LLM参数提取功能 ===

✅ LLM客户端已初始化，将使用AI方法提取参数

--- 开始提取参数 ---
使用AI方法提取到 7 个参数

--- 提取结果 ---
共提取到 7 个参数：

1. 参数名称: position
   类型: coordinate
   描述: Sets the initial position of the platform in geographic coordinates
   默认值: (0.0, 0.0, 0.0)
   是否必需: 是
   示例: position(39.9042, 116.4074, 1000.0)
   提取方法: llm

2. 参数名称: heading
   类型: angle
   描述: Sets the initial heading of the platform in degrees
   默认值: 0.0
   是否必需: 否
   示例: heading(90.0)
   提取方法: llm
```

## 性能优化

### 1. 内容长度限制

- 自动截取重要部分，避免超过模型限制
- 优先保留包含参数信息的段落
- 默认最大长度：8000字符

### 2. 缓存机制

```yaml
performance:
  cache_extracted_parameters: true
  cache_ttl: 3600  # 1小时
```

### 3. 并发控制

```yaml
performance:
  max_concurrent_extractions: 3
```

## 故障排除

### 1. LLM客户端初始化失败

**问题**：未设置API Key或网络连接问题

**解决方案**：
- 检查环境变量设置
- 验证API Key有效性
- 检查网络连接

### 2. 参数提取结果为空

**问题**：文档格式不符合预期或LLM理解错误

**解决方案**：
- 检查文档内容格式
- 尝试使用正则表达式方法对比
- 调整提示词或模型参数

### 3. JSON解析错误

**问题**：LLM返回格式不正确

**解决方案**：
- 系统会自动回退到正则表达式方法
- 检查模型配置和提示词
- 考虑降低temperature参数

## 最佳实践

### 1. 文档格式建议

为了获得最佳提取效果，建议文档包含：

```markdown
### parameter_name()
参数描述

**Type**: 参数类型
**Default**: 默认值
**Required**: Yes/No
**Example**: 使用示例
```

### 2. 配置建议

- 使用较低的temperature（0.1）确保输出稳定
- 设置合适的max_tokens避免截断
- 启用自动回退机制

### 3. 监控和日志

- 启用详细日志记录
- 监控提取成功率
- 定期验证提取结果质量

## API参考

### PlatformDocumentProcessor

#### 主要方法

- `_extract_platform_parameters(content)`: 自动选择提取方法
- `_extract_parameters_with_llm(content)`: 使用LLM提取
- `_extract_parameters_with_regex(content)`: 使用正则表达式提取

#### 配置选项

- `llm.provider`: LLM提供商
- `parameter_extraction.method`: 提取方法
- `parameter_extraction.llm_extraction.*`: LLM相关配置

## 更新日志

### v1.0.0
- 初始版本，支持DeepSeek和OpenAI
- 实现自动回退机制
- 添加详细的参数信息提取

### 计划功能
- 支持更多LLM提供商
- 参数关系分析
- 自动生成参数文档
