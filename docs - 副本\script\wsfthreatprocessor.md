orphan

:   

# WsfThreatProcessor

::: {.WsfThreatProcessor .inherits .WsfProcessor}
This is the script interface to the `WSF_THREAT_PROCESSOR`{.interpreted-text role="model"}. Currently, the list of threatening tracks is not accessible to script.
:::

## Methods

::: method
Array\<WsfTrackId\> Threats( )

Returns the track Ids of all local tracks that are considered threatening according to the inputs given to the `WSF_THREAT_PROCESSOR`{.interpreted-text role="model"} such as threat_velocity, threat_angle_spread, and threat_time_to_intercept.
:::

::: method
WsfTrack NearestThreat()

Returns the closest local track to the processor\'s platform position that is considered threatening. To be considered a track must have a valid 3D location. Use `IsValid() <Object>`{.interpreted-text role="class"} to verify value returned.
:::
