orphan

:   

# router_protocol

See `Predefined_Router_Protocol_Types`{.interpreted-text role="ref"}

Global Context Commands

> ::: {.command block=""}
> router_protocol \<name-or-type\> \<base-type-name\> \... end_router_protocol
>
> ::: parsed-literal
> router_protocol \<name-or-type\> \<base-type-name\> \... end_router_protocol
> :::
> :::

Router and Router Type Scope Commands

:   ::: command
    add router_protocol \<name-or-type\> \<base-type-name\> \... end_router_protocol
    :::

    ::: command
    edit router_protocol \<name\> \... end_router_protocol
    :::

    ::: {.command block=""}
    delete router_protocol \<name\> end_router_protocol

    ::: parsed-literal
    add router_protocol \<name-or-type\> \<base-type-name\> \... end_router_protocol

    edit router_protocol \<name\> \... end_router_protocol

    delete router_protocol \<name\> end_router_protocol
    :::
    :::

## Overview

A (router) protocol is an abstract representation of a particular router capability. It can provide or restrict functionality from the norm, and may alter how routers process messages during normal router operations.

Routers in AFSIM typically are referenced during both transmission and reception of a message. When a message is being sent, the router is queried to determine if a message can be sent to its destination, and the path (specifically, the next hop or comm interface) that a message should be forwarded (transmitted) to. During reception, the router usage is limited, but helps determine how a message is processed based on its destination, and whether that destination is the comm interface the message was received upon.

Router protocols are queried during all of these operations so that they may alter the normal processing of messages by the router, and to collect internal data specific to the protocol used for various routing operations.

As an example, the multicast protocol allows messages with multicast address destinations to be correctly identified, and allows the router to correctly identify which available interfaces should be used to forward the multicast message to other recipients. As such, this particular protocol may result in a single or many messages to be transmitted from a single message reception, which is not typical router behavior.

Only one instance of any type of router protocol should exist on any given router instance.

Several routing protocols are used by default in AFSIM to enable message routing and multicast capabilities. See `Predefined_Router_Protocol_Types`{.interpreted-text role="ref"} for more details.
