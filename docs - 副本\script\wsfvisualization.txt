# wsfvisualization.rst.txt.md
orphan

:   



::: WsfVisualization
`WsfVisualization`{.interpreted-text role="class"} provides routines which change how entities are visualized.
:::



::: method
void SetVisible(WsfPlatform aPlatform, bool aIsVisible)

Changes the visibility of a platform.
:::

::: method
void SetBehavior(WsfPlatform aPlatform, int aBehavior, double aAnimationSpeed, double aAnimationOffset)

Changes the animation mode of a platform. \* *aBehavior* \-- The enumerated animation id. \* *aAnimationSpeed* \-- A speed multiplier for the animation. 1.0 is normal speed. \* *aAnimationOffset* \-- An offset in seconds to begin playing the animation.
:::

::: method
int Behavior(WsfPlatform aPlatform)

Returns the behavior/animation ID of the last set behavior.
:::