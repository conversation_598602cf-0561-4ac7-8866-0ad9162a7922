orphan

:   

# WsfCommMessage

## Description {#description .WsfCommMessage .inherits .Object}

This script class provides access to the [WsfCommMessage](#wsfcommmessage) object, which is a container for a standard `WsfMessage`{.interpreted-text role="class"}. This object is typically used internally to the comm framework in AFSIM, but is occasionally exposed to provide the additional data used by the comm framework in the script context.

## Methods

::: method
Array\<WsfAddress\> GetTraceRoute()

Provides the traceroute of the current address. Every comm interface a message is transmitted from to reach its final destination adds its address to the traceroute within the comm framework. This provides that history of which interfaces this message has passed through up until the time of this call.

Note that the traceroute does not contain the address for an interface that has not yet transmitted a message, such that if the traceroute is being inspected on the originator interface, this traceroute will be empty.
:::

::: method
int GetTTL()

Provides the number of hops (time-to-live) for this message, as set by the message\'s originating router interface. Any potential path to a destination that exceeds the TTL value will not reach its destination using that path, and will be dropped.
:::

::: method
WsfMessage GetSourceMessage()

Provides the `WsfMessage`{.interpreted-text role="class"} object being held by this container.
:::

## Auxiliary Data Methods
