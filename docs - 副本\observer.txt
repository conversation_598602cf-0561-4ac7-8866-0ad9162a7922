# observer.rst.txt.md
orphan

:   



::: {.contents local=""}
:::

::: {.command block=""}
observer \... end_observer

::: parsed-literal

[observer](#observer)

:   [enable]() \<EVENT_TYPE\> \[ \<user defined script name\> \] [disable]() \<EVENT_TYPE\> \[ \<user defined script name\> \]

end_observer
:::
:::



The observer block allows the user to capture specific platform interaction data at the simulation level. It can be used as an alternative to capturing events using the event log to create user defined output information (such as comma delimited data). It is up to the user to create the necessary scripts to capture the desired interaction information as well as the output format. The observer block and scripts are defined outside of any platform definitions.



::: command
enable \<EVENT_TYPE\> \[ \<user defined script name\> \]
:::

::: command
disable \<EVENT_TYPE\> \[ \<user defined script name\> \]

Enable or disable a script observer for the specified \<EVENT_TYPE\>. If the \<user defined script name\> is the same as the \<EVENT_TYPE\> actual script name, the \<user defined script name\> is not required. Not all of the information that is printed with the `event_output`{.interpreted-text role="command"} command is available to the observer.
:::

::: note
::: title
Note
:::

Multiple scripts can be enabled for the same \<EVENT_TYPE\>
:::



The following are the values for \<EVENT_TYPE\> that can be enabled or disabled, and the corresponding script signature:

