# wsfxio_publishkey.rst.txt.md
orphan

:   



::: WsfXIO_PublishKey
A WsfXIO_PublishKey represents a unique identifier for a publication sent/received over XIO. See `WsfXIO`{.interpreted-text role="class"} for methods to publish and subscribe.
:::



::: method
WsfXIO_PublishKey Construct(Object aField1)
:::

::: method
WsfXIO_PublishKey Construct(Object aField1, Object aField2)
:::

::: method
WsfXIO_PublishKey Construct(Object aField1, Object aField2, Object aField3)
:::

::: method
WsfXIO_PublishKey Construct(Object aField1, Object aField2, Object aField3, Object aField4)

Parameters aField2, aField3, aField4 are all optional (A key can have 1-4 fields). Returns a key with the specified fields. Each parameter can be a basic script type, which is converted to a string.

::: note
::: title
Note
:::

In cases where a key is used to subscribe, a field with value \"\*\" is a wildcard and matches any value.
:::
:::



::: method
string ToString()

Return a string representation of the key. This is mainly useful for printing the key with writeln.
:::

::: method
string Field(int aFieldIndex)

Return the indexed field. aFieldIndex should be in the range \[0, 3\].
:::

::: method
bool Match(WsfXIO_PublishKey aKey)

Returns true if this key matches aKey. Wildcard fields in this key will be matched to any value in aKey.
:::