# universal_command_and_control.rst.txt.md
orphan

:   



::: {.command block=""}
uci_component
:::

::: parsed-literal
uci_component \<type\> \<base_type\>

> \... `Platform_Part_Commands`{.interpreted-text role="ref"} \...
>
> subsystem_uuid \... subsystem_descriptor \...

end_uci_component
:::



The Universal Command and Control Interface (UCI) sends messages and commands to and from components on platforms based on the Critical Abstraction Layer (CAL) spec. The current version of the CAL used is version 75.

This extension requires `ActiveMQ<activemq>`{.interpreted-text role="doc"}, the middleware in charge of handling messages. To connect a simulation to ActiveMQ, a Reference CAL (RefCAL) configuration file is required. The RefCAL configuration file is set using the environment variable CAL_ACTIVEMQ_CONFIG. This file configures all platform and component Universally Unique Identifiers (UUIDs), so the simulation can send messages/commands to the correct platforms, sensors, etc.



To send UCI messages, each platform component must have a corresponding UCI_Component attached to its owning platform. To receieve UCI messages, a platform must have a `COMPUTER<computer_component>`{.interpreted-text role="doc"} component.

\<type\>

> The type of UCI component being defined.

\<base_type\>

> The name of an existing UCI component type or pre-defined UCI type.
>
> The pre-defined UCI base types include:
>
> > -   `AMTI<amti_component>`{.interpreted-text role="doc"}
> > -   `COMPUTER<computer_component>`{.interpreted-text role="doc"}
> > -   `ESM<esm_component>`{.interpreted-text role="doc"}
> > -   `IRST<irst_component>`{.interpreted-text role="doc"}
> > -   `WEAPON<weapon_component>`{.interpreted-text role="doc"}

Instantiation on a platform type:

::: parsed-literal

platform_type \<type\> \<base_type\>

:   

    uci_component \<name\> \<type\>

    :   \...

    end_uci_component

end_platform_type
:::

Adding uci_component to a platform:

::: parsed-literal

platform \<name\> \<type\>

:   

    add uci_component \<name\> \<type\>

    :   \...

    end_uci_component

end_platform
:::

Editing a uci_component on a platform:

::: parsed-literal

platform \<name\> \<type\>

:   

    edit uci_component \<name\>

    :   \...

    end_uci_component

end_platform
:::

Deleting a uci_component on a platform:

::: parsed-literal

platform \<name\> \<type\>

:   

    delete uci_component \<name\>

    :   \...

    end_uci_component

end_platform
:::



::: command
subsystem_uuid \<string-value\>

Sets the subsystem UUID of the corresponding subsystem to the given string.

::: note
::: title
Note
:::

The UUID should be in the format \'########-####-####-####-############\', where \# is a uppercase alphanumeric value \[A-Z0-9\].
:::

Default: Auto-generated by the interface.
:::

::: command
subsystem_descriptor \<string-value\>

Sets the human readable description for the subsystem.
:::