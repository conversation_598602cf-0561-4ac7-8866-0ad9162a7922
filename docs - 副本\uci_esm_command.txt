# uci_esm_command.rst.txt.md
orphan

:   





This type is used in `UCI_ESM_CommandMessage`{.interpreted-text role="class"} to define commands for ESM sensors.



::: method
static UCI_ESM_Command Construct(UCI_ESM_ActivityCommand esmActivityCommand)

Creates an instance of an [UCI_ESM_Command](#uci_esm_command) that, if approved, will result in the given ESM Activity being modified and reported via `UCI_ESM_ActivityMessage`{.interpreted-text role="class"}.
:::

::: warning
::: title
Warning
:::

This method will have very limited use since the ability to alter activities has yet to be implemented.
:::

::: method
static UCI_ESM_Command Construct(UCI_ESM_CapabilityCommand esmCapabilityCommand)

Creates an instance of an [UCI_ESM_Command](#uci_esm_command) that, if approved, will result in one or more ESM Activies being created and reported via `UCI_ESM_ActivityMessage`{.interpreted-text role="class"}.
:::