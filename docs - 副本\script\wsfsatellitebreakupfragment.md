orphan

:   

# WsfSatelliteBreakupFragment

::: {.WsfSatelliteBreakupFragment .inherits .Object}
`WsfSatelliteBreakupFragment`{.interpreted-text role="class"} contains the details of a debris fragment produced by a satellite breakup model, such as `WsfNASA_BreakupModel`{.interpreted-text role="class"}.
:::

## Methods

::: method
double GetLength()

Return the length scale of the debris fragment in meters.
:::

::: method
double GetAoverM()

Return the ratio of the area of the fragment to the mass of the fragment in $\rm{m}^2/\rm{kg}$.
:::

::: method
double GetMass()

Return the mass in kilograms of the fragment.
:::

::: method
double GetArea()

Return the area of the fragment in square meters.
:::

::: method
double GetDeltaV()

Return the change in velocity experienced by the fragment relative to its progenitor.
:::

::: method
string GetName()

Return the name of the platform that was generated to represent this fragment.
:::

::: method
bool IsInitialized()

Return if the fragment has been properly initialized.
:::
