# print_sensor_result.rst.txt.md
orphan

:   



Sensor Detection Attempt and Sensor Detection Changed use PrintSensorResult to print sensor details.



::: parsed-literal
Sensor: \<name\> Mode: \<mode name \| \<none\> Beam Index: \<index\> `Print_EM_Interaction`{.interpreted-text role="ref"} {sensor warning, if any} Pd: \<probability\> \<\< RequiredPd: \<probability\> Detected: \<binary\>
:::



Format ALL - \"All formats begin with these fields\"

sensor: \<name\>

:   sensor name

Mode: \<mode name\>

:   name of sensor mode, if valid, else \"\<none\>\"

Beam Index: \<index\>

:   index of sensor beam `Print_EM_Interaction`{.interpreted-text role="ref"} event_output goes here \|

sensor warnings, may include:

:   Target_Concealed Doppler_Limits_Exceeded Velocity_Limits_Exceeded Target_Deleted

Probability of Detection statistics:

Pd: \<probability\>

:   Probability of Detection

RequiredPd: \<probability\>

:   Pd required for Xmtr/Rcvr to see target

Detected:

:   0 (not detected) or 1 (detected)







::: parsed-literal
sensor: ew-radar Mode: default Beam Index: 0 `Print_EM_Interaction`{.interpreted-text role="ref"} Rcvr_Masked_By_Horizon Pd: 0 RequiredPd: 0.509842 Detected: 0
:::



::: parsed-literal
sensor: ew-radar Mode: default Beam Index: 0 `Print_EM_Interaction`{.interpreted-text role="ref"} Insufficient_Signal Pd: 0 RequiredPd: 0.835712 Detected: 0
:::



::: parsed-literal
sensor: ew-radar Mode: default Beam Index: 0 `Print_EM_Interaction`{.interpreted-text role="ref"} Pd: 1 RequiredPd: 0.491776 Detected: 1
:::



::: parsed-literal

`event_output`{.interpreted-text role="command"}

:   file replay.evt \# write event messages to file \"replay.evt\" enable `docs/event/sensor_events:SENSOR_DETECTION_ATTEMPT`{.interpreted-text role="ref"} enable `docs/event/sensor_events:SENSOR_DETECTION_CHANGED`{.interpreted-text role="ref"}

end_event_output

#\-\-\-\-\--# Define comm type IADS_EW_COMM comm IADS_EW_COMM `WSF_COMM_TRANSCEIVER`{.interpreted-text role="model"} transfer_rate 56 kbits/sec end_comm

#\-\-\-\-\--# Define a commander `platform.platform_type`{.interpreted-text role="command"} IADS_CMDR that contains ew-radar sensor `platform.platform_type`{.interpreted-text role="command"} IADS_CMDR `WSF_PLATFORM`{.interpreted-text role="model"}

> \# We have our own local acquisition sensor to supplement the reports we get from the EW network. \# For now this is simply another 2-D EW radar.
>
> sensor ew-radar EW_RADAR_SENSOR
>
> :   on processor track-processor ignore ignored-by-iads-radar
>
> end_sensor

end_platform_type

#\-\-\-\-\--# Define a radar `platform.platform_type`{.interpreted-text role="command"} EW_RADAR that contains ew-radar sensor `radar_signature`{.interpreted-text role="command"} EW_RADAR_SIGNATURE constant 10 m\^2 end_radar_signature

`platform.platform_type`{.interpreted-text role="command"} EW_RADAR `WSF_PLATFORM`{.interpreted-text role="model"}

> `radar_signature`{.interpreted-text role="command"} EW_RADAR_SIGNATURE
>
> sensor ew-radar EW_RADAR_SENSOR
>
> :   processor collector ignore ignored-by-iads-radar
>
> end_sensor

end_platform_type

#\-\-\-\-\--# Define a platform that uses ew-radar sensor platform ew-e commander iads-cmdr category ignored-by-iads-radar comm ew-net network_name ew-net end_comm sensor ew-radar on end_sensor end_platform

#\-\-\-\-\--# Define a platform that platform strike-11 F-18E side blue `route`{.interpreted-text role="command"} position 39:56:48n 113:11:36w altitude 30000 ft msl speed 500 kts radial_acceleration 2.00 g position 36:31:59n 114:54:35w altitude 30000 ft msl speed 500 kts radial_acceleration 2.00 g position 40:03:04n 113:29:52w altitude 30000 ft msl speed 500 kts radial_acceleration 2.00 g end_route end_platform
:::