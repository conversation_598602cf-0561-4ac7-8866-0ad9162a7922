# launcher.rst.txt.md
orphan

:   

::: demo
launcher
:::

| This demo is meant to be a simple way of demonstrating a build up of tactics processor
| and launch computer to fire a sam.
| 
| All launchers, sensors, and weapons are made up for this example.
| 
| Four different tactic processors / launch computers are used to shoot a sam.
| 
| The tactics_processor (in the platforms directory) is changed out for each of the demos.
| 
| The first one has all the logic to fire the missile within the task processor.
| Very little checks are done to fire the sam.
| \"run 1st_run.txt\" executes it.
| 
| The second removes the logic for the launch criteria and places it in a
| separate launch computer script.
| \"run 2nd_run.txt\" executes it.
| 
| The third adds more criteria to the launch computer and adds additional state machine logic.
| \"run 3rd_run.txt\" executes it.
| 
| The fourth processor separates the launch platform script and missile launch computer calls so
| other launchers/missiles can be added. This allows the same tactics processor to be used for
| other launchers and missiles. Also, additional checks are done prior to launching a
| sam missile.
| \"run 4th_run.txt\" executes it.