orphan

:   

# Command Line - Warlock

Warlock supports the most of the same command line options as the `mission <mission>`{.interpreted-text role="doc"} executable. Warlock will always run in realtime and as such the frame-stepped option (-fs) should not be used.

<PERSON><PERSON> supports starting it with no command line arguments and provides a `Start Dialog <warlock_start_dialog>`{.interpreted-text role="doc"} where the user can select what scenario file they want to run.

  Command Line Options            
  ------------------------------- ------------------------------------------------------------------------------------------------
  -?, -h, -help                   Display command line `options<warlock_help>`{.interpreted-text role="doc"} and quit
  -cf \<filename\>                Use the specified configuration file, modifications will be saved to specified file
  -icf \<filename\>               Imports the specified configuration file, modifications will be not be saved to specified file
  -permission_file \<filename\>   Use the specified permissions file. Prevents the user from editing permissions.
  -console                        Enables the console window
  -ups                            Uses the previous scenario, if no scenario was specified
  -minimized                      The application will start minimized
