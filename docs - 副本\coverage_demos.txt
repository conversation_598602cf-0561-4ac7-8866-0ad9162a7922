# coverage_demos.rst.txt.md
orphan

:   

::: demo
coverage_demos
:::

| This directory contains demos that illustrate the `AFSIM Coverage capability<wsf_coverage_overview>`{.interpreted-text role="doc"}.
| The following top-level startup files demonstrate the various operations as indicated:



| Demonstrates coverage using a `WSF_ZONE_BASED_GRID`{.interpreted-text role="model"} of platforms within CONUS. The zones/conus.txt
| file is where the zone is defined, and the gridpoints are spaced according to what is defined in the
| grid block. The sat_con satellites are the assets providing the sensor coverage.



| Demonstrates coverage using a `WSF_DISTANCE_STEPPED_GRID`{.interpreted-text role="model"} of platforms, which sit on terrain in Colorado.
| The gridpoints are spaced with a given distance and locked to a defined origin. The UAV platform flies in an
| orbital route to provide coverage. The terrain/uav_coverage_terrain.txt file loads the terrain and draws it
| for visualization in Mystic.



| Demonstrates a `WSF_COMPOSITE_GRID`{.interpreted-text role="model"}, which is made up of two grids, each of which is a `WSF_ZONE_BASED_GRID`{.interpreted-text role="model"},
| of platforms in the states of Texas and Florida. The sat_con satellites are the assets providing sensor coverage.



| Demonstrates global coverage using a `WSF_LAT_LON_GRID`{.interpreted-text role="model"} spanning the earth. The sat_con
| satellites are the assets providing sensor coverage.



| Demonstrates coverage using a `WSF_EXISTING_PLATFORM_GRID`{.interpreted-text role="model"}, made up of five air targets flying West
| to East over a ground station. The ground station is the coverage asset providing sensor coverage,
| with the ground station sensor tracking the air targets as they move.



| Demonstrates coverage of satellites in geosynchronous orbit using a `WSF_LAT_LON_GRID`{.interpreted-text role="model"}.
| The satellite coverage assets are made up of the sat_con constellation, with scripts used to point their
| respective sensors towards the GEO grid, and a ground station with a stationary sensor. The included SAT
| platform has a processor attached checking for exclusion constraints that disallow geometric access and
| line of sight to target.