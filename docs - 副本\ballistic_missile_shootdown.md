orphan

:   

::: demo
ballistic_missile_shootdown
:::

| UNCLASSIFIED
| Any resemblance or approximations to actual weapon system capabilities
| in this demo are purely accidental. The intent of this DEMONSTRATION is
| to illustrate how to setup a processor to launch an anti-ballistic missile
| at sometime in the future to intercept a ballistic missile.
| (processors/anti_ballistic_missile_processor.txt)
| For the demo, a red_mrbm_1 is launched from Ontario Canada at Washington DC. An anti
| ballistic missile (in this case a blue_naval_sam) is fired to intercept the
| red_mrbm_1(s) to save Washington.
| 
| The top startup file to run is \"ballistic_shootdown.txt\".
| This is a one on one example.
| 
| The \"many_ballistic_shootdown.txt\" is another top startup file
| that depicts a nine against nine shootdown by only changing out
| the scenario files. Take note that there are nine launchers for the red_mrbm_1\'s
| and only six for the blue_naval_sam\'s. One of the launchers has more than one weapon.
| 
| The command \"show_graphics\" provides output for Mystic concerning
| the interceptor evaluation criteria on the ballistic path of what is being
| evaluated to be shot down. (The show_graphics subcommand is located within the
| WSF_GUIDANCE_COMPUTER block on the weapon.)
|   An orange dot is created at the time of evaluation.
|   A green dot is displayed at the target location when the interceptor will launch.
|   A red dot at the point of possible intercepts
|   A white dot shows the last possible intercept location.
|   A yellow dot shows the final location of the weapon intercept.
