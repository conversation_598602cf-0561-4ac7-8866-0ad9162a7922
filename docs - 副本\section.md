orphan

:   

# Section Formation

::: command
section
:::

::: parsed-literal

section \<name\>

:   debug detached offset \... \[lead\] unit \... end_unit

end_section
:::

## Overview

A sections is a formation that is formed from two unit sub-formations. Sections cannot be defined at the root level in input files. Sections can also be defined in script, see `WsfFormation`{.interpreted-text role="class"} and `WsfFormationManager`{.interpreted-text role="class"}. Sections can be given commands, see `WsfFormationCommand`{.interpreted-text role="class"}.

::: {#Section_Formation.Common_Formation_Commands}
:::

## Sub-formation Commands

::: command
unit \... end_unit

Define a unit sub-formation. See `unit`{.interpreted-text role="doc"}.
:::

::: command
lead

Specify that the following unit sub-formation is to be the lead sub-formation of this section. Sections defined in input files must have one of their sub-formations designated as the lead sub-formation.
:::
