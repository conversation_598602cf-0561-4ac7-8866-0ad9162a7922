# cyber_protect.rst.txt.md
orphan

:   



Navigation: `cyber_overview`{.interpreted-text role="ref"}

::: {.command block=""}
cyber_protect \<type\> \<base_type\> \... end_cyber_protect

::: parsed-literal
cyber_protect \<type\> \<base_type\>

> \# Defines the response to the attack *\<attack_type\>*. \# Repeat as necessary to handle additional attack types
>
> [attack_response]() *\<attack_type\>*
>
> :   [probability_of_scan_detection]() \... [probability_of_scan_attribution]() \... [probability_of_attack_success]() \... [probability_of_status_report]() \... [probability_of_attack_detection]() \... [probability_of_attack_attribution]() \... [probability_of_future_immunity]() \... [attack_detection_delay_time]() \... [attack_recovery_delay_time]() \... script bool `cyber_protect.IsVulnerable`{.interpreted-text role="command"} \... script bool `cyber_protect.OnScanDetection`{.interpreted-text role="command"} \... script bool `cyber_protect.OnAttackDetection`{.interpreted-text role="command"} \... script bool `cyber_protect.OnAttackRecovery`{.interpreted-text role="command"} \...
>
> end_attack_response
>
> \# Define the default attack response should an attack occur \# for an attack type that does not have an accompanying response.
>
> [attack_response]() default
>
> :   \...
>
> end_attack_response

end_cyber_protect
:::
:::

\<type\>

:   The name of the cyber_protect type being defined.

\<base_type\>

:   The name of an existing cyber_protect type or WSF_CYBER_PROTECT, whose definition will be used as the initial definition of the new type.

Instantiation on a platform type:

::: parsed-literal

platform_type \<type\> \<base_type\>

:   

    cyber_protect \<type\>

    :   \...

    end_cyber_protect

end_platform_type
:::

Adding [cyber_protect](#cyber_protect) to a platform:

::: parsed-literal

platform \<name\> \<type\> \# or edit platform \<name\>

:   

    add cyber_protect \<type\>

    :   \...

    end_cyber_protect

end_platform
:::

Editing a [cyber_protect](#cyber_protect) object on a platform:

> platform \<name\> \<type\> \# or edit platform \<name\>
>
> :   
>
>     edit cyber_protect
>
>     :   \...
>
>     end_cyber_protect
>
> end_platform



The [cyber_protect](#cyber_protect) object defines a platforms response to cyber attacks, which includes the ability to block, detect and recover from an attack. Every active platform active in a simulation will have either an explicitly or implicitly define [cyber_protect](#cyber_protect) object.

The object contains zero or more [attack_response]() blocks, each of which defines the response to a particular type of attack as defined by `cyber_attack`{.interpreted-text role="command"}. When an attack is attempted the response will be selected according to the following algorithm:

1.  Attempt to locate the `cyber_protect.attack_response`{.interpreted-text role="command"} with the name \'\<T\>\'.
2.  If step 1 fails, for each `cyber_attack`{.interpreted-text role="command"} type that \'\<T\>\' inherits from, attempt to locate the `cyber_protect.attack_response`{.interpreted-text role="command"} whose name matches the inherited type.
3.  If steps 1 and 2 fail attempt to locate the `cyber_protect.attack_response`{.interpreted-text role="command"} entry named \'default\'.
4.  If steps 1, 2 and 3 fail use an internal response dynamically created from the default default response values in the `cyber_attack`{.interpreted-text role="command"} type \'\<T\>\'.

For more information about the implementation of a cyber attack please see `cyber_overview`{.interpreted-text role="ref"}.



::: command
attack_response \[ \<attack_type\> \| default \] \... end_attack_respone

Define the response to the attack *\<attack_type\>*. *\<attack_type\>* must be a defined `cyber_attack`{.interpreted-text role="command"} type or the word \'default\', which signifies the attack response to use if an attack is received for which there is not a defined attack response.
:::



This section defines the commands that occur within the [attack_response]().

::: command
probability_of_scan_detection \[ 0 .. 1 \]

The probability threshold that a scan function will be detected.

Default: From `cyber_attack.probability_of_scan_detection`{.interpreted-text role="command"} in the applicable `cyber_attack`{.interpreted-text role="command"}.
:::

::: command
probability_of_scan_attribution \[ 0 .. 1 \]

The probability threshold that a scan function will be attributed.

Default: From `cyber_attack.probability_of_scan_attribution`{.interpreted-text role="command"} in the applicable `cyber_attack`{.interpreted-text role="command"}.
:::

::: command
probability_of_attack_success \[ 0 .. 1 \]

The probability threshold that an attack will be declared successful.

Default: From `cyber_attack.probability_of_attack_success`{.interpreted-text role="command"} in the applicable `cyber_attack`{.interpreted-text role="command"}.
:::

::: command
probability_of_status_report \[ 0 .. 1 \]

The probability threshold that the attacker will receive immediate notification of the success or failure of an attack.

Default: From `cyber_attack.probability_of_status_report`{.interpreted-text role="command"} in the applicable `cyber_attack`{.interpreted-text role="command"}.
:::

::: command
probability_of_attack_detection \[ 0 .. 1 \]

The probability threshold that an attack will be detected by the victim.

Default: From `cyber_attack.probability_of_attack_detection`{.interpreted-text role="command"} in the applicable `cyber_attack`{.interpreted-text role="command"}.
:::

::: command
probability_of_attack_attribution \[ 0 .. 1 \]

The probability threshold that an attack will be attributed by the victim.

Default: From `cyber_attack.probability_of_attack_attribution`{.interpreted-text role="command"} in the applicable `cyber_attack`{.interpreted-text role="command"}.
:::

::: command
probability_of_future_immunity \[ 0 .. 1 \]

The probability threshold that the victim will be immune to future attacks of the same type.

Default: From `cyber_attack.probability_of_future_immunity`{.interpreted-text role="command"} in the applicable `cyber_attack`{.interpreted-text role="command"}.
:::

::: command
attack_detection_delay_time \<random_time_value\>

The amount of time it takes before the victim become aware of an attack.

Default: Infinity
:::

::: command
attack_recovery_delay_time \<random_time_value\>

The amount of time it takes for the victim to recover from an attack after it becomes aware of the attack.

Default: Infinity
:::

::: command
IsVulnerable (script)

::: parsed-literal
script bool IsVulnerable(`WsfCyberEngagement`{.interpreted-text role="class"} aEngagement) end_script
:::

Define an optional script that is invoked when a scan or an attack occurs against the platform. This may be used to implement more complex vulnerability assessment based on the operating conditions rather than just using random draws.

The script should return a \'true\' if the platform is vulnerable to the scan or attack or \'false\' if not.

If this script returns a \'false\' not vulnerable value, then the scan or attack currently in progress will be aborted without providing any notification to the attacker. Note that even if this script determines that a victim platform is vulnerable to an attack or scan, the victim\'s immunity status will still be evaluated in determining whether or not to proceed with the scan or attack.

Default: Not defined.
:::

::: command
OnScanDetection (script)

::: parsed-literal
script void OnScanDetection(`WsfCyberEngagement`{.interpreted-text role="class"} aEngagement) end_script
:::

Define an optional script that is invoked when a scan occurs against the platform. This may be used to model such effects as notifying others that something suspicious is occurring.

Default: Not defined.
:::

::: command
OnAttackDetection (script)

::: parsed-literal
script void OnAttackDetection(`WsfCyberEngagement`{.interpreted-text role="class"} aEngagement) end_script
:::

Define an optional script that is invoked after an attack has occurred upon the expiration of the [attack_detection_delay_time](). This might be used to model effects such notify others of the attack.

Default: Not defined.
:::

::: command
OnAttackRecovery (script)

::: parsed-literal
script void OnAttackRecovery(`WsfCyberEngagement`{.interpreted-text role="class"} aEngagement) end_script
:::

Define an optional script that is invoked during an attack after the expiration of the [attack_recovery_delay_time](). This may be used to model such effects as notifying others that they are once again operational.

Default: Not defined.
:::