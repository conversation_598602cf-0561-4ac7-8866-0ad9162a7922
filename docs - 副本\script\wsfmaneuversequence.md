orphan

:   

# WsfManeuverSequence

## Overview {#overview .WsfManeuverSequence .inherits .WsfManeuver}

A `WsfManeuverSequence`{.interpreted-text role="class"} is a set of maneuvers that are executed in order. The elements of the sequence can be anything derived from `WsfManeuver`{.interpreted-text role="class"} including another `WsfManeuverSequence`{.interpreted-text role="class"}. In addition to the individual members of the sequence each can have their own entry and exit constraints (see `WsfManeuver`{.interpreted-text role="class"} and `WsfManeuverConstraint`{.interpreted-text role="class"}) the sequence as a whole can have entry and exit constraints. A `WsfManeuverSequence`{.interpreted-text role="class"} can be set to looping so that once the final maneuver of the sequence completes, the first will then begin execution again.

## Methods

The `WsfManeuverSequence`{.interpreted-text role="class"} defines the following methods in addition to those it inherits from `WsfManeuver`{.interpreted-text role="class"}.

::: method
WsfManeuverSequence Construct()

Returns a new instance of a maneuver sequence.
:::

::: method
bool IsLoop()

Returns true if the sequence is a loop.
:::

::: method
void SetLoop(bool aLoop)

Enable or disable looping on this maneuver sequence. In the looping mode, the constraints on the member maneuvers will be evaluated each time the maneuver is current, but the overall sequence constraints will be evaluated only once. Further, unless the loop is canceled or if `WsfManeuverSequence.SetLoop`{.interpreted-text role="method"} is called again to stop the loop, the exit constraint will never evaluate.
:::

::: method
void Clear()

Clear the maneuver sequence.
:::

::: method
void Append(WsfManeuver aManeuver)

Append the given maneuver to the end of this sequence.
:::

::: method
void InsertNext(WsfManeuver aManeuver)

Insert the given maneuver after the current maneuver. If there is no current maneuver, the given maneuver will be appended to the sequence.
:::

::: method
WsfManeuver GetCurrentManeuver()

Returns the currently executing maneuver in the sequence. If there is not current maneuver, this will return an invalid object.
:::
