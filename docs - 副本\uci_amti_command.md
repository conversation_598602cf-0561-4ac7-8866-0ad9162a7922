orphan

:   

# UCI_AMTI_Command

## Overview {#overview .UCI_AMTI_Command}

This type is used in `UCI_AMTI_CommandMessage`{.interpreted-text role="class"} to define commands for `WSF_RADAR_SENSOR`{.interpreted-text role="model"}.

## Static Methods

::: method
static UCI_AMTI_Command Construct(UCI_AMTI_ActivityCommand amtiActivityCommand)

Creates an instance of an [UCI_AMTI_Command](#uci_amti_command) that, if approved, will result in one or more AMTI Activies being modified and reported via `UCI_AMTI_ActivityMessage`{.interpreted-text role="class"}.
:::

::: note
::: title
Note
:::

The ability to change activities has yet to be implemented.
:::

::: method
static UCI_AMTI_Command Construct(UCI_AMTI_CapabilityCommand amtiCapabilityCommand)

Creates an instance of an [UCI_AMTI_Command](#uci_amti_command) that, if approved, will result in one or more AMTI Activies being created and reported via `UCI_AMTI_ActivityMessage`{.interpreted-text role="class"}.
:::
