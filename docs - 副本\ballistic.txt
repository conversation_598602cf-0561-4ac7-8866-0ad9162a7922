# ballistic.rst.txt.md
orphan

:   

::: demo
ballistic
:::

| This demo illustrates how to create a script to launch various weapons.
| Since the processor is a SCRIPT_PROCESSOR, the TRACK variable is not
| available as it would be if a TASK_PROCESSOR would have been used.
| 
| Variables can be changed in the scenario file to launch at a specific
| time (TIME_TO_LAUNCH = XX where XX is sim time in seconds), or make any
| launcher fire at a random time (LAUNCH_RANDOM = 1). If launching at a random
| time, the MIN_TIME and MAX_TIME variables can be adjusted for each launcher
| and the TIME_TO_LAUNCH variable is set to a random time within the min and
| max time constraints.
| 
| The default times reside in the processors/launch_weapon_processor.txt file.
| 
| The weapons fired are ballistic missiles located in ../base_types/weapons/ssm
| directory (i.e. red_srbm_1, red_srbm_2, red_mrbm_2, red_srbm_3, red_srbm_4).
| 
| To run this, just type \"run launch_all.txt\".
| 
| After running, the Mystic file to view is \"output/launch_all.aer\".