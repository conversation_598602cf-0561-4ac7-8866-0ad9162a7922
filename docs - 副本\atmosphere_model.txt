# atmosphere_model.rst.txt.md
orphan

:   



::: parsed-literal

[atmosphere_model]() \<model-name\> \<atmosphere-model-type\>

:   \...

end_atmosphere_model
:::

::: command
atmosphere_model

Define a new atmosphere model with the specified *\<model-name\>* that is an instance of the specified *\<atmosphere-model-type\>*.
:::



-   `WSF_PIECEWISE_EXPONENTIAL_ATMOSPHERE`{.interpreted-text role="model"}
-   `WSF_JACCHIA_ROBERTS_ATMOSPHERE`{.interpreted-text role="model"}