orphan

:   

::: demo
satellite_demos
:::

| This directory contains various examples and demonstrations of AFSIM\'s
| satellite capability and satellite interaction with other platforms
| and the overall environment.
| 
| Following are the top-level files to be loaded and run in the AFSIM Wizard.

# 1st_comsat_demo.txt

| This example uses the amateur radio satellite AO-51 (Echo) to simulate a
| standard communications satellite. Transmitters on the ground use it as
| a relay to communicate between themselves.
| The transmit/receive parameters were retrieved from the AO-51 link budget
| on the website \'www.amsat.org\'. The debug output was verified against the
| results of the calculator.
| 
| Note that the satellite will be available to both ground stations only during
| the time from about 420 sec to 980 sec and in some cases the S/N may be
| too low.
| 
| Message traffic is generated by having a sensor produce tracks. The
| sensor will \'detect\' the satellite and then attempt to send the track
| to the other ground stations.

# 2nd_gps_demo.txt

| This example demonstrates a GPS constellation and its availability
| to be seen from a specified location on the earth.

# 3rd_background_sats.txt

| This example places over 600 distinct satellites in orbit.
| 
| The TLE\'s were retrieved from <http://celestrak.com> using the Perl script
| \"download_tle.pl\" This script may be re-run as needed to update the TLE
| definitions to the current time.

# 4th_constellation_demo.txt

| This example demonstrates how to easily create a notional satellite
| constellation with only a basic understanding of orbital elements.

# 5th_iridium_flares_demo.txt

| Iridium Flares are very localized events that look like flares
| or shooting stars: they become very bright and then fade away
| over a period of about ten seconds. Some flares are 100x brighter than
| the brightest star. They are caused by specular reflection by the sun
| from the Iridium Satellites solar panels and main communications antennae,
| which look like shiny flat plates that act like mirrors.
| This demo computes flare locations for the entire Iridium satellite
| constellation. Lines are drawn from the satellites to the approximate flare
| location on the ground. A platform is created and moved across the ground
| to visualize the flare\'s ground track.

| If you want to use this as a tool to see the flares, change the lat/lon/alt
| defined in script_variables, below, to your location; then set the
| GMT_Offset to the correct offset for your time zone. Either use the
| \"start_time_now\" setting to get a current report, or set the start_date and
| start_time to the desired observing date and time (keep in mind that the time
| is UT, not local time).

| Update the iridium platforms list by running the \"download_tle.pl\"
| Perl script, which will automatically update all major TLE lists from the
| Celestrack website, including the Iridium satellite TLEs
| (see <http://www.celestrak.com/NORAD/elements/iridium.txt>).

| When you run this demo, a custom file gives a report of nearby flares
| and local az/el locations of the satellites. You can also try tethering to
| the \"center\" platform in Mystic and pan around to visualize
| the satellite\'s location relative to your viewing location.

# 6th_Hohmann_leo_orbit_transfer_demo.txt

| This example demonstrates a Hohmann transfer to a satellite in LEO.
| 
| There are two burns in a Hohmann transfer, the first one is critically
| timed so the apogee of the transfer orbit coincides with the
| final orbit. The second burn is performed at apogee, at the
| time of rendezvous. After both burns, the two satellites are in
| the same orbit with the same \"phase\" (anomaly).

# GPS_denied_demo.txt

| In this demo, a fighter bomber fires two missiles at a ground radar site.
| When the radar site begins to track the incoming missiles, it begins to
| jam the GPS signal going to the missiles. The GPS on-board the missiles
| is then denied and they revert back to ins errors,
| which ultimately leads to their missing the target.

# iridium_demo.txt

| This example demonstrates how a person in India can call a person in Paraguay
| at the same time someone in Oklahoma calls a person in Chad. Both calls
| take the path with fewest hops between satellites. These paths update
| as the satellites move in and out of range.

# aerobraking_demo.txt

| This example demonstrates the use of the `WSF_INTEGRATING_SPACE_MOVER`{.interpreted-text role="model"}
| to set up dynamics that include atmospheric drag. This scenario includes
| one platform on an orbit starting near the Moon, and plunging into the
| atmosphere of the Earth. The drag resulting from the operation slows the
| platform down, and over a number of passes through the atmosphere, the
| orbit apoapsis is significantly reduced.
