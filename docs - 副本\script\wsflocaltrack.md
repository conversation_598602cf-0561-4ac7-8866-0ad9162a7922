orphan

:   

# WsfLocalTrack

::: {.WsfLocalTrack .inherits .WsfTrack cloneable="" constructible=""}
WsfLocalTrack represents a platforms local perception of an object, and is the combination of one or more contributing `WsfTrack`{.interpreted-text role="class"} objects. The contributing tracks are sometimes called \'raw\' tracks and represent the products of a sensor or some other platform.
:::

## Methods

::: method
int RawTrackCount()

Return the number tracks that are contributing to this track. Contributing tracks are also called \"raw\' tracks.
:::

::: method
WsfTrackId RawTrackId(int aTrackIndex)

Return the selected track ID from the list of contributing tracks, where 0 \<= aTrackIndex \< RawTrackCount().
:::

::: method
WsfTrack RawTrack(int aTrackIndex)

Return the selected track from the list of contributing tracks, where 0 \<= aTrackIndex \< RawTrackCount().
:::

## Auxiliary Data Methods

## Contributor Methods

::: method
bool ContributorOf(WsfPlatform aPlatform) bool ContributorOf(WsfPlatform aPlatform, WsfSensor aSensor)

Returns \'true\' if raw tracks from a given platform are contributing to this local track. All sensors are considered unless a given sensor is supplied.
:::

::: method
bool SensorTypeContributor(string aSensorType)

Returns \'true\' if raw tracks from a given sensor type are contributing to this local track.
:::

::: method
int NumContributors()

Returns the number of platforms contributing to this local track.
:::

::: method
Array\<WsfTrack\> RawTracksFrom(WsfPlatform aPlatform)

Returns the list of raw tracks contributed from aPlatform.
:::

::: method
WsfTrack RawTrackFromSensorName(WsfPlatform aPlatform, string aSensorName)

Returns a track contributing to this local track detected by aPlatform\'s sensor with aSensorName. Returns null if no track was found.
:::

::: method
WsfTrack RawTrackFromSensorType(WsfPlatform aPlatform, string aSensorType)

Returns a track contributing to this local track detected by aPlatform\'s sensor with type aSensorType. Returns null if no track was found. If multiple raw tracks fit this criteria, the most recent track is returned. aSensorType must be the sensor\'s direct type, as in:

    platform...
     sensor sensor-name <u>sensor-type</u>
     ...
:::
