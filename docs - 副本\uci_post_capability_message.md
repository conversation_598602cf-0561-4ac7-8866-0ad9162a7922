orphan

:   

# UCI_POST_CapabilityMessage

## Overview {#overview .UCI_POST_CapabilityMessage .inherits .UCI_Message}

This message gives the current capabilities (modes) of the corresponding `WSF_IRST_SENSOR`{.interpreted-text role="model"}.

## Methods

::: method
UCI_POST_Capability Capability(int capabilityIndex)

Returns the capability at the indexed value.
:::

::: method
int Size()

Returns the number of capabilities available in the message.
:::
