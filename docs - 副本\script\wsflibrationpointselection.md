orphan

:   

# WsfLibrationPointSelection

## Overview {#overview .WsfLibrationPointSelection}

The `WsfLibrationPointSelection`{.interpreted-text role="class"} class provides a means to select for which libration point information is being queried in the `WsfLibrationPoint`{.interpreted-text role="class"}.

## Static Methods

::: method
WsfLibrationPointSelection L1()

Return a `WsfLibrationPointSelection`{.interpreted-text role="class"} instance representing the L1 point.
:::

::: method
WsfLibrationPointSelection L2()

Return a `WsfLibrationPointSelection`{.interpreted-text role="class"} instance representing the L2 point.
:::

::: method
WsfLibrationPointSelection L3()

Return a `WsfLibrationPointSelection`{.interpreted-text role="class"} instance representing the L3 point.
:::

::: method
WsfLibrationPointSelection L4()

Return a `WsfLibrationPointSelection`{.interpreted-text role="class"} instance representing the L4 point.
:::

::: method
WsfLibrationPointSelection L5()

Return a `WsfLibrationPointSelection`{.interpreted-text role="class"} instance representing the L5 point.
:::
