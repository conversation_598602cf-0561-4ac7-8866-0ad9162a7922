# uci_amti_settings_command_message.rst.txt.md
orphan

:   





This message allows the user to enable/disable a capability (mode) on a radar sensor.



::: method
static UCI_AMTI_SettingsCommandMessage Construct(UCI_CapabilityId aCapabilityId, UCI_CapabilityState aState)

This method constructs an instance of an UCI_AMTI_SettingsCommandMessage that will command the capability with the UUID of aCapabilityId to the state, aState. If aState is `ENABLE<UCI_CapabilityState.ENABLE>`{.interpreted-text role="method"}, the capability state will be set to UCI_AVAILABLE. `DISABLE<UCI_CapabilityState.DISABLE>`{.interpreted-text role="method"} will set the capability state to UCI_UNAVAILABLE.
:::