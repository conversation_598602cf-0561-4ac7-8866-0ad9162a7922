# advanced_behavior.rst.txt.md
orphan

:   



advanced_behaviors are similar to normal `behaviors<behavior>`{.interpreted-text role="command"}, with the addition of 3-state and blackboard functionality, as well as additional visualization features.

::: {.contents local="" depth="4"}
:::

::: {.command block=""}
advanced_behavior

::: parsed-literal

[advanced_behavior](#advanced_behavior) \<type-name\>

:   enabled \<boolean\> name \<string\> desc \<string\> color \<color-value\> script_variables \... end_script_variables on_init \... end_on_init on_message \... end_on_message precondition \... end_precondition on_new_execute \... end_on_new_execute on_new_fail \... end_on_new_fail execute \... end_execute \<finite state machine inputs\> \<zero or more nodes\>

end_advanced_behavior
:::
:::



Advanced behaviors allow the scripter to return based around 3 states rather than 2.

Inside of precondition and execute blocks, return statements should return either a double/float for weighted_random and priority_selector, or one of the 3 states below. These return statements will inform the behavior tree of what state to set for the node that tick. An optional string may be given that will set the tooltip text for the node in the Behavior Tree Tool in Mystic. This can be useful for seeing what the node is currently doing.

::: command
return \[Running(\<string\>) \| Success(\<string\>) \| Failure(\<string\>)\];
:::



::: command
name \<string\>

Name the node. The new name will be displayed on the node in the `Behavior Tree View<wkf_plugin/rv_behavior_tree_tool>`{.interpreted-text role="doc"} in Mystic.
:::

::: command
desc \<string\>

Define a description for the node. This will show when the node is hovered over in the `Behavior Tree View<wkf_plugin/rv_behavior_tree_tool>`{.interpreted-text role="doc"} in Mystic.
:::

::: command
color \<color-value\>

Set the color to display as a platform trace line in Mystic to indicate when the behavior is running.
:::