# wsfprincedormand45integrator.rst.txt.md
orphan

:   





The `WsfPrinceDormand45Integrator`{.interpreted-text role="class"} is a `WsfOrbitalIntegrator`{.interpreted-text role="class"} that implements an embedded Runge-Kutta scheme with a 5th order solution and an embedded 4th order estimate used to control the error in the integration (see also, `Prince-Dormand 45 <orbital_integrator_models.dormand_prince_45>`{.interpreted-text role="ref"}).

