# propagator.rst.txt.md
orphan

:   



::: {.command block=""}
propagator \... end_propagator

::: parsed-literal

propagator *\<propagator-type\>*

:   \...

end_propagator
:::
:::

An orbital propagator determines the future kinematic state (position and velocity) of a space platform based on a given initial state. Propagators are utilized in AFSIM to model the orbital motion of satellites with space movers (e.g., see `WSF_SPACE_MOVER`{.interpreted-text role="model"}), as well as to track estimated satellite locations (e.g., see `orbit determination fusion<orbit_determination_fusion>`{.interpreted-text role="doc"}).



See `Predefined_Propagator_Types`{.interpreted-text role="ref"} for a list of available propagator types.