orphan

:   

# filter

see `Predefined_Filter_Types`{.interpreted-text role="ref"}

::: command
filter \... end_filter
:::

::: parsed-literal
\# Define a filter type (occurs outside a `track_manager`{.interpreted-text role="command"} or `sensor`{.interpreted-text role="command"} block)

filter \<name\> \<base-type\>

:   \... type-specific filter commands \...

end_filter

\# Instantiate a filter object

platform \... (or platform_type)

:   

    track_manager

    :   

        filter \<filter-type\>

        :   \... filter commands \...

        end_filter

    end_track_manager

end_platform

sensor \...

:   

    filter \<filter-type\>

    :   \... filter commands \...

    end_filter

end_sensor
:::

## Overview

A filter is an object that can be attached to a `track_manager`{.interpreted-text role="command"} or a `sensor`{.interpreted-text role="command"} to implement track filtering.

## Commands

Commands vary by filter type. See the list of `Predefined_Filter_Types`{.interpreted-text role="ref"} for specific commands.
