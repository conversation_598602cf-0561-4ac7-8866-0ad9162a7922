orphan

:   

# WsfPathFinder

## General Methods {#general-methods .WsfPathFinder .inherits .WsfObject}

::: method
WsfGeoPoint FindClosestPointOnEdge(WsfGeoPoint aStartPoint)

Returns the closest valid path node on the nearest edge of the pathable grid area.
:::

::: method
WsfGeoPoint FindClosestValidPoint(WsfGeoPoint aStartPoint)

Returns the closest valid path node on the pathable grid area that is not inside of a zone.
:::

::: method
bool NearEndOfPath(WsfPlatform aPlatform, double aMaximumDistance)

Returns true if the given platform\'s location is less than the given maximum distance from the end waypoint of it\'s current route. The distance is calculated as the magnitude of the vector from the platform to the route endpoint.
:::

::: method
WsfGeoPoint RandomLocation()

Returns a random path node on the pathable grid area.
:::

## Debug Methods

::: method
void SetDebugEnabled(bool aDebugEnabled)

Toggles debug mode on the pathfinder object.
:::

::: method
bool DebugEnabled()

Returns true if debugging is enabled on the pathfinder object.
:::

::: method
void DebugDrawGrid()

Draws the pathing grid of nodes using `WsfDraw`{.interpreted-text role="class"} points.
:::

::: method
void DebugDrawZones()

Draws all zones using `WsfDraw`{.interpreted-text role="class"} lines.
:::
