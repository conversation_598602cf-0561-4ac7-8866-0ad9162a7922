orphan

:   

# Azimuth-Elevation Table Definition {#Azimuth-Elevation_Table_Definition}

::: block
\_.az_el_table
:::

Platform signatures (`acoustic_signature`{.interpreted-text role="command"}, `infrared_signature`{.interpreted-text role="command"}, `optical_signature`{.interpreted-text role="command"}, `radar_signature`{.interpreted-text role="command"}),and `antenna gain patterns <antenna_pattern>`{.interpreted-text role="command"} are examples of things that are a function of azimuth and elevation. These commands provide the ability to define the function using a table of discrete values.

There are several formats in which a table definition can be entered.

## Define a table with constant value

::: command
constant \<value\> \<value-units\>

*\<value-units\>*

:   Units of the data value. The units (dB, dBsm, m\^2, absolute, etc.) must be consistent with the application (e.g., antenna pattern, signature) for which the table is intended.

Defines a table whose value is constant throughout the whole spherical domain.
:::

**Example:**:

    constant -20 dBsm

## Define a pattern with an inline table

::: command
inline_table \... end_inline_table

    inline_table *<value-units> <number-of-az-values> <number-of-el-values>*
        *      el1   el2   el3      eln*
        *az1   v11   v12   v13      v1n*
        *az2   v21   v22   v23      v2n*
        *az3   v31   v32   v33      v3n*

        *azm   vm1   vm2   vm3      vmn*
    end_inline_table

**\<value-units\>**

:   Units of the data values in the table. The units (e.g., dB, dBsm, m\^2, absolute) must be consistent with the application (e.g., antenna pattern, signature) for which the table is intended.

**\<number-az-values\>**

:   Number of azimuth values in the definition. Valid values are \>= 2.

**\<number-of-el-values\>**

:   Number of elevation values in the definition. Valid values are \>=2.

**el**~n~

:   Elevation values in degrees. Valid values of -90 deg \<= *el\<sub\>n\</sub\>* \<= 90 deg in increasing order from *el1* to *eln*.

**az**~m~

:   Azimuth values in degrees. Valid values of -180 deg \<= *az\<sub\>m\</sub\>* \<= 180 deg in increasing order from *az1* to *azm*.

**v**~mn~

:   Data values in units *\<value-units\>*.

Note that the table data does not have to be formatted in rows and columns as illustrated in the above example. WSF simply reads the required number of values.

**Example:**:

    inline_table dBsm 5 3
             -90   0    90
       -180   10   20   10
        -90    5   10    5
          0    0    0    0
         90    5   10    5
        180   10   20   10
    end_inline_table
:::

## Read a table from a file

::: command
file \<file-name\> units \<value-units\>

Loads the file with \<file-name\> consistent with the expected format (SALRAM convention).

**\<value-units\>**

:   Units of the data values in the table. The units (e.g., dB, dBsm, m\^2, absolute) must be consistent with the application (e.g., antenna pattern, signature) for which the table is intended.

The format of the file is as follows:

    *header-line-1*
    *header-line-2*
    *header-line-3*
    *<number-of-az-values> <number-of-el-values>*
    *      el1   el2   el3      eln*
    *az1   v11   v12   v13      v1n*
    *az1   v21   v22   v23      v2n*
    *az1   v31   v32   v33      v3n*

    *azm   vm1   vm2   vm3      vmn*

Where

**header-line-n**

:   Three user-definable header lines. The contents are not used by WSF.

**\<number-az-values\>**

:   Number of azimuth values in the definition. Valid values are \>= 2.

**\<number-of-el-values\>**

:   Number of elevation values in the definition. Valid values are \>= 2.

**el**~n~

:   Elevation values in degrees. Valid values of -90 deg \<= *el\<sub\>n\</sub\>* \<= 90 deg in increasing order from *el1* to *eln*.

**az**~m~

:   Azimuth values in degrees. Valid values of -180 deg \<= *az\<sub\>m\</sub\>* \<= 180 deg in increasing order from *az1* to *azm*.

**v**~mn~

:   Data values in units *\<value-units\>*.

::: note
::: title
Note
:::

The table data does not have to be formatted in rows and columns as illustrated in the above example. WSF simply reads the required number of values.
:::
:::
