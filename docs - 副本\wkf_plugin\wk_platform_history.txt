# wk_platform_history.rst.txt.md
orphan

:   



A platform\'s positional history can be shown either with trace lines or wing ribbons.





Trace lines and wing ribbons can be toggled on/off from the Platform Options.







The line width determines the line width in pixels of the trace lines.

The length option determines how much time is displayed from the current position to the end of the trace line.

Trace lines may be colored by team color, platform name, or state.

When *state* is selected the trace-line use the configured colors and states (default, detected, tracked, attacked and killed.)



The length option determines how much time is displayed from the current position to the end of the wing ribbon. The width scale allows the ribbon to be exaggerated. The color scheme may be team color, green, or gray. The transparency on death will change the transparency of the wing ribbon when a platform is killed