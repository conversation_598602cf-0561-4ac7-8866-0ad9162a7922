# wiz_rotate.rst.txt.md
orphan

:   





The \"Rotate Scenario\" dialog can be used to rotate an entire scenario or a selection of objects on the Map Display. This tool can be found in the Map Utilities menu under Tools.

::: warning
::: title
Warning
:::

The Rotate tools are incapable of changing the position and orientation of platforms with space movers, as most of the time, the movers are not defined with the 6 degrees of freedom, but by other parameters.
:::





The menu has the following items:

-   \"Rotate Scenario\": this item sets the dialog in a mode that allows the user to rotate an entire scenario.
-   \"Rotate Selection\": this item sets the dialog in a mode that allows the user to rotate a selection of objects.

::: note
::: title
Note
:::

If there is nothing on the map, no menu option is enabled. If there is nothing selected on the map, only the \"Rotate Scenario\" option is enabled.
:::



The line edit allows the user to type in an angle of rotation.

::: note
::: title
Note
:::

The format for the angle is: \"v u,\" where v is the decimal value and u is the units. A space is between the value and unit.
:::

It initializes the line edit with the angle 0. The unit is determined by preferences, but can be changed to any of the supported units in AFSIM.

The dial allows the user to rotate in an \"animated\" style.

::: note
::: title
Note
:::

The dial only rotates by an integral degree.
:::