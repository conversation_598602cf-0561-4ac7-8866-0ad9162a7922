orphan

:   

# DisFire {#DisFire}

::: {.DisFire .inherits .DisPdu}
::: parsed-literal
`DisFire.FiringEntity`{.interpreted-text role="method"} `DisFire.TargetEntity`{.interpreted-text role="method"} `DisFire.WeaponEntity`{.interpreted-text role="method"} `DisFire.Event`{.interpreted-text role="method"} `DisFire.Location`{.interpreted-text role="method"} `DisFire.WeaponType`{.interpreted-text role="method"} `DisFire.Warhead`{.interpreted-text role="method"} `DisFire.Fuse`{.interpreted-text role="method"} `DisFire.Quantity`{.interpreted-text role="method"} `DisFire.Rate`{.interpreted-text role="method"} `DisFire.Velocity`{.interpreted-text role="method"} `DisFire.Range`{.interpreted-text role="method"}
:::
:::

## Overview

[DisFire](#disfire) is an implementation of the DIS fire PDU. Fire PDUs are used to indicate that a munition has been fired from a platform.

## Methods

::: method
DisEntityId FiringEntity()

Returns the ID of the platform that fired the munition.
:::

::: method
DisEntityId TargetEntity()

Returns the ID of the platform that is fired upon.
:::

::: method
DisEntityId WeaponEntity()

Returns the ID of the platform representing the munition.
:::

::: method
DisEventId Event()

Returns the event ID. The event ID on the detonation PDU should be the same as one on the related `DisDetonation`{.interpreted-text role="class"}.
:::

::: method
Array \<double\> Location()

Returns the location of the munition at time of detonation. The location is in earth centered meters.
:::

::: method
DisEntityType WeaponType()

Returns the type of the munition.
:::

::: method
int Warhead()

Returns the warhead ID.
:::

::: method
int Fuse()

Returns the fuse ID.
:::

::: method
int Quantity()

Returns the quantity of munitions.
:::

::: method
int Rate()

Returns the rate of detonation.
:::

::: method
Array \<double\> Velocity()

Returns the velocity of the munition at time of detonation. The velocity is in meters per second and earth centric coordinates.
:::

::: method
int Range()

Returns the range for the event.
:::
