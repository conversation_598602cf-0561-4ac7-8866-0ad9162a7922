# wiz_color_utils.rst.txt.md
orphan

:   



The Color Utils Wizard plugin allows the user to construct and view script colors. This plugin also allows the user to view `platform.side`{.interpreted-text role="command"} colors.





Right clicking on the `Color.Construct`{.interpreted-text role="method"} script method will add the \"Choose color\...\" option to the context menu. Selecting this option will bring up a color dialog for the user to choose a customized color. Once the OK button is clicked, the construct method will populate with the \[0, 255\] red, green, blue, and alpha (RGBA) values of the selected color.

::: note
::: title
Note
:::

If there are existing values, they will be replaced.
:::







This plugin allows the user to hover over `Color`{.interpreted-text role="class"} static script methods or the side name of the `platform.side`{.interpreted-text role="command"} command to view what the color looks like in AFSIM. If a color or side is unknown, the tip will show the \"default\" color defined in `Team Visibility<../wkf_pref_team_visibility>`{.interpreted-text role="doc"}.