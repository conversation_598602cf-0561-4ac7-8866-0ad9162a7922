orphan

:   

# Warlock Reference Guide

The Warlock Reference Guide lists the features included within Warlock organized into various categories.

## Start-up

-   `Command Line Options<warlock_command_line>`{.interpreted-text role="doc"}
-   `Start-up Dialog<warlock_start_dialog>`{.interpreted-text role="doc"}

## Application Layout

![image](images/warlock_application_screen.png)

This image above shows the layout of Warlock with the major components labeled.

Many of these components are dockable and may be moved to other locations in the window, or into their own \'floating\' windows.

-   Menus

> -   `File<warlock_file_menu>`{.interpreted-text role="doc"}
> -   `View<warlock_view_menu>`{.interpreted-text role="doc"}
> -   `Options<warlock_options_menu>`{.interpreted-text role="doc"}
> -   `Tools<warlock_tools_menu>`{.interpreted-text role="doc"}
> -   `Developer<warlock_developer_menu>`{.interpreted-text role="doc"}
> -   `Run<warlock_run_menu>`{.interpreted-text role="doc"}
> -   `Help<warlock_help>`{.interpreted-text role="doc"}

-   `Application Launcher<wkf_plugin/wk_application_launcher>`{.interpreted-text role="doc"}
-   `Central View<warlock_central_display>`{.interpreted-text role="doc"}
-   `Map Layer Manager<wkf_map_layer_manager>`{.interpreted-text role="doc"}
-   `Platform Browser<wkf_plugin/wk_platform_browser>`{.interpreted-text role="doc"}
-   `Platform Details<wkf_plugin/wk_platform_data>`{.interpreted-text role="doc"}
-   `Platform Options<warlock_platform_options>`{.interpreted-text role="doc"}
-   `Simulation Controller<wkf_plugin/wk_sim_controller>`{.interpreted-text role="doc"}
-   `Map Toolbar<wkf_plugin/wk_map_toolbar>`{.interpreted-text role="doc"}
-   Status Bar

## Dialogs, Views and Toolbars

Most of the Dialogs and Toolbars can be accessed through the `View <warlock_view_menu>`{.interpreted-text role="doc"} menu. A few are accessed through the `Context<warlock_platform_context_menus>`{.interpreted-text role="doc"} menu.

## Application Configuration

The following items control the appearance of Warlock.

-   `Configuration Files<warlock_user_configurations>`{.interpreted-text role="doc"}
-   `Map Definitions<warlock_map_definitions>`{.interpreted-text role="doc"}
-   `Model Configuration<wkf_model_definitions>`{.interpreted-text role="doc"}
-   `Permission Manager<warlock_permission_manager>`{.interpreted-text role="doc"}
-   `Platform Options<warlock_platform_options>`{.interpreted-text role="doc"}
-   `Plug-in Manager<warlock_plugin_manager>`{.interpreted-text role="doc"}
-   `Preferences<warlock_preferences>`{.interpreted-text role="doc"}

## Platforms

The following items describe interactions with Platforms not mentioned in the above sections.

-   `Context Menus<warlock_platform_context_menus>`{.interpreted-text role="doc"}
-   `Platform Selection<warlock_selection>`{.interpreted-text role="doc"}

## Plug-in Features

The following is a comprehensive list of functionality provided by Warlock extensions.

::: {.toctree glob="" titlesonly=""}
wkf_plugin/[wk]()\*
:::

## Demos

The following demos are provided to demonstrate a variety of Warlock capabilities.
