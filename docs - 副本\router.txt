# router.rst.txt.md
orphan

:   



See `Predefined_Router_Types`{.interpreted-text role="ref"}

Script Class: `WsfCommRouter`{.interpreted-text role="class"}

::: {.command block=""}
router \<name-or-type\> \<base-type-name\> \... end_router

::: parsed-literal

[router](#router) \<name-or-type\> \<base-type-name\>

:   \... `Platform_part_commands`{.interpreted-text role="ref"} \... \... `router_protocol`{.interpreted-text role="command"} Commands \... \... `medium`{.interpreted-text role="command"} Commands \...

    [gateway_address]() \<address\> [gateway]() \<comm-name\> [hop_limit]() \<integer-value\> [automated_interface_linking]() \<boolean-value\> [use_default_protocol]() \<boolean-value\> [use_multicast_protocol]() \<boolean-value\>

end_router
:::
:::



A router object represents a device that provides the ability to determine if a message can be sent to its destination, and which path that message should take to reach its destination. It is associated uniquely with comm interfaces on the same platform that use the router instance when messages are sent and received.

Any number of routers may be used on a platform instance, but each comm may only be associated with a single router. This allows the usage of multiple routers with unique comm interfaces on every platform instance in AFSIM.

By default, every platform in AFSIM has a router, and unless specified, all comms will be assigned to this default router.

::: note
::: title
Note
:::

The default router has the name \"default\". If users want to override the default router settings, they simply need to define and assign a router to the platform using the same name, \"default\".
:::

Default router behavior may be modified with the usage of `router_protocol`{.interpreted-text role="command"} commands.

::: note
::: title
Note
:::

The router is required at this time to be on and operational upon instantiation in the simulation to support legacy communications behavior. The `Platform_part_commands`{.interpreted-text role="ref"} that initially disable the router are not currently supported, and will not affect the router object. However, usage of the `WsfPlatformPart`{.interpreted-text role="class"} script methods that disable or turn off the router will still function. It is expected that these commands will be fully supported in AFSIM 3.0.
:::

All comm interfaces assigned to a router with connections between those comms are considered bridged, in that communications can pass between them regardless of the comm model type and reception/transmission restrictions defined on the comm model. Thus, the router object assumes the capabilities previously provided directly via the comm bridge command.

::: command
gateway_address \<address\>

If provided, defines the particular interface associated with this router that should be used to forward messages if the router is unable to find a destination for a message received on one of the router\'s defined comm interfaces. The destination used for the selected comm gateway is defined on the comm itself.

This command provides a static addressing alternative to the [gateway]() command, and only one of these commands should be used on any given router.

Default No gateway address specification or usage.
:::

::: command
gateway \<comm-name\>

If provided, defines the particular interface associated with this router that should be used to forward messages if the router is unable to find a destination for a message received on one of the router\'s defined comm interfaces. The destination used for the selected comm gateway is defined on the comm itself.

This command provides a dynamic addressing alternative to the [gateway_address]() command, and only one of these commands should be used on any given router.

Default No gateway specification or usage.
:::

::: command
hop_limit \<integer\>

Specifies the number of nodes/comms that a message may visit on the way to the destination before the message is simply discarded. Often referred to as time to live (TTL). The hop_limit applies to any messages initiated from this router\'s interfaces. This value ensures that messages that may enter circular routing are eventually dropped.

Default 64
:::

::: command
automated_interface_linking \<boolean-value\>

If set to true, all interfaces specified for this router will have connections created between members. This ensures that router interfaces are correctly represented as having direct connections to each other via the router hardware interface.

If set to false, no connections will be automatically created between members, and messages sent or received from any particular interface on the router may not be able to reach another interface on the same router.

::: warning
::: title
Warning
:::

Some network types are not compatible with [automated_interface_linking](), and the user should not enable this setting if the network topology will be violated.
:::

Default false
:::

::: command
use_default_protocol \<boolean-value\>

A convenience setting to determine if the router should use the default legacy routing protocol. True indicates to use this routing_protocol, false to remove it.

Default true
:::

::: command
use_multicast_protocol \<boolean-value\>

A convenience setting to determine if the router should use the default multicast routing protocol. True indicates to use this routing_protocol, false to remove it.

Default true
:::