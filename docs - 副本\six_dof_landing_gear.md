orphan

:   

# SixDOF Landing Gear {#SixDOF_Landing_Gear_Label}

The landing gear block defines the landing gear on a `rigid_body_vehicle_type`{.interpreted-text role="command"}. The main components of [landing_gear]() are [ground_reaction_point]() objects.

::: command
landing_gear \... end_landing_gear

::: parsed-literal
landing_gear

> [nws_enable_control_name]() \...
>
> [nominal_height_above_ground_on_gear]() \...
>
> [ground_reaction_point]() NoseGear
>
> :   \...
>
> end_ground_reaction_point
>
> [ground_reaction_point]() LeftMainGear
>
> :   \...
>
> end_ground_reaction_point
>
> [ground_reaction_point]() RightMainGear
>
> :   \...
>
> end_ground_reaction_point

end_landing_gear
:::
:::

::: command
nws_enable_control_name \<string\>

This specifies the nose wheel steering (NWS) enable control input.
:::

::: command
nominal_height_above_ground_on_gear \<length-value\>

This specifies the height above the ground for the reference point of the aircraft when resting on the landing gear.
:::

::: command
ground_reaction_point \... end_ground_reaction_point

::: parsed-literal
ground_reaction_point \<string\>

> // Retraction Control Name [control_surface_name]() \...
>
> // Steering Control Name [steering_control_surface_name]() \... [nws_angle_control_surface_name]() \...
>
> // Nose Gear Flag [is_nose_gear]() \...
>
> // Braking Control Name [braking_control_surface_name]() \...
>
> // Relative Position [gear_extended_relative_position_x]() \... [gear_extended_relative_position_y]() \... [gear_extended_relative_position_z]() \...
>
> // Compression Vector [gear_compression_vector_x]() \... [gear_compression_vector_y]() \... [gear_compression_vector_z]() \...
>
> // Rolling Vector [gear_rolling_vector_x]() \... [gear_rolling_vector_y]() \... [gear_rolling_vector_z]() \...
>
> // Spring/Damper [spring_constant_lbs_per_ft]() \... [damper_constant_lbs_per_fps]() \...
>
> // Compression Data [uncompressed_length]() \... [max_compression]() \...
>
> // Friction Data [rolling_coefficient_of_friction]() \... [braking_coefficient_of_friction]() \... [scuffing_coefficient_of_friction]() \... [ignore_friction]() \...
>
> // Landing Gear or Contact Point [is_landing_gear]() \... [is_contact_point]() \...

end_ground_reaction_point
:::
:::

::: command
control_surface_name \<string\>

This specifies the name of the retraction control surface.
:::

::: command
steering_control_surface_name \<string\>

This specifies the name of the steering control surface (when NWS is not engaged/active).
:::

::: command
nws_angle_control_surface_name \<string\>

This specifies the name of the steering control surface (when NWS is engaged/active).
:::

::: command
is_nose_gear \<boolean-value\>

This indicates whether or not this [ground_reaction_point]() is a nose gear.
:::

::: command
braking_control_surface_name \<string\>

This specifies the name of the braking control surface. If unspecified, no braking is provided for this [ground_reaction_point]().
:::

::: command
gear_extended_relative_position_x \<length-value\>

This is the x-location (in object coordinates) of the bottom of the landing gear (or contact point) when extended.
:::

::: command
gear_extended_relative_position_y \<length-value\>

This is the y-location (in object coordinates) of the bottom of the landing gear (or contact point) when extended.
:::

::: command
gear_extended_relative_position_z \<length-value\>

This is the z-location (in object coordinates) of the bottom of the landing gear (or contact point) when extended.
:::

::: command
gear_compression_vector_x \<real-value\>

This is the x-direction (in object coordinates) of the compression vector.
:::

::: command
gear_compression_vector_y \<real-value\>

This is the y-direction (in object coordinates) of the compression vector.
:::

::: command
gear_compression_vector_z \<real-value\>

This is the z-direction (in object coordinates) of the compression vector.
:::

::: command
gear_rolling_vector_x \<real-value\>

This is the x-direction (in object coordinates) of the rolling vector.
:::

::: command
gear_rolling_vector_y \<real-value\>

This is the y-direction (in object coordinates) of the rolling vector.
:::

::: command
gear_rolling_vector_z \<real-value\>

This is the z-direction (in object coordinates) of the rolling vector.
:::

::: command
spring_constant_lbs_per_ft \<real-value\>

This is the spring constant in lbs/ft.
:::

::: command
damper_constant_lbs_per_fps \<real-value\>

This is the damper constant in lbs/(ft/sec).
:::

::: command
uncompressed_length \<length-value\>

This is the uncompressed length of the landing gear.
:::

::: command
max_compression \<length-value\>

This is the maximum length of compression.
:::

::: command
rolling_coefficient_of_friction \<real-value\>

This is the rolling coefficient of friction.
:::

::: command
braking_coefficient_of_friction \<real-value\>

This is the braking coefficient of friction.
:::

::: command
scuffing_coefficient_of_friction \<real-value\>

This is the scuffing coefficient of friction.
:::

::: command
ignore_friction \<boolean-value\>

This indicates if friction should be ignored.
:::

::: command
is_landing_gear \<boolean-value\>

This indicates if the [ground_reaction_point]() is a landing gear (true) or a contact point (false).
:::

::: command
is_contact_point \<boolean-value\>

This indicates if the [ground_reaction_point]() is a contact point (true) or a landing gear (false).
:::

------------------------------------------------------------------------

Return to `rigid_body_vehicle_type`{.interpreted-text role="doc"}
