# turbulence_model.rst.txt.md
orphan

:   





Accounting for atmospheric turbulence in infrared and optical wavelengths is important to AFSIM laser propagation because it produces an average beam spreading effect, independent of other considerations like diffraction and wavefront error, that reduces the average beam power impacting a high-energy-laser target or laser communications receiver.



AFSIM implements the isoplantic relation for turbulent beam spread angle:

$\theta_0 = [2.91 k^2 \int_{0}^{L} C_n^2(z) z^\frac{5}{3}dz]^\frac{-3}{5}$

where $z(h)$ is a function relating altitude, $h$, to the distance along the path from receiver to transmitter, with length $L$. $k = \frac{2\pi}{\lambda}$ is the wave number for characteristic laser wavelength $\lambda$, and $C_n^2$ is the atmospheric structure parameter, having units of $m^{-2/3}$.

::: note
::: title
Note
:::

When evaluating this integral it is understood that actual $C_n^2$ expressions are typically functions of $h$, not $z$.
:::



There are various documented $C_n^2$ models. Currently implemented in AFSIM is the Hufnagel-Valley (5/7) (\"hv57\") atmospheric structure function, which takes the form:

$C_n^2(h)= 8.2 \times 10^{-26}W^2 (\frac{h}{1000})^{10} e^{-h/1000} + 2.7\times 10^{-16} e^{-h/1500} + A e^{-h/100}$

where $h$ is the height above the surface where the function is computed (meters), $A=1.7\times10^-14$ and $W=21$.



\"The Effect of Atmospheric Optical Turbulence on Laser Communication Systems: Part 1, Theory\" Thomas C Farrell, Air Force Research Laboratory, Space Vehicles Directorate 3550 Aberdeen Ave., SE, Kirtland AFB, NM 87117-5776; Unpublished