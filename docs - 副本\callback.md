orphan

:   

# callback

::: {.command block=""}
callback \<name\> \... end_callback
:::

::: parsed-literal

callback \<name\> **WSF_SCRIPT_CALLBACK**

:   \... Callback [Commands](#commands)\...

end_callback
:::

## Overview

callback provides a mechanism to trigger one or more scripts when a platform reaches a waypoint. The script that the callback triggers can be executed in the context of the platform itself or one of its script processors.

## Commands

::: command
execute \<name\> \[in \<processor-name\>\]

Specify the script that the callback should call when it is triggered. As an optional parameter you can specify a processor that the callback should execute in. To trigger more than one script when the callback occurs, simply include multiple **execute** statements. To add a callback to a waypoint, see the `route.execute`{.interpreted-text role="command"} command of the `route`{.interpreted-text role="command"} and the `AddCallbackToWaypoint <WsfRoute>`{.interpreted-text role="class"} script method.
:::
