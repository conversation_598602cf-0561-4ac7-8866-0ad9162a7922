# rv_head_down_view.rst.txt.md
orphan

:   



::: warning
::: title
Warning
:::

The Head Down View plugin is DEPRECATED and no longer supported. Users should avoid using it, since it will be removed from AFSIM in the near future. The `ACES Display<../wkf_plugin/rv_aces_display>`{.interpreted-text role="doc"} should be used instead and provides many of the same features plus other capabilities.
:::

The Head Down View shows a glass-cockpit-like head down display for the selected platform. The tool is available on the context menu when right clicking on a platform. Multiple head down views may be opened at the same time.

::: note
::: title
Note
:::

For all available data to be shown, a platform with a `Situation Awareness Processor <../wsf_sa_processor>`{.interpreted-text role="doc"} is necessary.
:::

::: note
::: title
Note
:::

The Head Down View is a prototype capability and is not complete. As a result of this, it is disabled by default and must be enabled through the `Plugin Manager <../warlock_plugin_manager>`{.interpreted-text role="doc"}.
:::



The display is broken into 12 default pages. Pages may be expanded or contracted by clicking on the arrows at the bottom. The pages are identified by the following numbers (default pages may be picked in Preferences):





Pages may be changed by opening the menu button at the top left of one of the main pages (1-4).



Below is a list of pages along with their current functionality:

+-------+--------------------------------------------------------------------------+
| Page  | > Function                                                               |
+=======+==========================================================================+
| ASR   | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| CKLST | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| CNI   | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| DAS   | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| DIM   | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| EFI   | Shows pitch and roll                                                     |
+-------+--------------------------------------------------------------------------+
| ENG   | Shows throttle, thrust, and fuel flow values                             |
+-------+--------------------------------------------------------------------------+
| FCS   | Not Implemented (Visual Placeholder)                                     |
+-------+--------------------------------------------------------------------------+
| FTA   | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| FTI   | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| FUEL  | Shows fuel information such as tank levels, fuel flow, and time to empty |
+-------+--------------------------------------------------------------------------+
| HUD   | (Visual Placeholder) Shows heading, speed (KTAS), and altitude           |
+-------+--------------------------------------------------------------------------+
| ICAWS | Not Implemented (Implemented in header)                                  |
+-------+--------------------------------------------------------------------------+
| NAV   | Shows an overhead map view of the aircraft (Not currently in menu)       |
+-------+--------------------------------------------------------------------------+
| PWM   | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| SMS   | Shows information on weapons, master arm, and external fuel tanks        |
+-------+--------------------------------------------------------------------------+
| SACH  | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| TFLIR | Not Implemented (Visual Placeholder)                                     |
+-------+--------------------------------------------------------------------------+
| TSD-1 | Shows the tactical situation using track information                     |
+-------+--------------------------------------------------------------------------+
| TSD-2 | Shows the tactical situation using track information                     |
+-------+--------------------------------------------------------------------------+
| TSD-3 | Shows the tactical situation using track information                     |
+-------+--------------------------------------------------------------------------+
| TWD   | Shows RWR/ESM threats                                                    |
+-------+--------------------------------------------------------------------------+
| WPN-A | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+
| WPN-S | Not Implemented                                                          |
+-------+--------------------------------------------------------------------------+





The header bar at the top of the display shows off twelve different regions of information. From left to right:

+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Region        | > Function                                                                                                                               |
+===============+==========================================================================================================================================+
| Engine        | Shows the thrust and throttle values                                                                                                     |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Fuel          | Shows gross weight, as well as total, internal, and external fuel values                                                                 |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Weapons       | Shows weapons and their quantities. The selected weapon will show white                                                                  |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Landing Gear  | Shows the landing gear positions. Empty Green means the gear is up, red means the gear is moving, and solid green means the gear is down |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| ICAWS         | Shows three warnings: Master Caution, Master Warning, and Stall Warning                                                                  |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Blank         | None (Empty space)                                                                                                                       |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| COM           | None (Visual only)                                                                                                                       |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| NAV           | None (Visual only)                                                                                                                       |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| ADF/XPDR      | None (Visual only)                                                                                                                       |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Menu          | None (Visual only)                                                                                                                       |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Platform Info | Shows platform altitude(ft), heading (deg), and speed (KTAS)                                                                             |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+
| Sim Time      | Shows the time since simulation start                                                                                                    |
+---------------+------------------------------------------------------------------------------------------------------------------------------------------+





The artificial horizon will show the platform orientation.





The engine display will show the thrust, throttle, and fuel flow (pounds per hour) values for the platform.





The fuel display will show information on total, internal, and external fuel levels, as well as fuel flow, time and distance to empty, and joker and bingo levels. Units are pounds.





The HUD repeater display shows the platform\'s heading (deg), altitude (ft), and true airspeed (knots).





The moving map display will show the platform\'s location over a map. The map is determined by the navigational map profile in the Map Definition preferences. The map may be zoomed in and out using the mouse wheel.





The SMS display shows the platform\'s current weapon and external fuel tank configuration.



Weapons will be shown in a two row 16 weapon layout if more than 8 weapons are present, or otherwise in a one row 8 weapon layout. Weapons will be loaded from the middle outward depending on each weapons draw type. The currently supported draw types are Bomb, SRM, and MRM. They are loaded into the display in this order. The currently selected weapon will be shown in white.

::: note
::: title
Note
:::

The weapon name must contain the string listed (Case insensitive), or it will default to drawing as an MRM.
:::

  ----------------------------------------
  Weapon Name Contains         Draw Type
  ---------------------------- -----------
  \"bomb\", \"agm\"            Bomb

  \"srm\", \"fox2\", \"srv\"   SRM

  default                      MRM
  ----------------------------------------



If the master arm is off, a SAFE box will be shown with a cyan outline. When the master arm is switched on, the box will turn green. If a weapon is then selected, it will be shown in a box below the master arm box. If the weapon is out of ammo, its box will turn red.



Countermeasure counts for chaff, decoys, and flares are shown. A doors label indicates the state of weapon bay doors (open/closed).





::: note
::: title
Note
:::

If Page 1 in the preferences is set to TSD, it will be expanded to maximum size by default.
:::

The TSD shows the current \'Tactical Situation\' for the current platform using track data. Entities are colored depending on identification information provided in the track data. Hovering over an entity with the mouse will show additional information about that item in the lower right of the TSD. Clicking the item will keep the window displayed until either some location away from the entity is clicked, or the track is lost.



If the master arm is off, a SAFE box will be shown with a cyan outline. When the master arm is switched on, the box will turn green. If a weapon is then selected, it will be shown in a box below the master arm box. If the weapon is out of ammo, its box will turn red.



Entities are drawn as one of the following, depending on their identification:

+----------------+---------------+---------------------------------------------------------------------------------------------+
| Identification | > Symbol      |                                                                                             |
+================+===============+=============================================================================================+
| Bogie/Unknown  | Yellow square | {.align-middle width="0.20833in" height="0.34722in"}       |
+----------------+---------------+---------------------------------------------------------------------------------------------+
| Neutral        | White square  | {.align-middle width="0.20833in" height="0.34722in"}   |
+----------------+---------------+---------------------------------------------------------------------------------------------+
| Bandit/Threat  | Red triangle  | {.align-middle width="0.20833in" height="0.34722in"}     |
+----------------+---------------+---------------------------------------------------------------------------------------------+
| Friendly       | Green circle  | {.align-middle width="0.20833in" height="0.34722in"} |
+----------------+---------------+---------------------------------------------------------------------------------------------+
| Flight         | Blue circle   | {.align-middle width="0.20833in" height="0.34722in"}     |
+----------------+---------------+---------------------------------------------------------------------------------------------+



The TSD includes the following buttons, which may be left-clicked with the mouse:

+---------------+-------------------------------------------------------------------------------------------+
| Button        | > Action                                                                                  |
+===============+===========================================================================================+
| Range Up/Down | Changes the current range in nm. Ranges include: 5, 10, 20, 40, 80, 160, 320, and 640 nm. |
+---------------+-------------------------------------------------------------------------------------------+
| Air           | Toggles visibility of air domain entities                                                 |
+---------------+-------------------------------------------------------------------------------------------+
| Gnd           | Toggles visibility of ground domain entities                                              |
+---------------+-------------------------------------------------------------------------------------------+
| Waypt         | Toggles visibility of waypoints/routes                                                    |
+---------------+-------------------------------------------------------------------------------------------+





The TWD shows ESM/RWR tracks along with a possible identification id (Id is not implemented yet, so a \"U\" for unknown is used). To get data into this display, a track processor with either the name \"esm\" or \"rwr\" (case insensitive) must be used. Threats will show in the outer ring of the display as a white square with an id string in the middle.



Countermeasure counts for chaff, decoys, and flares are shown.





In the preferences, the resolution as well as the default page for each slot may be set.

::: note
::: title
Note
:::

If Page 1 is set to TSD, it will be expanded to maximum size by default.
:::