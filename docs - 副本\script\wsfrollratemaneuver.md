orphan

:   

# WsfRollRateManeuver

## Overview {#overview .WsfRollRateManeuver .inherits .WsfManeuver}

This maneuver sets the target roll rate of the platform to which it is assigned. This maneuver is done executing as soon as the target is set.

## Methods

::: method
WsfRollRateManeuver Construct(double aRollRateDegPerSec)

Construct a maneuver that sets a target roll rate for the platform to which the maneuver is assigned.
:::

::: method
double GetRollRate()

Return this maneuver\'s target roll rate in degrees per second.
:::
