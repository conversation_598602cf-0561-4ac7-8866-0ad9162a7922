orphan

:   

# WsfCommGraphEdge

## Description {#description .WsfCommGraphEdge}

The `WsfCommGraphEdge`{.interpreted-text role="class"} represents an edge in a `WsfCommGraph`{.interpreted-text role="class"}. In the comm framework, graphs are used to represent network states, routing tables, etc. An edge in such a graph is directional, and indicates that communication is possible from the node (comm interface) in which the edge originates to the destination.

## Methods

::: method
WsfAddress SourceAddress()

Provides the source address for this edge. Since edges in comm graphs are directional, this address indicates the node (comm interface) from which this edge originates, and can successfully initiate communications.
:::

::: method
WsfAddress DestinationAddress()

Provides the destination address for this edge. Since edges in comm graphs are directional, this address indicates the node (comm interface) that can successfully receive a communication.
:::

::: method
double Weight()

Provides the weight, or the cost, associated with sending a message using the source and destination interface represented by this edge.

Edge weights are typically 1 in most applications, where hop number is the desired pathing metric. The usage is usually dependent on routing protocol implementation, and can represent one or many factors in determination of the best path to a destination in the graph. However, lower values are always considered better for pathing determination.
:::

::: method
bool IsEnabled()

Returns true if this edge is enabled, or false if this edge is disabled. Generally, edges that are disabled are not considered in pathing algorithms within the comm framework.
:::
