# disentitystate.rst.txt.md
orphan

:   



::: {.DisEntityState .inherits .DisPdu}
::: parsed-literal
`DisEntityState.EntityId`{.interpreted-text role="method"} `DisEntityState.ForceId`{.interpreted-text role="method"} `DisEntityState.EntityPartCount`{.interpreted-text role="method"} `DisEntityState.EntityType`{.interpreted-text role="method"} `DisEntityState.AlternativeEntityType`{.interpreted-text role="method"} `DisEntityState.Velocity`{.interpreted-text role="method"} `DisEntityState.Location`{.interpreted-text role="method"} `DisEntityState.Orientation`{.interpreted-text role="method"} `DisEntityState.Appearance`{.interpreted-text role="method"} `DisEntityState.DeadreckoningAlgorithm`{.interpreted-text role="method"} `DisEntityState.Acceleration`{.interpreted-text role="method"} `DisEntityState.AngularVelocity`{.interpreted-text role="method"} `DisEntityState.MarkingCharSet`{.interpreted-text role="method"} `DisEntityState.MarkingChars`{.interpreted-text role="method"} `DisEntityState.Capabilities`{.interpreted-text role="method"} `DisEntityState.EntityPart`{.interpreted-text role="method"}
:::
:::



[DisEntityState](#disentitystate) is an implementation of the DIS entity state PDU. Entity state PDUs are used to communicate information about the state of a platform. This data includes position, orientation, velocity, acceleration and appearance.



::: method
DisEntityId EntityId()

Returns the ID of the subject platform.
:::

::: method
int ForceId()

Returns the ID of the platform\'s team.
:::

::: method
int EntityPartCount()

Returns the number of articulated parts on the entity.
:::

::: method
DisEntityType EntityType()

Returns the type of the platform.
:::

::: method
DisEntityType AlternativeEntityType()

Returns an alternative type of the platform.
:::

::: method
Array\<double\> Velocity()

Returns the velocity of the platform in meters per second in a geocentric coordinate system.
:::

::: method
Array\<double\> Location()

Returns the location of the platform in meters in a geocentric coordinate system.
:::

::: method
Array\<double\> Orientation()

Returns the orientation of the platform in radians.
:::

::: method
int Appearance()

Returns an appearance bit mask for the platform.
:::

::: method
int DeadreckoningAlgorithm()

Returns an index identifying the dead reckoning method used by the platform for extrapolation.
:::

::: method
Array\<double\> Acceleration()

Returns the acceleration of the platform in meters per second squared in a geocentric coordinate system.
:::

::: method
Array\<double\> AngularVelocity()

Returns the orientation of the platform in radians per second.
:::

::: method
int MarkingCharSet()

Returns the marking character set of the platform.
:::

::: method
string MarkingChars()

Returns the marking characters for the platform.
:::

::: method
string Capabilities()

Returns a capability index for the platform.
:::

::: method
DisEntityPart EntityPart(int aIndex)

Returns the articulated part associated with the given index, aIndex.
:::