orphan

:   

# UCI_POST_CapabilityStatusMessage

## Overview {#overview .UCI_POST_CapabilityStatusMessage .inherits .UCI_Message}

This message gives the status of each of the capabilities (modes) for a `WSF_IRST_SENSOR`{.interpreted-text role="model"}.

## Methods

::: method
UCI_POST_CapabilityStatus CapabilityStatus(int aIndex)

Returns the capability status at aIndex.
:::

::: method
int Size()

Returns the size of the CapabilityStatus.
:::
