orphan

:   

# WsfSixDOF_FormationManager

::: WsfSixDOF_FormationManager
The `WsfSixDOF_FormationManager`{.interpreted-text role="class"} provides the primary means to interact with the lifetimes of `WsfSixDOF_Formation`{.interpreted-text role="class"} objects in simulations. The following methods are all static, and can be accessed from any script context.
:::

## Formation Query Methods

::: method
WsfSixDOF_Formation GetFormation(string aQualifiedName)

Given the fully qualified name of a formation, this method will return the formation with that name. If there is no such formation, then this method will return an invalid object.
:::

::: method
bool HasFormation(string aQualifiedName)

Returns true if the simulation has a formation with the given qualified name.
:::

::: method
Array\<WsfSixDOF_Formation\> GetTopLevelFormations()

Returns an array containing all of the current top-level formations.
:::

::: method
Array\<WsfSixDOF_Formation\> GetAllFormations()

Returns an array containing all of the current formations.
:::

## Formation Creation Methods

::: method
WsfSixDOF_Formation CreateUnit(string aName)

This will create a new unit formation with the given name. A unit formation cannot have any sub-formations, but can have a member platform assigned to it. This method can fail if there is already a formation with the given name; in this case it will return an invalid object.
:::

::: method
WsfSixDOF_Formation CreateSection(string aName)

This will create a new section with the given name. A section is a formation that can have two sub-formations, each of which is a unit. This method will return an invalid object if there is already a formation with the given name.
:::

::: method
WsfSixDOF_Formation CreateFormation(string aName)

This will create a new formation with the given name. This will produce the most general sort of formation, with no restrictions on the number or kinds of sub-formations. This method will return an invalid object if there is already a formation with the given name.
:::

::: method
WsfSixDOF_Formation CreateFormation(string aType, string aName)

This will create a formation of the given type with the given name. The available types of formation are \'unit\', \'section\' and \'formation\'. If there is no such type, or if there is already a formation with the given name, this method will return an invalid object.
:::

## Formation Destruction Methods

::: method
bool DisbandFormation(string aName)

This will disband the formation with the given name. Disbanding a formation will destroy that formation and all its descendant sub-formations. When destroyed, a formation will not destroy the platforms that were members of the various unit formations part of the disbanded formation. The state of the member platforms will also no longer be subject to update by the formation or the commands executed on the formations.

This method only works on top-level formations. So if a sub-formation is to be disbanded, first remove that sub-formation from its parent, and then disband the removed formation.
:::
