# wsfplatformpart.rst.txt.md
orphan

:   





::: method
WsfPlatform Platform()

Returns the owning platform of the part.
:::

::: method
bool IsNonOperational()

Returns true if the part is marked as non-operational. Any request to turn a non-operational part on will be rejected.
:::

::: method
bool IsOperational()

Returns true if the part is marked as operational. An operational part can be turned on and off as desired.
:::

::: method
bool SetOperational(bool aOperational)

Sets the operational state of the object. If the argument is true, the object will be defined to be \'operational,\' which means it can be turned on and off as desired. If the argument is false, the object will be defined to be \'non-operational,\' which means any attempt to turn the object on will be rejected.

The return value is true if the change was successful, or false if not.
:::

::: method
bool IsTurnedOff()

Returns true if the part is turned off.
:::

::: method
bool IsTurnedOn()

Returns true if the part is turned on.
:::

::: method
bool InitiallyTurnedOn()

Returns true if the part is turned on when initially added to the simulation.
:::

::: method
double TimeSinceLastStatusChange()

Returns the number of seconds since the part was last turned off or on.
:::

::: method
void SendMessage(WsfMessage aMessage)

Sends the message to all linked objects.
:::

::: method
void SendMessage(WsfMessage aMessage, string aPartName)

Sends the message to the platform part with the specified name on the current platform.

::: note
::: title
Note
:::

This probably only makes sense for sending messages from one processor to another. In the future it may be possible to send messages to sensors, etc.
:::
:::

::: method
bool DebugEnabled()

Returns the value of the \'debug\' attribute for the part.
:::

::: method
void SetDebugEnabled(bool aValue)

Sets the \"debug\' attribute of the part to the indicated value. Many parts use this attribute to control the display of debugging output.
:::

::: method
double DamageFactor()

Returns the current value of the \'damage factor\' attribute for the part. See details below on \'damage factor\' attribute.
:::

::: method
void SetDamageFactor(double aValue)

Sets the \"damage factor\' attribute of the part to the indicated value. Each platform part may now be damaged separately from the platform as a whole. Zero is no damage, One is totally destroyed, and values supplied outside this range are constrained to the closed interval.
:::

::: method
bool CategoryMemberOf(string aCategoryName)

Returns true if the platform is a member of the specified category.
:::



::: method
bool GroupMemberOf(string aGroupName)
:::

::: method
bool GroupMemberOf(WsfGroup aGroupPtr)

Returns true if the platform part is a member of the specified group.
:::

::: method
Array\<WsfGroup\> Groups()

Returns a list of all groups this platform part is a member of.
:::

::: method
Array\<string\> GroupsByName()

Returns a list of names of all groups this platform part is a member of.
:::

::: method
void GroupJoin(string aGroupName)

Joins the given group if it exists in the simulation.
:::

::: method
void GroupLeave(string aGroupName)

Leaves the given group if this platform part is a member.
:::

