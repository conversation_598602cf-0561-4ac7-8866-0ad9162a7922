# sensor.rst.txt.md
orphan

:   



::: {.command block=""}
sensor
:::

Script Class: `WsfSensor`{.interpreted-text role="class"}

Single mode sensor definition

::: parsed-literal

sensor \<name\> \<base-type-name\>

:   \... `Platform_Part_Commands`{.interpreted-text role="ref"} \... \... `Articulated_Part_Commands`{.interpreted-text role="ref"} \... \... `Common_Script_Interface`{.interpreted-text role="ref"} \...

    [ignore]() \<category-name\> [ignore_domain]() \[ land \| air \| surface \| subsurface \| space \] [ignore_side]() \<side\> [ignore_same_side]() [ignore_nothing]() [message_length]() \<data-size-value\> [message_priority]() \<integer-priority\> [modifier_category]() \<category-name\> [mode_template]() \... end_mode_template [mode]() \<mode-name\> \... end_mode [selection_mode]() \[ single \| multiple \] [initial_mode]() \<mode-name\> [mode_select_delay]() \<time-value\> script bool `sensor.OnSensorDetectionAttempt`{.interpreted-text role="command"} \...

    `Filter Commands <filter>`{.interpreted-text role="command"} \... [Common Mode Commands](#common-mode-commands) \... [Detection Scheduling Commands](#detection-scheduling-commands) \... [Track Formation Commands](#track-formation-commands) \... [Track Information Reporting Commands](#track-information-reporting-commands) \...

    \... sensor-specific mode commands \...

end_sensor

\# Multiple mode sensor definition

sensor \<name\> \<base-type-name\>

> \... `Platform_Part_Commands`{.interpreted-text role="ref"} \... \... `Articulated_Part_Commands`{.interpreted-text role="ref"} \... [Commands](#commands) \...
>
> [mode_template]()
>
> :   [Common Mode Commands](#common-mode-commands) \... `Filter Commands <filter>`{.interpreted-text role="command"} \... sensor-specific mode commands \...
>
> end_mode_template
>
> [mode]() \<mode-name-1\>
>
> :   [Common Mode Commands](#common-mode-commands) \... `Filter Commands <filter>`{.interpreted-text role="command"} \... sensor-specific mode commands \...
>
> end_mode
>
> \... additional mode definitions end_sensor
:::



A sensor provides the ability for a platform to detect other platforms or their constituent parts.



Most (but not all) sensors support the concept of \'modes\'. A mode is a named set of operating characteristics of a sensor. Multiple modes do not have to be used. If a sensor implementation supports modes and an explicit mode is not defined, then any mode-related commands are assumed to belong to the implicitly defined mode named \'default\'.

If multiple modes are to be used, a \'mode_template\' can be defined that specifies the characteristics that are common between all modes. A \'mode_template\' does not have to be defined, but if it is it must be defined prior to the first \'mode\' command. If a \'mode_template\' is used, the initial configuration for each mode is copied from the \'mode_template\' and then any additions or modifications to the mode should appear between the applicable \'mode\' and \'end_mode\' commands.



::: command
ignore \<category-name\>

Indicates the sensor should ignore detection attempts against objects that are a member of the specified category. This command may be specified multiple times to ignore multiple categories.
:::

::: command
ignore_domain \[ land \| air \| surface \| subsurface \| space \]

Indicates the sensor should ignore detection attempts against platforms or objects contained on platforms that are of the specified spatial domain. This command may be specified multiple times to ignore multiple domain.
:::

::: command
ignore_side \<side\>

Indicates the sensor should ignore detection attempts against platforms or objects contained on platforms that are of the specified side. This command may be specified multiple times to ignore multiple sides.
:::

::: command
ignore_same_side

Indicates the sensor should ignore detection attempts against platforms or objects contained on platforms that are on the same side of the platform to which the sensor is attached.
:::

::: command
ignore_nothing

Has the effect of canceling the effect of any previous [ignore](), [ignore_domain](), [ignore_side]() and [ignore_same_side]() commands. This is useful if one wants to reuse a sensor definition with embedded \'ignore\' commands but wished to eliminate or change what is ignored.
:::

::: command
message_length \<data-size-value\>

Specifies the logical length assigned to the track messages that are created from the sensor.

This command is available at both the sensor level (this command) and the sensor mode level. See [message_length]() below.
:::

::: command
message_priority \<integer-priority\>

Specifies the priority assigned to the track messages that are created from the sensor.

This command is available at both the sensor level (this command) and the sensor mode level. See [message_priority]() below.
:::

::: command
modifier_category \<category-name\>

The category that maps to the `zone-based_attenuation`{.interpreted-text role="ref"} value defined in the `zone_set`{.interpreted-text role="command"}. Setting this value tells the sensor to evaluate zones for attenuation.
:::

::: command
mode_template \... end_mode_template

Defines the default values for the modes of the sensor. When a new mode is defined it is first populated with the values from the mode template. This is useful when a sensor has multiple modes and the parameters for each mode are the same with the exception of a few values.

::: note
::: title
Note
:::

This command must appear before any [mode]() command in the second.
:::
:::

::: command
mode \<mode-name\> \... end_mode

Defines a mode, which is nothing more than a selectable set of parameters. The initial or default values of a mode are defined by the [mode_template]() (if provided).
:::

::: command
selection_mode \[ single \| multiple \]

Indicates if the sensor supports the simultaneous operation of multiple modes.

Default single
:::

::: command
initial_mode \<mode-name\>

The operation of this command depends on if [selection_mode]() is single or multiple.

If [selection_mode]() is single then this defines the mode to be selected the first time the sensor is turned on. If this command is not specified then the first mode that is defined will be selected. If the sensor is subsequently turned off and turned back on, the sensor will resume the mode it was in when it was last turned off.

If [selection_mode]() is multiple then this defines the mode to be selected whenever the sensor changes from the \'off\' state to the \'on\' state unless the scripting language is used to select a different mode while the sensor is off. If this command is not defined then no sensor mode will be activated when the sensor is turned on; it is the responsibility of the user to use the scripting language to select a mode.
:::

::: command
mode_select_delay \<time-value\>

Specifies the delay when selecting modes.

::: note
::: title
Note
:::

This input is operational only for the `WSF_AGILITY_EFFECT`{.interpreted-text role="model"} mode_changing functionality.
:::

Default 0.0 seconds
:::

::: command
OnSensorDetectionAttempt

::: parsed-literal
script bool OnSensorDetectionAttempt(`WsfPlatform`{.interpreted-text role="class"} aTarget, `WsfSensorInteraction`{.interpreted-text role="class"} aInteraction) \... end_script
:::

Defines an optional script that imposes additional detection constraints on a sensor model. This script is invoked immediately after a detection attempt occurs but prior to any SENSOR_DETECTION_ATTEMPT events in the `observer`{.interpreted-text role="command"}. As a result, the `WsfSensorInteraction`{.interpreted-text role="class"} parameter includes any relevant calculations from the sensor\'s inbuilt detection checks and represents what the result would have been if this script was not defined.

A boolean value MUST be returned from this script indicating whether the sensor detection should be accepted or denied. An accepted detection is not necessarily synonymous with a successful detection; it simply means that the existing sensor result, which could have failed due to other constraints, should be used.
:::



The remaining commands are applicable on a per-mode basis.

::: {.command hidden=""}
sensor_mode
:::

::: block
sensor_mode
:::

::: command
required_pd (0..1)

Specifies the constant \'required_pd\' value to be used if the value of the global simulation command `use_constant_required_pd`{.interpreted-text role="command"} was specified as true. This is applicable only for sensors which implement probabilistic detectors (e.g., The optional Marcum-Swerling detector in `WSF_RADAR_SENSOR`{.interpreted-text role="model"}).

Default 0.5
:::

::: command
cue_mode \[ fixed \| azimuth \| elevation \| both \| azimuth_and_elevation\]

This command, along with [azimuth_cue_limits]() and [elevation_cue_limits]() can be used to limit the cueing capabilities for a particular mode to something less than those which were defined by the `articulated part <_.articulated_part>`{.interpreted-text role="command"}. For instance, a sensor may physically be able to cue in any direction but in a given mode may only cue in azimuth.

-   fixed - The sensor cannot be cued.
-   azimuth - The sensor can be cued only in azimuth.
-   elevation - The sensor can be cued only in elevation.
-   both or azimuth_and_elevation - The system can be cued in both azimuth and elevation.

Default As defined by the `_.articulated_part.slew_mode`{.interpreted-text role="command"} command in the articulated part.
:::

::: command
azimuth_cue_limits \<angle-value\> \<angle-value\>

Specifies the minimum and maximum angle about which the sensor can be cued in azimuth. These values are applicable only if cue_mode is azimuth or both. The limits are specified in the subsystem coordinate frame.

Default As defined by the `_.articulated_part.azimuth_slew_limits`{.interpreted-text role="command"} command in the articulated part.
:::

::: command
elevation_cue_limits \<angle-value\> \<angle-value\>

Specifies the minimum and maximum angle about which the sensor can be cued in elevation. These values are applicable only if cue_mode is elevation or both. The limits are specified in the subsystem coordinate frame.

Default As defined by the `_.articulated_part.elevation_slew_limits`{.interpreted-text role="command"} command in the articulated part.
:::

::: command
azimuth_cue_rate \<angle-rate-value\>
:::

::: command
elevation_cue_rate \<angle-rate-value\>

Specifies the angular velocities to be employed when slewing the part to satisfy a cueing request. This is primarily used for modeling systems that track single target. It is not used for scanning systems and should not be used for multiple-target tracking systems.

The value must be greater than zero, and values greater than or equal to 1.0E+12 deg/sec will be treated as \'infinite\'. The values will be limited to the values specified by the `_.articulated_part.azimuth_slew_rate`{.interpreted-text role="command"} and `_.articulated_part.elevation_slew_rate`{.interpreted-text role="command"} commands in articulated part (which default to \'infinite\').

Default As defined by the `_.articulated_part.azimuth_slew_rate`{.interpreted-text role="command"} and `_.articulated_part.elevation_slew_rate`{.interpreted-text role="command"} commands in the articulated part.
:::

::: command
error_model \<derived-name\>

error_model *\<base-name\>* \...commands\... end_error_model

Specify the error model. See `error_model`{.interpreted-text role="command"} for more information about the available error effects and how to configure the models.

Default: none (No error model)
:::

::: command
solar_exclusion_angle \<angle-value\>

This sensor will not detect targets if the angle between its line-of-sight with the sun\'s limb and the target is less than this value.

Default: no solar exclusion
:::

::: command
lunar_exclusion_angle \<angle-value\>

This sensor will not detect targets if the angle between its line-of-sight with the moon\'s limb and the target is less than this value.

Default: no lunar exclusion
:::

::: command
target_solar_illumination_angle \<angle-value\> \<angle-value\>

Defines the bounds of solar illumination required for the target to be detected.

Default: No constraint

::: note
::: title
Note
:::

This command only works with passive IR sensors and passive visual sensors, as well as with `WSF_GEOMETRIC_SENSOR`{.interpreted-text role="model"}.
:::
:::

::: command
solar_elevation_at_target \<angle-value\> \<angle-value\>

Defines the bounds of solar elevation at the target\'s location required for the target to be detected.

Default: No constraint

::: note
::: title
Note
:::

This command only works with passive IR sensors and passive visual sensors, as well as with `WSF_GEOMETRIC_SENSOR`{.interpreted-text role="model"}.
:::
:::

::: command
enable_moon_los_block \<boolean-value\>

If set to true, this sensor will not detect targets whose line-of-sight is blocked by the moon.

Default: false
:::



These commands define the parameters that are used to determine when sensing chances are to occur.

::: warning
::: title
Warning
:::

Not all sensors support all commands. For instance, a sensor whose product is a image does not support these commands. The `WSF_COMPOSITE_SENSOR`{.interpreted-text role="model"} is not integrated with many of these commands.
:::

::: command
update_interval \<time-value\>

This value is required when the [scheduler]() is assigned a scheduler-type of `scheduler_commands.physical_scan`{.interpreted-text role="ref"} or `scheduler_commands.sector_scan`{.interpreted-text role="ref"}. It is used in conjunction with the [frame_time]() value defined within the final mode that is read in from the input file for the sensor in order to calculate and physically sweep sectors for radar detections. For example, if the final frame-time value, which is read during the input loading sequence, is set to 20 sec, and if the update_interval is set to 2 sec, then 10 sectors are needed to sweep through 360 degrees during the 20-sec frame. For this example, each sector would cover 36 degrees of azimuth during each 2-sec update_interval.
:::

::: note
::: title
Note
:::

This keyword should be placed within the sensor-end_sensor block; not a mode block.
:::

::: command
frame_time \<time-value\>

Specifies how long the sensor takes to perform one scan of its search volume. How this parameter is actually used depends on the actual sensor implementation. It also signifies how often a sensor detection is reported.
:::

::: command
maximum_request_count \<integer\>

If this value is greater than zero then this mode only responds to explicit requests as initiated by `WsfTaskManager::StartTracking <WsfTaskManager.StartTracking>`{.interpreted-text role="method"}.
:::

::: command
revisit_time \<time-value\>

If [maximum_request_count]() is non-zero, this specifies how often the request should be revisited.
:::

::: command
dwell_time \<time-value\>

If [maximum_request_count]() is non-zero, this specifies how long the sensor will dwell or otherwise take to perform a detection attempt associated with a request.
:::

::: command
search_while_track

If [maximum_request_count]() is non-zero, this indicates search mode requests can continue to be processed.
:::

::: command
disables_search

If [maximum_request_count]() is non-zero, this indicates that if this mode is selected then any detection attempts by search modes will be blocked.
:::

::: command
scheduler \<scheduler-type\> end_scheduler

::: parsed-literal

scheduler `<scheduler-type> <scheduler_commands.available_schedulers>`{.interpreted-text role="ref"}

:   `Type Commands <scheduler_commands>`{.interpreted-text role="ref"}

end_scheduler
:::

\*\<scheduler-type\>\* can be:
:::

::: command
debug_scheduler

Enables output to the console window for scheduler data.

::: note
::: title
Note
:::

This keyword will not work in the `WSF_COMPOSITE_SENSOR`{.interpreted-text role="model"} definition block, but it can work in the constituent sensors, which are used for `WSF_COMPOSITE_SENSOR`{.interpreted-text role="model"}, when the component sensors are identical.
:::

::: note
::: title
Note
:::

This command is valid for all scheduler types.
:::
:::



These commands define the criteria for establishing a track and the type and quality of information reported in the tracks produced by this sensor.

::: note
::: title
Note
:::

These commands are ignored for those sensors that do not produce tracks.
:::

::: command
azimuth_error_sigma \[ \<angle-value\> \| \<real-value\> percent_of_true_range \]
:::

::: command
elevation_error_sigma \[ \<angle-value\> \| \<real-value\> percent_of_true_range \]
:::

::: command
range_error_sigma \[ \<length-value\> \| \<real-value\> percent_of_true_range \]
:::

::: command
range_rate_error_sigma \<speed-value\>

Specifies the standard deviation for a Gaussian distribution for errors to be applied to position measurements from the sensor. The standard deviation may be specified as either an angle, length or speed value(as appropriate) or may be specified as a function of \'percent_of_true_range\' for certain error types. In the latter case, the following formulas are used:

sigma(angle) = atan2(0.01 \* value \* R~true~, R~true~)

sigma(range) = 0.01 \* value \* R~true~

Where \'value\' is the \'\<real-value\>\' specified in the command (in the range \[0..100\]) and \'R~true~\' is the true range to the target.

Default 0 (no errors) for all
:::

::: command
hits_to_establish_track \<M\> \<N\>

Indicates that \<M\> of the last \<N\> attempts to detect an object must be successful in order to establish a track.

Default 1 for both \<M\> and \<N\>.
:::

::: command
hits_to_maintain_track \<M\> \<N\>

Once a track has been established, \<M\> of the last \<N\> attempts to detect an object must be successful in order to maintain a track.

Default 1 for both \<M\> and \<N\>.
:::

::: command
establish_track_probability \[0 .. 1\]

When the M/N establish track criteria is met (see [hits_to_establish_track]()), this is the probability that a track will be established.

Default: 1.0
:::

::: command
maintain_track_probability \[0 .. 1\]

As long as the M/N maintain track criteria is met (see [hits_to_maintain_track]()), this is the probability that the track will be maintained.

Default: 1.0
:::



These commands determine the target information reported in a given sensor\'s track report.

::: note
::: title
Note
:::

If a filter (e.g., `WSF_KALMAN_FILTER`{.interpreted-text role="model"}, `WSF_ALPHA_BETA_FILTER`{.interpreted-text role="model"}) is being used, the reported tracks are marked as being filtered, and reported position and velocity information (range, bearing, elevation, location, and velocity) will be the filtered position and velocity. If the `Kalman Filter <WSF_KALMAN_FILTER>`{.interpreted-text role="model"} is used, a state covariance matrix is also available (see reports_state_covariance).
:::

::: command
message_length \<data-size-value\>

Specifies the logical length of report messages from this sensor mode.

The message length is assigned as follows using the first value that results in a non-zero value.

-   The value of the sensor mode message_length command (this command).
-   The value of the sensor message_length command.
-   The value the applicable `message_table`{.interpreted-text role="command"} entry.

Default 0
:::

::: command
message_priority \<integer-priority\>

Specifies the priority to be assigned to the report messages that originate from this mode.

The message priority is assigned as follows using the first value that results in a non-zero value.

-   The value of the sensor mode message_priority command (this command).
-   The value of the sensor message_priority command.
-   The value the applicable `message_table`{.interpreted-text role="command"} entry.

Default 0
:::

::: command
reports_range

The slant range from the sensor to the target is reported.
:::

::: command
reports_bearing

The bearing from the sensor to the target is reported. This angle is measured in radians from the sensor\'s north orientation, and it is in the range { -pi, pi }.
:::

::: command
reports_elevation

The elevation angle from from the sensor to the target is reported.
:::

::: command
reports_location

The location (latitude, longitude, altitude) of the target is reported.
:::

::: command
reports_velocity

The velocity of the target is reported.
:::

::: command
reports_range_rate

The range-rate of the target is reported.
:::

::: command
reports_iff

The identify friend-or-foe (IFF) status is reported.
:::

::: command
reports_side

The side of the target is reported.
:::

::: command
reports_type

The type of the target is reported.
:::

::: command
reports_signal_to_noise

The signal-to-noise ratio is reported.
:::

::: command
reports_frequency

The frequency of the detected signal should be reported.
:::

::: command
reports_pulsewidth \| reports_pw

The pulse-width of the signal is reported.

::: note
::: title
Note
:::

Also sets [reports_frequency]().
:::
:::

::: command
reports_pulse_repetition_interval \| reports_pri

The pulse-repetition interval of the signal is reported.

::: note
::: title
Note
:::

Also sets [reports_frequency]().
:::
:::

::: command
reports_other

Specifies what data elements will be reported in tracks from this sensor.
:::

::: command
reports_nothing

Nothing is reported. This is the default behavior if no reporting flags are specified, and also has the effect of canceling any previous \'reports\_\' commands. This is useful if one wants to reuse an existing sensor definition with embedded \'reports\_\' commands but needs to change what is reported, or in situations where only the detected platform is needed in generated tracks.
:::

::: command
track_quality \[0 .. 1\]

Specifies the \'quality\' of the track when produced from this mode.

Default 0.5
:::

::: command
send_track_drop_on_turn_off \<boolean-value\>

Indicates if \'track drop\' messages should be sent for each active track when the sensor is turned off.

Default off

::: note
::: title
Note
:::

\'track drop\' messages are not sent if the platform that owns the sensor is deleted.
:::
:::