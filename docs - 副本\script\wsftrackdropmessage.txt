# wsftrackdropmessage.rst.txt.md
orphan

:   



::: {.WsfTrackDropMessage .inherits .WsfMessage cloneable="" constructible=""}
A `WsfTrackDropMessage`{.interpreted-text role="class"} is sent by a sensor to its linked processors when it drops a track.
:::



::: method
double Time()

Return the simulation time in seconds that the track was dropped.
:::

::: method
WsfTrackId TrackId()

Return the track Id of the dropped track.
:::

::: method
int TargetIndex()

Return the truth target index of the dropped track.
:::