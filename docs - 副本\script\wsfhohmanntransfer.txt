# wsfhohmanntransfer.rst.txt.md
orphan

:   



::: {.WsfHohmannTransfer .inherits .WsfOrbitalManeuver}
Input type: `hohmann_transfer<../hohmann_transfer>`{.interpreted-text role="doc"}
:::

[WsfHohmannTransfer](#wsfhohmanntransfer) can be used to transfer a object between different orbits.

::: method
static WsfHohmannTransfer Construct(WsfOrbitalEventCondition aCondition, double aFinalRadius)

Static method to create a [WsfHohmannTransfer](#wsfhohmanntransfer) using a specific condition and the radius of the final orbit (m).
:::