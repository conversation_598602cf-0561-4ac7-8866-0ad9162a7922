orphan

:   

# Atmosphere Model

::: parsed-literal

[atmosphere_model]() \<model-name\> \<atmosphere-model-type\>

:   \...

end_atmosphere_model
:::

::: command
atmosphere_model

Define a new atmosphere model with the specified *\<model-name\>* that is an instance of the specified *\<atmosphere-model-type\>*.
:::

## Available Atmosphere Model Types

-   `WSF_PIECEWISE_EXPONENTIAL_ATMOSPHERE`{.interpreted-text role="model"}
-   `WSF_JACCHIA_ROBERTS_ATMOSPHERE`{.interpreted-text role="model"}
