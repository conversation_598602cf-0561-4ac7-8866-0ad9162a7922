orphan

:   

# Weapon Component

::: model
uci_component WEAPON
:::

::: parsed-literal
uci_component \<type\> WEAPON

> Part Commands \...
>
> `uci_component`{.interpreted-text role="command"} Commands \...

end_uci_component
:::

## Overview

This component handles the Strike UCI messages for all weapons on a platform.

## Commands

## Message Types

All message types are able to be sent via script methods, unless noted otherwise.

### Activity

`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::

### Capability

`UCI_StrikeCapabilityMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval

### CapabilityStatus

`UCI_StrikeCapabilityStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval

### Command

`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::

### CommandStatus

`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::

### ControlRequest

`UCI_ControlRequestMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   Script only

### ControlRequestStatus

`UCI_ControlRequestStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   When a ControlRequestMessage is received and handled (one per controlee in message)

### ControlStatus

`UCI_ControlStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   When a ControlRequestMessage is received and handled (one per controlee in message)
> -   On update interval

### SettingsCommand

`UCI_StrikeSettingsCommandMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::

### SettingsCommandStatus

`UCI_StrikeActivityMessage`{.interpreted-text role="class"} will be sent on the following events:

> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented
> :::

### SubsystemStatus

`UCI_SubsystemStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval

### SystemStatus

`UCI_SystemStatusMessage`{.interpreted-text role="class"} will be sent on the following events:

> -   On update interval
>
> ::: note
> ::: title
> Note
> :::
>
> Not yet implemented in script
> :::
