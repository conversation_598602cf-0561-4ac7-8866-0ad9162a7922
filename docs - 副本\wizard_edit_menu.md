orphan

:   

# Edit Menu - Wizard

![image](images/wizard_edit_menu.png)

These options apply to the currently active `text editor<wizard_text_editor>`{.interpreted-text role="doc"}, unless otherwise specified.

-   Go To Defintion - Find the definition of the text under the cursor.
-   Find References - Find references to the text under the cursor.
-   Undo - Undo the last change made.
-   Redo - Redo the last change undone.
-   Cut - Cut the highlighted text to the OS clipboard.
-   Copy - Copy the highlighted text to the OS clipboard.
-   Past<PERSON> - <PERSON>e from the OS clipboard to the cursor location.
-   Select All - Select the entire contents of the document.
-   Find\... - Input and find the a text fragment.
-   Find Next - Find the next instance of the last input text.
-   Find Previous - Find the previous instance of the last input text.
-   Find in Files - Find all instances of a text fragment in every project file.
-   Replace\... - Replace a text fragment with a new text fragment.
-   Go To line\... - Jump the cursor to a selected line.
-   Back - Move the cursor back to its previous location.
-   Forward - Move the cursor forward to a location previous moved *Back* from.
-   Auto-Complete - Attempt to complete the text fragment under the cursor.
-   Comment Selection - Comment the selected block of text.
-   Uncomment Selection - Uncomment the selected block of text.
-   Toggle Selection Comment - Comment or uncomment the selected block of text based on its current state.
-   Format Selection - Attempt to format the currently selected block of text. Note that this is done according to the indention rules described in the `preferences<wizard_preferences>`{.interpreted-text role="doc"}.
