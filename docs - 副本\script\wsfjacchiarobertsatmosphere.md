orphan

:   

# WsfJacchiaRobertsAtmosphere

## Overview {#overview .WsfJacchiaRobertsAtmosphere .inherits .WsfAtmosphere}

The `WsfJacchiaRobertsAtmosphere`{.interpreted-text role="class"} provides an atmosphere model that models the exospheric temperature profile, with dependence on solar and geomagnetic activity. The density is then obtained for various altitude zones. For details, see Vallado\'s *Fundamentals of Astrodynamics and Application*, Fourth Edition, Appendix B.

## Static Methods

::: method
WsfJacchiaRobertsAtmosphere Construct(double aSolarFlux, double aAverageSolarFlux, double aGeomagneticIndex)

Construct a Jacchia-Roberts atmosphere model that uses the given *aSolarFlux*, *aAverageSolarFlux*, and *aGeomagneticIndex*. The values for *aSolarFlux*, and *aAverageSolarFlux* should be provided with units of $10^{-22} \rm{W}/\rm{m}^2/\rm{Hz}$. Reasonably realistic values for *aSolarFlux*, and *aAverageSolarFlux* are between 50 and 400. *aGeomagneticIndex* must be in the range \[0, 9\].
:::

## Methods

::: method
double SolarFlux()

Return the solar flux used for this atmosphere model. The returned value has units of $10^{-22} \rm{W}/\rm{m}^2/\rm{Hz}$.
:::

::: method
double AverageSolarFlux()

Return the 81-day average solar flux used for this atmosphere model. The returned value has units of $10^{-22} \rm{W}/\rm{m}^2/\rm{Hz}$.
:::

::: method
double GeomagneticIndex()

Return the geomagnetic index used for this atmosphere model. This value will be in the range \[0, 9\].
:::
