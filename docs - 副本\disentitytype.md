orphan

:   

# DisEntityType {#DisEntityType}

::: DisEntityType
::: parsed-literal
`DisEntityType.EntityKind`{.interpreted-text role="method"} `DisEntityType.Domain`{.interpreted-text role="method"} `DisEntityType.Country`{.interpreted-text role="method"} `DisEntityType.Category`{.interpreted-text role="method"} `DisEntityType.Subcategory`{.interpreted-text role="method"} `DisEntityType.Specific`{.interpreted-text role="method"} `DisEntityType.Extra`{.interpreted-text role="method"}
:::
:::

## Overview

[DisEntityType](#disentitytype) is an implementation of the DIS entity type. The entity type is used by DIS to identify the types of platforms.

## Methods

::: method
int EntityKind()

Returns the entity kind ID.
:::

::: method
int Domain()

Returns the domain ID.
:::

::: method
int Country()

Returns the country ID.
:::

::: method
int Category()

Returns the category ID.
:::

::: method
int Subcategory()

Returns the subcategoryID.
:::

::: method
int Specific()

Returns the specific ID.
:::

::: method
int Extra()

Returns the extra ID.
:::
