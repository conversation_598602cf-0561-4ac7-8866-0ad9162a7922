# wsfterrain.rst.txt.md
orphan

:   



::: {.WsfTerrain constructible=""}
`WsfTerrain`{.interpreted-text role="class"} allows querying the terrain defined in the `terrain`{.interpreted-text role="command"} block.
:::



::: method
double BathymetryElevApprox(double aLatitude, double aLongitude)

Returns the approximate underwater terrain elevation in meters at the specified location. Returned values will be negative indicating depth.
:::

::: method
double BathymetryElevInterp(double aLatitude, double aLongitude)

Returns the interpolated underwater terrain elevation in meters at the specified location. Returned values will be negative indicating depth.
:::

::: method
double TerrainElevApprox(double aLatitude, double aLongitude)

Returns the approximate terrain elevation in meters at the specified location.
:::

::: method
double TerrainElevInterp(double aLatitude, double aLongitude)

Returns the interpolated terrain elevation in meters at the specified location.
:::

::: method
double VegetationElevApprox(double aLatitude, double aLongitude)

Returns the approximate vegetation elevation in meters at the specified location.
:::

::: method
double VegetationElevInterp(double aLatitude, double aLongitude)

Returns the interpolated vegetation elevation in meters at the specified location.
:::



::: method
static bool MaskedByTerrain(WsfGeoPoint aSource, WsfGeoPoint aDestination, double aRadiusScale)

Returns true if line of sight between aSource and aDestination is masked by the terrain.

aRadiusScale

:   Multiplier to apply to earth radius, 1.0 for no change.
:::

::: method
static bool MaskedByVegetation(WsfGeoPoint aSource, WsfGeoPoint aDestination, double aRadiusScale)

Returns true if line of sight between aSource and aDestination is masked by the vegetation.

aRadiusScale

:   Multiplier to apply to earth radius, 1.0 for no change.
:::

::: method
static bool MaskedByUnderwaterTerrain(WsfGeoPoint aSource, WsfGeoPoint aDestination, double aRadiusScale)

Returns true if line of sight between aSource and aDestination is masked by underwater terrain. This method uses only terrain defined using the `terrain.bathymetry`{.interpreted-text role="command"} command.

aRadiusScale

:   Multiplier to apply to earth radius, 1.0 for no change.
:::