orphan

:   

# FUEL_ALIAS

`event_output`{.interpreted-text role="command"} for **FUEL_EVENT** and an alias, RAN_OUT_OF_FUEL.

(FUEL_EVENT has replaced the previous event, RAN_OUT_OF_FUEL.)

## Format

*All Events*

    <time> FUEL_EVENT <platform> <event name>

## Breakdown

\<time\>

:   current simulation time

FUEL_EVENT

:   simulation event recorded in this message

\<platform\>

:   name of platform fuel event applies to

\<event name\>

:   name of fuel event (e.g., Refuel, Quantity, TimeToBingo, TimeToEmpty, TimeToReserve)

## How to Show Event Messages

::: parsed-literal

`event_output`{.interpreted-text role="command"}

:   file replay.evt \# write event messages to file \"replay.evt\" enable FUEL_EVENT

end_event_output
:::
