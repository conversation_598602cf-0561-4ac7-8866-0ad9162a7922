# coordinate_systems.rst.txt.md
orphan

:   



WSF utilizes the coordinate systems as defined by the \"IEEE Std. 1278.1-1995; Standard for Distributed Simulation Application Protocols\'.



The WCS, a.k.a Earth Centered, Earth Fixed (ECEF), is a right-handed Cartesian system as follows:

-   The origin is at the center of the Earth.
-   The +X axis passes through 0N, 0E
-   The +Y axis passes through 0N, 90E
-   The +Z axis passes through 90N (the North Pole).

The Earths surface is modeled as an oblate ellipsoid as defined by the WGS-84 standard (NIMA TR-8350.2). Positions can be supplied as latitude, longitude, and altitude but are converted to Cartesian coordinates.



This system is Earth-Centered like WCS but fixed with respect to background stars instead of the Earth. Axis orientations are referenced to the J2000 epoch: Jan. 1, 2000, 12:00 TT (Terrestrial Time), such that the x and y axes define the equatorial plane at J2000.

To a good approximation:

-   The origin is at the center of the Earth.
-   The +X axis points towards the vernal equinox (the point at which the Sun\'s path crosses the equator from south to north).
-   The +Z axis is perpendicular to the equatorial plane, approximately passing through 90N \[the North Pole\].
-   The +Y axis is perpendicular to the X and Z axes in a right-handed coordinate system.

::: note
::: title
Note
:::

The AFSIM ECI coordinate system is commonly known as the GCRF (Geocentric Celestial Reference Frame). It is an inertial coordinate system in the limit that relativistic corrections can be ignored.
:::

::: note
::: title
Note
:::

`polar_offset_angles<global_environment.polar_offset_angles>`{.interpreted-text role="command"}, `delta_universal_time`{.interpreted-text role="command"}, and `delta_atomic_time`{.interpreted-text role="command"} should be defined for highest WCS-ECI coordinate conversion accuracy.
:::



This system is similar to ECI, except that axis orientations are referenced to the date (epoch) of interest, such that the x and y axes define the equatorial plane at that epoch.

To a good approximation:

-   The origin is at the center of the Earth.
-   The +X axis points towards the vernal equinox (the point at which the Sun\'s path crosses the equator from south to north).
-   The +Z axis is perpendicular to the equatorial plane (approximately passing through 90N \[the North Pole\].
-   The +Y axis is perpendicular to the X and Z axes in a right-handed coordinate system.

::: note
::: title
Note
:::

TOD differs from ECI because effects of precession and nutation are not considered in WCS-TOD coordinate transformations.
:::



This ECS is the local coordinate system attached to the entity (platform). This is a right-handed Cartesian system:

-   The origin is at the centric of the entity.
-   The +X axis goes out the front of the entity.
-   The +Y axis goes out the right side of the entity (when looking down the +X axis).
-   The +Z axis goes out the bottom of the entity.
-   Yaw is a rotation about the Z axis. Positive yaw is to the right.
-   Pitch is a rotation about the Y axis. Positive pitch raises the +X axis.
-   Roll is a rotation about the X axis. Positive roll drops the +Y axis.



The PCS is used to represent the local coordinate system of parts that are attached to an entity (e.g., sensor). The location of the origin and the orientation of the PCS are specified relative to the ECS of the entity (platform) to which the part is attached. The axis and angle conventions of the PCS are the similar to those of the ECS:

-   The +X axis goes out the front of the part.
-   The +Y axis goes out the right side of the part (when looking down the +X axis).
-   The +Z axis goes out the bottom of the part.
-   Yaw is a rotation about the Z axis. Positive yaw is to the right.
-   Pitch is a rotation about the Y axis. Positive pitch raises the +X axis.
-   Roll is a rotation about the X axis. Positive roll drops the +Y axis.