# predefined_cyber_effect_types.rst.txt.md
orphan

:   



-   `WSF_CYBER_SCRIPT_EFFECT`{.interpreted-text role="model"} - User defined script effects.
-   `WSF_CYBER_SCRIPT_EFFECT_ENHANCED`{.interpreted-text role="model"} - User defined script effects with user supplied parameter usage/passage.
-   `WSF_CYBER_TOGGLE_SENSORS_EFFECT`{.interpreted-text role="model"} - An effect that disables sensors.
-   `WSF_CYBER_TOGGLE_WEAPONS_EFFECT`{.interpreted-text role="model"} - An effect that disables weapons.
-   `WSF_CYBER_TOGGLE_COMMS_EFFECT`{.interpreted-text role="model"} - An effect that disables comms.
-   `WSF_CYBER_TOGGLE_PROCESSORS_EFFECT`{.interpreted-text role="model"} - An effect that disables processors.
-   `WSF_CYBER_MAN_IN_THE_MIDDLE_EFFECT`{.interpreted-text role="model"} - An effect that provides man-in-the-middle message manipulation effects.
-   `WSF_CYBER_WEAPONS_RETARGET_EFFECT`{.interpreted-text role="model"} - An effect that swaps the current target on a platform with one provided during the time of the attack. Typically intended for explicit weapon usage, but can apply to any platform.
-   `WSF_CYBER_DETONATE_EFFECT`{.interpreted-text role="model"} - An effect that detonates all weapons of a given name or type on victim platform.
-   `WSF_CYBER_WEAPONS_UNTARGETED_EFFECT`{.interpreted-text role="model"} - An effect that removes the current target on a platform. Typically intended for explicit weapon usage, but can apply to any platform.
-   `WSF_CYBER_TRACK_MANAGER_EFFECT`{.interpreted-text role="model"} - An effect that modifies victim track management via the victim\'s master track manager.
-   `WSF_CYBER_TRACK_PROCESSOR_EFFECT`{.interpreted-text role="model"} - An effect that modifies victim track management via the victim\'s track processor.