# wsfdeltavmaneuver.rst.txt.md
orphan

:   



::: {.WsfDeltaV_Maneuver .inherits .WsfOrbitalManeuver}
Input type: `delta_v<../delta_v>`{.interpreted-text role="doc"}
:::

[WsfDeltaV_Maneuver](#wsfdeltav_maneuver) can be used to change the orbit of a platform by adding an arbitrary delta-v to its current velocity.



::: method
static WsfDeltaV_Maneuver Construct(WsfOrbitalEventCondition aCondition, Vec3 aDelta_V, WsfOrbitalReferenceFrame aFrame)

Static method to create a `WsfDeltaV_Maneuver`{.interpreted-text role="class"} using the given *aCondition*, and the desired change in the platform\'s velocity, *aDelta_V*, in m/s, which is interpreted in the given *aFrame*.

::: note
::: title
Note
:::

The frames that are currently supported by this method are `WsfOrbitalReferenceFrame.INERTIAL`{.interpreted-text role="method"} and `WsfOrbitalReferenceFrame.RIC`{.interpreted-text role="method"}.
:::
:::

::: method
Vec3 DeltaV()

Return the change in velocity in units of m/s that this maneuver will cause in the executing `WsfSpaceMover`{.interpreted-text role="class"}. The returned value is given in the frame of reference returned by `WsfDeltaV_Maneuver.Frame`{.interpreted-text role="method"}.
:::

::: method
WsfOrbitalReferenceFrame Frame()

Return the frame of reference that will be used by this maneuver to interpret the configured delta-v.
:::



::: method
static WsfDeltaV_Maneuver Construct(WsfOrbitalEventCondition aCondition, Vec3 aDelta_V)

Static method to create a WsfDeltaV_Maneuver using a specific `condition <wsforbitaleventcondition>`{.interpreted-text role="doc"} and the desired change in the platform\'s velocity, specified by a `Vec3`{.interpreted-text role="class"} of three ECI components in units of m/s.
:::