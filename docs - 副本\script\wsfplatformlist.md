orphan

:   

# WsfPlatformList

::: {.WsfPlatformList container=""}
`WsfPlatformList`{.interpreted-text role="class"} is a container of references to `WsfPlatform`{.interpreted-text role="class"} objects. Platform lists are returned by many script types including:
:::

-   `WsfPlatform::Peers <WsfPlatform.Peers>`{.interpreted-text role="method"}
-   `WsfPlatform::Subordinates <WsfPlatform.Subordinates>`{.interpreted-text role="method"}

In addition to the methods described below, the container may be processed using the **foreach** script language statement as follows:

::: parsed-literal
`WsfPlatformList`{.interpreted-text role="class"} platformList = \<a method that returns a `WsfPlatformList`{.interpreted-text role="class"}\>; foreach (`WsfPlatform`{.interpreted-text role="class"} p in platformList) { \# \...Code to process the `WsfPlatform`{.interpreted-text role="class"} object referenced through the variable \'p\'\... }
:::

## Methods

::: method
int Count() int Size()

Returns the number of entries list.
:::

::: method
bool Empty()

Returns true if the list is empty.

::: note
::: title
Note
:::

This is faster than checking for Count() != 0.
:::
:::

::: method
WsfPlatform Entry(int aIndex) WsfPlatform Get(int aIndex)

Returns the entry at the given index, which must be in the range \[ 0 .. Count()-1 \].

::: note
::: title
Note
:::

The Get method allows the entries to be accessed via array indexing: e.g:

    WsfPlatform p = platformList[i];
:::
:::

::: method
Iterator WsfPlatformListIterator GetIterator()

Returns an iterator that points to the beginning of the list. This is used by the script language to support the **foreach** command but may also be used directly.
:::
