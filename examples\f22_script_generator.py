#!/usr/bin/env python3
"""
F-22 Raptor平台脚本生成器示例
展示如何使用增强版Agent生成专业的F-22脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from core.enhanced_platform_agent import EnhancedPlatformAgent
import json


def generate_basic_f22():
    """生成基础F-22脚本"""
    print("🛩️ 生成基础F-22 Raptor脚本")
    print("=" * 50)
    
    agent = EnhancedPlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 使用预定义模板
    script = agent.generate_f22_script()
    print(script)


def generate_custom_f22():
    """生成自定义F-22脚本"""
    print("\n🛩️ 生成自定义F-22 Raptor脚本")
    print("=" * 50)
    
    agent = EnhancedPlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 自定义参数
    custom_params = {
        "platform_name": "f22_alpha_01",
        "side": "BLUE",
        "position": {"lat": 35.0, "lon": -118.0},  # Edwards AFB
        "altitude": 18000,  # 18000英尺
        "heading": 45,      # 东北方向
        "fuel_mass": 6000,  # 部分燃料
        "creation_time": 300  # 5分钟后创建
    }
    
    script = agent.generate_f22_script(custom_params)
    print(script)


def generate_f22_formation():
    """生成F-22编队脚本"""
    print("\n🛩️ 生成F-22编队脚本")
    print("=" * 50)
    
    agent = EnhancedPlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 编队配置
    formation_configs = [
        {
            "platform_name": "f22_lead",
            "position": {"lat": 35.0, "lon": -118.0},
            "altitude": 18000,
            "heading": 90,
            "side": "BLUE"
        },
        {
            "platform_name": "f22_wing_1",
            "position": {"lat": 34.99, "lon": -117.98},
            "altitude": 18000,
            "heading": 90,
            "side": "BLUE"
        },
        {
            "platform_name": "f22_wing_2", 
            "position": {"lat": 35.01, "lon": -117.98},
            "altitude": 18000,
            "heading": 90,
            "side": "BLUE"
        }
    ]
    
    for i, config in enumerate(formation_configs, 1):
        print(f"\n--- F-22 编队成员 {i} ---")
        script = agent.generate_f22_script(config)
        print(script)


def interactive_f22_builder():
    """交互式F-22脚本构建器"""
    print("\n🛩️ 交互式F-22脚本构建器")
    print("=" * 50)
    
    agent = EnhancedPlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 获取用户输入
    print("请输入F-22配置参数（按回车使用默认值）:")
    
    config = {}
    
    # 平台名称
    name = input("平台名称 [f22_raptor]: ").strip()
    config["platform_name"] = name if name else "f22_raptor"
    
    # 阵营
    side = input("阵营 (BLUE/RED) [BLUE]: ").strip().upper()
    config["side"] = side if side in ["BLUE", "RED"] else "BLUE"
    
    # 位置
    try:
        lat = input("纬度 [35.0]: ").strip()
        lon = input("经度 [-118.0]: ").strip()
        config["position"] = {
            "lat": float(lat) if lat else 35.0,
            "lon": float(lon) if lon else -118.0
        }
    except ValueError:
        config["position"] = {"lat": 35.0, "lon": -118.0}
    
    # 高度
    try:
        alt = input("高度 (英尺) [15000]: ").strip()
        config["altitude"] = int(alt) if alt else 15000
    except ValueError:
        config["altitude"] = 15000
    
    # 航向
    try:
        hdg = input("航向 (度) [0]: ").strip()
        config["heading"] = int(hdg) if hdg else 0
    except ValueError:
        config["heading"] = 0
    
    print(f"\n生成的配置: {json.dumps(config, indent=2)}")
    print("\n生成的脚本:")
    print("-" * 50)
    
    script = agent.generate_f22_script(config)
    print(script)


def explain_f22_parameters():
    """解释F-22相关参数"""
    print("\n📚 F-22平台参数说明")
    print("=" * 50)
    
    agent = EnhancedPlatformAgent()
    if not agent.initialize():
        print("❌ Agent初始化失败")
        return
    
    # 重要参数列表
    important_params = [
        "altitude",
        "radar_signature", 
        "infrared_signature",
        "empty_mass",
        "fuel_mass",
        "spatial_domain"
    ]
    
    explanations = agent.explain_platform_parameters(important_params)
    print(explanations)


def main():
    """主函数"""
    print("🚀 F-22 Raptor脚本生成器")
    print("=" * 60)
    
    options = [
        ("1", "生成基础F-22脚本", generate_basic_f22),
        ("2", "生成自定义F-22脚本", generate_custom_f22),
        ("3", "生成F-22编队脚本", generate_f22_formation),
        ("4", "交互式F-22脚本构建器", interactive_f22_builder),
        ("5", "F-22参数说明", explain_f22_parameters),
        ("q", "退出", None)
    ]
    
    while True:
        print("\n请选择功能:")
        for opt_key, opt_desc, _ in options:
            print(f"  {opt_key}. {opt_desc}")
        
        choice = input("\n请输入选项: ").strip().lower()
        
        if choice == 'q':
            print("👋 再见！")
            break
        
        # 查找并执行对应功能
        for opt_key, opt_desc, func in options:
            if choice == opt_key and func:
                try:
                    func()
                except Exception as e:
                    print(f"❌ 执行失败: {e}")
                break
        else:
            print("❌ 无效选项，请重新选择")


if __name__ == '__main__':
    # 检查环境变量
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ 请设置DEEPSEEK_API_KEY环境变量")
        sys.exit(1)
    
    main()
