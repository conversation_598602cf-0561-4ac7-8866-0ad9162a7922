orphan

:   

# track

::: {.command block=""}
track \... end_track

::: parsed-literal

track

:   [position]() \... [mgrs_coordinate]() \... [altitude]() \... [range]() \... [bearing]() \... [elevation]() \... [speed]() \... [heading]() \... [type]() \... [side]() \... [spatial_domain]() \... [frequency]() \... [platform]() \... `_.aux_data`{.interpreted-text role="command"}\... end_aux_data

end_track
:::
:::

## Overview

The `track`{.interpreted-text role="command"} block is a subcommand of `platform`{.interpreted-text role="command"} or `track_manager`{.interpreted-text role="command"} that defines an initial track (or perception) of another object. Multiple track blocks may be specified in a given platform. Commands should only be specified for those attributes that are perceived to be known. All other attributes should be omitted.

If no track location is explicitly specified using [position](), [mgrs_coordinate](), [range](), or [bearing](), the truth location of the target [platform](), if one is provided, will be used to initially populate the location data in the track.

::: note
::: title
Note
:::

The [position]() and [mgrs_coordinate]() commands are mutually exclusive with [range]() and [bearing]().
:::

## Commands

::: command
position \<latitude-value\> \<longitude-value\>

Perceived position of the object.
:::

::: command
mgrs_coordinate \<MGRS-value\>

Perceived coordinates of the object in the Military Grid Reference System.
:::

::: command
altitude \<length-value\> \[ agl \| msl \]

Perceived altitude of the object. **agl** (above ground level) and **msl** (above mean sea level) specify the reference for the altitude specification. If the reference specification is omitted then **msl** is assumed.
:::

::: command
range \<length-value\>

Perceived range to the object from the initial position of the tracking platform.
:::

::: command
bearing \<angle-value\>

Perceived bearing to the object from the initial position of the tracking platform.
:::

::: command
elevation \<angle-value\>

Perceived elevation of the object from the initial position of the tracking platform.
:::

::: command
speed \<speed-value\>

Perceived speed of the object.
:::

::: command
heading \<angle-value\>

Perceived heading of the object.
:::

::: command
type \<platform-type\>

Perceived type of the object.
:::

::: command
side \<side-name\>

Perceived side (\"team\' or \"affiliation\') of the object.
:::

::: command
spatial_domain \[ land \| air \| surface \| subsurface \| space \]

Defines the perceived spatial domain of the object.
:::

::: command
frequency \<frequency\>

Perceived frequency of the object.
:::

::: command
platform \<platform-name\>

The platform whose truth location to use when initially populating the location data in the track, if no location is specified. The platform must be defined prior to the track.

::: warning
::: title
Warning
:::

Input file order dependency!
:::
:::
