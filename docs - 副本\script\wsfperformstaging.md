orphan

:   

# WsfPerformStaging

::: {.WsfPerformStaging .inherits .WsfOrbitalEvent}
**Input type:** `perform_staging<../perform_staging>`{.interpreted-text role="doc"}
:::

[WsfPerformStaging](#wsfperformstaging) is used to perform a staging operation when utilizing the `rocket orbital maneuvering<orbital_maneuvering_models.rocket>`{.interpreted-text role="ref"} model.

::: method
static WsfPerformStaging Construct(WsfOrbitalEventCondition aCondition)

Static method to create a [WsfPerformStaging](#wsfperformstaging) event.
:::
