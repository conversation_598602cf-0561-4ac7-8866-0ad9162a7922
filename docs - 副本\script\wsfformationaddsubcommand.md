orphan

:   

# WsfFormationAddSubCommand

::: {.WsfFormationAddSubCommand .inherits .WsfFormationCommand}
`WsfFormationAddSubCommand`{.interpreted-text role="class"} can be used to add a formation as a sub-formation to which the command is assigned. This will add the given formation as a sub-formation, but it will add it in a detached state. To have the added sub-formation attach to its new parent, use `WsfFormationAttachCommand`{.interpreted-text role="class"}.
:::

## Methods

::: method
WsfFormationAddSubCommand Construct(string aQualifiedName, WsfFormationOffset aOffset)

Create a command that will add the formation with the given qualified name with the given offset to the formation to which this command is assigned.
:::

::: method
WsfFormationAddSubCommand Construct(string aQualifiedName, string aProposedParent, WsfFormationOffset aOffset)

Create a command that works as above, but will instead add the formation as a sub-formation of one of the sub-formations of the formation to which the command is assigned.

For example, the following will add the formation \'lead\' as a subformation of \'yankee.alpha.one\':

    WsfFormationAddSubCommand cmd = WsfFormationAddSubCommand.Construct("lead", "alpha.one", WsfFormationOffset.Construct(...));
    WsfFormation yankee = WsfFormationManager.GetFormation("yankee");
    yankee.ExecuteCommand(cmd);
:::

::: method
string GetFormationToAdd()

Return the qualified name that this command will add as a sub-formation.
:::

::: method
WsfFormationOffset GetOffset()

Return the offset with which the sub-formation will be added.
:::
