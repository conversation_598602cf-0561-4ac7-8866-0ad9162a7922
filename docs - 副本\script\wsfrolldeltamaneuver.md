orphan

:   

# WsfRollDeltaManeuver

## Overview {#overview .WsfRollDeltaManeuver .inherits .WsfManeuver}

This maneuver sets a target change in roll angle for the platform to which it is assigned. This maneuver is done executing as soon as the target is set, so if there is a need to wait for the achieved change in roll angle, a `WsfManeuverConstraint`{.interpreted-text role="class"} must be used.

## Methods

::: method
WsfRollDeltaManeuver Construct(double aRollDeltaDeg)

Construct a maneuver that will set a target change in roll angle for the platform to which the maneuver is assigned.
:::

::: method
double GetRollDelta()

Return this maneuver\'s target change in roll angle.
:::
