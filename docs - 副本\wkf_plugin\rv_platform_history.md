orphan

:   

# Platform History - Mystic

The platform history feature displays the geometric history of a platform in a view either in the form of a trace line:

![image](../images/rv_trace.png)

or a wing ribbon:

![image](../images/rv_wing_ribbons.png)

## Platform Options

Platform History can be enabled/disabled in the `platform options<../mystic_platform_options>`{.interpreted-text role="doc"}

## Preferences {#rv_platform_history_preferences}

Platform History can be configured in the `preferences<../mystic_preferences>`{.interpreted-text role="doc"}

![The preferences for the Platform History plugin are under \'Map\'.](../images/rv_platform_history_prefs.png)

### Trace Lines

The width option determines line width in pixels.

The length option determines how much time is displayed from the current position to the end of the trace line.

Trace lines may be colored by:

> -   **team-color:** The platform\'s team color.
> -   **platform name:** A unique color for each platform.
> -   **state:** Chooses color depending on the platform\'s state, which can be normal, detected, tracked, being attacked, or being killed.
> -   **behavior:** Chooses color depending on which node is executing on each platform\'s `advanced behavior tree<../advanced_behavior_tree>`{.interpreted-text role="doc"}. By default, the plugin will automatically choose a color for each behavior tree leaf node. Alternatively, colors may be assigned to behavior nodes in the AFSIM input. In the preferences, colors may be changed, and behaviors may be hidden or unhidden. A default color will be used for platforms without a behavior tree.

![image](../images/rv_platform_history_state.png)

![image](../images/rv_platform_history_behavior.png)

### Wing Ribbons

The length option determines how much time is displayed from the current position to the end of the wing ribbon. The width scale allow the ribbon to be exaggerated. The color scheme may be team-color, green, or gray. The transparency on death will change to transparency of the wing-ribbon when a platform is killed.
