# uci_post_command.rst.txt.md
orphan

:   





This type is used in :class\`UCI_POST_CommandMessage\` to define commands for `WSF_IRST_SENSOR`{.interpreted-text role="model"}.



::: method
static UCI_POST_Command Construct(UCI_POST_ActivityCommand POSTActivityCommand)

Creates an instance of an [UCI_POST_Command](#uci_post_command) that, if approved, will result in one or more POST Activies being modified and reported via `UCI_POST_ActivityMessage`{.interpreted-text role="class"}.
:::

::: note
::: title
Note
:::

The ability to change activities has yet to be implemented.
:::

::: method
static UCI_POST_Command Construct(UCI_POST_CapabilityCommand POSTCapabilityCommand)

Creates an instance of an [UCI_POST_Command](#uci_post_command) that, if approved, will result in one or more POST Activies being created and reported via `UCI_POST_ActivityMessage`{.interpreted-text role="class"}.
:::