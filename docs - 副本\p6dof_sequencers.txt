# p6dof_sequencers.rst.txt.md
orphan

:   



A `p6dof_object_type`{.interpreted-text role="doc"} may include one or more \"sequencers\" which provide a means for a P6Dof object to take action based on the occurrence of one or more events. Sequencers can be used to perform a range of tasks, such as deploying high-drag devices on a retarded-fall bomb several seconds after release from its parent aircraft, or triggering staging events on a multi-stage rocket. Sequencers are a powerful, yet easy to use, means of controlling a sequence of events for a P6Dof object that can be triggered by events based on time, altitude, speed, etc.

::: command
sequencer \... end_sequencer

A sequencer block has an identifier, which is the string argument after \'sequencer\'. This identifier must be unique within the `p6dof_object_type`{.interpreted-text role="doc"}. The sequencer block has to include at least one [Sequencer Actions](#sequencer-actions), which is the action the sequencer shall perform when it is activated. The sequencer block may include one or more [Sequencer Events](#sequencer-events), if no Sequencer Events are provided the sequencer may only be activated through the `WsfP6DOF_Mover.ActivateSequencer`{.interpreted-text role="method"} script function. The sequencer block may include one or more [Nested Sequencers](#nested-sequencers) which will be used once the containing sequencer has been activated \-- this allows for sequential events, where the second event cannot occur until the first event has occurred.

::: parsed-literal
sequencer \<string\>

> // [Sequencer Events](#sequencer-events) [event_timer]() \... [event_timer_int_msec]() \... [event_timer_int_nanosec]() \... [event_lifetime]() \... [event_lifetime_int_msec]() \... [event_lifetime_int_nanosec]() \... [event_above_alt]() \... [event_below_alt]() \... [event_dynamic_pressure_above]() \... [event_dynamic_pressure_below]() \... [event_static_pressure_above]() \... [event_static_pressure_below]() \... [event_fuel_percent_below]() \... [event_ground_distance]() \... [event_nx_above]() \... [event_nx_below]() \... [event_ny_above]() \... [event_ny_below]() \... [event_nz_above]() \... [event_nz_below]() \... [event_released_from_parent]() \... [event_manual_input_trigger]() \... [event_manual_input_button]() \... [event_manual_input_button_released]() \... [event_boolean_input]() \... [event_null]() \... [all_events]() \...
>
> // [Sequencer Actions](#sequencer-actions) [action_null]() \... [action_jett_obj]() \... [action_jett_self]() \... [action_ignite_engine]() \... [action_ignite_subobject]() \... [action_ignite_self]() \... [action_terminate_thrust]() \... [action_shutdown_engine]() \... [action_shutdown_subobject]() \... [action_change_aero_mode]() \... [action_enable_size_factor]() \... [action_set_graphical_model]() \... [action_activate_subobject_sequencer]() \... [action_enable_controls]() \... [action_disable_controls]() \...
>
> // [Nested Sequencers](#nested-sequencers) [sequencer]() \... end_sequencer

end_sequencer

Each sequencer is named, using the first argument after sequencer.
:::

An example of a simple, timer-event sequencer that jettisons two external fuel tank objects after 120 seconds has elapsed is:

    sequencer DropTipTanks
       event_timer      120.0 sec
       action_jett_obj  RightTipTank
       action_jett_obj  LeftTipTank
    end_sequencer
:::



Sequencer events are tested to determine if a sequencer should be activated. If more than one event is specified, the sequencer will activate if at least one of the events is true. If is it desired to activate only if all of the events are true, the [all_events]() command should be included.

::: command
event_timer \<time-value\>

This event is based on reaching the specified time. The timer will begin counting once the sequencer is being tested. For top-level sequencers, this is equivalent to [event_lifetime]() but if the sequencer is nested, its counter will not begin running until its parent sequencer has been activated.
:::

::: command
event_timer_int_msec \<integer-value\>

This event is based on reaching the specified time, which is an integer number of milliseconds. The timer will begin counting once the sequencer is being tested. For top-level sequencers, this is equivalent to [event_lifetime_int_msec]() but if the sequencer is nested, its counter will not begin running until its parent sequencer has been activated.
:::

::: command
event_timer_int_nanosec \<integer-value\>

This event is based on reaching the specified time, which is an integer number of nanoseconds. The timer will begin counting once the sequencer is being tested. For top-level sequencers, this is equivalent to [event_lifetime_int_nanosec]() but if the sequencer is nested, its counter will not begin running until its parent sequencer has been activated.
:::

::: command
event_lifetime \<time-value\>

This event is based on reaching the specified lifetime. The lifetime timer begins counting once the platform is active (alive). This timer is different from the [event_timer]() since it uses a platform-centric timer as opposed to a sequencer-centric timer.
:::

::: command
event_lifetime_int_msec \<integer-value\>

This event is based on reaching the specified lifetime, which is an integer number of milliseconds. The lifetime timer begins counting once the platform is active (alive). This timer is different from the [event_timer_int_msec]() since it uses a platform-centric timer as opposed to a sequencer-centric timer.
:::

::: command
event_lifetime_int_nanosec \<integer-value\>

This event is based on reaching the specified lifetime, which is an integer number of nanoseconds. The lifetime timer begins counting once the platform is active (alive). This timer is different from the [event_timer_int_nanosec]() since it uses a platform-centric timer as opposed to a sequencer-centric timer.
:::

::: command
event_above_alt \<length-value\>

This event is based on the platform being above the specified altitude (MSL).
:::

::: command
event_below_alt \<length-value\>

This event is based on the platform being below the specified altitude (MSL).
:::

::: command
event_dynamic_pressure_above \<pressure-value\>

This event is based on the platform having a dynamic pressure above the specified value.
:::

::: command
event_dynamic_pressure_below \<pressure-value\>

This event is based on the platform having a dynamic pressure below the specified value.
:::

::: command
event_static_pressure_above \<pressure-value\>

This event is based on the platform having a static pressure above the specified value.
:::

::: command
event_static_pressure_below \<pressure-value\>

This event is based on the platform having a static pressure below the specified value.
:::

::: command
event_fuel_percent_below \<real-value\>

Specify the fuel value as a normalized number (50% is 0.50). This event is based on the fuel level in the \"main tank\" being below the specified value. The main tank is considered to be the first tank specified in the `propulsion_data`{.interpreted-text role="command"} block.
:::

::: command
event_ground_distance \<length-value\>

This event is based on the platform being beyond the specified distance (great circle) from the point at which it started.
:::

::: command
event_nx_above \<acceleration-value\>

This event is based on the x-acceleration, known as Nx, (in body coordinates) being above the specified value.
:::

::: command
event_nx_below \<acceleration-value\>

This event is based on the y-acceleration, known as Nx, (in body coordinates) being below the specified value.
:::

::: command
event_ny_above \<acceleration-value\>

This event is based on the y-acceleration, known as Ny, (in body coordinates) being above the specified value.
:::

::: command
event_ny_below \<acceleration-value\>

This event is based on the y-acceleration, known as Ny, (in body coordinates) being below the specified value.
:::

::: command
event_nz_above \<acceleration-value\>

This event is based on the z-acceleration, known as Nz, (in body coordinates) being above the specified value.
:::

::: command
event_nz_below \<acceleration-value\>

This event is based on the z-acceleration, known as Nz, (in body coordinates) being below the specified value.
:::

::: command
event_released_from_parent

This event is based on the platform being newly released from its parent.
:::

::: command
event_manual_input_trigger

This event allows a trigger pulled input from the P6Net interface to activate.
:::

::: command
event_manual_input_button

This event allows an input button down from the P6Net interface to activate.
:::

::: command
event_manual_input_button_released

This event allows an input button release from the P6Net interface to activate.
:::

::: command
event_boolean_input \<string\>

This event is based on a `flight_controls.control_boolean`{.interpreted-text role="command"} within `flight_controls`{.interpreted-text role="command"} being true. This allows an event to be controlled by a ManualPilot or SyntheticPilot, for example.
:::

::: command
event_null

This purpose of this event is for to prevent any action without an external command to active the sequencer (see [action_activate_subobject_sequencer]()).
:::

::: command
all_events

This flag indicates that the sequencer will be activated only if all events for the sequencer are true.
:::



::: command
action_null

This action should be specified when no action is desired, such as for a parent sequencer that does not need any action to be performed at its level.
:::

::: command
action_jett_obj \<string\>

This action will jettison the specified subobject.
:::

::: command
action_jett_self

This action will jettison the object from its parent.
:::

::: command
action_ignite_engine \<string\>

This action will \"ignite\" the specified engine and set thrust (if applicable) to 100%.
:::

::: command
action_ignite_subobject \<string\>

This action will \"ignite\" all engines on the specified subobject.
:::

::: command
action_ignite_self

This action will \"ignite\" all engines on the object.
:::

::: command
action_terminate_thrust

This action will terminate the thrust for all engines on the object (shutdown the engines).
:::

::: command
action_shutdown_engine \<string\>

This action will \"shutdown\" the specified engine and set thrust (if applicable) to 0%.
:::

::: command
action_shutdown_subobject \<string\>

This action will \"shutdown\" all engines on the specified subobject.
:::

::: command
action_change_aero_mode \<string\>

This action will change the object\'s `aero_mode`{.interpreted-text role="command"} to the specified mode.
:::

::: command
action_enable_size_factor

This action will enable the size factor. See `P6DOF_Size_Factor_Parameters`{.interpreted-text role="ref"} for details.
:::

::: command
action_set_graphical_model \<integer-value\>

This action will set the current graphical model for networked tools.
:::

::: command
action_activate_subobject_sequencer \<string\> \<string\>

The first string is the name of the subobject and the second string is the name of the sequencer. This action will activate the sequencer with the specified name on the specified subobject. If no subobject or sequencer exists, no action will be performed.
:::

::: command
action_enable_controls

This action will enable control input for normal operation. See [action_disable_controls]() to turn off the controls.
:::

::: command
action_disable_controls

This action will disable control input, zeroing all control inputs. See [action_enable_controls]() to turn on the controls.
:::



Nested sequencers are sequencers that are defined within another (parent) sequencer. The nested sequencer does not have its events tested until its parent sequencer has been activated. This allows for sequential events, since the second event cannot occur until the first event has occurred. In many cases, the various parent sequencers will use an [action_null]() action, since no action is desired until the final sequencer is activated.

Consider this case:

    sequencer
       name             Sequencer1
       event_above_alt  80000.0 ft
       action_null
       sequencer
          name             Sequencer2
          event_below_alt   70000.0 ft
          action_null
          sequencer
             name                  Sequencer3
             event_timer           10.0 sec
             action_ignite_engine  BoostRocket
          end_sequencer
       end_sequencer
    end_sequencer

In this case, the sequencer chain will ignite a BoostRocket 10 seconds after the platform comes back down below 70,000 feet after first reaching 80,000 feet.



::: command
remove_sequencer \<string\>

This removes the specified sequencer. It is typically used to remove a sequencer that was derived from a parent class object. If no sequencer with the specified name exists, the command is ignored.
:::

Return to `p6dof_object_types`{.interpreted-text role="doc"} or `p6dof_object_type`{.interpreted-text role="doc"}