# electronic_warfare_demos.rst.txt.md
orphan

:   

::: demo
electronic_warfare
:::



| Electronic warfare consists of Electronic Attack (EA) and Electronic Protect (EP), each consisting of multiple techniques.
| These techniques are discussed briefly with a link to a simple example file demonstrating the corresponding technique in
| the Test Tool. Then a category of miscellaneous examples is discussed.



| EA is the utilization of electronic devices to trick or deceive electronic sensors. EA is comprised of general factors
| and effects. The general factors combine with zero or more effects to achieve the overall desired Effective Radiated Power (ERP).
| 
| \* General Factors
|    Power, frequency, and bandwidth are the general factors of EA. Depending on the relative values of these factors and
|    the emitter under attack the attack may be considered barrage or spot jamming.
| 
| \* Barrage Noise Jamming
|   A barrage noise jamming attack is the transmitting of a large frequency band with respect to an emitter\'s bandwidth.
|   Barrage noise jamming can be accomplished with multiple jammers on adjacent frequencies, a single wideband transmitter,
|   or a transmitter capable of frequency sweep fast enough to appear radiating simultaneously over wide band.
|       barrage_jammer.txt (single wideband transmitter)
| 
| \* Spot Noise Jamming
|    The transmitting of a small frequency band with respect an emitter\'s bandwidth is considered spot noise jamming.
|       spot_jammer.txt
|       agile_jamming.txt
|       ..\\iads_demo\\platforms\\soj.txt
|       ..\\aea_demo\\platforms\\soj.txt
| 
| \* Technique Effects
|    Any one or more of the following effects can be used with the general factors above to further describe the overall EA
|    effect. Random Pulse Jamming and False Target Jamming effects are developed for a specific technique to simplify the user inputs.
|    Delta Gain and Track Error are general purpose effects. These effects allow for the modeling of future and/or unknown techniques
|    where only the effect is understood or in a design role to determining desired effects for a given scenario outcome.
| 
| \* Random Pulse Jamming (RPJ)
|     RPJ is the EA technique of transmitting random pulses of noise.
|       rpj_jammer.txt
|       ..\\aea_demo\\weapons\\soj_base_jammer.txt
| 
| \* False Target Jamming
|     An EA technique of deception, False Target Jamming creates the perception of false targets to tracking systems causing them to break lock.
|       ..\\aea_demo\\weapons\\soj_base_jammer.txt
| 
|   - Pulse Density Based
|       False target are introduced to an emitter based on the pulse density.
|         false-target_jamming_1.txt
| 
|   - Constrained
|       False target range to emitter is not less than jammer range to emitter.
|         false-target_jamming_2.txt
| 
|   - Random
|       False targets are random from scan to scan.
|         false-target_jamming_2.txt
|         false-target_jamming_3.txt
| 
| \* Delta Gain (jamming improvements)
|     Delta Gain (jamming improvements) is the general purpose EA effect on an emitter\'s gain of the jamming signal.
|       spot_jammer.txt
| 
| \* Track Error Inducement
|     Track Error Inducement is the general purpose EA effect on an emitter\'s perceived tracks accuracy.
|       \<track_error.txt\>
|       ..\\aea_demo\\weapons\\soj_base_jammer.txt



| EP is the utilization of electronic devices to reduce or eliminate the effect of EA on electronic sensors.
| 
| \* Sidelode Blanker (SLB)
|     SLB uses an auxiliary omnidirectional antenna to ignore the main antennas signal when the auxiliary antenna\'s signal is larger.
|     The auxiliary typically has a larger gain than any of the main antennas sidelobes. Thus during successful jamming the radar
|     will be blank expect for the direction of the jammer, thus rendering the direction of jamming as the only useful information.
|       rpj_jammer.txt
|       false-target_jamming_1.txt
|       false-target_jamming_2.txt
|       false-target_jamming_3.txt
|       ..\\iads_demo\\sensor\\ew_radar.txt
|       ..\\aea_demo\\sensor\\ew_radar.txt
| 
| \* Sidelobe Canceller (SLC)
|     Similar to SLB except through additional circuitry the direction of jamming is used to cancel its effect through the scan
|     cycle rather than blanking the main antennas signal. Thus re-enabling target detections off angle of jamming.
|       barrage_jammer.txt
|       spot_jammer.txt
|       ..\\aea_demo\\sensor\\ew_radar.txt
| 
| \* Pulse Suppression
| 
| \* Agility
|     Agility is the rapid switching of the transmitted frequency and receiving only that frequency during the receiving time window.
|       agile_jamming.txt
|       ..\\aea_demo
| 
| \* Delta Gain (jamming reduction)
|      A Delta Gain (jamming reduction) compensates jamming improvements of a Delta Gain EA effect.
|       spot_jammer.txt
| 
| \* Track Error Mitigation
|     A Track Error Mitigation compensates track errors from a Track Error Inducement effect.
|       \<track_error.txt\>
|       ..\\aea_demo



| \* Open Loop Jamming Assignment
|     Open Loop Jamming occurs without the perception of an emitter.
|       spot_jammer.txt
| 
| \* Closed Loop (ESM Based) EW Selection
|     Jamming based on the perception of an emitter.
|       agile_jamming.txt
|       ..\\iads_demo\\platforms\\soj.txt
|       ..\\aea_demo\\weapons\\soj_base_jammer.txt
| 
| \* DIS Jamming
|       \<dis_jammer_1\>
|       \<dis_jammer_2\>
| 
| \* Command Center Correlation/Fusion
|       false-target_command_center.txt
| 
| \* False Target sensor detect/track plotting capability
|      Allows for the plotting of detect and track events (not-detecting, detecting, tracking) via the observer and gnuplot
|      plotting file. Creates 4 files, one replay file and three \*.dat files containing the data for no-detect, detect
|      and tracking.
|      \<ft_track-plot.txt\> & \<ft_track_plot.gnu\> use gnuplot with command \>load \"ft_track_plot.gnu\" to plot output.